package com.fxiaoke.open.erpsyncdata.main.crmevent


import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2023/11/15 17:01:01
 */
class ErpGlobalConfigSyncChangeSetListenTest extends Specification {
    @Unroll
    def "#name-测试出站"() {
        when:
        def outboundEi = 84801
        def listen = new ErpGlobalConfigSyncChangeSetListen(
                tenantConfigurationManager: Mock(TenantConfigurationManager) {
                    findGlobal(*_) >> {
                        String type = it[0]
                        if (type.equals(TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name())) {
                            if (passSyncData == null) {
                                return null
                            }

                            return new ErpTenantConfigurationEntity(configuration: passSyncData)
                        } else if (ConfigCenter.globalTenantListConfigType.contains(type)) {
                            def s = hasoutbusiness.contains(type) ? outboundEi : ""
                            return new ErpTenantConfigurationEntity(
                                    type: type,
                                    dataCenterId: "0",
                                    channel: channel,
                                    configuration: "1;2;3;" + s
                            )
                        }
                    }
                }
        )

        List<Map<String, Object>> outbound = []
        listen.innerOutBoundPart(outboundEi, "", [], { outbound.add(it) })

        then:
        result == outbound

        where:
        name                | passSyncData                                           | channel | hasoutbusiness                                         || result
        "paas-没有数据"     | null                                                   | "0"     | []                                                     || [[tenant_id: "84801"]]
        "pass-没有企业数据" | '{"726451":["optool_batch"]}'                          | "0"     | []                                                     || [[tenant_id: "84801"]]
        "pass-有企业数据"   | '{"84801":["optool_batch"]}'                           | "0"     | []                                                     || [[tenant_id: "84801", TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']]
        "pass-有企业数据2"  | '{"726451":["optool_batch"],"84801":["optool_batch"]}' | "0"     | []                                                     || [[tenant_id: "84801", TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']]


        "Tenant-没有数据"   | null                                                   | "0"     | []                                                     || [[tenant_id: "84801"]]
        "Tenant-有1数据"    | null                                                   | "0"     | ["SERIALIZE_NULL_TENANTS"]                             || [[tenant_id: "84801", TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"SERIALIZE_NULL_TENANTS"}]']]
        "Tenant-有1数据2"   | null                                                   | "0"     | ["checkQuotaDisableTenants"]                           || [[tenant_id: "84801", TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"checkQuotaDisableTenants"}]']]
        "Tenant-有1数据3"   | null                                                   | "ALL"   | ["checkQuotaDisableTenants"]                           || [[tenant_id: "84801", TenantListConfigWithKey: '[{"channel":"ALL","dcId":"0","type":"checkQuotaDisableTenants"}]']]
        "Tenant-有2数据"    | null                                                   | "ALL"   | ["SERIALIZE_NULL_TENANTS", "checkQuotaDisableTenants"] || [[tenant_id: "84801", TenantListConfigWithKey: '[{"channel":"ALL","dcId":"0","type":"SERIALIZE_NULL_TENANTS"},{"channel":"ALL","dcId":"0","type":"checkQuotaDisableTenants"}]']]


        "都有数据"          | '{"84801":["optool_batch"]}'                           | "0"     | ["checkQuotaDisableTenants"]                           || [[tenant_id: "84801", TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}', TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"checkQuotaDisableTenants"}]']]
    }

    @Unroll
    def "#name-测试入站"() {
        when:
        def inboundEi = 88521
        Map<String, String> inbounData = [:]
        def listen = new ErpGlobalConfigSyncChangeSetListen(
                redissonClient: Mock(RedissonClient) {
                    getLock(*_) >> Mock(RLock) {
                        tryLock(*_) >> true
                    }
                },
                tenantConfigurationManager: Mock(TenantConfigurationManager) {
                    findGlobal(*_) >> {
                        String type = it[0]
                        if (type.equals(TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name())) {
                            if (inboundSyncData == null) {
                                return null
                            }

                            return new ErpTenantConfigurationEntity(configuration: inboundSyncData)
                        } else if (ConfigCenter.globalTenantListConfigType.contains(type)) {
                            if (oldValue == null) {
                                return null
                            }
                            return new ErpTenantConfigurationEntity(
                                    type: type,
                                    channel: "ALL",
                                    dataCenterId: "0",
                                    configuration: oldValue
                            )
                        }
                    }
                    findOne(*_) >> {
                        if (oldValue == null) {
                            return null
                        }
                        return new ErpTenantConfigurationEntity(
                                type: it[0],
                                configuration: oldValue
                        )
                    }

                    updateConfig(*_) >> {
                        inbounData.put(it[2] + "_" + it[3], it[4])
                    }
                }
        )

        listen.innerInBoundPart(inboundEi, "", [], outboundJson as JSONObject, [])

        then:
        result == inbounData

        where:
        name            | outboundJson                                                                                                                                                                                                        | inboundSyncData                                        | oldValue    || result
        "没入站数据"    | [:]                                                                                                                                                                                                                 | null                                                   | _           || [:]
        "没入站数据2"   | [tenant_id: "84801"]                                                                                                                                                                                                | null                                                   | _           || [:]
        "没入站数据3"   | [asdlhjkl: "84801"]                                                                                                                                                                                                 | null                                                   | _           || [:]


        "pass-没值"     | [TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']                                                                                                                                                         | null                                                   | _           || ['0_TENANT_NEED_PASS_DATASOURCE': '{"88521":["optool_batch"]}']
        "pass-无关值"   | [TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']                                                                                                                                                         | '{"726451":["optool_batch"]}'                          | _           || ['0_TENANT_NEED_PASS_DATASOURCE': '{"726451":["optool_batch"],"88521":["optool_batch"]}']
        "pass-有值"     | [TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']                                                                                                                                                         | '{"88521":["optool_batch"]}'                           | _           || ['0_TENANT_NEED_PASS_DATASOURCE': '{"88521":["optool_batch"]}']
        "pass-有值2"    | [TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']                                                                                                                                                         | '{"726451":["optool_batch"],"88521":["optool_batch"]}' | _           || ['0_TENANT_NEED_PASS_DATASOURCE': '{"726451":["optool_batch"],"88521":["optool_batch"]}']

        "pass-并值"     | [TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']                                                                                                                                                         | '{"88521":["123"]}'                                    | _           || ['0_TENANT_NEED_PASS_DATASOURCE': '{"88521":[]}']
        "pass-并值2"    | [TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}']                                                                                                                                                         | '{"726451":["optool_batch"],"88521":["123"]}'          | _           || ['0_TENANT_NEED_PASS_DATASOURCE': '{"726451":["optool_batch"],"88521":[]}']


        "Tenant-没值"   | [TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"SERIALIZE_NULL_TENANTS"}]']                                                                                                                           | _                                                      | null         | ['0_SERIALIZE_NULL_TENANTS': '88521']
        "Tenant-没值2"  | [TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"SERIALIZE_NULL_TENANTS"},{"channel":"ALL","dcId":"0","type":"checkQuotaDisableTenants"}]']                                                            | _                                                      | null         | ['0_SERIALIZE_NULL_TENANTS': '88521', ALL_checkQuotaDisableTenants: '88521']

        "Tenant-无关值" | [TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"SERIALIZE_NULL_TENANTS"},{"channel":"ALL","dcId":"0","type":"checkQuotaDisableTenants"}]']                                                            | _                                                      | '123;545'    | ['0_SERIALIZE_NULL_TENANTS': '123;545;88521', ALL_checkQuotaDisableTenants: '123;545;88521']

        "Tenant-有值"   | [TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"SERIALIZE_NULL_TENANTS"},{"channel":"ALL","dcId":"0","type":"checkQuotaDisableTenants"}]']                                                            | _                                                      | '123;88521'  | [:]

        "兼容-没值"     | [TenantList: '["SERIALIZE_NULL_TENANTS"]']                                                                                                                                                                          | null                                                   | null         | ['ALL_SERIALIZE_NULL_TENANTS': '88521']
        "兼容-没值2"    | [TenantList: '["SERIALIZE_NULL_TENANTS","checkQuotaDisableTenants"]']                                                                                                                                               | null                                                   | null         | ['ALL_SERIALIZE_NULL_TENANTS': '88521', 'ALL_checkQuotaDisableTenants': '88521']

        "兼容-无关值"   | [TenantList: '["SERIALIZE_NULL_TENANTS","checkQuotaDisableTenants"]']                                                                                                                                               | null                                                   | '123;545;'   | ['ALL_SERIALIZE_NULL_TENANTS': '123;545;88521', 'ALL_checkQuotaDisableTenants': '123;545;88521']

        "兼容-有值"     | [TenantList: '["SERIALIZE_NULL_TENANTS","checkQuotaDisableTenants"]']                                                                                                                                               | null                                                   | '123;88521;' | [:]

        "pass+Tenant"   | [TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"SERIALIZE_NULL_TENANTS"},{"channel":"ALL","dcId":"0","type":"checkQuotaDisableTenants"}]', TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}'] | null                                                   | null         | ['0_SERIALIZE_NULL_TENANTS': '88521', ALL_checkQuotaDisableTenants: '88521', '0_TENANT_NEED_PASS_DATASOURCE': '{"88521":["optool_batch"]}']
        "pass+Tenant2"  | [TenantListConfigWithKey: '[{"channel":"0","dcId":"0","type":"SERIALIZE_NULL_TENANTS"},{"channel":"ALL","dcId":"0","type":"checkQuotaDisableTenants"}]', TENANT_NEED_PASS_DATASOURCE: '{"84801":["optool_batch"]}'] | '{"726451":["optool_batch"],"88521":["123"]}'          | '123;545'    | ['0_SERIALIZE_NULL_TENANTS': '123;545;88521', ALL_checkQuotaDisableTenants: '123;545;88521', '0_TENANT_NEED_PASS_DATASOURCE': '{"726451":["optool_batch"],"88521":[]}']
    }
}
