package com.fxiaoke.open.erpsyncdata.main.dispatcher.parser;

import com.fxiaoke.dispatcher.common.BaseEvent;
import com.fxiaoke.dispatcher.repository.mongo.AbstractMongoEventParser;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DispatcherEventData;
import org.bson.Document;

/**
 * <AUTHOR> (^_−)☆
 */
public class DispatcherMongoEventParser extends AbstractMongoEventParser {
    @Override
    public BaseEvent doParse(Document event) {
        DispatcherEventData message = new DispatcherEventData();
        return message.parse(event);
    }
}
