package com.fxiaoke.open.erpsyncdata.main.dispatcher.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.dispatcher.listener.EventListener;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.ListenBigCostLog;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DispatcherEventData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.LogConsoleStrategyFilter;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BomUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.main.service.CRMOuterServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.fxiaoke.ps.ProtostuffUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

import static com.fxiaoke.dispatcher.common.Constants.EventStatus;



/**
 * <AUTHOR>
 * @Date: 10:25 2021/6/21
 * @Desc:
 */
@Slf4j
public class DispatcherEventListen implements EventListener<DispatcherEventData> {
    @Autowired
    private SyncMainService syncMainService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private CRMOuterServiceImpl crmOuterService;
    @Autowired
    private SpeedLimitManager speedLimitManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;

    private static Random randomIntGenerator = new Random();

    private void delayDispatch(String tenantId, DispatcherEventData event) {
        try {
            event.setStatus(EventStatus.STATUS_REDISPATCH);
            if (event.getTries() >= 500) {
                event.setDispatchTime(System.currentTimeMillis() + randomIntGenerator.nextInt(120 * 60 * 1000 - 110 * 60 * 1000) + 110 * 60 * 1000);
                if (event.getTries() == 500) {
                    log.info("delay dispatch retry500：ei:{},obj:{},dataid:{}", tenantId, event.getApiName(), event.getDataId());
                }
            } else if (event.getTries() >= 200) {
                event.setDispatchTime(System.currentTimeMillis() + randomIntGenerator.nextInt(60 * 60 * 1000 - 50 * 60 * 1000) + 50 * 60 * 1000);
                if (event.getTries() == 200) {
                    log.info("delay dispatch retry200：ei:{},obj:{},dataid:{}", tenantId, event.getApiName(), event.getDataId());
                }
            } else if (event.getTries() >= 50) {
                /**随机延迟20-30分钟, 为什么要加10分钟范围的随机延迟？ 因为一批数据如果都延迟一个固定时间，可能会挤到下一个时间点又开始拥堵。**/
                event.setDispatchTime(System.currentTimeMillis() + randomIntGenerator.nextInt(30 * 60 * 1000 - 20 * 60 * 1000) + 20 * 60 * 1000);
                if (event.getTries() == 50) {
                    log.info("delay dispatch retry50：ei:{},obj:{},dataid:{}", tenantId, event.getApiName(), event.getDataId());
                }
            } else if (event.getTries() >= 20) {
                event.setDispatchTime(System.currentTimeMillis() + randomIntGenerator.nextInt(5 * 60 * 1000));
            }
        } catch (Exception e) {
            log.warn("alertBlockToFSDeveloper get exception, ", e);
        }
    }

    private boolean rateLimitAllow(String tenantId, Integer eventDataTenantType, String eventDataSourceObjApiName) {
        List<String> destObjAPINameList = Lists.newArrayList();
        try {
            List<SyncPloyDetailSnapshotData2> listSyncPloyDetail = syncPloyDetailSnapshotService.listNewestEnableSyncPloyDetailsSnapshots(tenantId,
                    eventDataSourceObjApiName, eventDataTenantType, Lists.newArrayList(tenantId)).getData();
            if (null != listSyncPloyDetail) {
                destObjAPINameList = listSyncPloyDetail.stream().map(v -> v.getDestObjectApiName()).collect(Collectors.toList());
            }
            //这里的destObjAPINameList，已经是主对象apiname了
            long writeRemain = speedLimitManager.getObjRemainLimit(tenantId, eventDataTenantType, destObjAPINameList, eventDataSourceObjApiName);
            log.debug("trace rate tenantId:{} eventDataTenantType:{},eventDataSourceObjApiName:{},toCrmRemain:{}",
                    tenantId, eventDataTenantType, eventDataSourceObjApiName, writeRemain);
            return writeRemain > 0;
        } catch (Exception e) {
            log.warn("trace rate tenantId get exception, ", e);
        }
        return false;
    }
 

    /**
     * 聚合分发框架从mongo中扫到一批新数据后，调用listen（）处理。
     */
    @Override
    @InvokeMonitor(tenantId = "#events.get(0).getTenantId()", dcId = "", invokeType = InvokeTypeEnum.ERPDSS, count = "#events.size()", data = "#events", objAPIName = "", action = ActionEnum.GET_FROM_DISPATCHER, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    public void listen(List<DispatcherEventData> events) {
        long now = System.currentTimeMillis();

        final Map<String, List<DispatcherEventData>> collect = events.stream().collect(Collectors.groupingBy(DispatcherEventData::getTenantId));

        collect.forEach((tenantId, eventDataList) -> process(eventDataList, tenantId, now));      

        try {
            // 构建监控日志
            ListenBigCostLog monitorLog = ListenBigCostLog.builder()
                    .tenantId(events.get(0).getTenantId())
                    .podName(System.getenv("HOSTNAME"))  // 获取当前pod名称
                    .threadId(String.valueOf(Thread.currentThread().getId())) // 获取当前线程ID
                    .listenCost((System.currentTimeMillis() - now) / 1000) // 计算耗时(秒)
                    .build();
            
            // 发送监控日志
            BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(monitorLog));
        } catch (Exception e) {
            log.warn("Send listen cost monitor log failed", e);
        }
    }

    private void process(List<DispatcherEventData> events, String tenantId, long now) {
        String oldTraceId = TraceUtil.get();
        String eventIdStr = events.stream().map(v -> v.getId() + "," + v.getDataId()).collect(Collectors.joining(";"));

        log.info("scan events tenantId={} time={} eventIdStr={}", tenantId, now, eventIdStr);
        TraceUtil.setEi(tenantId);
        Map<String, List<DispatcherEventData>> eventDataMap =
                events.stream().collect(Collectors.groupingBy(event -> event.getApiName()));

        for (String sourceObjAPIName : eventDataMap.keySet()) {
            for (int i = 0; i < eventDataMap.get(sourceObjAPIName).size(); i++) {
                DispatcherEventData m = eventDataMap.get(sourceObjAPIName).get(i);               

                //1.一次listen, 超过55s处理不完也要返回
                if (System.currentTimeMillis() - now > 55*1000) {
                    delayDispatch(tenantId, m);
                    continue;
                }

                //2.检查是否超过限速，做延迟分发
                boolean allowDiaptch = rateLimitAllow(tenantId, m.getTenantType(), sourceObjAPIName);
                if (!allowDiaptch) {
                    delayDispatch(tenantId, m);
                    continue;
                }
                //避免组装logId出现错误
                try {
                    TraceUtil.initTrace(m.getSyncLogId());
                    LogIdUtil.reset(m.getSyncLogId());
                    //有快照id.设置dataCenterId
                    SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData =
                            syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(tenantId, m.getPloyDetailSnapshotId()).getData();
                    String realApiName = m.getApiName();
                    LogIdUtil.setSourceObjApiName(realApiName);
                    //转换真实apiName
                    if (m.getTenantType().equals(TenantType.ERP)) {
                        realApiName = idFieldConvertManager.getRealObjApiName(m.getTenantId(), m.getApiName());
                    }
                    if (ObjectUtils.isNotEmpty(syncPloyDetailSnapshotData)) {
                        boolean erp2crm = m.getTenantType().equals(TenantType.ERP);
                        String dataCenterId = erp2crm ?
                                syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceDataCenterId() : syncPloyDetailSnapshotData.getSyncPloyDetailData().getDestDataCenterId();
                        LogIdUtil.setDataCenterId(dataCenterId, erp2crm);
                    }
                    LogIdUtil.setRealObjApiName(realApiName);
                    LogIdUtil.setDataId(m.getDataId());

                } catch (Exception e) {
                    log.warn("sync dispatch processor logId exception:{}", e.getMessage());
                }
                /**每10条查询一次CRM数据缓存，先通过批量获取crm的数据。ThreadLocal的数据存储减少后续的findOne **/
                if ((i % 10) == 0) {
                    crmOuterService.removeBatchCache();
                    //需要查询缓存了
                    batchGetObjectData(tenantId, m.getApiName(), i, i + 10, eventDataMap.get(sourceObjAPIName));
                }
                TimePointRecorderStatic.begin(tenantId, m.getApiName(), m.getDataId(), m.getCreateTime());
                List<SyncDataContextEvent> eventDataList = getEventDataList(m);
                if (CollectionUtils.isNotEmpty(eventDataList)) {
                    String preLogId = LogIdUtil.get();
                    for (int j = 0; j < eventDataList.size(); j++) {//如果有多次，不管上一次返回结果都执行，如果抛异常就异常结束
                        SyncDataContextEvent eventData = eventDataList.get(j);
                        if (eventDataList.size() > 1) {//加一层
                            LogIdUtil.buildChildLogIdRestId(preLogId, j);
                        }
                        //如果不为修改，则先填充数据
                        if (eventData.getSourceEventType() != EventTypeEnum.UPDATE.getType()) {
                            //该逻辑原来在消费paas mq里面对所有不等于更新的数据执行，在这里对不为修改的数据执行。只处理了crm->erp
                            fillSourceData(m.getTenantType(), eventData.getSourceData());
                            log.info("SyncDispatchProcessor.fillSourceData,eventData.getSourceData()={}", eventData.getSourceData());
                        }
                        log.info("listen logid={} eventType={} eventTypeList={} priority={}", m.getSyncLogId(), eventData.getSourceEventType(), m.getEventTypeList(), m.getPriority());
                        Result2<ObjectDataSyncMsg> result = syncMainService.syncDataMain(eventData);
                        setUpStatus(result, m); //可能会有多次赋值
                    }
                }
                TraceUtil.removeTrace();
                LogIdUtil.clear();
                LogConsoleStrategyFilter.removeDataReceiveType();
            }
        }
        crmOuterService.removeBatchCache();
        TraceUtil.initTrace(oldTraceId);
        log.debug("processed {} items", events.size());
    }

    public List<SyncDataContextEvent> getEventDataList(DispatcherEventData m) {
        List<SyncDataContextEvent> eventDataList = Lists.newArrayList();
        SyncDataContextEvent eventData = new SyncDataContextEvent();
        eventData.setPloyDetailSnapshotId(m.getPloyDetailSnapshotId());
        eventData.setSourceTenantType(m.getTenantType());
        eventData.setSourceEventType(m.getEventType());
        eventData.setSourceData(mergeSourceData(m.getUpdateJson(), m.getErpSourceData()));
        eventData.setSyncLogId(m.getSyncLogId());
        eventData.setDataVersionList(m.getDataVersion());
        eventData.setSourceContextUserId(m.getSourceContextUserId());
        eventData.setLocale(m.getLocale());
        eventData.setForceSyncSnapshotIds(m.getForceSyncSnapshotIds());
        eventData.setAllObjCount(m.getAllObjCount());
        if (CollectionUtils.isNotEmpty(m.getRemark())) {
            eventData.setSourceRemark(m.getRemark().toString());
        }
        if (CollectionUtils.isNotEmpty(m.getDataVersion())) {
            eventData.setDataVersion(m.getDataVersion().get(m.getDataVersion().size() - 1));
        }
        eventData.setDataReceiveType(m.getDataReceiveType());
        if (CollectionUtils.isEmpty(m.getEventTypeList())) {
            if (m.getEventType() == null) {
                eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
            } else {
                eventData.setSourceEventType(m.getEventType());
            }
            eventDataList.add(eventData);
            return eventDataList;
        }
        if (m.getEventTypeList().contains(EventTypeEnum.DELETE_DIRECT.getType())) {//删除
            if (m.getEventTypeList().contains(EventTypeEnum.INVALID.getType())) {
                SyncDataContextEvent invalidData = BeanUtil.deepCopy(eventData, SyncDataContextEvent.class);
                invalidData.setSourceEventType(EventTypeEnum.INVALID.getType());
                eventDataList.add(invalidData);
                SyncDataContextEvent deleteData = BeanUtil.deepCopy(eventData, SyncDataContextEvent.class);
                deleteData.setSourceEventType(EventTypeEnum.DELETE_DIRECT.getType());
                eventDataList.add(deleteData);
                return eventDataList;
            } else {
                eventData.setSourceEventType(EventTypeEnum.DELETE_DIRECT.getType());
                eventDataList.add(eventData);
                return eventDataList;
            }
        } else if (m.getEventTypeList().contains(EventTypeEnum.RECOVER.getType())) {//恢复
            if (m.getEventTypeList().contains(EventTypeEnum.INVALID.getType())) {//包含作废
                if (m.getEventTypeList().size() == 2) {//只包含恢复和作废
                    return Lists.newArrayList();//返回空，不处理
                } else {//包含恢复和作废和其他，默认其他
                    List<Integer> eventTypeList = m.getEventTypeList().stream().filter(event -> event != EventTypeEnum.INVALID.getType() && event != EventTypeEnum.RECOVER.getType()).collect(Collectors.toList());
                    m.setEventTypeList(eventTypeList);
                    return getEventDataList(m);//去掉恢复作废，再走一次逻辑
                }
            } else {//不包含作废
                SyncDataContextEvent recoverData = BeanUtil.deepCopy(eventData, SyncDataContextEvent.class);
                recoverData.setSourceEventType(EventTypeEnum.RECOVER.getType());
                eventDataList.add(recoverData);
                if (m.getEventTypeList().contains(EventTypeEnum.UPDATE.getType())) {//包含更新
                    SyncDataContextEvent updateData = BeanUtil.deepCopy(eventData, SyncDataContextEvent.class);
                    updateData.setSourceEventType(EventTypeEnum.UPDATE.getType());
                    eventDataList.add(updateData);
                }
                return eventDataList;
            }
        } else if (m.getEventTypeList().contains(EventTypeEnum.INVALID.getType())) {//作废，忽略其他的
            eventData.setSourceEventType(EventTypeEnum.INVALID.getType());
            eventDataList.add(eventData);
            return eventDataList;
        } else if (m.getEventTypeList().contains(EventTypeEnum.ADD.getType())) {//新增
            if (m.getEventTypeList().contains(EventTypeEnum.UPDATE.getType())) {//如果包含更新，那么先更新再新增，更新如果触发了同步成功，那么新增就会被过滤掉
                SyncDataContextEvent updateData = BeanUtil.deepCopy(eventData, SyncDataContextEvent.class);
                updateData.setSourceEventType(EventTypeEnum.UPDATE.getType());
                eventDataList.add(updateData);
                SyncDataContextEvent addData = BeanUtil.deepCopy(eventData, SyncDataContextEvent.class);
                addData.setSourceEventType(EventTypeEnum.ADD.getType());
                eventDataList.add(addData);
                return eventDataList;
            } else {
                eventData.setSourceEventType(EventTypeEnum.ADD.getType());
                eventDataList.add(eventData);
                return eventDataList;
            }
        } else {
            eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
            eventDataList.add(eventData);
            return eventDataList;
        }
    }

    private void batchGetObjectData(String tenantId, String apiName, Integer fromIndex, Integer endIndex, List<DispatcherEventData> dispatcherEventDataList) {
        List<DispatcherEventData> eventDataList = CollectionUtil.sub(dispatcherEventDataList, fromIndex, endIndex);
        if (CollectionUtil.isNotEmpty(eventDataList)) {
            //根据TenantType判断，查询crm的数据
            List<String> queryDataIds = eventDataList.stream().filter(item -> item.getTenantType().equals(TenantType.CRM)).map(DispatcherEventData::getDataId).collect(Collectors.toList());
            crmOuterService.batchGetObjectDataAndCache(tenantId, apiName, queryDataIds);
        }
    }

    private void setUpStatus(Result2<ObjectDataSyncMsg> result, DispatcherEventData m) {
        /**标记为STATUS_DONE的数据，聚合分发框架会从mongo中删除*/
        try {
            boolean needRetry = Optional.ofNullable(LogIdUtil.getBaseLogNoCreate()).map(item -> item.getNeedRetry()).orElse(false);
            if (needRetry || ResultCodeEnum.SYSTEM_BASE_COMPONENT_ERROR.getErrCode().equals(result.getErrCode())) {
                log.warn("setData status dispactch id:{} tenantid:{}", m.getDataId(), m.getTenantId());
                m.setStatus(EventStatus.STATUS_DELAY_DISPATCH);
            } else {
                m.setStatus(EventStatus.STATUS_DONE);
            }
        } catch (Exception e) {
            log.warn("setData status Exception", e);
            m.setStatus(EventStatus.STATUS_DONE);
        }
        LogIdUtil.removeRetry();
    }

    private void fillSourceData(Integer srcTenantType, ObjectData srcData) {
        if (srcTenantType.equals(TenantType.ERP)) {
            return;
        }
        try {
            String objectApiName = srcData.getApiName();
            HeaderObj headerObj = new HeaderObj(Integer.valueOf(srcData.getTenantId()), CrmConstants.SYSTEM_USER);

            String oldDataId = srcData.getId();
            String dataId = BomUtils.getBomInstanceId(srcData.getId(), srcData.getApiName());
            log.info("SyncDispatchProcessor.fillSourceData,dataId={}", dataId);
            GetByIdArg getByIdArg = new GetByIdArg();
            getByIdArg.setDescribeApiName(objectApiName);
            getByIdArg.setDataId(dataId);
            getByIdArg.setIncludeInvalid(true);
            Result<ObjectDataGetByIdV3Result> getByIdResult = objectDataServiceV3.getById(headerObj, getByIdArg);
            if (getByIdResult.isSuccess()) {
                if (getByIdResult.getData() == null || getByIdResult.getData().getObjectData() == null) {
                    //数据已作废或已删除，这里不再补全了
                    return;
                }
                com.fxiaoke.crmrestapi.common.data.ObjectData crmObjectData = getByIdResult.getData().getObjectData();
                srcData.putAll(crmObjectData);
                srcData.putId(oldDataId);
            }
            log.info("SyncDispatchProcessor.fillSourceData,srcData={}", srcData);
        } catch (Exception e) {
            log.error("fill sourceData failed", e);
        }
    }

    private ObjectData mergeSourceData(List<String> sourceDatas, String erpSourceData) {
        if (StringUtils.isNotBlank(erpSourceData)) {
            return JSONObject.parseObject(erpSourceData, ObjectData.class);
        }
        if (CollectionUtils.isEmpty(sourceDatas)) {
            return null;
        }
        List<ObjectData> sourceDataList = Lists.newArrayList();
        sourceDatas.forEach(sourceData -> {
            sourceDataList.add(JSONObject.parseObject(sourceData, ObjectData.class));
        });
        ObjectData sourceData = sourceDataList.get(0);
        for (ObjectData objectData : sourceDataList) {
            Long maxModifiedTime = sourceData.getLong("modifiedTime");
            Long modifiedTime = objectData.getLong("modifiedTime");
            if (maxModifiedTime >= modifiedTime) {
                for (String thisKey : objectData.keySet()) {
                    sourceData.putIfAbsent(thisKey, objectData.get(thisKey));
                }
            } else {
                for (String thisKey : sourceData.keySet()) {
                    objectData.putIfAbsent(thisKey, sourceData.get(thisKey));
                }
                sourceData = objectData;
            }
        }
        return sourceData;
    }
}
