package com.fxiaoke.open.erpsyncdata.main.dispatcher.processor;

import com.fxiaoke.open.erpsyncdata.common.monitor.MemoryMonitor;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/20
 */
@Slf4j
@Component
public class ProcessLimiter {
    @Autowired
    private ProcessLimiter processLimiter;

    /**
     * 限制最少的空闲空间大小，小于等于0则不使用
     */
    private long minFreeSize = 4194304000L;

    /**
     * 限制最小的空闲空间占比,b优先使用,小于等于0则不使用,单位%
     */
    private int minFreeSizePercent = 0;

    /**
     * 是否开启
     */
    private boolean enableLimiter = false;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("erp-sync-data-all", config -> {
            minFreeSize = config.getLong("ProcessLimiter.minFreeSize", 4194304000L);
            minFreeSizePercent = config.getInt("ProcessLimiter.minFreeSizePercent", 0);
            enableLimiter = config.getBool("ProcessLimiter.minFreeSizePercent", false);

        });
    }

    public boolean permitProcess() {
        if (!enableLimiter) {
            return true;
        }
        MemoryMonitor.MemoryInfo memoryInfo = MemoryMonitor.getInstance().getMemoryInfo();
        long minFreeSize;
        if (minFreeSizePercent > 0) {
            minFreeSize = memoryInfo.getMax() * 100 / minFreeSizePercent;
        } else {
            minFreeSize = this.minFreeSize;
        }
        boolean b = memoryInfo.getMaxFree() > minFreeSize;
        if (b) {
            processLimiter.permitCount();
        } else {
            log.info("deny process,minFreeSize:{},memoryInfo:{}", minFreeSize, memoryInfo);
            processLimiter.denyCount();
        }
        return b;
    }

    public void permitCount() {
    }

    public void denyCount() {
    }

}
