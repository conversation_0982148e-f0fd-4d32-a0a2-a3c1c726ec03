package com.fxiaoke.open.erpsyncdata.main.dispatcher.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.dispatcher.EventPorter;
import com.fxiaoke.dispatcher.common.BaseEvent;
import com.fxiaoke.dispatcher.common.Constants.EventStatus;
import com.fxiaoke.dispatcher.common.EventCollection;
import com.fxiaoke.dispatcher.common.MessageHelper;
import com.fxiaoke.helper.StringHelper;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.monitor.TimePointRecorderStatic;
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmDataManager;
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmObjAndFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DispatcherEventData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjDispatchPriorityConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.DispatcherMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.AviatorUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpTenantConfiguration;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.TenantConfigurationService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Joiner;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.*;

import static java.lang.Math.abs;

/**
 * <AUTHOR>
 * @Date: 10:25 2021/6/21
 * @Desc:
 */
@Slf4j
public class DispatcherMqListener implements MessageListenerConcurrently {
    private Map<String, List<String>> tenantId2ApiNameList = new HashMap<>();
    private long aggregationTime = 10000L;
    @Autowired
    private TenantConfigurationService tenantConfigurationService;
    @Autowired
    private CrmObjAndFieldManager crmObjAndFieldManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;
    @Autowired
    private CrmDataManager crmDataManager;
    @Autowired
    private EventPorter eventPorter;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("erp-sync-data-doDispatcher", config -> {
            String tenantId2ApiNames = config.get("dispatcher.tenantId.apiNames", "{}");
            Map<String, List<String>> tenantId2ApiNameList = JSON.parseObject(tenantId2ApiNames, Map.class);
            this.tenantId2ApiNameList = tenantId2ApiNameList;
            this.aggregationTime = config.getLong("aggregation.time", 10000L);
        });
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        Multimap<EventCollection, BaseEvent> eventMultimap = ArrayListMultimap.create();
        LinkedHashMap<String, BaseEvent> eventMap = new LinkedHashMap<>();
        for (MessageExt msg : list) {
            BaseEvent event = parse(msg);
            if (event != null) {
                BaseEvent oldVal = eventMap.putIfAbsent(event.getKey(), event);
                if (oldVal != null) {
                    //重复id数据，则merge
                    oldVal.merge(event);
                } else {
                    //新数据，放到待插入队列
                    EventCollection eventCollection = EventCollection.parseFrom(event.getTopic());
                    eventMultimap.put(eventCollection, event);
                }
            }
        }
        eventMultimap.asMap().forEach((k, v) -> {
            List<BaseEvent> eventList = Lists.newArrayList(v);
            eventPorter.upsert(k, eventList);
        });
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 从聚合分发框架的mq中拉取信息后，逐条调用parse（）转换为BaseEvent结构，
     * 再存储到聚合分发框架的mongo中。
     * 可参考com.fxiaoke.dispatcher.mq.PullTask.parseMessages()
     */
    public BaseEvent parse(MessageExt msg) {
        JSONObject jsonObject = JSON.parseObject(StringHelper.toString(msg.getBody()));
        log.info("event parse,msgId:{},jsonObject={}", msg.getMsgId(), jsonObject);
        // 提取 tenantType
        Integer tenantType = jsonObject.getInteger("sourceTenantType");
        // 提取 eventType
        Integer eventType = jsonObject.getInteger("sourceEventType");
        // 提取 ployDetailSnapshotId
        String ployDetailSnapshotId = jsonObject.getString("ployDetailSnapshotId");

        String syncLogId = jsonObject.getString("syncLogId");

        Integer dataReceiveType = jsonObject.getInteger("dataReceiveType");

        String sourceContextUserId = jsonObject.getString("sourceContextUserId");

        String sourceRemark = jsonObject.getString("sourceRemark");

        Long version = jsonObject.getLong("dataVersion");
        final JSONArray forceSnapshotIds = jsonObject.getJSONArray("forceSyncSnapshotIds");
        List<String> forceSyncSnapshotIds = Objects.nonNull(forceSnapshotIds) ? forceSnapshotIds.toJavaList(String.class) : null;

        JSONObject sourceData = jsonObject.getJSONObject("sourceData");
        if (sourceData == null) {
            log.warn("cannot found keys, json:{}", jsonObject);
            return null;
        }
        // 提取 objectDescribeApiName
        String objectApiName = sourceData.getString("object_describe_api_name");
        if (StringUtils.isBlank(objectApiName)) {
            log.warn("cannot found object_describe_api_name, json:{}", jsonObject);
            return null;
        }
        // 提取 tenantId
        String tenantId = sourceData.getString("tenant_id");
        //提取 dataId
        String dataId = sourceData.getString("_id");
        sourceData.put("modifiedTime", msg.getBornTimestamp());
        if (dataId == null) {
            log.warn("cannot found id, json:{}", jsonObject);
            return null;
        }

        // 唯一ID(tenantId + objectDescribeApiName + dataId + ployDetailSnapshotId)
        String uniqKey;
        if (StringUtils.isNotBlank(ployDetailSnapshotId)) {
            uniqKey = MessageHelper.md5(tenantId, objectApiName, dataId, ployDetailSnapshotId);
        } else {
            uniqKey = MessageHelper.md5(tenantId, objectApiName, dataId);
        }
        // 构建分类（tenantId + objectDescribeApiName）
        String category = Joiner.on('^').join(tenantId, objectApiName);

        // 根据tenantId查询topic名称
        String topic = getTenantTopic(tenantType, tenantId, objectApiName, dataId) + "@DEFAULT";

        long tempAggregationTime = getTempAggregationTime(tenantType, tenantId, objectApiName);
        //有些waiting的数据需要延迟发送的
        Long delayDispatcherTime = jsonObject.getLong("delayDispatcherTime");
        if (ObjectUtils.isNotEmpty(delayDispatcherTime)) {
            tempAggregationTime += delayDispatcherTime;
        }
        String locale = jsonObject.getString("locale");
        //如果传输进来，优先取传输的。不传或者传50，将根据配置计算
        Integer priority = jsonObject.getInteger("priority");
        if (priority == null || priority == 50) {
            priority = Opt.ofTry(() -> getPriority(tenantType, tenantId, objectApiName, sourceData)).orElse(50);
        }
        Integer allObjCount = jsonObject.getInteger("allObjCount");
        long now = System.currentTimeMillis();
        long dispatchTime = now + tempAggregationTime;//分发时间，当前时间+固定聚合时间
        DispatcherEventData data = DispatcherEventData.builder()
                .topic(topic)
                .key(uniqKey)
                .category(category)
                .tenantId(tenantId)
                .createTime(now)
                .modifiedTime(msg.getBornTimestamp())
                .dispatchTime(dispatchTime)
                .priority(priority)
                .commits(1)
                .locale(locale)
                .forceSyncSnapshotIds(forceSyncSnapshotIds)
                .allObjCount(allObjCount)
                .status(EventStatus.STATUS_READY)
                .build();
        data.setDataId(dataId);
        data.setApiName(objectApiName);
        data.setEventTypeList(Lists.newArrayList(eventType));
        data.setEventType(eventType);
        data.setPloyDetailSnapshotId(ployDetailSnapshotId);
        data.setTenantType(tenantType);
        if (tenantType != null && TenantTypeEnum.ERP.getType() == tenantType) {
            data.setErpSourceData(JacksonUtil.toJson(sourceData));
            if (needSetUpdateJsonField()) {
                data.setUpdateJson(Lists.newArrayList(JacksonUtil.toJson(sourceData)));
            }
        } else {
            data.setUpdateJson(Lists.newArrayList(JacksonUtil.toJson(sourceData)));
        }
        data.setSyncLogId(syncLogId);
        data.setDataReceiveType(dataReceiveType);
        data.setSourceContextUserId(sourceContextUserId);
        if (version != null) {
            data.setDataVersion(Lists.newArrayList(version));
        } else {
            Lists.newArrayList();
        }
        if (StringUtils.isNotBlank(sourceRemark)) {
            data.setRemark(Lists.newArrayList(sourceRemark));
        }
        TimePointRecorderStatic.asyncRecord(tenantId, objectApiName, dataId, "parse");
        return data;
    }

    private boolean needSetUpdateJsonField() {
        List<String> needSetEnv = tenantConfigurationManager.getNeedSetUpdateJsonFieldEnv();
        if (needSetEnv != null && ConfigHelper.getProcessInfo().getProfile() != null) {
            return needSetEnv.contains(ConfigHelper.getProcessInfo().getProfile());
        }
        return false;
    }

    public long getTempAggregationTime(Integer sourceTenantType, String tenantId, String objectApiName) {
        //读取聚合时间配置。没有配置则默认2000ms
        long tempAggregationTime = aggregationTime;
        try {
            if (TenantTypeEnum.CRM.getType() == sourceTenantType) {//crm->erp
                String masterObjApiName = crmObjAndFieldManager.getCrmObjMasterObjApiName(tenantId, objectApiName);//如果找不到描述或者本来就是是主对象，返回自己
                if (StringUtils.isNotBlank(masterObjApiName) && !masterObjApiName.equals(objectApiName)) {//从对象
                    long detailAggregationTime = tenantConfigurationManager.getCrmDetailObjDefaultAggregationTime();//从对象默认时间，可被MONGO_DISPATCHER_DATA_AGGREGATION_TIME覆盖
                    tempAggregationTime = detailAggregationTime;
                }
            }
            Result<ErpTenantConfiguration> erpTenantConfigurationResult = tenantConfigurationService.queryConfig(tenantId, "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.MONGO_DISPATCHER_DATA_AGGREGATION_TIME.toString());
            if (ObjectUtils.isNotEmpty(erpTenantConfigurationResult.getData())) {
                Map map = JSONObject.parseObject(erpTenantConfigurationResult.getData().getConfiguration(), Map.class);
                Object dispatchTimeObj = map.get(objectApiName);
                tempAggregationTime = ObjectUtils.isNotEmpty(dispatchTimeObj) ? Long.valueOf(dispatchTimeObj.toString()) : tempAggregationTime;
            }
        } catch (Exception e) {
            log.error("parse for ei:{} get exception,:{} ", tenantId, e);
        }
        return tempAggregationTime;
    }

    /**
     * @param sourceTenantType
     * @param tenantId
     * @param objectApiName
     * @param dataId
     * @return
     */
    public String getTenantTopic(Integer sourceTenantType, String tenantId, String objectApiName, String dataId) {
        // 防止蒙牛云的集合太多导致mongo扛不住
        if (StringUtils.isNumeric(tenantId) && ConfigCenter.isAllowMergeDispatcherCollection(tenantId)) {
            return DispatcherMongoStore.getMergeDispatcherCollectionName(tenantId);
        }

        //默认集合名称
        String topic = "buf_" + tenantId;
        //特殊场景下根据id拆分更多的topic,通过id取余放到不同集合
        if (StringUtils.isNotEmpty(dataId) && tenantConfigurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.DISPATCHER_MONGO_SPLIT_BYID_TENANTS)) {
            topic = topic + "_" + abs(dataId.hashCode() % 10);
        }
        log.debug("tenantId2ApiNameList={}", tenantId2ApiNameList);
        if (tenantId2ApiNameList != null && tenantId2ApiNameList.keySet().contains(tenantId)
                && tenantId2ApiNameList.get(tenantId) != null && tenantId2ApiNameList.get(tenantId).contains(objectApiName)) {//对象单独队列
            topic = topic + "_" + objectApiName;
        } else {
            /**
             * 主从对象单独队列：
             * 目前只支持crm->erp
             * 与对象单独队列互斥，如果配置了没有从对象的主对象，效果与对象单独队列一样，不过判断逻辑复杂一点，所以这种情况请使用对象单独队列配置。
             * 例：{"1":["xxxMainObj"]},企业1的xxxMainObj及其所有从对象会被聚合为一个集合:buf_tenantId_(i_)masterObjApiName;
             */
            if (TenantTypeEnum.CRM.getType() == sourceTenantType) {//crm->erp
                Map<String, List<String>> mergeMasterAndDetailTenantObj = tenantConfigurationManager.getMergeMasterAndDetailTenantObj();
                if (mergeMasterAndDetailTenantObj.containsKey(tenantId) || mergeMasterAndDetailTenantObj.containsKey("*")) {
                    String masterObjApiName = crmObjAndFieldManager.getCrmObjMasterObjApiName(tenantId, objectApiName);//如果找不到描述或者本来就是是主对象，返回自己
                    if (StringUtils.isNotBlank(masterObjApiName)) {
                        if ((mergeMasterAndDetailTenantObj.containsKey(tenantId) && mergeMasterAndDetailTenantObj.get(tenantId).contains(masterObjApiName))
                                || (mergeMasterAndDetailTenantObj.containsKey("*") && mergeMasterAndDetailTenantObj.get("*").contains(masterObjApiName))) {
                            topic = topic + "_" + masterObjApiName;
                        }
                    }
                }
            }
        }
        return topic;
    }

    public int getPriority(Integer tenantType, String tenantId, String objectApiName, JSONObject sourceData) {
        int priority = 50;
        if (sourceData == null) {
            return priority;
        }
        if (TenantType.CRM.equals(tenantType)) {
            if (sourceData.size() <= CommonConstant.crmCommonField.size() + 1) {
                //检查仅有 统计 计算 引用字段变化，将固定是低优先级
                Set<String> keySet = new HashSet<>(sourceData.keySet());
                CommonConstant.crmCommonField.forEach(keySet::remove);
                if (keySet.size() == 1) {
                    String field = keySet.iterator().next();
                    Boolean isSpecialField = crmObjAndFieldManager.objFieldIsCountOrFormulaOrQuoteField(tenantId, objectApiName, field);
                    if (isSpecialField) {//是统计、计算、引用字段
                        priority = 1001;
                    }
                }
            }
        }
        //通过配置计算 优先级
        Integer priorityByConfig = calPriorityByConfig(tenantType, tenantId, objectApiName, sourceData);
        if (priorityByConfig != null) {
            return priorityByConfig;
        }
        if (objectApiName.contains("SalesOrderProductObj") || objectApiName.contains("SalesOrderObj")) {
            //订单数据优先同步
            priority = 1;
        }
        return priority;
    }

    private Integer calPriorityByConfig(Integer tenantType, String tenantId, String objectApiName, JSONObject sourceData) {
        //对象级别配置
        Map<String, ObjDispatchPriorityConfig> objConfigMap = plusTenantConfigManager.getObjConfig(tenantId, "0", TenantConfigurationTypeEnum.OBJ_DISPATCH_PRIORITY_CONFIG_MAP, new TypeReference<Map<String, ObjDispatchPriorityConfig>>() {
        });
        if (objConfigMap == null || !objConfigMap.containsKey(objectApiName)) {
            return null;
        }
        ObjDispatchPriorityConfig config = objConfigMap.get(objectApiName);
        List<ObjDispatchPriorityConfig.PriorityExpr> priorityExprList = config.getPriorityExprList();
        Integer priority = config.getPriority();
        if (CollUtil.isEmpty(priorityExprList)) {
            //数据级别判断 为空 不需要检查
            return priority;
        }
        Map<String, Object> checkingData = sourceData;
        if (TenantTypeEnum.CRM.getType() == tenantType) {
            //检查补全字段
            if (CollUtil.isNotEmpty(config.getFields())) {
                boolean needFind = config.getFields().stream().anyMatch(k -> !sourceData.containsKey(k));
                if (needFind) {
                    try {
                        String dataId = sourceData.getString("_id");
                        //不缓存
                        checkingData = crmDataManager.getByIdSelective(tenantId, objectApiName, dataId, config.getFields());
                    } catch (Exception e) {
                        log.warn("calPriorityByConfig crmDataManager.getByIdSelective error", e);
                        return priority;
                    }
                }
            }
        }
        for (ObjDispatchPriorityConfig.PriorityExpr priorityExpr : priorityExprList) {
            boolean match = AviatorUtils.normalConditionValid(priorityExpr.getExpr(), checkingData, true);
            if (match) {
                return priorityExpr.getPriority();
            }
        }
        return priority;
    }
}
