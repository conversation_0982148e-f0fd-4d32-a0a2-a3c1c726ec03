package com.fxiaoke.open.erpsyncdata.main.crmevent;

import com.alibaba.fastjson.JSONObject;
import com.facishare.change.set.component.handler.module.Filter;
import com.facishare.change.set.listener.ChangeSetListener;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CopyTenantConfigurationArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CopyTenantConfigurationArg.CopyApiNameMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CopyTenantConfigurationArg.CopyErpObjArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CopyService;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/9/1
 */
@Slf4j
public class ErpSyncChangeSetListen extends ChangeSetListener {
    @Autowired
    private CopyService copyService;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private I18NStringManager i18NStringManager;

    public ErpSyncChangeSetListen() {
        super("erpSyncData-changeSet", "erpSyncInbound");
    }

    /**
     * 入站时 直接物理修改原始数据 重写该方法
     *
     * @param enterpriseId  入站企业id
     * @param tableName     出站组件的数据库表名
     * @param uniqueKeys    该表格的业务唯一id,upsert的where条件 对应表描述中的uniqueKeys
     * @param data          出站的数据 已替换企业id等数据
     * @param defaultFilter 表的默认过滤条件 对应表描述中的basicFilterCondition
     */
    @Override
    protected void innerInBoundPart(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter) throws Throwable {
        log.info("changeSet inner bound part,ei:{},table:{},uniqueKeys:{},data:{},filter:{}", enterpriseId, tableName, uniqueKeys, data, defaultFilter);
        switch (tableName) {
            case "sync_ploy_detail":
                inBoundStream(data);

                // field_extends 设置灰度初始化配置
                initFieldExtendDataByEi(String.valueOf(enterpriseId));
                break;
            default:
                throw new IllegalStateException(i18NStringManager.getByEi(I18NStringEnum.s974,enterpriseId+"") + tableName);
        }
    }

    public void initFieldExtendDataByEi(String tenantId) {
        new Thread(MonitorTaskWrapper.wrap(()->{
            erpObjectFieldsService.initFieldExtendData(Lists.newArrayList(tenantId));
        })).start();
    }

    public void inBoundStream(JSONObject data) {
        //找出映射的
        String sourceTenantId = data.getJSONArray("source_tenant_ids").getString(0);
        String destTenantId = data.getString("tenant_id");
        boolean erp2Crm = TenantType.ERP.equals(data.getInteger("source_tenant_type"));
        String sourceDcId = erp2Crm
                ? data.getString("source_data_center_id")
                : data.getString("dest_data_center_id");

        Integer direction = erp2Crm ? 1 : 2;
        ErpConnectInfoEntity sourceConnectInfo = erpConnectInfoDao.setGlobalTenant(sourceTenantId).getByIdAndTenantId(sourceTenantId, sourceDcId);
        ErpConnectInfoEntity destConnectInfo = erpConnectInfoDao.setGlobalTenant(destTenantId).getByNumber(destTenantId, sourceConnectInfo.getChannel(), sourceConnectInfo.getNumber());
        if (destConnectInfo == null) {
            throw new RuntimeException(i18NStringManager.getByEi(I18NStringEnum.s975,sourceConnectInfo.getTenantId()));
        }
        //初始化目标crm连接器
        ErpConnectInfoEntity destCrmDc = erpConnectInfoManager.getOrCreateCrmDc(destTenantId,null);
        String destDcId = destConnectInfo.getId();

        String sourceObj = data.getString("source_object_api_name");
        String destObj = data.getString("dest_object_api_name");
        String erpSplitObj = erp2Crm ? sourceObj : destObj;
        ErpObjectRelationshipEntity objectRelationship = erpObjectDao.setGlobalTenant(sourceTenantId).findBySplit(sourceTenantId, erpSplitObj);
        //复制ERP对象
        CopyErpObjArg copyErpObjArg = new CopyErpObjArg();
        copyErpObjArg.setSourceTenantId(sourceTenantId);
        copyErpObjArg.setSourceDataCenterId(sourceDcId);
        copyErpObjArg.setTargetTenantId(destTenantId);
        copyErpObjArg.setDestDataCenterId(destDcId);
        copyErpObjArg.setObjApiNames(Lists.newArrayList(objectRelationship.getErpRealObjectApiname()));
        Result<List<String>> copyObjRes = copyService.copyTenantErpObj(copyErpObjArg,null);
        if (!copyObjRes.isSuccess()) {
            throw new RuntimeException(i18NStringManager.getByEi(I18NStringEnum.s976,sourceConnectInfo.getTenantId()) + objectRelationship.getErpRealObjectApiname());
        }
        CopyTenantConfigurationArg.CopyPloyAndDetailArg copyPloyAndDetailArg = new CopyTenantConfigurationArg.CopyPloyAndDetailArg();
        copyPloyAndDetailArg.setSourceTenantId(sourceTenantId);
        copyPloyAndDetailArg.setSourceDataCenterId(sourceDcId);
        copyPloyAndDetailArg.setTargetTenantId(destTenantId);
        copyPloyAndDetailArg.setDestDataCenterId(destDcId);
        CopyApiNameMapping objMapping = CopyApiNameMapping.builder().sourceApiName(sourceObj).destApiName(destObj).build();
        copyPloyAndDetailArg.setObjApiNames(Lists.newArrayList(objMapping));
        copyPloyAndDetailArg.setDirection(direction);
        Result<List<String>> copyResult = copyService.copyTenantPloyAndPloyDetail(copyPloyAndDetailArg,null);
        if (!copyResult.isSuccess()) {
            String streamName = data.getString("integration_stream_name");
            throw new RuntimeException(i18NStringManager.getByEi2(I18NStringEnum.s977.getI18nKey(),
                    sourceConnectInfo.getTenantId(),
                    String.format(I18NStringEnum.s977.getI18nValue(), streamName),
                    Lists.newArrayList(streamName)));
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!ConfigCenter.ENABLE_CONSUME_SANDBOX_EVENT) {
            log.info("this env not consume changeSet event");
            return;
        }
        super.afterPropertiesSet();
    }
}
