package com.facishare.open.erp.connertor.sdk.model.linkedin;

import com.facishare.open.erp.connertor.sdk.model.Base;
import com.facishare.open.erp.connertor.sdk.model.dto.IdAndName;
import lombok.*;

import java.util.List;


public interface GetAllAdAccountForm {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends Base.ConnectorArg {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    class Result extends Base.Result {
        private List<String> adAccountIdList;
    }
}
