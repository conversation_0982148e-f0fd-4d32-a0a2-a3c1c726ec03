package com.fxiaoke.open.erpsyncdata.preprocess.impl


import cn.hutool.core.thread.ThreadUtil
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.TriggerPollingMongoManager
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailQueryObject2SyncDataMappingsData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.QueryObjectMappingData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MonitorReportManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.RedisDataSourceManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.PollingTempFailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataMongoDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingTempFailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CountSyncDataAndSendArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ExtraEventTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ErpHistoryTaskExecuteManager
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager
import com.fxiaoke.open.erpsyncdata.preprocess.manager.PollingMongoReportManager
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager
import com.fxiaoke.open.erpsyncdata.preprocess.manager.Send2DispatcherSpeedLimitManager
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpSyncTimeVO
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpTempResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager
import com.github.jedis.support.MergeJedisCmd
import com.github.trace.TraceContext
import org.apache.commons.lang3.tuple.Triple
import org.bson.types.ObjectId
import org.redisson.api.RLock
import org.redisson.api.RSemaphore
import org.redisson.api.RedissonClient
import spock.lang.Ignore
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.RejectedExecutionException
import java.util.concurrent.TimeUnit

/**
 * 另一个测试在 com.fxiaoke.open.erpsyncdata.preprocess.impl.ProbeErpDataServiceImplSpockTest
 * <AUTHOR> (^_−)☆
 * @date 2023/5/10
 */
@Unroll
class ProbeErpDataServiceImplTest extends Specification {


    String tenantId = "81961"
    boolean canGetLock = true

    ProbeErpDataServiceImpl probeErpDataService

    void setup() {
        //groovy.lang.ReadOnlyPropertyException: Cannot set readonly property: probeErpExecutor
//        def probeErpExecutor = Mock(Executor) {
//            execute(*_) >> { args ->
//                Runnable r = args[0] as Runnable
//                // 要让子线程在主线程运行以阻塞主线程
//                r.run()
//            }
//        }
        def redissonClient = Mock(RedissonClient) {
            getLock(*_) >> {
                println("get lock")
                return Mock(RLock) {
                    tryLock(*_) >> {
                        println("try lock: " + canGetLock)
                        return canGetLock
                    }
                }
            }
            getSemaphore(_) >> {
                return Mock(RSemaphore) {
                    tryAcquire(*_) >> true
                }
            }
        }
        def erpHistoryTaskExecuteManager = Mock(ErpHistoryTaskExecuteManager) {
            executeErpHistoryDataTask(*_) >> Result.newSuccess()
        }
        def erpObjManager = Mock(ErpObjManager) {
            getErpObj(*_) >> new ErpObjectEntity(dataCenterId: "test-dataCenterId", channel: ErpChannelEnum.ALL)
            getDetailsNeedSendAlone(*_) >> new HashSet<String>(Arrays.asList("test"))
        }
        def tenantConfigurationManager = Mock(TenantConfigurationManager) {
            findOne(*_) >> { args ->
                def e = args[3] as String
                if (TenantConfigurationTypeEnum.SERVER_TIME_DIFF.toString().equals(e)) {
                    // 结束时间
                    return new ErpTenantConfigurationEntity(configuration: "20000")
                }
                // 开始时间
                return new ErpTenantConfigurationEntity(configuration: "10000")
            }
            inWhiteList(*_) >> true
        }
        def conf = Mock(ConfigCenterConfig) {
            getSpecialListSize(*_) >> [:]
            getErpListPageSize(*_) >> 10
            getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET(*_) >> new HashSet(Arrays.asList("test_api_name"))
            getTENANT_LIMIT_PER_COUNT_SECOND_2CRM(*_) >> [:]
            getDefaultLimitPerCountSecond2CRM(*_) >> 1000
            getQueryTempCoefficient(*_) >> 0.5
        }
        def erpDataPreprocessService = Mock(ErpDataPreprocessService) {
            listErpObjDataFromMongo(*_) >> { args ->
                // 这里不是通过offset处理的，所以要判断arg的更新
                def arg = args[0] as QueryTempTimeFilterArg
                if ("temp_id".equals(arg.lastErpTempId)) {
                    // 防止为null
                    arg.setEndTime(System.currentTimeMillis())
                    return Result.newError("第二次即失败")
                }
                def srcData = new ObjectData()
                srcData.put("mongo_id", "test_mongo")
                srcData.putId("test_id")
                def event = new SyncDataContextEvent(sourceData: srcData, dataVersion: 1, sourceEventType: EventTypeEnum.ADD.getType(), detailData: ["test":[new ObjectData()]])
                def res = new ListErpTempResult(maxId: "1000", erpObjDataResultList: [event], complete: false, maxErpTempId: "temp_id")
                return Result.newSuccess(res)
            }
            listErpObjDataByTime(*_) >> { args ->
                def arg = args[0] as TimeFilterArg
                if (arg.offset != 0) {
                    return Result.newError("首次成功，否则失败")
                }
                def srcData = new ObjectData()
                srcData.put("mongo_id", "test_mongo")
                srcData.putId("test_id")
                def event = new SyncDataContextEvent(sourceData: srcData, dataVersion: 1, sourceEventType: EventTypeEnum.ADD.getType())
                // 第一个先不要完成
                def res = new ListErpObjDataResult(maxId: "1000", maxTime: 10000, complete: false, erpObjDataResultList: [event])
                return Result.newSuccess(res)
            }
        }
        def ployBreakManager = Mock(PloyBreakManager) // 全为空方法即可
        def pollingDataSpeedRateLimitManager = Mock(PollingDataSpeedRateLimitManager) // 全为空方法即可
        def pollingMongoReportManager = Mock(PollingMongoReportManager) // 空实现即可
        def monitorReportManager = Mock(MonitorReportManager) // 空实现即可
        def triggerPollingMongoManager = Mock(TriggerPollingMongoManager) // 空实现即可
        def notificationService = Mock(NotificationService) // 空实现即可
        def pollingTempFailDao = Mock(PollingTempFailDao) {
            getAndRetry(*_) >> {
                PollingTempFailEntity entity = new PollingTempFailEntity()
                entity.setTraceId("test-traceId")
                QueryTempTimeFilterArg arg = new QueryTempTimeFilterArg(endTime: 10000+System.currentTimeMillis(), tenantId: tenantId,
                                                    objAPIName: "test_api_name", detailsNeedSendAlone: new HashSet<String>(Arrays.asList("test")))
                def argStr = JacksonUtil.toJson(arg)
                entity.setTimeFilterArg(argStr)
                entity.setTryTime(5)
                return entity
            }
            // 其余方法皆为空实现即可
        }
        def probeErpDataManager = Mock(ProbeErpDataManager) {
            asyncSendErpDataMq(*_) >> 0
            buildBatchSendErpObjDataArg(*_) >> {
                SyncDataContextEvent event = new SyncDataContextEvent()
                return [event]
            }
        }
        def redis = Mock(MergeJedisCmd) {
            Set set = new HashSet()
            spop(*_) >> { args ->
                def key = args[0] as String
                if (set.contains(key)) {
                    println("redis: pop " + key)
                    set.remove(key)
                    return "test_api_name"
                }
                println("redis: pop null")
//                return null
                return "test_api_name"
            }
            sadd(*_) >> { args ->
                def key = args[0] as String
                println("redis: add " + key)
                if (set.contains(key)) {
                    return 0
                }
                set.add(key)
                return 1
            }
        }
        def redisDataSource = Mock(RedisDataSource) {
            get(*_) >> redis
        }
        def redisDataSourceManager = Mock(RedisDataSourceManager) {
            getTriggerPollingTempDataObj(*_) >> {
                def set = new HashSet()
                set.add("test_api_name")
                return set
            }
        }
        def erpSyncTimeDao = Mock(ErpSyncTimeDao) {
            setTenantId(*_) >> {
                def mock = Mock(ErpSyncTimeDao) {
                    updateById(*_) >> 1
                    listSyncExtentByTenantId(*_) >> {
                        def syncPloyDetailData = new SyncPloyDetailData(syncRules: new SyncRulesData(events: [ExtraEventTypeEnum.UPDATE.extraType]),
                                            destObjectApiName: "test_api_name")
                        def data = new ErpSyncExtentDTO(id: UUID.randomUUID().toString(), objectApiName: "test_api_name", operationType: ExtraEventTypeEnum.UPDATE.extraType,
                                syncPloyDetailData: syncPloyDetailData, tenantId: tenantId, lastSyncTime: System.currentTimeMillis() - 3600*1000)
                        return [data]
                    }
                }
                return mock
            }
        }
        def syncPloyDetailSnapshotDao = Mock(SyncPloyDetailSnapshotDao) {
            setTenantId(*_) >> {
                def mock = Mock(SyncPloyDetailSnapshotDao) {
                    get(*_) >> {
                        def entity = new SyncPloyDetailSnapshotEntity(syncPloyDetailId: "1000")
                        return entity
                    }
                }
                return mock
            }
            listBySourceTenantIdAndObjectApiName(*_) >> {
                def qmData = new QueryObjectMappingData(destObjectApiName: "test_api_name")
                def  mappingData = new DetailQueryObject2SyncDataMappingsData.DetailQueryObject2SyncDataMappingData(queryObjectMappingData: qmData)
                def detailQueryObject2SyncDataMappingsData = new DetailQueryObject2SyncDataMappingsData()
                detailQueryObject2SyncDataMappingsData.add(mappingData)
                def node = new IntegrationStreamNodesData.CheckSyncDataMappingNode(detailCheckSyncDataMappingData: detailQueryObject2SyncDataMappingsData)
                def data = new IntegrationStreamNodesData(checkSyncDataMappingNode: node)
                def entity = new SyncPloyDetailSnapshotEntity(syncPloyDetailData: new SyncPloyDetailData(integrationStreamNodes: data))
                return [entity]
            }
        }
        def send2DispatcherSpeedLimitManager = Mock(Send2DispatcherSpeedLimitManager) {
            needSendDispatcherNum(*_) >> -1
        }
        def erpTempDataManager = Mock(ErpTempDataManager) {
            getTenantLastSyncTime(*_) >> System.currentTimeMillis()
            updateStatusByIds(*_) >> Result.newSuccess()
        }
        def syncPloyDetailManager = Mock(SyncPloyDetailManager) {
            listPartialFieldsByTenantId(*_) >> {
                def api_name = "test_api_name"
                def entity_crm = new SyncPloyDetailEntity(status: SyncPloyDetailStatusEnum.ENABLE.getStatus(), sourceTenantType: TenantTypeEnum.CRM.getType(),
                                        integrationStreamName: "test", sourceObjectApiName: api_name, destObjectApiName: api_name, destDataCenterId: "test_dcId")
                def entity_erp = new SyncPloyDetailEntity(status: SyncPloyDetailStatusEnum.ENABLE.getStatus(), sourceTenantType: TenantTypeEnum.ERP.getType(),
                                        integrationStreamName: "test", sourceObjectApiName: api_name, destObjectApiName: api_name, sourceDataCenterId: "test_dcId")
                return [entity_crm, entity_erp]
            }
        }
        def syncDataMongoDao = Mock(CHSyncDataManager) {
            getAllTypeCount(*_) >> {
                def res = []
                // 右值为计数的值；中值对应状态；左值对应create，1为true，0为false
                res.add(Triple.of(0, SyncDataStatusEnum.TRIGGER_FAILED, 1))
                res.add(Triple.of(0, SyncDataStatusEnum.WRITE_SUCCESS, 1))
                res.add(Triple.of(0, SyncDataStatusEnum.BE_PROCESS, 1))
                res.add(Triple.of(1, SyncDataStatusEnum.TRIGGER_FAILED, 1))
                res.add(Triple.of(1, SyncDataStatusEnum.WRITE_SUCCESS, 1))
                res.add(Triple.of(1, SyncDataStatusEnum.BE_PROCESS, 1))
                return res
            }
        }
        def erpConnectInfoManager = Mock(ErpConnectInfoManager) {
            listByTenantId(*_) >> {
                def res = []
                res.add(new ErpConnectInfoEntity(id: "test_dcId", channel: ErpChannelEnum.ERP_K3CLOUD))
                res.add(new ErpConnectInfoEntity(id: "test_dcId", channel: ErpChannelEnum.ALL))
                return res
            }
            getByIdAndTenantId(*_) >> new ErpConnectInfoEntity(dataCenterName: "test_dcId")
            getFirstNumErpDcByTenantId(*_) >> new ErpConnectInfoEntity(id: "test")
        }
        def erpFieldManager = Mock(ErpFieldManager) {
            findIdField(*_) >> {
                def entity = new ErpObjectFieldEntity(fieldApiName: "test")
                return entity
            }
        }
        def erpTempDataDao = Mock(ErpTempDataDao) {
            pageByPollingTime(*_) >> { args ->
                def maxId = args[4] as ObjectId
                if (maxId != null) {
                    return []
                }
                StandardData d = new StandardData(masterFieldVal: new ObjectData(id: "test"))
                def d_str = JacksonUtil.toJson(d)
                ErpTempData data1 = new ErpTempData(dataBody: d_str, dataId: "test")
                ErpTempData data2 = new ErpTempData(dataBody: d_str, dataId: "test")
                return [data1, data2]
            }
        }
        def idFieldConvertManager = Mock(IdFieldConvertManager) {
            getObjectDataId(*_) >> "test"
        }
        def i18NStringManager = new I18NStringManager()
        def dbFileManager = Mock(DBFileManager) {
            writeAndUploadExcel(*_) >> Result.newSuccess("test send")
        }
        probeErpDataService = new ProbeErpDataServiceImpl(
                userCenterService: Mock(UserCenterService) {
                    getDownloadFilePath(*_) >> '%s'
                },
                redissonClient: redissonClient, erpHistoryTaskExecuteManager: erpHistoryTaskExecuteManager, erpObjManager: erpObjManager,
                tenantConfigurationManager: tenantConfigurationManager, configCenterConfig: conf, erpDataPreprocessService: erpDataPreprocessService,
                ployBreakManager: ployBreakManager, pollingDataSpeedRateLimitManager: pollingDataSpeedRateLimitManager,
                probeErpDataManager: probeErpDataManager, redisDataSource: redisDataSource, redisDataSourceManager: redisDataSourceManager,
                erpSyncTimeDao: erpSyncTimeDao, pollingMongoReportManager: pollingMongoReportManager, monitorReportManager: monitorReportManager,
                pollingTempFailDao: pollingTempFailDao, syncPloyDetailSnapshotDao: syncPloyDetailSnapshotDao, send2DispatcherSpeedLimitManager: send2DispatcherSpeedLimitManager,
                erpTempDataManager: erpTempDataManager, triggerPollingMongoManager: triggerPollingMongoManager, syncPloyDetailManager: syncPloyDetailManager,
                chSyncDataManager: syncDataMongoDao, erpConnectInfoManager: erpConnectInfoManager, idFieldConvertManager: idFieldConvertManager,
                erpFieldManager: erpFieldManager, erpTempDataDao: erpTempDataDao, i18NStringManager: i18NStringManager,
                dbFileManager: dbFileManager, notificationService: notificationService
        )
//        TraceContext.remove()
        println("set up")
    }

    def "trace try"() {
        given:
        TraceUtil.initTraceWithFormat("12345")
        expect:
        def tid = TraceContext.get().getTraceId()
        println(tid)
    }

    def "test executePloys - syncTimeVos非空"() {
        given:
        canGetLock = true
        def list = [new ErpSyncTimeVO(id: UUID.randomUUID().toString(), tenantId: tenantId, objectApiName: "test_api_name", operationType: 1, lastSyncTime: System.currentTimeMillis()),
                    new ErpSyncTimeVO(id: UUID.randomUUID().toString(), tenantId: tenantId, objectApiName: "test_api_name", operationType: 2, lastSyncTime: System.currentTimeMillis())]

        expect:
        // 因为原测试类里用线程池执行的业务逻辑，我们直接模拟调用
        def objMap = list.groupBy { it.objectApiName }
        for (Map.Entry<String, List<ErpSyncTimeVO>> entry : objMap.entrySet()) {
            String objApiName = entry.getKey()
            List<ErpSyncTimeVO> objSyncTimes = entry.getValue()
            try {
                probeErpDataService.executeRollingErpByObj(tenantId, objApiName, objSyncTimes, false)
            } catch (RejectedExecutionException e) {
                //暂时先打印错误日志
                println(tenantId + " probe erp pool EXHAUSTED!" + e)
            }
        }
        probeErpDataService.executePloys(tenantId, list).isSuccess()
    }

    def "test executePloys - syncTimeVos为空"() {
        given:
        canGetLock = false
        expect:
        probeErpDataService.executePloys(tenantId, new ArrayList()).isSuccess()
    }

    def "测试轮询mongo - #name"() {
        // 因为checkAndDequeueTenant函数是开启子线程执行的，直接测试没办法覆盖，要另外调用
        given:
        canGetLock = lockFlag
        def objApiName = "test_api_name"
        expect:
        List<ErpSyncTimeVO> syncTimeVos = probeErpDataService.getSyncTimesByObjApiName(tenantId, objApiName)
        probeErpDataService.executeRollingErpByObj(tenantId, objApiName, syncTimeVos, true)
        //触发执行轮询mongo临时库,抢不到锁就结束了，每分钟轮询任务会轮询到数据，只不过会延迟一点点
        probeErpDataService.executeRollingErpDataFromMongoJob(tenantId, syncTimeVos)

        where:
        name    | lockFlag
        "有锁"    | true
        "无锁"    | false
    }

    def "测试轮询mongo - 有锁且list为空"() {
        given:
        canGetLock = true
        expect:
        probeErpDataService.executeRollingErpDataFromMongoJob(tenantId, null)
    }

    def "test dequeuePollingMongo"() {
        // 这个分支很难构建数据覆盖到，索性直接调用，看下反应
        expect:
        probeErpDataService.dequeuePollingMongo(tenantId)
    }

    def "测试执行历史任务"() {
        expect:
        probeErpDataService.executeRollingErpHistoryDataJob(tenantId)

    }

    def "test retryPollingTempFail"() {
        expect:
        probeErpDataService.retryPollingTempFail(tenantId)
    }

    def "测试批量发送数据"() {
        expect:
        probeErpDataService.batchSendErpDataMq([new ErpObjDataResult()], false).isSuccess()
        probeErpDataService.batchSendErpDataMqByContext([], false).isSuccess()
    }

    def "test executePollingTempData"() {
        given:
        canGetLock = false
        expect:
        probeErpDataService.executePollingTempData(tenantId)
    }

    def "test countSyncDataAndSend"() {
        expect:
        probeErpDataService.countSyncDataAndSend(new CountSyncDataAndSendArg(tenantId, System.currentTimeMillis()-1000*10, System.currentTimeMillis())).isSuccess()
    }


    @Ignore
    def "testHistoryTaskExecute"() {
//        erpHistoryTaskExecuteManager.executeErpHistoryDataTask("1") >> {
//            ThreadUtil.sleep(2000)
//            return Result.newSuccess()
//        }
//        erpHistoryTaskExecuteManager.executeErpHistoryDataTask("2") >> {
//            ThreadUtil.sleep(2000)
//            return Result.newSuccess()
//        }
//        erpHistoryTaskExecuteManager.executeErpHistoryDataTask("3") >> {
//            ThreadUtil.sleep(1, TimeUnit.MINUTES)
//            return Result.newSuccess()
//        }
        //多个线程并发执行测试
        List testData = [
                [0, "1", ResultCodeEnum.SUCCESS],
                [1, "1", ResultCodeEnum.GET_LOCK_FAILED],
                [0, "2", ResultCodeEnum.SUCCESS],
                [50, "2", ResultCodeEnum.SUCCESS],
                [0, "3", ResultCodeEnum.SUCCESS],
                [1, "3", ResultCodeEnum.GET_LOCK_FAILED],
                [30, "3", ResultCodeEnum.GET_LOCK_FAILED],
                [50, "3", ResultCodeEnum.GET_LOCK_FAILED],
        ]
        def cdl = ThreadUtil.newCountDownLatch(testData.size())
        when:
        for (j in 0..<testData.size()) {
            final int i = j
            ThreadUtil.execute {
                def list = testData[i] as List
                println("ttt start {},{}", i, list)
                try {
                    def s = list[0] as int
                    if (s > 0) {
                        ThreadUtil.sleep(s, TimeUnit.SECONDS)
                    }
                    def result = probeErpDataService.executeRollingErpHistoryDataJob(list[1] as String)
                    list.add(result.getErrCode())
                    println("ttt end {},{},{},{}", i, list, result,((ResultCodeEnum) list[2]).getErrCode() == list[3])
                } catch (Exception e) {
                    println("ttt exception {},{}", i, list, e)
                } finally {
                    cdl.countDown()
                }
            }
        }
        println("ttt running")
        cdl.await()
        println("ttt end all,{}", testData)
        for (final def list in testData) {
            println("ttt end all,{}", list)
        }
        def valid = true
        for (final def list in testData) {
            valid = list.size() == 4 && ((ResultCodeEnum) list[2]).getErrCode() == list[3]
            if (!valid){
                break
            }
        }
        then:
        valid
    }
}
