package com.fxiaoke.open.erpsyncdata.preprocess.impl

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import spock.lang.Ignore
import spock.lang.Specification

class AuditLogServiceImplTest extends Specification {

    AuditLogServiceImpl impl
    String tenantId = "123456"

    def setup() {
        def proxyHttpClient = Mock(ProxyHttpClient) {
            postUrl(*_) >> {
                HttpRspLimitLenUtil.ResponseBodyModel response = HttpRspLimitLenUtil.ResponseBodyModel.builder().code(200).build()
                response.setBody("{\"code\": 301}")
                return response
            }
        }
        impl = new AuditLogServiceImpl(proxyHttpClient: proxyHttpClient)
    }

    def "test record"() {
        expect:
        impl.recordExportSystemFieldLog(ErpFieldTypeEnum.text, tenantId, "user_id", "tesxt_msg")
        impl.recordDeleteSystemFieldLog(ErpFieldTypeEnum.text, tenantId, "user_id", "tesxt_msg")
    }

    @Ignore
    def "test buildAuditLog"() {
        expect:
        def log = impl.buildAuditLog("op_obj", "biz_op_name", "obj_name", tenantId, "user_id", "tesxt_msg", 0)
        println(log)
    }
}
