package com.fxiaoke.open.erpsyncdata.preprocess.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class SyncDepartmentOrPersonnelServiceImplTest extends BaseSpockTest {

    def "NeedAutoBindEmployeeMapping"() {
        TenantConfigurationManager tenantConfigurationManager = Stub()
        tenantConfigurationManager.findOne(*_) >> {args->
            if (configStr == null) {
                return null
            }else{
                ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
                entity.setConfiguration(configStr);
                return entity
            }
        }
        SyncDepartmentOrPersonnelServiceImpl service = new SyncDepartmentOrPersonnelServiceImpl(tenantConfigurationManager: tenantConfigurationManager)

        when:
        Boolean result = service.needAutoBindEmployeeMapping("","")
        then:
        result == expect
        where:
        configStr                      || expect
        null                           || true
        ""                             || true
        "f1;f2"                        || false
        "{\"isOpen\":true,\"type\":1}" || true
        "{\"isOpen\":true,\"type\":2}" || false
        "{\"isOpen\":false}"           || false
    }
}
