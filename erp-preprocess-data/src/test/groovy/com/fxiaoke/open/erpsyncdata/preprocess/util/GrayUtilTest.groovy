package com.fxiaoke.open.erpsyncdata.preprocess.util

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 */
class GrayUtilTest extends BaseSpockTest {

    @Unroll
    def "AutoGenerateSpu"(String tenantId,boolean allow) {
        allow = GrayUtil.autoGenerateSpu(tenantId)
        println(allow)
        where:
        tenantId | allow
        "83952"  | false
        "90314"  | true
    }
}
