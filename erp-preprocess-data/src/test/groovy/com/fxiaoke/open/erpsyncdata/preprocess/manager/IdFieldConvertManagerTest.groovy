package com.fxiaoke.open.erpsyncdata.preprocess.manager

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl.StockBusinessImpl
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class IdFieldConvertManagerTest extends Specification {

    IdFieldConvertManager manager
    @Shared
    def tenantId = "123456"
    def apiName = "test_api_name"

    def setup() {
        def erpFieldManager = Mock(ErpFieldManager) {
            findIdField(*_) >> {
                def e = new CompositeIdExtend(composite: true, saveIdField: "save_id_field", separator: "_", compositeFields: [], viewIdField: "test")
                new ErpObjectFieldEntity(fieldExtendValue: JacksonUtil.toJson(e))
            }
            findByObjApiNameAndType(*_) >> {
                def e = new CompositeIdExtend(composite: true, saveIdField: "save_id_field", separator: "_", compositeFields: [], viewIdField: "test")
                ErpObjectFieldEntity entity = new ErpObjectFieldEntity(fieldExtendValue: JacksonUtil.toJson(e), fieldApiName: apiName)
                return [entity]
            }
            findMasterDetailField(*_) >> new ErpObjectFieldEntity(fieldApiName: apiName)
        }
        def dataCenterManager = Mock(DataCenterManager) {
            getDataCenterByObjApiName(*_) >> "apiName"
        }
        def erpObjManager = Mock(ErpObjManager) {
            getRelation(*_) >> new ErpObjectRelationshipEntity(erpRealObjectApiname: "apiName")
        }
        def syncDataMappingsManager = Mock(SyncDataMappingsManager) {
            findMappingBySyncDataId(*_) >> new SyncDataMappingsEntity(isCreated: true)
        }
        def conf = Mock(ConfigCenterConfig) {
            getSTOCKLOC_FIELDS_SET(*_) >> {
                def set = new HashSet()
                set.add(apiName)
                return set
            }
        }
        def erpConnectInfoManager = Mock(ErpConnectInfoManager) {
            getByIdAndTenantId(*_) >> { args ->
                if (args[0] == null) {
                    return new ErpConnectInfoEntity(channel: ErpChannelEnum.STANDARD_CHANNEL)
                }
                return new ErpConnectInfoEntity(channel: ErpChannelEnum.ERP_K3CLOUD)
            }
        }
        def stockBusiness = Mock(StockBusinessImpl) {
            getFlexValueFields(*_) >> ["t1", "t2"]
        }
        def apiClientHolder = Mock(ApiClientHolder)
        manager = new IdFieldConvertManager(erpFieldManager: erpFieldManager, dataCenterManager: dataCenterManager,
                erpObjManager: erpObjManager, syncDataMappingsManager: syncDataMappingsManager, configCenterConfig: conf,
                erpConnectInfoManager: erpConnectInfoManager, stockBusiness: stockBusiness, apiClientHolder: apiClientHolder)
    }

    def "test convertField2Crm"() {
        given:
        ObjectData obj = new ObjectData()
        obj.putApiName(apiName)
        ObjectData data = new ObjectData()
        SyncDataContextEvent event = new SyncDataContextEvent(sourceData: obj, detailData: ["test": [data]])
        expect:
        manager.convertField2Crm(tenantId, event)
    }

    def "test convertField2Erp - #name"() {
        given:
        ObjectData data = new ObjectData()
        data.putId("test_main_id")
        data.putApiName(apiName)
        data.put(apiName, "test_id")
        def detail = new ObjectData()
        detail.putApiName(apiName)
        detail.putId("test_id")
        detail.put(apiName, "test_id")
        def detail1 = new ObjectData()
        detail1.putApiName(apiName)
        detail1.putId("test_id")
        detail1.put(apiName, "test||l1.l2")
        SyncDataContextEvent event = new SyncDataContextEvent(destTenantId: deal, destData: data,
                destEventType: EventTypeEnum.UPDATE.getType(), destDetailSyncDataIdAndDestDataMap: ["test": detail, "test1": detail1])
        expect:
        manager.convertField2Erp(event, "dcId")
        where:
        name        | deal
        "处理仓位"    | tenantId
        "不处理仓位"  | null
    }


    def "test getRealObjApiName"() {
        expect:
        null != manager.getRealObjApiName(tenantId, "")
    }

    def "test getDataCenterId"() {
        expect:
        null != manager.getDataCenterId(tenantId, "")
    }

    def "test convertDetailApiName - #name"() {
        given:
        ErpObjExtendDto dto = new ErpObjExtendDto(splitType: ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT, splitObjApiName: "apiName")
        expect:
        manager.convertDetailApiName(res, [dto])
        where:
        name     | res
        "为空"    | null
        "非空"    | new ErpIdResult(detailDataIds: ["test":[]])
    }

    def "test getIdField"() {
        given:
        ErpFieldManager erpFieldManager = Mock(ErpFieldManager)
        manager = new IdFieldConvertManager(erpFieldManager: erpFieldManager)
        when:
        manager.getIdField(tenantId, "")
        then:
        thrown(ErpSyncDataException)
    }

    def "test convertId2Erp"() {
        given:
        def data = new SyncDataContextEvent(destDataId: "test_dest_id")
        expect:
        manager.convertId2Erp(data)

    }
}
