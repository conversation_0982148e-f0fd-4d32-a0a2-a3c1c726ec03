package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import org.junit.platform.launcher.Launcher;
import org.junit.platform.launcher.LauncherDiscoveryRequest;
import org.junit.platform.launcher.core.LauncherDiscoveryRequestBuilder;
import org.junit.platform.launcher.core.LauncherFactory;
import org.junit.platform.launcher.listeners.SummaryGeneratingListener;
import org.junit.platform.launcher.listeners.TestExecutionSummary;

import static org.junit.platform.engine.discovery.DiscoverySelectors.selectClass;

/**
 * 测试运行器
 * 用于手动运行测试并查看结果
 * 
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
public class TestRunner {
    
    public static void main(String[] args) {
        // 创建启动器
        Launcher launcher = LauncherFactory.create();
        
        // 创建监听器
        SummaryGeneratingListener listener = new SummaryGeneratingListener();
        
        // 构建发现请求
        LauncherDiscoveryRequest request = LauncherDiscoveryRequestBuilder.request()
                .selectors(selectClass(AbstractErpHistoryTaskServiceImplTest.class))
                .build();
        
        // 注册监听器并执行测试
        launcher.registerTestExecutionListeners(listener);
        launcher.execute(request);
        
        // 获取测试结果摘要
        TestExecutionSummary summary = listener.getSummary();
        
        // 打印测试结果
        System.out.println("=== 测试执行摘要 ===");
        System.out.println("总测试数: " + summary.getTestsFoundCount());
        System.out.println("成功测试数: " + summary.getTestsSucceededCount());
        System.out.println("失败测试数: " + summary.getTestsFailedCount());
        System.out.println("跳过测试数: " + summary.getTestsSkippedCount());
        System.out.println("执行时间: " + summary.getTotalTime().toMillis() + "ms");
        
        // 如果有失败的测试，打印失败信息
        if (summary.getTestsFailedCount() > 0) {
            System.out.println("\n=== 失败测试详情 ===");
            summary.getFailures().forEach(failure -> {
                System.out.println("失败测试: " + failure.getTestIdentifier().getDisplayName());
                System.out.println("异常信息: " + failure.getException().getMessage());
                System.out.println("---");
            });
        }
        
        System.out.println("\n测试执行完成！");
    }
}
