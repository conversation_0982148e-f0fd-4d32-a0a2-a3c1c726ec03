package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import org.junit.platform.launcher.Launcher;
import org.junit.platform.launcher.LauncherDiscoveryRequest;
import org.junit.platform.launcher.core.LauncherDiscoveryRequestBuilder;
import org.junit.platform.launcher.core.LauncherFactory;
import org.junit.platform.launcher.listeners.SummaryGeneratingListener;
import org.junit.platform.launcher.listeners.TestExecutionSummary;

import static org.junit.platform.engine.discovery.DiscoverySelectors.selectClass;

/**
 * Test Runner for CrmFilterErpIdsHistoryTaskServiceImplTest
 * Used to manually run tests and view results
 * 
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
public class CrmFilterErpIdsTestRunner {
    
    public static void main(String[] args) {
        // Create launcher
        Launcher launcher = LauncherFactory.create();
        
        // Create listener
        SummaryGeneratingListener listener = new SummaryGeneratingListener();
        
        // Build discovery request
        LauncherDiscoveryRequest request = LauncherDiscoveryRequestBuilder.request()
                .selectors(selectClass(CrmFilterErpIdsHistoryTaskServiceImplTest.class))
                .build();
        
        // Register listener and execute tests
        launcher.registerTestExecutionListeners(listener);
        launcher.execute(request);
        
        // Get test result summary
        TestExecutionSummary summary = listener.getSummary();
        
        // Print test results
        System.out.println("=== Test Execution Summary ===");
        System.out.println("Total tests: " + summary.getTestsFoundCount());
        System.out.println("Successful tests: " + summary.getTestsSucceededCount());
        System.out.println("Failed tests: " + summary.getTestsFailedCount());
        System.out.println("Skipped tests: " + summary.getTestsSkippedCount());
        System.out.println("Execution time: " + summary.getTotalTime().toMillis() + "ms");
        
        // If there are failed tests, print failure details
        if (summary.getTestsFailedCount() > 0) {
            System.out.println("\n=== Failed Test Details ===");
            summary.getFailures().forEach(failure -> {
                System.out.println("Failed test: " + failure.getTestIdentifier().getDisplayName());
                System.out.println("Exception: " + failure.getException().getMessage());
                System.out.println("---");
            });
        }
        
        System.out.println("\nTest execution completed!");
    }
}
