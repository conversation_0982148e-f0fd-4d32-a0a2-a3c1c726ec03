package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpHistoryTaskLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.HistoryTaskDuplicateDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CreateErpHistoryDataTaskArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.IntegrationSimpleViewInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractErpHistoryTaskServiceImpl 简化单元测试类
 * 专注于核心功能测试，避免复杂的依赖问题
 *
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
@ExtendWith(MockitoExtension.class)
class AbstractErpHistoryTaskServiceImplSimpleTest {

    @Mock
    private I18NStringManager i18NStringManager;

    @Mock
    private ErpObjectRelationshipDao erpObjectRelationshipDao;

    @Mock
    private TenantConfigurationManager tenantConfigurationManager;

    @Mock
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;

    @Mock
    private ErpHistoryTaskLogDao erpHistoryTaskLogDao;

    @Mock
    private HistoryTaskDuplicateDataDao historyTaskDuplicateDataDao;

    @Mock
    private RedisDataSource redisDataSource;

    @InjectMocks
    private TestableAbstractErpHistoryTaskServiceImpl testService;

    private static final String TEST_TENANT_ID = "test_tenant_123";
    private static final String TEST_DC_ID = "test_dc_456";
    private static final Integer TEST_USER_ID = 1001;
    private static final String TEST_LANG = "zh_CN";

    @BeforeEach
    void setUp() {
        // 注入私有字段
        ReflectionTestUtils.setField(testService, "i18NStringManager", i18NStringManager);
        ReflectionTestUtils.setField(testService, "erpObjectRelationshipDao", erpObjectRelationshipDao);
        ReflectionTestUtils.setField(testService, "tenantConfigurationManager", tenantConfigurationManager);
        ReflectionTestUtils.setField(testService, "erpHistoryDataTaskDao", erpHistoryDataTaskDao);
        ReflectionTestUtils.setField(testService, "erpHistoryTaskLogDao", erpHistoryTaskLogDao);
        ReflectionTestUtils.setField(testService, "historyTaskDuplicateDataDao", historyTaskDuplicateDataDao);
        ReflectionTestUtils.setField(testService, "redisDataSource", redisDataSource);
    }

    /**
     * 测试创建历史任务 - 成功场景
     */
    @Test
    void testCreateHistoryTask_Success() {
        // 准备测试数据
        ErpHistoryDataTaskEntity entity1 = createTestEntity("task1");
        ErpHistoryDataTaskEntity entity2 = createTestEntity("task2");
        List<ErpHistoryDataTaskEntity> entities = Arrays.asList(entity1, entity2);

        // Mock DAO 行为
        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.batchInsert(anyList())).thenReturn(1);
        doNothing().when(erpHistoryTaskLogDao).batchInsert(eq(TEST_TENANT_ID), anyList());

        // 执行测试
        boolean result = testService.createHistoryTask(TEST_TENANT_ID, entities);

        // 验证结果
        assertTrue(result);
        verify(erpHistoryDataTaskDao).setTenantId(anyString());
        verify(erpHistoryDataTaskDao).batchInsert(entities);
        verify(erpHistoryTaskLogDao).batchInsert(eq(TEST_TENANT_ID), anyList());
    }

    /**
     * 测试编辑历史快照任务 - 成功场景
     */
    @Test
    void testEditHistorySnapshotTask_Success() {
        // 准备测试数据
        ErpHistoryDataTaskEntity entity = createTestEntity("edit_task");

        // Mock DAO 行为
        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.updateEntityById(any())).thenReturn(1);
        doNothing().when(erpHistoryTaskLogDao).updateLastErpHistoryByTaskId(anyString(), anyString(), anyString(), any());

        // 执行测试
        boolean result = testService.editHistorySnapshotTask(TEST_TENANT_ID, entity);

        // 验证结果
        assertTrue(result);
        verify(erpHistoryDataTaskDao).setTenantId(anyString());
        verify(erpHistoryDataTaskDao).updateEntityById(entity);
        verify(erpHistoryTaskLogDao).updateLastErpHistoryByTaskId(eq(TEST_TENANT_ID), eq(entity.getDataCenterId()),
                eq(entity.getId()), any());
    }

    /**
     * 测试创建ERP历史数据任务 - 参数错误场景
     */
    @Test
    void testCreateErpHistoryDataTask_ParamError() {
        // 测试空的objApiName
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setObjApiName("");

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());

        // 测试空的taskName
        arg = createTestArg();
        arg.getTask().setTaskName("");

        result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());
    }

    /**
     * 测试创建ERP历史数据任务 - 成功场景（非ERP类型）
     */
    @Test
    void testCreateErpHistoryDataTask_Success_NonErpType() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_CRM_FILTER.getStatus());

        // Mock 限制检查
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), anyList())).thenReturn(5);
        when(redisDataSource.incrAndExpire(anyString(), anyLong(), anyLong(), anyString())).thenReturn(50L);

        // Mock 保存成功
        testService.setSaveTaskSuccessResult(true);

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
        // 验证没有调用对象关系查询
        verify(erpObjectRelationshipDao, never()).findBySplit(anyString(), anyString());
    }

    /**
     * 测试afterConvert2Vo方法 - 空实现
     */
    @Test
    void testAfterConvert2Vo() {
        ErpHistoryDataTaskResult result = new ErpHistoryDataTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity("test");

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> testService.afterConvert2Vo(result, entity));
    }

    /**
     * 创建测试用的 ErpHistoryDataTaskEntity
     */
    private ErpHistoryDataTaskEntity createTestEntity(String taskId) {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId(taskId);
        entity.setTenantId(TEST_TENANT_ID);
        entity.setDataCenterId(TEST_DC_ID);
        entity.setTaskName("Test Task");
        entity.setObjApiName("test_obj");
        entity.setRealObjApiName("real_test_obj");
        entity.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus());
        entity.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setExecuteTime(System.currentTimeMillis() + 3600000); // 1小时后执行
        return entity;
    }

    /**
     * 创建测试用的 CreateErpHistoryDataTaskArg
     */
    private CreateErpHistoryDataTaskArg createTestArg() {
        CreateErpHistoryDataTaskArg arg = new CreateErpHistoryDataTaskArg();
        arg.setIsCheck(true);

        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult();
        task.setObjApiName("test_obj");
        task.setTaskName("Test Task");
        task.setRemark("Test Remark");
        task.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus());
        task.setExecuteTime(System.currentTimeMillis() + 3600000);
        task.setPriority(1);

        // 添加集成结果
        IntegrationSimpleViewInfoResult integration = new IntegrationSimpleViewInfoResult();
        integration.setId("integration_1");
        task.setIntegrationResults(Arrays.asList(integration));

        arg.setTask(task);
        return arg;
    }

    /**
     * 可测试的抽象类实现
     */
    private static class TestableAbstractErpHistoryTaskServiceImpl extends AbstractErpHistoryTaskServiceImpl {

        private boolean checkParamsErrorResult = false;
        private boolean checkTaskValidResult = true;
        private boolean saveTaskSuccessResult = true;
        private boolean editTaskSuccessResult = true;

        public void setCheckParamsErrorResult(boolean result) {
            this.checkParamsErrorResult = result;
        }

        public void setCheckTaskValidResult(boolean result) {
            this.checkTaskValidResult = result;
        }

        public void setSaveTaskSuccessResult(boolean result) {
            this.saveTaskSuccessResult = result;
        }

        public void setEditTaskSuccessResult(boolean result) {
            this.editTaskSuccessResult = result;
        }

        @Override
        public ErpHistoryDataTaskTypeEnum taskType() {
            return ErpHistoryDataTaskTypeEnum.TYPE_IDS;
        }

        @Override
        public boolean checkParamsError(ErpHistoryDataTaskResult task) {
            return checkParamsErrorResult;
        }

        @Override
        public boolean checkTaskValid(ErpHistoryDataTaskEntity task) {
            return checkTaskValidResult;
        }

        @Override
        public boolean saveTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
            return saveTaskSuccessResult;
        }

        @Override
        public boolean editTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
            return editTaskSuccessResult;
        }

        @Override
        public Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task) {
            return new Result<>(ResultCodeEnum.SUCCESS.getErrCode(), "success", null);
        }
    }
}
