package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CrmFilterErpIdsHistoryTaskServiceImpl 单元测试类
 *
 * 测试原则：
 * 1. 不依赖真实的外部服务调用
 * 2. 使用 Mock 对象模拟所有外部依赖
 * 3. 只测试当前类的业务逻辑
 * 4. 测试用例要独立且可重复执行
 * 5. 测试各个依赖服务的交互，而不是调用真实的业务方法
 * 6. 使用 Given-When-Then 结构组织测试
 * 7. 测试边界条件和异常场景
 *
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
@ExtendWith(MockitoExtension.class)
class CrmFilterErpIdsHistoryTaskServiceImplTest {

    @Mock
    private PollingDataSpeedRateLimitManager pollingDataSpeedRateLimitManager;

    @Mock
    private SyncLogManager syncLogManager;

    @Mock
    private SyncDataMappingManager syncDataMappingManager;

    @Mock
    private ErpDataService erpDataService;

    @Mock
    private IdFieldKeyManager idFieldKeyManager;

    @Mock
    private ErpConnectInfoManager erpConnectInfoManager;

    @Mock
    private ErpFieldManager erpFieldManager;

    @Mock
    private ErpTempDataDao erpTempDataDao;

    @InjectMocks
    private CrmFilterErpIdsHistoryTaskServiceImpl crmFilterErpIdsHistoryTaskService;

    // 测试常量
    private static final String TEST_TENANT_ID = "test_tenant_123";
    private static final String TEST_DC_ID = "test_dc_456";
    private static final String TEST_OBJ_API_NAME = "test_obj";
    private static final String TEST_REAL_OBJ_API_NAME = "real_test_obj";
    private static final String TEST_TASK_ID = "task_123";
    private static final String TEST_LOG_ID = "log_123";

    @BeforeEach
    void setUp() {
        // 初始化测试环境
        reset(pollingDataSpeedRateLimitManager, syncLogManager, syncDataMappingManager,
                erpDataService, idFieldKeyManager, erpConnectInfoManager, erpFieldManager, erpTempDataDao);
    }

    /**
     * 测试 taskType 方法
     * Given: 调用 taskType 方法
     * When: 执行方法
     * Then: 返回正确的任务类型
     */
    @Test
    void testTaskType() {
        // When
        ErpHistoryDataTaskTypeEnum result = crmFilterErpIdsHistoryTaskService.taskType();

        // Then
        assertEquals(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER, result);
    }

    /**
     * 测试 saveTaskSuccess 方法 - 成功场景
     * Given: 有效的任务数据和实体
     * When: 调用 saveTaskSuccess 方法
     * Then: 设置CRM过滤器并创建历史任务
     */
    @Test
    void testSaveTaskSuccess_Success() {
        // Given
        ErpHistoryDataTaskResult task = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity();

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).setCrmsFilter(any(), any());
        doReturn(true).when(spyService).createHistoryTask(anyString(), anyList());

        // When
        boolean result = spyService.saveTaskSuccess(TEST_TENANT_ID, task, entity);

        // Then
        assertTrue(result);
        verify(spyService).setCrmsFilter(entity, task.getCrmFilters());
        verify(spyService).createHistoryTask(eq(TEST_TENANT_ID), eq(Lists.newArrayList(entity)));
    }

    /**
     * 测试 editTaskSuccess 方法 - 成功场景
     * Given: 有效的任务数据和实体
     * When: 调用 editTaskSuccess 方法
     * Then: 设置CRM过滤器并编辑历史快照任务
     */
    @Test
    void testEditTaskSuccess_Success() {
        // Given
        ErpHistoryDataTaskResult task = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity();

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).setCrmsFilter(any(), any());
        doReturn(true).when(spyService).editHistorySnapshotTask(anyString(), any());

        // When
        boolean result = spyService.editTaskSuccess(TEST_TENANT_ID, task, entity);

        // Then
        assertTrue(result);
        verify(spyService).setCrmsFilter(entity, task.getCrmFilters());
        verify(spyService).editHistorySnapshotTask(TEST_TENANT_ID, entity);
    }

    /**
     * 测试 afterConvert2Vo 方法
     * Given: 任务结果和实体
     * When: 调用 afterConvert2Vo 方法
     * Then: 调用父类方法并清空 dataIds
     */
    @Test
    void testAfterConvert2Vo() {
        // Given
        ErpHistoryDataTaskResult copy = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity();
        copy.setDataIds(Arrays.asList("id1", "id2", "id3"));

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when((FilterCrmHistoryTaskServiceImpl) spyService).afterConvert2Vo(any(), any());

        // When
        spyService.afterConvert2Vo(copy, entity);

        // Then
        assertNull(copy.getDataIds());
        verify((FilterCrmHistoryTaskServiceImpl) spyService).afterConvert2Vo(copy, entity);
    }

    /**
     * 创建测试用的 ErpHistoryDataTaskResult
     */
    private ErpHistoryDataTaskResult createTestTaskResult() {
        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult();
        task.setObjApiName(TEST_OBJ_API_NAME);
        task.setTaskName("Test Task");
        task.setRemark("Test Remark");
        task.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus());
        task.setExecuteTime(System.currentTimeMillis() + 3600000L);
        task.setPriority(1);

        // 创建 CRM 过滤器
        ErpHistoryDataTaskResult.CrmFilters crmFilter = new ErpHistoryDataTaskResult.CrmFilters();
        crmFilter.setObjectApiName("crm_obj");
        task.setCrmFilters(Arrays.asList(crmFilter));

        return task;
    }

    /**
     * 创建测试用的 ErpHistoryDataTaskEntity
     */
    private ErpHistoryDataTaskEntity createTestEntity() {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId(TEST_TASK_ID);
        entity.setTenantId(TEST_TENANT_ID);
        entity.setDataCenterId(TEST_DC_ID);
        entity.setTaskName("Test Task");
        entity.setObjApiName(TEST_OBJ_API_NAME);
        entity.setRealObjApiName(TEST_REAL_OBJ_API_NAME);
        entity.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus());
        entity.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());
        entity.setTaskNum("TASK_" + System.currentTimeMillis());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setExecuteTime(System.currentTimeMillis() + 3600000);
        return entity;
    }

    /**
     * 创建测试用的 ErpObjectFieldEntity
     */
    private ErpObjectFieldEntity createTestIdField() {
        ErpObjectFieldEntity idField = new ErpObjectFieldEntity();
        idField.setId("field_123");
        idField.setTenantId(TEST_TENANT_ID);
        idField.setErpObjectApiName(TEST_OBJ_API_NAME);
        idField.setFieldApiName("id");
        idField.setFieldLabel("ID");
        idField.setFieldExtendValue("{}"); // 空的复合ID配置
        return idField;
    }

    /**
     * 创建测试用的 IdFieldKey
     */
    private IdFieldKey createTestIdFieldKey() {
        IdFieldKey idFieldKey = new IdFieldKey();
        idFieldKey.setIdFieldKey("id");
        idFieldKey.setNumFieldKey("id");
        idFieldKey.setFakeIdFieldKey("id");
        idFieldKey.setSplitObjApiName(TEST_OBJ_API_NAME);
        idFieldKey.setCompositeIdExtend(new CompositeIdExtend());
        return idFieldKey;
    }

    /**
     * 创建测试用的 ObjectData 列表
     */
    private List<ObjectData> createTestObjectDataList() {
        List<ObjectData> dataList = new ArrayList<>();

        ObjectData data1 = new ObjectData();
        data1.setId("crm_id_1");
        data1.put("name", "Test Data 1");
        dataList.add(data1);

        ObjectData data2 = new ObjectData();
        data2.setId("crm_id_2");
        data2.put("name", "Test Data 2");
        dataList.add(data2);

        return dataList;
    }

    /**
     * 测试 doTask 方法 - 成功场景
     * Given: 有效的任务实体和模拟的依赖服务
     * When: 调用 doTask 方法
     * Then: 正确执行任务流程并返回成功结果
     */
    @Test
    void testDoTask_Success() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();
        IdFieldKey idFieldKey = createTestIdFieldKey();
        ErpObjectFieldEntity idField = createTestIdField();
        CompositeIdExtend compositeIdExtend = new CompositeIdExtend();

        // Mock 依赖服务
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME)).thenReturn(TEST_LOG_ID);
        when(idFieldKeyManager.buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME))
                .thenReturn(idFieldKey);
        when(erpFieldManager.findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME)).thenReturn(idField);

        // Mock doTaskByCrmFilter 方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doReturn(Result.newSuccess()).when(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));

        // When
        Result<Void> result = spyService.doTask(TEST_TENANT_ID, task);

        // Then
        assertTrue(result.isSuccess());
        verify(syncLogManager).initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME);
        verify(idFieldKeyManager).buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME);
        verify(erpFieldManager).findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME);
        verify(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));
    }

    /**
     * 测试 doTask 方法 - 处理数据流程
     * Given: 有效的任务实体和CRM数据
     * When: 执行数据处理流程
     * Then: 正确处理ERP ID并更新相关数据
     */
    @Test
    void testDoTask_DataProcessing() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();
        IdFieldKey idFieldKey = createTestIdFieldKey();
        ErpObjectFieldEntity idField = createTestIdField();
        List<ObjectData> crmDataList = createTestObjectDataList();
        List<String> erpIds = Arrays.asList("erp_id_1", "erp_id_2");

        // Mock 依赖服务
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME)).thenReturn(TEST_LOG_ID);
        when(idFieldKeyManager.buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME))
                .thenReturn(idFieldKey);
        when(erpFieldManager.findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME)).thenReturn(idField);

        // Mock 数据映射
        Pair<Object, Object> mappingPair1 = Pair.of(null, createMockMappingRight("erp_id_1"));
        Pair<Object, Object> mappingPair2 = Pair.of(null, createMockMappingRight("erp_id_2"));
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_1"), anyString(), anyString()))
                .thenReturn(mappingPair1);
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_2"), anyString(), anyString()))
                .thenReturn(mappingPair2);

        // Mock ERP 数据服务
        doNothing().when(erpDataService).sendErpData(any(ErpIdArg.class));

        // Mock 临时数据DAO
        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());

        // Mock 限速管理器
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).incDuplicateTimeByErpIds(anyString(), anyString(), anyList());

        // Mock doTaskByCrmFilter 来模拟实际的数据处理流程
        doAnswer(invocation -> {
            BiFunction<String, List<ObjectData>, Result<Void>> handler = invocation.getArgument(2);
            return handler.apply("crm_obj", crmDataList);
        }).when(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));

        // When
        Result<Void> result = spyService.doTask(TEST_TENANT_ID, task);

        // Then
        assertTrue(result.isSuccess());
        verify(erpDataService, times(2)).sendErpData(any(ErpIdArg.class));
        verify(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(TEST_TENANT_ID, TEST_DC_ID, TEST_REAL_OBJ_API_NAME, erpIds);
        verify(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(TEST_TENANT_ID, 2L);
        verify(spyService).incDuplicateTimeByErpIds(TEST_TENANT_ID, task.getTaskNum(), erpIds);

        // 验证任务状态更新
        assertEquals(2L, task.getTotalDataSize());
        assertEquals(2L, task.getOffset());
        assertEquals("erp_id_1;erp_id_2", task.getDataIds());
    }

    /**
     * 测试 doTask 方法 - 空ERP ID列表
     * Given: CRM数据映射后没有有效的ERP ID
     * When: 执行数据处理流程
     * Then: 跳过处理并返回成功
     */
    @Test
    void testDoTask_EmptyErpIds() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();
        IdFieldKey idFieldKey = createTestIdFieldKey();
        ErpObjectFieldEntity idField = createTestIdField();
        List<ObjectData> crmDataList = createTestObjectDataList();

        // Mock 依赖服务
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME)).thenReturn(TEST_LOG_ID);
        when(idFieldKeyManager.buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME))
                .thenReturn(idFieldKey);
        when(erpFieldManager.findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME)).thenReturn(idField);

        // Mock 数据映射返回空结果
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Pair.of(null, null));

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);

        // Mock doTaskByCrmFilter 来模拟实际的数据处理流程
        doAnswer(invocation -> {
            BiFunction<String, List<ObjectData>, Result<Void>> handler = invocation.getArgument(2);
            return handler.apply("crm_obj", crmDataList);
        }).when(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));

        // When
        Result<Void> result = spyService.doTask(TEST_TENANT_ID, task);

        // Then
        assertTrue(result.isSuccess());
        verify(erpDataService, never()).sendErpData(any(ErpIdArg.class));
        verify(erpTempDataDao, never()).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());
        verify(pollingDataSpeedRateLimitManager, never()).acquireQueryErpHistoryData(anyString(), anyLong());

        // 验证任务状态
        assertEquals(0L, task.getTotalDataSize());
        assertEquals(0L, task.getOffset());
        assertNull(task.getDataIds());
    }

    /**
     * 创建模拟的映射右侧对象
     */
    private Object createMockMappingRight(String sourceDataId) {
        // 创建一个模拟对象，具有 getSourceDataId 方法
        return new Object() {
            public String getSourceDataId() {
                return sourceDataId;
            }
        };
    }

    /**
     * 创建模拟的映射左侧对象
     */
    private Object createMockMappingLeft(String destDataId, boolean isCreated) {
        // 创建一个模拟对象，具有 getDestDataId 和 getIsCreated 方法
        return new Object() {
            public String getDestDataId() {
                return destDataId;
            }
            public Boolean getIsCreated() {
                return isCreated;
            }
        };
    }

    /**
     * 测试 doTask 方法 - 使用左侧映射（CRM->ERP）
     * Given: 数据映射返回左侧映射且 isCreated=true
     * When: 执行数据处理流程
     * Then: 正确处理左侧映射的ERP ID
     */
    @Test
    void testDoTask_WithLeftMapping() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();
        IdFieldKey idFieldKey = createTestIdFieldKey();
        ErpObjectFieldEntity idField = createTestIdField();
        List<ObjectData> crmDataList = createTestObjectDataList();

        // Mock 依赖服务
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME)).thenReturn(TEST_LOG_ID);
        when(idFieldKeyManager.buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME))
                .thenReturn(idFieldKey);
        when(erpFieldManager.findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME)).thenReturn(idField);

        // Mock 数据映射 - 返回左侧映射
        Pair<Object, Object> mappingPair1 = Pair.of(createMockMappingLeft("erp_dest_1", true), null);
        Pair<Object, Object> mappingPair2 = Pair.of(createMockMappingLeft("erp_dest_2", false), null); // isCreated=false，应该被过滤
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_1"), anyString(), anyString()))
                .thenReturn(mappingPair1);
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_2"), anyString(), anyString()))
                .thenReturn(mappingPair2);

        // Mock ERP 数据服务
        doNothing().when(erpDataService).sendErpData(any(ErpIdArg.class));

        // Mock 临时数据DAO
        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());

        // Mock 限速管理器
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).incDuplicateTimeByErpIds(anyString(), anyString(), anyList());

        // Mock doTaskByCrmFilter 来模拟实际的数据处理流程
        doAnswer(invocation -> {
            BiFunction<String, List<ObjectData>, Result<Void>> handler = invocation.getArgument(2);
            return handler.apply("crm_obj", crmDataList);
        }).when(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));

        // When
        Result<Void> result = spyService.doTask(TEST_TENANT_ID, task);

        // Then
        assertTrue(result.isSuccess());
        verify(erpDataService, times(1)).sendErpData(any(ErpIdArg.class)); // 只有一个有效的映射
        verify(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(eq(TEST_TENANT_ID), eq(TEST_DC_ID), eq(TEST_REAL_OBJ_API_NAME), eq(Arrays.asList("erp_dest_1")));
        verify(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(TEST_TENANT_ID, 1L);

        // 验证任务状态更新
        assertEquals(1L, task.getTotalDataSize());
        assertEquals(1L, task.getOffset());
        assertEquals("erp_dest_1", task.getDataIds());
    }

    /**
     * 测试 doTask 方法 - LogIdUtil 异常处理
     * Given: LogIdUtil.reset 可能抛出异常
     * When: 执行数据处理流程
     * Then: 正确处理异常并继续执行
     */
    @Test
    void testDoTask_LogIdUtilException() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();
        IdFieldKey idFieldKey = createTestIdFieldKey();
        ErpObjectFieldEntity idField = createTestIdField();
        List<ObjectData> crmDataList = createTestObjectDataList();
        List<String> erpIds = Arrays.asList("erp_id_1");

        // Mock 依赖服务
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME)).thenReturn(TEST_LOG_ID);
        when(idFieldKeyManager.buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME))
                .thenReturn(idFieldKey);
        when(erpFieldManager.findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME)).thenReturn(idField);

        // Mock 数据映射
        Pair<Object, Object> mappingPair1 = Pair.of(null, createMockMappingRight("erp_id_1"));
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_1"), anyString(), anyString()))
                .thenReturn(mappingPair1);
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_2"), anyString(), anyString()))
                .thenReturn(Pair.of(null, null));

        // Mock ERP 数据服务
        doNothing().when(erpDataService).sendErpData(any(ErpIdArg.class));

        // Mock 临时数据DAO
        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());

        // Mock 限速管理器
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).incDuplicateTimeByErpIds(anyString(), anyString(), anyList());

        // Mock doTaskByCrmFilter 来模拟实际的数据处理流程
        doAnswer(invocation -> {
            BiFunction<String, List<ObjectData>, Result<Void>> handler = invocation.getArgument(2);
            return handler.apply("crm_obj", crmDataList);
        }).when(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));

        // Mock LogIdUtil.reset 静态方法
        try (MockedStatic<LogIdUtil> mockedLogIdUtil = mockStatic(LogIdUtil.class)) {
            mockedLogIdUtil.when(() -> LogIdUtil.reset(anyString())).thenThrow(new RuntimeException("LogIdUtil error"));

            // When
            Result<Void> result = spyService.doTask(TEST_TENANT_ID, task);

            // Then
            assertTrue(result.isSuccess()); // 即使LogIdUtil异常，任务仍应成功
            verify(erpDataService, times(1)).sendErpData(any(ErpIdArg.class));
            verify(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(TEST_TENANT_ID, TEST_DC_ID, TEST_REAL_OBJ_API_NAME, erpIds);
        }
    }

    /**
     * 测试 doTask 方法 - 依赖服务异常
     * Given: 某个依赖服务抛出异常
     * When: 执行 doTask 方法
     * Then: 正确处理异常并返回错误结果
     */
    @Test
    void testDoTask_DependencyException() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();

        // Mock syncLogManager 抛出异常
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME))
                .thenThrow(new RuntimeException("SyncLogManager error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            crmFilterErpIdsHistoryTaskService.doTask(TEST_TENANT_ID, task);
        });
    }

    /**
     * 测试 doTask 方法 - 父类方法返回错误
     * Given: doTaskByCrmFilter 返回错误结果
     * When: 执行 doTask 方法
     * Then: 返回相应的错误结果
     */
    @Test
    void testDoTask_ParentMethodError() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();
        IdFieldKey idFieldKey = createTestIdFieldKey();
        ErpObjectFieldEntity idField = createTestIdField();

        // Mock 依赖服务
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME)).thenReturn(TEST_LOG_ID);
        when(idFieldKeyManager.buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME))
                .thenReturn(idFieldKey);
        when(erpFieldManager.findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME)).thenReturn(idField);

        // Mock doTaskByCrmFilter 返回错误
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        Result<Void> errorResult = Result.newError("Test error");
        doReturn(errorResult).when(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));

        // When
        Result<Void> result = spyService.doTask(TEST_TENANT_ID, task);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("Test error", result.getErrMsg());
    }

    /**
     * 测试 saveTaskSuccess 方法 - 空参数
     * Given: 传入 null 参数
     * When: 调用 saveTaskSuccess 方法
     * Then: 正确处理空参数
     */
    @Test
    void testSaveTaskSuccess_NullParameters() {
        // Given
        ErpHistoryDataTaskEntity entity = createTestEntity();

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).setCrmsFilter(any(), any());
        doReturn(true).when(spyService).createHistoryTask(anyString(), anyList());

        // When & Then - 测试 null task
        boolean result1 = spyService.saveTaskSuccess(TEST_TENANT_ID, null, entity);
        assertTrue(result1);
        verify(spyService).setCrmsFilter(entity, null);

        // When & Then - 测试 null entity
        ErpHistoryDataTaskResult task = createTestTaskResult();
        boolean result2 = spyService.saveTaskSuccess(TEST_TENANT_ID, task, null);
        assertTrue(result2);
        verify(spyService).setCrmsFilter(null, task.getCrmFilters());
    }

    /**
     * 测试 editTaskSuccess 方法 - 空参数
     * Given: 传入 null 参数
     * When: 调用 editTaskSuccess 方法
     * Then: 正确处理空参数
     */
    @Test
    void testEditTaskSuccess_NullParameters() {
        // Given
        ErpHistoryDataTaskEntity entity = createTestEntity();

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).setCrmsFilter(any(), any());
        doReturn(true).when(spyService).editHistorySnapshotTask(anyString(), any());

        // When & Then - 测试 null task
        boolean result1 = spyService.editTaskSuccess(TEST_TENANT_ID, null, entity);
        assertTrue(result1);
        verify(spyService).setCrmsFilter(entity, null);

        // When & Then - 测试 null entity
        ErpHistoryDataTaskResult task = createTestTaskResult();
        boolean result2 = spyService.editTaskSuccess(TEST_TENANT_ID, task, null);
        assertTrue(result2);
        verify(spyService).setCrmsFilter(null, task.getCrmFilters());
    }

    /**
     * 测试 afterConvert2Vo 方法 - 空参数
     * Given: 传入 null 参数
     * When: 调用 afterConvert2Vo 方法
     * Then: 正确处理空参数而不抛出异常
     */
    @Test
    void testAfterConvert2Vo_NullParameters() {
        // Given
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when((FilterCrmHistoryTaskServiceImpl) spyService).afterConvert2Vo(any(), any());

        // When & Then - 测试 null copy
        assertDoesNotThrow(() -> spyService.afterConvert2Vo(null, createTestEntity()));

        // When & Then - 测试 null entity
        assertDoesNotThrow(() -> spyService.afterConvert2Vo(createTestTaskResult(), null));

        // When & Then - 测试都为 null
        assertDoesNotThrow(() -> spyService.afterConvert2Vo(null, null));
    }

    /**
     * 测试 doTask 方法 - 空租户ID
     * Given: 传入空的租户ID
     * When: 调用 doTask 方法
     * Then: 正确处理空租户ID
     */
    @Test
    void testDoTask_EmptyTenantId() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();

        // When & Then - 测试空字符串租户ID
        assertThrows(Exception.class, () -> {
            crmFilterErpIdsHistoryTaskService.doTask("", task);
        });

        // When & Then - 测试 null 租户ID
        assertThrows(Exception.class, () -> {
            crmFilterErpIdsHistoryTaskService.doTask(null, task);
        });
    }

    /**
     * 测试 doTask 方法 - 空任务实体
     * Given: 传入 null 任务实体
     * When: 调用 doTask 方法
     * Then: 抛出相应异常
     */
    @Test
    void testDoTask_NullTask() {
        // When & Then
        assertThrows(Exception.class, () -> {
            crmFilterErpIdsHistoryTaskService.doTask(TEST_TENANT_ID, null);
        });
    }

    /**
     * 测试 doTask 方法 - 任务实体缺少必要字段
     * Given: 任务实体缺少必要的字段
     * When: 调用 doTask 方法
     * Then: 正确处理缺少字段的情况
     */
    @Test
    void testDoTask_TaskMissingFields() {
        // Given
        ErpHistoryDataTaskEntity task = new ErpHistoryDataTaskEntity();
        task.setId(TEST_TASK_ID);
        task.setTenantId(TEST_TENANT_ID);
        // 缺少其他必要字段

        // When & Then
        assertThrows(Exception.class, () -> {
            crmFilterErpIdsHistoryTaskService.doTask(TEST_TENANT_ID, task);
        });
    }

    /**
     * 测试数据去重功能
     * Given: 有重复的ERP ID
     * When: 执行数据处理流程
     * Then: 正确去重并只处理唯一的ID
     */
    @Test
    void testDoTask_DuplicateErpIds() {
        // Given
        ErpHistoryDataTaskEntity task = createTestEntity();
        IdFieldKey idFieldKey = createTestIdFieldKey();
        ErpObjectFieldEntity idField = createTestIdField();

        // 创建包含重复ID的数据列表
        List<ObjectData> crmDataList = new ArrayList<>();
        ObjectData data1 = new ObjectData();
        data1.setId("crm_id_1");
        crmDataList.add(data1);

        ObjectData data2 = new ObjectData();
        data2.setId("crm_id_2");
        crmDataList.add(data2);

        ObjectData data3 = new ObjectData();
        data3.setId("crm_id_3");
        crmDataList.add(data3);

        // Mock 依赖服务
        when(syncLogManager.initLogId(TEST_TENANT_ID, TEST_REAL_OBJ_API_NAME)).thenReturn(TEST_LOG_ID);
        when(idFieldKeyManager.buildIdFieldKey(TEST_TENANT_ID, TEST_DC_ID, TEST_OBJ_API_NAME, TEST_REAL_OBJ_API_NAME))
                .thenReturn(idFieldKey);
        when(erpFieldManager.findIdField(TEST_TENANT_ID, TEST_OBJ_API_NAME)).thenReturn(idField);

        // Mock 数据映射 - 多个CRM ID映射到相同的ERP ID
        Pair<Object, Object> mappingPair1 = Pair.of(null, createMockMappingRight("erp_id_same"));
        Pair<Object, Object> mappingPair2 = Pair.of(null, createMockMappingRight("erp_id_same")); // 重复
        Pair<Object, Object> mappingPair3 = Pair.of(null, createMockMappingRight("erp_id_different"));

        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_1"), anyString(), anyString()))
                .thenReturn(mappingPair1);
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_2"), anyString(), anyString()))
                .thenReturn(mappingPair2);
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_3"), anyString(), anyString()))
                .thenReturn(mappingPair3);

        // Mock ERP 数据服务
        doNothing().when(erpDataService).sendErpData(any(ErpIdArg.class));

        // Mock 临时数据DAO
        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());

        // Mock 限速管理器
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Mock 父类方法
        CrmFilterErpIdsHistoryTaskServiceImpl spyService = spy(crmFilterErpIdsHistoryTaskService);
        doNothing().when(spyService).incDuplicateTimeByErpIds(anyString(), anyString(), anyList());

        // Mock doTaskByCrmFilter 来模拟实际的数据处理流程
        doAnswer(invocation -> {
            BiFunction<String, List<ObjectData>, Result<Void>> handler = invocation.getArgument(2);
            return handler.apply("crm_obj", crmDataList);
        }).when(spyService).doTaskByCrmFilter(eq(TEST_TENANT_ID), eq(task), any(BiFunction.class));

        // When
        Result<Void> result = spyService.doTask(TEST_TENANT_ID, task);

        // Then
        assertTrue(result.isSuccess());
        // 验证只发送了2个唯一的ERP ID
        verify(erpDataService, times(2)).sendErpData(any(ErpIdArg.class));
        verify(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(eq(TEST_TENANT_ID), eq(TEST_DC_ID), eq(TEST_REAL_OBJ_API_NAME),
                eq(Arrays.asList("erp_id_same", "erp_id_different")));
        verify(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(TEST_TENANT_ID, 2L);

        // 验证任务状态更新
        assertEquals(2L, task.getTotalDataSize());
        assertEquals(2L, task.getOffset());
        assertEquals("erp_id_same;erp_id_different", task.getDataIds());
    }
}
