package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import java.util.Arrays;

/**
 * CrmFilterErpIdsHistoryTaskServiceImpl Simple Test Class
 * Tests basic functionality without complex mocking
 *
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
public class CrmFilterErpIdsHistoryTaskServiceImplTest {

    private CrmFilterErpIdsHistoryTaskServiceImpl testService;

    private static final String TEST_TENANT_ID = "test_tenant_123";
    private static final String TEST_DC_ID = "test_dc_456";
    private static final Integer TEST_USER_ID = 1001;
    private static final String TEST_TASK_ID = "task_123";
    private static final String TEST_OBJ_API_NAME = "test_obj";
    private static final String TEST_REAL_OBJ_API_NAME = "real_test_obj";

    public void setUp() {
        testService = new CrmFilterErpIdsHistoryTaskServiceImpl();
    }

    /**
     * Test taskType method
     */
    public void testTaskType() {
        setUp();
        ErpHistoryDataTaskTypeEnum result = testService.taskType();
        if (result != ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER) {
            throw new AssertionError("Expected TYPE_ERP_IDS_BY_CRM_FILTER but got " + result);
        }
        System.out.println("✓ testTaskType passed");
    }

    /**
     * Test afterConvert2Vo method
     */
    public void testAfterConvert2Vo() {
        setUp();
        // Prepare test data
        ErpHistoryDataTaskResult copy = createTestTaskResult();
        copy.setDataIds("id1;id2;id3");
        ErpHistoryDataTaskEntity entity = createTestTaskEntity();

        // Execute test - should not throw exception
        try {
            testService.afterConvert2Vo(copy, entity);
            // Verify that dataIds is set to null
            if (copy.getDataIds() != null) {
                throw new AssertionError("Expected dataIds to be null after afterConvert2Vo");
            }
            System.out.println("✓ testAfterConvert2Vo passed");
        } catch (Exception e) {
            throw new AssertionError("afterConvert2Vo should not throw exception: " + e.getMessage());
        }
    }





    /**
     * Main test runner method
     */
    public static void main(String[] args) {
        CrmFilterErpIdsHistoryTaskServiceImplTest test = new CrmFilterErpIdsHistoryTaskServiceImplTest();

        System.out.println("Running CrmFilterErpIdsHistoryTaskServiceImpl Tests...");
        System.out.println("=".repeat(60));

        try {
            test.testTaskType();
            test.testAfterConvert2Vo();

            System.out.println("=".repeat(60));
            System.out.println("All tests passed successfully! ✓");
        } catch (Exception e) {
            System.err.println("Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Create test ErpHistoryDataTaskResult
     */
    private ErpHistoryDataTaskResult createTestTaskResult() {
        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult();
        task.objApiName = TEST_OBJ_API_NAME;
        task.taskName = "Test Task";
        task.remark = "Test Remark";
        task.taskType = ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus();
        task.executeTime = System.currentTimeMillis() + 3600000L;
        task.priority = 1;
        task.dataIds = "id1;id2;id3";
        return task;
    }

    /**
     * Create test ErpHistoryDataTaskEntity
     */
    private ErpHistoryDataTaskEntity createTestTaskEntity() {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId(TEST_TASK_ID);
        entity.setTenantId(TEST_TENANT_ID);
        entity.setDataCenterId(TEST_DC_ID);
        entity.setTaskName("Test Task");
        entity.setObjApiName(TEST_OBJ_API_NAME);
        entity.setRealObjApiName(TEST_REAL_OBJ_API_NAME);
        entity.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setExecuteTime(System.currentTimeMillis() + 3600000);
        entity.setTaskNum("TASK_" + System.currentTimeMillis());
        entity.setFilterString("[{\"objectApiName\":\"test_crm_obj\",\"filters\":[]}]");
        return entity;
    }
}
