package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.BadProbeErpLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.context.FromErp2TempCtx;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.Send2DispatcherSpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.NodeFromErp2TempProcessor;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager;
import com.fxiaoke.ps.ProtostuffUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/7/18 17:57:45
 */
@Component
@Slf4j
public class FilterStringErpHistoryTaskServiceImpl extends AbstractErpHistoryTaskServiceImpl {
    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    @Autowired
    private ProbeErpDataManager probeErpDataManager;
    @Autowired
    private PollingDataSpeedRateLimitManager pollingDataSpeedRateLimitManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private Send2DispatcherSpeedLimitManager send2DispatcherSpeedLimitManager;
    @Autowired
    private NodeFromErp2TempProcessor nodeFromErp2TempProcessor;

    @Override
    public ErpHistoryDataTaskTypeEnum taskType() {
        return ErpHistoryDataTaskTypeEnum.TYPE_K3C_FILTER_STRING;
    }

    @Override
    public boolean checkParamsError(ErpHistoryDataTaskResult task) {
        return StringUtils.isBlank(task.getFilterString());
    }

    @Override
    public boolean checkTaskValid(ErpHistoryDataTaskEntity task) {
        return StringUtils.isBlank(task.getFilterString());
    }

    @Override
    public boolean saveTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        return createHistoryTask(tenantId, Lists.newArrayList(entity));
    }

    @Override
    public boolean editTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        entity.setFilterString(task.getFilterString());
        return editHistorySnapshotTask(tenantId, entity);
    }

    @Override
    public Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task) {
        if (send2DispatcherSpeedLimitManager.needSendDispatcherNum(tenantId) < 0) {//待分发数据过多
            return Result.newError(ResultCodeEnum.MANY_WAITING_DISPATCHER);
        }

//        //对于大表来说，这个SQL很慢，会拖死数据库，先去掉。
//        Result<Void> hasMoreError = checkAllPloyDetailErrorByRealObjApiName(tenantId, task);
//        if (!hasMoreError.isSuccess()) {
//            return Result.copy(hasMoreError);
//        }

        //初始化logid
        final String realObjApiName = task.getRealObjApiName();
        syncLogManager.initLogId(tenantId, realObjApiName);
        final String splitObjApiName = task.getObjApiName();

        String dataCenterId = idFieldConvertManager.getDataCenterId(tenantId, splitObjApiName);
        Result<ListErpObjDataResult> result;
        Long totalDataSize = 0L;
        int limit = configCenterConfig.getErpListPageSize(task.getTenantId(), splitObjApiName);
        task.setLimit((long) limit);
        int offset = Objects.isNull(task.getOffset()) ? 0 : task.getOffset().intValue();
        int failCount = 0;
        Long maxTime = null;
        String maxId = null;
        Set<String> oldErpId = Sets.newHashSet();
        Map<Integer, Integer> repeatIdNum = Maps.newHashMap();
        List<Integer> historyComparePageList = Lists.newArrayList();

        //下面两个是为了检测ID重复返回的。
        List<String> lastPageErpObjData = Lists.newArrayList();
        int cntRepeatIDTime = 0;

        // 获取id,检查是否
        while (true) {
            ErpHistoryDataTaskEntity byId = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(task.getId());
            if (byId != null && byId.getNeedStop()) {//中断
                return Result.newError(ResultCodeEnum.STOP_HISTORY_TASK);
            }

            result = listErpObjDataByFilterString(tenantId, dataCenterId, splitObjApiName, realObjApiName, task.getFilterString(), offset, limit, maxTime, maxId);
            if (!result.isSuccess()) {
                //轮询返回错误太多，打成warn日志,如果不是第一页，打成error日志。
                if (offset != 0) {
                    log.error("pollingData erpDataService listErpObjDataByFilterString error, filterString:{}, offset:{}, limit:{}, result:{}", task.getFilterString(), offset, limit, result);
                } else {
                    log.warn("pollingData erpDataService listErpObjDataByFilterString error, filterString:{}, offset:{}, limit:{}, result:{}", task.getFilterString(), offset, limit, result);
                }
                return Result.copy(result);
            }

            List<SyncDataContextEvent> erpObjDataResultList = result.getData().getErpObjDataResultList();
            //计算查询出来的重复数据
            incDuplicateTime(task.getTenantId(), task.getTaskNum(), erpObjDataResultList);

            offset += limit;
            maxTime = result.getData().getMaxTime();
            maxId = result.getData().getMaxId();
            totalDataSize += erpObjDataResultList.size();
            //每次更新一下总数，只是更新实例，最后在处理结果更新到db。
            task.setOffset((long) offset);
            task.setTotalDataSize(totalDataSize);
            if (result.getData().isComplete() || offset >= ConfigCenter.ERP_LIST_MAX_NUM) {
                break;
            }
            pollingDataSpeedRateLimitManager.acquireQueryErpHistoryData(tenantId, (long) erpObjDataResultList.size());

            /**检测恶意轮询ERP**
             * 一个时间片轮询到的数据超过1万条的，做恶意轮询检测。
             * 检查该分页和上一个分页是否 完全重复ID。像用友U8和sap的一些接口，日期精确到天，会存在多次查询返回相同数据的情况。
             */
            if ((offset >= 10000)) {
                try {
                    if (offset >= ConfigCenter.ERP_LIST_MAX_NUM) {
                        log.warn("stop probeerp filterStringErpHistory for exceed ERP_LIST_MAX_NUM,ei:{},obj:{}, offset:{}", tenantId, splitObjApiName, offset);
                        break;
                    }
                    boolean isRepeatID = probeErpDataManager.isAllIDRepeatAsLastPage(tenantId, splitObjApiName, offset, limit, erpObjDataResultList, lastPageErpObjData);
                    cntRepeatIDTime = isRepeatID ? cntRepeatIDTime + 1 : 0;

                    BadProbeErpLog dumpLog = BadProbeErpLog.builder().tenantId(tenantId).appName("erpdss").objectApiName(splitObjApiName).offset(offset).stopProbe(cntRepeatIDTime >= 10 ? "y" : "n").build();
                    BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));

                    if (cntRepeatIDTime >= 10) { //连续10次轮询到完全相同的数据，判定为没有分页接口，强制终止轮询
                        log.warn("stop probeerp filterStringErpHistory for repeat 10 times,ei:{},obj:{},offset:{}", tenantId, splitObjApiName,offset);
                        break;
                    }

                } catch (Exception e) {
                    log.error("get exception,", e);
                    //避免抛异常导致cntRepeatIDTime没有被正常清空，错误终止轮询引发事故
                    cntRepeatIDTime = 0;
                }
            }
        }

        return Result.newSuccess();
    }

    private Result<ListErpObjDataResult> listErpObjDataByFilterString(String tenantId, String dataCenterId, String splitObjApiName, String realObjApiName, String filterString, int offset, int limit, Long maxTime, String maxId) {
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setTenantId(tenantId);
        timeFilterArg.setObjAPIName(realObjApiName);
        timeFilterArg.setOperationType(EventTypeEnum.UPDATE.getType());//默认更新
        timeFilterArg.setOffset(offset);
        timeFilterArg.setLimit(limit);
        timeFilterArg.setFilters(new ArrayList<>());
        timeFilterArg.setCustomFilterString(filterString);
        timeFilterArg.setLastMaxId(maxId);
        timeFilterArg.setStartTime(maxTime);
        FromErp2TempCtx context = new FromErp2TempCtx()
                .setBasic(timeFilterArg.getTenantId(),dataCenterId)
                .setSplitObjApiName(splitObjApiName)
                .setTimeFilterArg(timeFilterArg);
        nodeFromErp2TempProcessor.processMessage(context);
        return context.getResult();
    }
}
