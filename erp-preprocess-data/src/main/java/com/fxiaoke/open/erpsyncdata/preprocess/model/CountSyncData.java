package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/26 16:35:34
 */
@Data
public class CountSyncData implements Serializable {
    @ExcelProperty("erpdss.global.global.s1098")
    private String streamName;
    @ExcelProperty("erpdss.global.global.s289")
    private int count;
    @ExcelProperty("erpdss.global.global.s2057")
    private int createFailCount;
    @ExcelProperty("erpdss.global.global.s2058")
    private int updateFailCount;
    @ExcelProperty("erpdss.global.global.s2059")
    private int createSuccessCount;
    @ExcelProperty("erpdss.global.global.s2060")
    private int updateSuccessCount;
    @ExcelProperty("erpdss.global.global.s2061")
    private int creatingCount;
    @ExcelProperty("erpdss.global.global.s2062")
    private int updatingCount;
    @ExcelProperty("erpdss.global.global.s2063")
    private int notTriggeredCount;

    @ExcelProperty("erpdss.global.global.s657")
    private String sourceApiName;
    @ExcelProperty("erpdss.global.global.s660")
    private String destApiName;
    @ExcelIgnore
    private String dcId;
}
