package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SplitObjResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;

import java.util.List;

/**
 * Created by fengyh on 2020/9/4.
 *
 * 对象拆分管理接口类。
 * 这个服务完成下面几个功能：
 * 1.把一个ERP类拆分为多个 拆分ERP对象，并保存拆分关系。 (设置页面使用)
 * 2.查询 真实ERP对象和拆分对象的关系。 (同步数据的时候使用)
 *
 * 注意ERP真实对象和对象拆分不区分数据同步方向。
 * 就是无论是CRM->erp, 还是erp->crm同步数据，拆分出来的对象都是一样的。
 * 比如crm->erp，如果拆分出来的crm子对象obj1对应erp拆分对象obj2, 那么反方向如果用到了obj1，那么obj1对应的必须是obj2.
 *
 * 这里的拆分对象实现，基于一个假设： 只有ERP对象会拆分出来对应多个CRM对象，不会有CRM对象拆分出来对应多个ERP对象。
 */
public interface SplitObjectManager {

    /**
     * erp真实对象的数据转为 拆分对象的数据， erp->crm同步数据时使用。
     * 结果可能是一个拆分对象，也可能是多个拆分对象。
     * */
    List<SyncDataContextEvent> erpRealObj2SplitObjData(SyncDataContextEvent data, String dataCenterId);


    /**
     * crm->erp创建数据时使用
     *
     * @param data
     * @param erpObjExtendDtls
     * @return
     */
    SplitObjResult.Erp2Crm createErpSplitObj2RealObj(SyncDataContextEvent data,List<ErpObjExtendDto> erpObjExtendDtls,String dataCenterId);

    /**
     * crm->erp更新数据时使用
     *
     * @param data
     * @param erpObjExtendDtls
     * @return
     */
    SplitObjResult.Erp2Crm updateErpSplitObj2RealObj(SyncDataContextEvent data,List<ErpObjExtendDto> erpObjExtendDtls,String dataCenterId);

}
