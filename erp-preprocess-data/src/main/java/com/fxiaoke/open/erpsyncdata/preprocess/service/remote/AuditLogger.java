package com.fxiaoke.open.erpsyncdata.preprocess.service.remote;

import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.arg.AddRecordLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service.ModificationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/10/9 11:18:09
 */
@Component
@Slf4j
public class AuditLogger {
    @Autowired
    ModificationRecordService modificationRecordService;

    /**
     * @see com.facishare.paas.appframework.log.ActionType
     */
    public static final String ActionType_Add = "1";
    public static final String ActionType_Modify = "2";


    public static final String peerNameKey = "x-fs-peer-name";
    public static final String peerName = "erpdss";

    /**
     * 记录审计日志,用于记录新增删除(无需对比历史数据)
     * remove 接口自定义对象存在主从级联删除的情况
     */
    public void recordAddLog(String tenantId, String operatorId, String apiName, List<ObjectData> dataList) {
        recordLog(tenantId, operatorId, apiName, new ArrayList<>(), dataList, ActionType_Add);
    }


    /**
     * 记录审计日志,用于修改(需要和历史数据做对比)
     */
    public void recordModifyLog(String tenantId, String operatorId, String apiName, List<ObjectData> beforeData, List<ObjectData> afterData) {
        recordLog(tenantId, operatorId, apiName, beforeData, afterData, ActionType_Modify);
    }

    private void recordLog(String tenantId, String operatorId, String apiName, List<ObjectData> beforeData, List<ObjectData> afterData, String actionType) {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), Integer.valueOf(operatorId));
        // peerName配置为erpdss,配合配置文件fs-paas-appframework-peer-name显示操作人
        headerObj.put(peerNameKey, peerName);

        final List<Map<String, Object>> before = beforeData.stream().map(o -> (Map<String, Object>) o).collect(Collectors.toList());
        final List<Map<String, Object>> after = afterData.stream().map(o -> (Map<String, Object>) o).collect(Collectors.toList());
        final AddRecordLog arg = new AddRecordLog(before, after, actionType);
        try {
            modificationRecordService.addLog(headerObj, apiName, arg);
        } catch (Exception e) {
            log.error("CRM修改日志上报错误, tenantId:{} apiName:{} actionType:{}", tenantId, apiName, actionType, e);
        }
    }
}
