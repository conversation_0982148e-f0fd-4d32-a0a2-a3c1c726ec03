package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionParameterEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpCustomFunctionParameterEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date: 19:17 2020/8/26
 * @Desc:
 */
@Slf4j
@Service
public class ErpOuterServiceImpl extends AbsOuterServiceImpl {
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private CustomFunctionService customFunctionService;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;

    @Override
    public Result2<String> getReferName(String tenantId, Integer tenantType, String objectApiName, String id) {
        return Result2.newSuccess(id);
    }

    @Override
    public Result2<String> getDeptName(String tenantId, Integer tenantType, String detpId) {
        return Result2.newSuccess(detpId);
    }

    @Override
    public Result2<BatchGetOuterTenantIdByEaData> batchGetOuterTenantIdByEa(Integer upstreamEi, List<String> eas) {
        return null;
    }

    @Override
    public Result2<String> getMasterObjectApiName(String tenantId, Integer tenantType, String objectApiName) {
        ErpObjectFieldEntity record = new ErpObjectFieldEntity();
        record.setTenantId(tenantId);
        record.setErpObjectApiName(objectApiName);
        ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, objectApiName);
        if (ErpObjSplitTypeEnum.TYPES_ALLOW_INDEPENDENT_STREAM.contains(relation.getSplitType())){
            //允许单独集成流的类型
            return Result2.newSuccess();
        }
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(record);
        for (ErpObjectFieldEntity entity : erpObjectFieldEntities) {
            if (ErpFieldTypeEnum.master_detail.equals(entity.getFieldDefineType())) {
                return Result2.newSuccess(entity.getFieldExtendValue());
            }
        }
        return Result2.newSuccess();
    }

    @Override
    public Result2<List<ObjectData>> listObjectDatas(String tenantId, Integer tenantType, String objectApiName, Map<String, List<String>> filterFieldValues, Integer limit, Integer offset) {
        return null;
    }


    @Override
    public List<List<FilterData>> changeVariableFilter(List<List<FilterData>> filters, String sourceTenantId, String destTenantId) {
        return Lists.newArrayList();
    }


    @Override
    public Result2<Long> getDownstreamRelationOwnerOuterUid(Integer upstreamEi, Integer downstreamEi) {
        return null;
    }

    @Override
    public Result2<ObjectData> getObjectData(String tenantId, Integer tenantType, String objectApiName, String id) {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setDataId(id);
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setIncludeDetail(false);
        erpIdArg.setObjAPIName(objectApiName);
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<SyncDataContextEvent> erpObjDataById = erpDataPreprocessService.getErpObjDataFromMongoIfExist(erpIdArg);
        if (!erpObjDataById.isSuccess()) {
            //重置objApiName字段的值，因为getErpObjDataFromMongoIfExist函数会把这个字段的值改成真实对象的apiName
            erpIdArg.setObjAPIName(objectApiName);
            erpIdArg.setDataIdIsNumber(true);
            erpObjDataById = erpDataPreprocessService.getErpObjDataFromMongoIfExist(erpIdArg);
            if(!erpObjDataById.isSuccess()) {
                return Result2.newError(-1, erpObjDataById.getErrMsg());
            }
        }
        if (erpObjDataById.getData() != null && erpObjDataById.getData().getDetailData() != null) {
            return Result2.newSuccess(erpObjDataById.getData().getSourceData());
        }
        return Result2.newSuccess();
    }
    @Override
    public Result2<ObjectData> getObjectDataByName(String tenantId, Integer tenantType, String objectApiName, String name) {
        return Result2.newSuccess();
    }
    @Override
    public Result2<ObjectData> getDetailById(String tenantId, String apiName, String id) {

        return null;
    }

    @Override
    public Result2<Integer> getOwnerId(Integer tenantId, Integer tenantType, String objectApiName, String id) {
        return null;
    }

    @Override
    public Result2<Map<String, String>> batchGetEmployeeFieldValue(String tenantId, Integer tenantType, List<String> employeeIds, String fieldName) {
        Map<String, String> names = new HashedMap();//目前先返回一样的，
        for (String employeeId : employeeIds) {
            names.put(employeeId, employeeId);
        }
        return Result2.newSuccess(names);
    }

    @Override
    public Result2<List<Map<String, String>>> convertFiles(String sourceTenantId, Integer sourceTenantType, String type, List<Map<String, String>> sourceFiles, String destTenantId, Integer destTenantType) {

        return null;
    }

    @Override
    public Result2<GetOuterAccountByFsData> getOuterAccountByFs(Integer upstreamEi, Integer ei, Integer userId) {
        return null;
    }

    @Override
    public Result2<GetFsAccountByOuterData> getFsAccountByOuter(Long outerUid) {
        return null;
    }


    @Override
    public Result2<String> getMapperObjectId(Integer upstreamEi, Integer downstreamEi, String objectApiName) {
        return null;
    }

    @Override
    public Result2<FunctionServiceExecuteReturnData> executeCustomFunction(String tenantId,
                                                                           String erpDataCenterId,
                                                                           ExecuteCustomFunctionArg arg,
                                                                           ErpObjInterfaceUrlEnum interfaceUrl,
                                                                           CustomFunctionTypeEnum functionType,
                                                                           final String ployDetailSnapshotId, String objectName) {
        FunctionServiceExecuteArg functionServiceExecuteArg = new FunctionServiceExecuteArg();
        functionServiceExecuteArg.setApiName(arg.getApiName());
        functionServiceExecuteArg.setNameSpace(arg.getNameSpace());
        functionServiceExecuteArg.setBindingObjectAPIName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        List<FunctionServiceParameterData> functionServiceParameterDataList = new ArrayList<>();
        FunctionServiceParameterData<Map> functionServiceParameterData = new FunctionServiceParameterData();
        functionServiceParameterData.setName(CustomFunctionConstant.SYNC_ARG_NAME);
        functionServiceParameterData.setType(CustomFunctionConstant.SYNC_ARG_TYPE_MAP);
        Map<String, Object> dataValueMap = new HashMap<>();
        if (arg.getParameter() != null && arg.getParameter().getSourceData() != null) {
            dataValueMap.put(ErpCustomFunctionParameterEnum.SOURCE_DATA_ID.getArgName(), arg.getParameter().getSourceData().getId());//源数据id
        }
        dataValueMap.put(CustomFunctionParameterEnum.SYNC_DATA_ID.getArgName(), arg.getParameter().getSyncDataId());
        dataValueMap.put(CustomFunctionParameterEnum.SOURCE_TENANT_ID.getArgName(), arg.getParameter().getSourceTenantId());
        dataValueMap.put(CustomFunctionParameterEnum.SOURCE_OBJECT_APINAME.getArgName(), arg.getParameter().getSourceObjectApiName());
        dataValueMap.put(CustomFunctionParameterEnum.SOURCE_EVENT_TYPE.getArgName(), arg.getParameter().getSourceEventType());
        dataValueMap.put(CustomFunctionParameterEnum.DEST_TENANT_ID.getArgName(), arg.getParameter().getDestTenantId());
        dataValueMap.put(CustomFunctionParameterEnum.DEST_OBJECT_APINAME.getArgName(), arg.getParameter().getDestObjectApiName());
        dataValueMap.put(CustomFunctionParameterEnum.DEST_EVENT_TYPE.getArgName(), arg.getParameter().getDestEventType());
        dataValueMap.put(CustomFunctionParameterEnum.OBJECT_DATA.getArgName(), arg.getObjectData());
        dataValueMap.put(CustomFunctionParameterEnum.DETAILS.getArgName(), arg.getDetails());
        dataValueMap.put(CustomFunctionParameterEnum.COMPLETE_DATA_WRITE_RESULT.getArgName(), arg.getParameter().getCompleteDataWriteMqData());
        //从节点变量取值
        NodeContext<?> context = NodeHelper.getContext();
        if (context != null) {
            dataValueMap.put(CustomFunctionParameterEnum.DATA_CENTER_ID.getArgName(), context.getDcId());
            dataValueMap.put(CustomFunctionParameterEnum.STREAM_IDS.getArgName(), context.getStreamIds());
        }
        functionServiceParameterData.setValue(dataValueMap);
        functionServiceParameterDataList.add(functionServiceParameterData);
        functionServiceExecuteArg.setParameters(functionServiceParameterDataList);
        if (StringUtils.isNotBlank(objectName)) {
            //同步函数 添加函数主属性
            final String crmObjectApiName = Objects.equals(functionType, CustomFunctionTypeEnum.BEFORE_FUNCTION) ? arg.getParameter().getDestObjectApiName() : arg.getParameter().getSourceObjectApiName();
            functionServiceExecuteArg.setObjectData(ImmutableMap.of("name", objectName, "object_describe_api_name", crmObjectApiName));
        }
        com.fxiaoke.otherrestapi.function.data.HeaderObj headerObj = new com.fxiaoke.otherrestapi.function.data.HeaderObj(Integer.parseInt(tenantId), CrmConstants.SYSTEM_USER);
        Result2<FunctionServiceExecuteReturnData> result = customFunctionService.currencyFunction(tenantId,
                erpDataCenterId,headerObj, functionServiceExecuteArg,interfaceUrl, functionType, ployDetailSnapshotId);
        if (!result.isSuccess()) {
            return  result;
        }
        return Result2.newSuccess(result.getData());
    }

    @Override
    public Result2<ProductCategoryData> getSourceProductCategoryValue(String tenentId, String code) {
        return Result2.newSuccess();
    }

    @Override
    public Result2<ProductCategoryData> getOrCreateDestProductCategoryValue(String tenentId, String categoryName, String categoryCode, Integer orderField) {
        return Result2.newSuccess();
    }

    @Override
    public Result2<Map<String, ProductCategoryData>> listProductCategory(String tenentId) {
        return Result2.newSuccess();
    }

    @Override
    public Result2<FieldDescribeData> getFieldDescribe(String tenantId, String apiName, String fieldApiName) {
        return Result2.newSuccess();
    }

    /**
     * 获取对象名称，缓存5分钟
     *
     * @param tenantId
     * @param apiName
     * @return
     */
    @Override
    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 5)
    public Result2<String> getObjectNameByApiName(String tenantId, String apiName) {
        String objName = erpObjManager.getErpObjName(tenantId, apiName);
        return Result2.newSuccess(objName);
    }

    @Override
    public Result2<SyncDataContextEvent> executeCompleteWriteHook(SyncDataContextEvent message) {
        return Result2.newSuccess(message);
    }

//    @Override
    public Result2<Boolean> isUsePriceBookObj(String s) {
        //不应该走到这里，故不实现方法
        return Result2.newError(ResultCodeEnum.SYSTEM_ERROR);
    }

    @Override
    public Integer getTenantType() {
        return TenantType.ERP;
    }

    @Override
    public Result2<String> getIdByNumber(String tenantId, String splitObjApiName, String number) {
        ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, splitObjApiName);
        String realObjApiName = relation.getErpRealObjectApiname();
        String dataCenterId = relation.getDataCenterId();

        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId,dataCenterId);
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, connectInfoEntity.getConnectParams(),dataCenterId);
        String dataId = commonBusinessManager.getIdByNumber(realObjApiName,number,apiClient);
        return Result2.newSuccess(dataId);
    }

    @Override
    public Result2<String> batchGetObjectDataAndCache(String tenantId, String objectApiName, List<String> objectDataIds) {
        return null;
    }

    @Override
    public Result2<Void> removeBatchCache() {
        return null;
    }

    @Override
    public Result2<ObjectData> queryObjectDataByEqFilter(String erpDcId,String tenantId, Integer tenantType, String objectApiName, List<List<FilterData>>orFilters) {
        return Result2.newSuccess();
    }
}
