package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.apiproxy.service.SyncCpqService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.*;
import com.fxiaoke.open.erpsyncdata.preprocess.util.BuriedSitesStatisticsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 14:02 2020/10/20
 * @Desc:
 */
@Service
@Slf4j
public class ErpProcessMessageServiceImpl implements ProcessMessageService {
    @Autowired
    private SyncSkuSpuServiceImpl skuSpuService;
    @Autowired
    private SyncStockService syncStockService;
    @Autowired
    private SyncOrgService syncOrgService;
    @Autowired
    private SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService;
    @Autowired
    private SyncCpqService syncCpqService;
    @Autowired
    private SyncMultipleUnitService syncMultipleUnitService;
    @Autowired
    private SyncSerialNumberService syncSerialNumberService;
    @Autowired
    private BuriedSitesStatisticsUtils buriedSitesStatisticsUtils;


}
