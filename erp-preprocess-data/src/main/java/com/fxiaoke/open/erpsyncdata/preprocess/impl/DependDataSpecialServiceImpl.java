package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.DependDataSpecialService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDataMappingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DependDataSpecialServiceImpl implements DependDataSpecialService {
    @Resource
    private OuterServiceFactory outerServiceFactory;
    @Resource
    private SyncDataMappingService syncDataMappingService;

    @Override
    public Result2<SyncDataMappingData> getSyncDataMappingData(String tenantId,
                                                               ObjectData sourceData,
                                                               SyncDataData syncDataData,
                                                               FieldMappingData fieldMapping) {
        Object value = sourceData.get(fieldMapping.getSourceApiName());
        if(value==null) return Result2.newSuccess();

        SyncDataMappingData syncDataMappingData = null;

        //ERP订单明细关联销售订单编码，需要对关联的销售订单编码进行转换，比如应收单明细关联销售订单
        if(StringUtils.contains(fieldMapping.getSourceTargetApiName(),K3CloudForm.SAL_SaleOrder)) {
            String dataId = outerServiceFactory.get(syncDataData.getSourceTenantType())
                    .getIdByNumber(tenantId, fieldMapping.getSourceTargetApiName(), String.valueOf(value)).getData();
            log.info("DependDataSpecialServiceImpl.getSyncDataMappingData,dataId={}", dataId);
            syncDataMappingData = syncDataMappingService.getSyncDataMapping(tenantId,
                    syncDataData.getSourceTenantId(),
                    fieldMapping.getSourceTargetApiName(),
                    dataId + "#" + value,
                    syncDataData.getDestTenantId(),
                    fieldMapping.getDestTargetApiName()).getData();
            log.info("DependDataSpecialServiceImpl.getSyncDataMappingData,syncDataMappingData={}", syncDataMappingData);
            if(syncDataMappingData!=null) {
                sourceData.put(fieldMapping.getSourceApiName(),dataId + "#" + value);
            }
        }
        return Result2.newSuccess(syncDataMappingData);
    }
}
