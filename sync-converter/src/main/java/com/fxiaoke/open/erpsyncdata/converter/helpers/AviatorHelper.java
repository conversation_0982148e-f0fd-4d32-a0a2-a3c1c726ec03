package com.fxiaoke.open.erpsyncdata.converter.helpers;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.UserOperatorLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * AviatorEvaluator 工具类，方便业务使用
 * <AUTHOR>
 * @date 2022-03-17
 */
@Slf4j
@Component
public class AviatorHelper {
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private SyncPloyManager syncPloyManager;

    public Object execute(String expression,
                          Map<String, Object> env,
                          boolean cached,
                          String tenantId,
                          String dataCenterId,
                          String sourceObjectApiName,
                          String ployDetailId,
                          boolean isDetailObject) {
        log.debug("AviatorHelper.execute,expression={},env={},tenantId={},dataCenterId={},sourceObjectApiName={},ployDetailId={},isDetailObject={}",
                expression, JSONObject.toJSON(env),tenantId,dataCenterId,sourceObjectApiName,ployDetailId,isDetailObject);
        Object result = false;
        try {
            result = AviatorEvaluator.execute(expression, env, cached);

            // 如果结果为false，尝试检测导致false的字段
            if (Boolean.FALSE.equals(result)) {
                String failSubExpression =  detectFailedField(expression, env, cached, tenantId, sourceObjectApiName);
                if (failSubExpression != null) {
                    log.info("AviatorHelper.execute failed for ei:{}, obj:{}, dataId:{}, sub expression: {}", tenantId,sourceObjectApiName, extractDataId(env), failSubExpression);
                }
            }

        } catch (Exception e) {
            log.info("AviatorHelper.execute,exception={}",e.getMessage());
            if(e instanceof NullPointerException) {
                //数据为空导致的检验异常，这类异常需要良性异常，不需要发通知
                return false;
            }
            String exceptionStr = "\n"+i18NStringManager.getByEi(I18NStringEnum.s931,tenantId)+"\n " + e.getMessage();
            boolean needStopPloy = false;
            if(StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().startsWith("Could not compare")) {
                exceptionStr = formatExceptionString(tenantId,e.getMessage(),sourceObjectApiName);
                needStopPloy = true;
            }
            log.info("AviatorHelper.execute,exception2={}",exceptionStr);

            boolean ployStopped = false;
            if(needStopPloy) {
                boolean success = disablePloyDetail(tenantId,
                        sourceObjectApiName,
                        ployDetailId, exceptionStr);
                ployStopped = success;
                if (!success) {
                    log.info("AviatorHelper.execute,disable ploy detail failed,sourceObjectApiName={},ployDetailSnapshotId={}",
                            sourceObjectApiName,ployDetailId);
                }
            }

            StringBuilder sb = new StringBuilder();
            sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s932,tenantId)+exceptionStr+"\n");
            sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s933,tenantId)+sourceObjectApiName+"\n");
            if(env.containsKey("erp_id")) {
                sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s934,tenantId)+ env.getOrDefault("erp_id","")+"\n");
            }
            if(env.containsKey("erp_num")) {
                sb.append("\n"+i18NStringManager.getByEi(I18NStringEnum.s935,tenantId)+ env.getOrDefault("erp_num","")+"\n");
            }

            String ployStopTips = ployStopped ? "，"+i18NStringManager.getByEi(I18NStringEnum.s936,tenantId):"";

            SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                    .msg(sb.toString())
                    .msgTitle(i18NStringManager.getByEi2(I18NStringEnum.s937.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s937.getI18nValue(), ployStopTips),
                            Lists.newArrayList(ployStopTips)))
                    .tenantId(tenantId)
                    .dcId(dataCenterId)
                    .ployDetailId(ployDetailId)
                    .sendSuperAdminIfNoSendTenantAdmin(true)
                    .build();
            arg = arg.addTraceInfo();

            //如果对象数据范围检验导致的校验失败并且策略停用成功，则不需要重复发送通知
            String key = tenantId+"_"+dataCenterId+"_"+sourceObjectApiName+"_data_range_verify_failed";
            String cacheValue = redisCacheManager.getCache(key, this.getClass().getSimpleName());
            log.info("AviatorHelper.execute,cacheValue={}",cacheValue);
            if(StringUtils.isEmpty(cacheValue)) {
                //默认5分钟超时
                redisCacheManager.setCache(key,"true",5*60L, this.getClass().getSimpleName());
                notificationService.sendTenantAdminNotice(arg,
                        AlarmRuleType.GENERAL,
                        AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                        AlarmType.SYNC_EXCEPTION,
                        AlarmLevel.IMPORTANT);
            }
        }
        log.debug("AviatorHelper.execute,result={}",result);
        return result;
    }

    public Object execute(String expression,
                          Map<String, Object> env,
                          String tenantId,
                          String dataCenterId,
                          String sourceObjectApiName,
                          String ployDetailId,
                          boolean isDetailObject) {
        return execute(expression, env, true, tenantId, dataCenterId, sourceObjectApiName, ployDetailId,isDetailObject);
    }

    private String formatExceptionString(String tenantId,String message,String sourceObjectApiName) {
        String newStr = "\n";
        String splitStr = " with ";
        //String str = "Could not compare <String, 1> with <JavaType, FStockOrgId, 1, java.lang.Integer>";
        int pos = message.indexOf(splitStr);
//        String before = message.substring(0,pos).replace("Could not compare ","");
//        before = before.replace("<","").replace(">","");
//
//        List<String> items = Splitter.on(",").splitToList(before);
//        newStr += "源数据的类型="+items.get(0)+",源数据的值="+items.get(1)+";\n";

        String after = message.substring(pos+splitStr.length())
                .replace("<","")
                .replace(">","");

        List<String> items = Splitter.on(",").splitToList(after);
        String javaType = items.get(3);
        String fieldApiName = items.get(1);
        //newStr += "源对象字段apiName="+items.get(1)+",字段的类型="+items.get(3).replace("java.lang.","")+";\n";
        newStr += i18NStringManager.getByEi(I18NStringEnum.s938,tenantId)+fieldApiName+","+i18NStringManager.getByEi(I18NStringEnum.s939,tenantId) +
                "\n"+i18NStringManager.getByEi(I18NStringEnum.s940,tenantId) +
                "\n" + i18NStringManager.getByEi2(I18NStringEnum.s941.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s941.getI18nValue(), sourceObjectApiName,fieldApiName,javaType),
                Lists.newArrayList(sourceObjectApiName,fieldApiName,javaType)) +
                "\n" + i18NStringManager.getByEi(I18NStringEnum.s942,tenantId) +
                "\n"+ i18NStringManager.getByEi(I18NStringEnum.s943,tenantId);
        return newStr;
    }

    /**
     * 熔断使用，停止某个企业的策略
     * objApiName 或者 ployDetailSnapshotId传其一，有限使用snapId
     *
     * @param tenantId
     * @param objApiName
     * @param ployDetailId
     * @return
     */
    public boolean disablePloyDetail(String tenantId, String objApiName, String ployDetailId, String disableReason) {
        List<String> ployDetailIds = new ArrayList<>();
        if (StringUtils.isNotBlank(ployDetailId)) {
            ployDetailIds.add(ployDetailId);
        }

        if(CollectionUtils.isEmpty(ployDetailIds)) {
            List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listBySource(tenantId,
                            SyncPloyDetailStatusEnum.ENABLE.getStatus(),
                            TenantType.ERP,
                            objApiName);
            ployDetailIds = syncPloyDetailEntities.stream().map(SyncPloyDetailEntity::getId).collect(Collectors.toList());
        }
        boolean success = false;
        for (String detailId : ployDetailIds) {
            SyncPloyDetailEntity syncPloyDetailEntity =
                    adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .getById(tenantId, ployDetailId);
            final boolean disabled = syncPloyManager.disablePloyDetailByStreamId(tenantId, detailId, syncPloyDetailEntity.getSourceObjectApiName());
            Integer i3 = adminSyncPloyDetailDao
                    .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .updateValid(detailId, false);
            success = disabled && i3>0;

            if (disabled || i3>0){
                if(null == disableReason) {
                    disableReason="";
                }
                String erpDataCenterId;
                if(TenantType.ERP==syncPloyDetailEntity.getSourceTenantType()){
                    erpDataCenterId=syncPloyDetailEntity.getSourceDataCenterId();
                }else{
                    erpDataCenterId=syncPloyDetailEntity.getDestDataCenterId();
                }
                UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId,erpDataCenterId, "INTEGRATION_STREAM",
                        ployDetailId,-10000,"STOP",i18NStringManager.getByEi2(I18NStringEnum.s944.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s944.getI18nValue(), syncPloyDetailEntity.getIntegrationStreamName(),disableReason),
                                Lists.newArrayList(syncPloyDetailEntity.getIntegrationStreamName(),disableReason)),
                        null));
            }
        }
        return success;
    }

    /**
     * 检测导致表达式结果为false的字段
     * 使用简化方法：直接拆分表达式并逐个执行子表达式
     * @param expression 表达式
     * @param env 环境变量
     * @param cached 是否缓存
     * @param tenantId 租户ID
     * @param sourceObjectApiName 源对象API名称
     * @param depth 递归深度，防止无限递归
     * @return 导致失败的简单表达式，如果找不到则返回null
     */
    private String detectFailedField(String expression, Map<String, Object> env, boolean cached,
                                     String tenantId, String sourceObjectApiName, int depth) {
        // 防止无限递归
        if (depth > 10) {
            log.warn("AviatorHelper.detectFailedField - 递归深度超过限制，表达式可能过于复杂: {}", expression);
            return null;
        }

        try {
            String dataId = extractDataId(env);

            // 1. 首先处理AND表达式 (&&)
            if (expression.contains("&&")) {
                List<String> andParts = splitLogicalExpression(expression, "&&");
                for (String part : andParts) {
                    try {
                        Object result = AviatorEvaluator.execute(part, env, cached);
                        if (Boolean.FALSE.equals(result)) {
                            // 找到失败的子表达式，进一步分析
                            log.debug("trace AviatorHelper 找到导致失败的子表达式: tenantId={}, sourceObjectApiName={}, dataId={}, subExpression={}", tenantId, sourceObjectApiName, dataId, part);
                            // 递归分析子表达式，增加深度计数
                            if (part.contains("&&") || part.contains("||")) {
                                String failedSubExpr = detectFailedField(part, env, cached, tenantId, sourceObjectApiName, depth + 1);
                                if (failedSubExpr != null) {
                                    return failedSubExpr;
                                }
                            }

                            // 如果是简单表达式，直接返回
                            if (!part.contains("&&") && !part.contains("||")) {
                                analyzeSimpleExpression(part, env, tenantId, sourceObjectApiName, dataId);
                                return part;
                            }
                        }
                    } catch (Exception e) {
                        // 忽略子表达式执行异常
                    }
                }
            }

            // 2. 处理OR表达式 (||)
            if (expression.contains("||")) {
                List<String> orParts = splitLogicalExpression(expression, "\\|\\|");
                boolean allFalse = true;

                for (String part : orParts) {
                    try {
                        Object result = AviatorEvaluator.execute(part, env, cached);
                        if (Boolean.TRUE.equals(result)) {
                            allFalse = false;
                            break;
                        }
                    } catch (Exception e) {
                        // 忽略子表达式执行异常
                    }
                }

                if (allFalse) {
                    log.debug("trace AviatorHelper 所有OR子表达式均为false: tenantId={}, sourceObjectApiName={}, dataId={}", tenantId, sourceObjectApiName, dataId);
                    // 收集所有失败的OR子表达式
                    List<String> failedOrParts = new ArrayList<>();
                    // 分析每个OR子表达式
                    for (String part : orParts) {
                        log.debug("trace AviatorHelper 分析OR子表达式: tenantId={}, sourceObjectApiName={}, dataId={}, subExpression={}", tenantId, sourceObjectApiName, dataId, part);

                        // 递归分析子表达式，增加深度计数
                        if (part.contains("&&")) {
                            String failedSubExpr = detectFailedField(part, env, cached, tenantId, sourceObjectApiName, depth + 1);
                            if (failedSubExpr != null) {
                                return failedSubExpr;
                            }
                        } else {
                            // 如果是简单表达式，添加到失败列表
                            analyzeSimpleExpression(part, env, tenantId, sourceObjectApiName, dataId);
                            failedOrParts.add(part);
                        }
                    }

                    // 返回第一个失败的简单表达式
                    if (!failedOrParts.isEmpty()) {
                        return failedOrParts.get(0);
                    }
                }
            }

            // 3. 处理简单表达式
            if (!expression.contains("&&") && !expression.contains("||")) {
                analyzeSimpleExpression(expression, env, tenantId, sourceObjectApiName, dataId);
                return expression;
            }

            // 如果仍然没有找到，记录通用日志
            log.debug("trace AviatorHelper 无法找到导致数据范围校验失败的字段， tenantId={}, sourceObjectApiName={}, dataId={}, expression={}",  tenantId, sourceObjectApiName, dataId, expression);
            return null;
        } catch (Exception e) {
            // 字段检测过程中的异常不影响主流程
            log.debug("AviatorHelper.detectFailedField - 字段检测过程中发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检测导致表达式结果为false的字段
     * 公共入口方法，初始化递归深度为0
     * @return 导致失败的简单表达式，如果找不到则返回null
     */
    private String detectFailedField(String expression, Map<String, Object> env, boolean cached,
                                     String tenantId, String sourceObjectApiName) {
        //使用深拷贝避免数据被意外修改
        Map<String, Object> envCopy  = BeanUtil.deepCopy(env, Map.class);
        return detectFailedField(expression, envCopy, cached, tenantId, sourceObjectApiName, 0);
    }

    /**
     * 分析简单表达式中的字段
     */
    private void analyzeSimpleExpression(String expression, Map<String, Object> env,
                                         String tenantId, String sourceObjectApiName, String dataId) {
        // 处理比较表达式
        if (expression.contains("==") || expression.contains("!=") ||
                expression.contains(">") || expression.contains("<") ||
                expression.contains(">=") || expression.contains("<=")) {

            // 提取表达式中的字段
            Set<String> fields = extractFieldsFromExpression(expression);
            for (String field : fields) {
                if (env.containsKey(field)) {
                    Object value = env.get(field);
                    log.debug("trace AviatorHelper 找到可能导致失败的字段: tenantId={}, sourceObjectApiName={}, dataId={}, field={}, value={}, expression={}",
                            tenantId, sourceObjectApiName, dataId, field, value, expression);
                }
            }
        }

        // 处理string.contains函数
        if (expression.contains("string.contains")) {
            Pattern pattern = Pattern.compile("string\\.contains\\(([^,]+),\\s*\"([^\"]*)\"\\)");
            Matcher matcher = pattern.matcher(expression);
            if (matcher.find()) {
                String fieldName = matcher.group(1).trim();
                String searchText = matcher.group(2);

                if (env.containsKey(fieldName)) {
                    Object fieldValue = env.get(fieldName);
                    if (fieldValue != null) {
                        String strValue = fieldValue.toString();
                        if (!strValue.contains(searchText)) {
                            log.debug("trace AviatorHelper 找到导致string.contains失败的字段: tenantId={}, sourceObjectApiName={}, dataId={}, field={}, value='{}', searchText='{}', expression={}",
                                    tenantId, sourceObjectApiName, dataId, fieldName, strValue, searchText, expression);
                        }
                    } else {
                        log.debug("trace AviatorHelper 找到导致string.contains失败的字段: tenantId={}, sourceObjectApiName={}, dataId={}, field={}, value=null, searchText='{}', expression={}",
                                tenantId, sourceObjectApiName, dataId, fieldName, searchText, expression);
                    }
                }
            }
        }

        // 处理nil检查
        if (expression.contains("nil")) {
            Pattern pattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)?)\\s*(!?=)\\s*nil");
            Matcher matcher = pattern.matcher(expression);
            if (matcher.find()) {
                String fieldName = matcher.group(1);
                String operator = matcher.group(2);

                if (env.containsKey(fieldName)) {
                    Object value = env.get(fieldName);
                    boolean isNull = value == null;
                    boolean shouldBeNull = "=".equals(operator);

                    if (isNull != shouldBeNull) {
                        log.debug("trace AviatorHelper 找到导致nil检查失败的字段: tenantId={}, sourceObjectApiName={}, dataId={}, field={}, value={}, shouldBeNull={}, expression={}",
                                tenantId, sourceObjectApiName, dataId, fieldName, value, shouldBeNull, expression);
                    }
                }
            }
        }
    }

    /**
     * 智能分割逻辑表达式，处理嵌套括号
     */
    private List<String> splitLogicalExpression(String expression, String operator) {
        List<String> result = new ArrayList<>();

        // 去除最外层的括号
        expression = removeOuterBrackets(expression);

        int start = 0;
        int bracketCount = 0;
        boolean inQuotes = false;
        char quoteChar = 0;

        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);

            // 处理引号
            if ((c == '"' || c == '\'') && (i == 0 || expression.charAt(i - 1) != '\\')) {
                if (!inQuotes) {
                    inQuotes = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    inQuotes = false;
                }
            }

            // 在引号内的内容不处理
            if (inQuotes) {
                continue;
            }

            if (c == '(') {
                bracketCount++;
            } else if (c == ')') {
                bracketCount--;
            } else if (bracketCount == 0) {
                // 检查是否匹配操作符
                if (i + 1 < expression.length() &&
                        (operator.equals("&&") && expression.substring(i, i + 2).equals("&&") ||
                                operator.equals("\\|\\|") && expression.substring(i, i + 2).equals("||"))) {

                    String part = expression.substring(start, i).trim();
                    if (!part.isEmpty()) {
                        result.add(removeOuterBrackets(part));
                    }
                    start = i + 2;
                    i++; // 跳过操作符的第二个字符
                }
            }
        }

        // 添加最后一部分
        String lastPart = expression.substring(start).trim();
        if (!lastPart.isEmpty()) {
            result.add(removeOuterBrackets(lastPart));
        }

        return result;
    }

    /**
     * 去除表达式最外层的括号
     */
    private String removeOuterBrackets(String expression) {
        expression = expression.trim();

        while (expression.startsWith("(") && expression.endsWith(")")) {
            // 检查这对括号是否是匹配的外层括号
            int bracketCount = 0;
            boolean isOuterBracket = true;

            for (int i = 0; i < expression.length() - 1; i++) {
                char c = expression.charAt(i);
                if (c == '(') {
                    bracketCount++;
                } else if (c == ')') {
                    bracketCount--;
                }

                // 如果括号计数在中间某处变为0，则这不是外层括号
                if (bracketCount == 0 && i < expression.length() - 1) {
                    isOuterBracket = false;
                    break;
                }
            }

            if (isOuterBracket) {
                expression = expression.substring(1, expression.length() - 1).trim();
            } else {
                break;
            }
        }

        return expression;
    }

    /**
     * 从环境变量中提取数据ID
     */
    private String extractDataId(Map<String, Object> env) {
        if (env.containsKey("_id")) {
            return String.valueOf(env.get("_id"));
        } else if (env.containsKey("erp_id")) {
            return String.valueOf(env.get("erp_id"));
        } else if (env.containsKey("erp_num")) {
            return String.valueOf(env.get("erp_num"));
        } else if (env.containsKey("id")) {
            return String.valueOf(env.get("id"));
        }
        return "unknown";
    }

    /**
     * 从表达式中提取字段名
     */
    private Set<String> extractFieldsFromExpression(String expression) {
        Set<String> fields = new HashSet<>();

        // 提取普通字段和嵌套字段
        Pattern fieldPattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)?)");
        Matcher fieldMatcher = fieldPattern.matcher(expression);
        while (fieldMatcher.find()) {
            String field = fieldMatcher.group(1);
            if (!isKeyword(field) && !field.startsWith("string.") && !field.startsWith("math.")) {
                fields.add(field);
            }
        }

        return fields;
    }

    /**
     * 判断是否为关键字
     */
    private boolean isKeyword(String word) {
        Set<String> keywords = new HashSet<>(Arrays.asList(
                "true", "false", "nil", "null", "and", "or", "not", "if", "else", "end",
                "string", "math", "seq", "map", "lambda", "fn", "return", "let", "def"
        ));
        return keywords.contains(word.toLowerCase());
    }
}
