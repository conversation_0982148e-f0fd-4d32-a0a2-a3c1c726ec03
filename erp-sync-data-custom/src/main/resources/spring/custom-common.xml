<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>


    <dubbo:application name="erp-sync-data-customer"/>
    <dubbo:registry address="${dubbo.registry.address}"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!-- 蜂眼监控 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
    <aop:config>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:pointcut id="pointCutAround"
                          expression="execution(* com.fxiaoke.open.erpsyncdata.*.service.*.*(..))
                          or execution(* com.fxiaoke.open.erpsyncdata.*.manager.*.*(..))"/>
            <aop:around method="profile" pointcut-ref="pointCutAround"/>
        </aop:aspect>
    </aop:config>

    <bean id="accessLog" class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.LogInterceptor">
    </bean>
    <aop:config>
        <aop:aspect id="accessLogMonitor" ref="accessLog" order="0">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.dao..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.manager..*.*(..)))
                            or execution(* com.fxiaoke.open.oasyncdata.impl..*.*(..)))" method="around"/>
        </aop:aspect>
    </aop:config>


    <!--统一异常捕捉 erp-sync-data-->
    <bean id="erpApiExceptionInterceptor"
          class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.ApiExceptionInterceptor"/>
    <aop:config>
        <aop:aspect id="apiExceptionTransfer" ref="erpApiExceptionInterceptor" order="2">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                or execution(* com.fxiaoke.open.oasyncdata.impl..*.*(..))" method="around"/>
        </aop:aspect>
    </aop:config>


    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:syncdata-custom-applicationContext.properties"
          p:configName="erp-sync-data-all"/>

    <!-- 参数校验 -->
    <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean"/>
    <bean class="org.springframework.validation.beanvalidation.MethodValidationPostProcessor"/>

    <bean id="overrideOuterService" class="com.fxiaoke.open.erpsyncdata.preprocess.impl.ErpOverrideOuterServiceImpl"/>

    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="18000"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

<!--    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.custom.job"/>-->
<!--    &lt;!&ndash; 配置02、执行器 &ndash;&gt;-->
<!--    <bean id="xxlJobExecutor" class="com.xxl.job.core.executor.XxlJobExecutor" init-method="start"-->
<!--          destroy-method="destroy">-->
<!--        &lt;!&ndash; 执行器IP[选填]，为空则自动获取 &ndash;&gt;-->
<!--        <property name="ip" value="${xxl.job.executor.ip}"/>-->
<!--        &lt;!&ndash; 执行器端口号[必须] &ndash;&gt;-->
<!--        <property name="port" value="${xxl.job.executor.port}"/>-->
<!--        &lt;!&ndash; 执行器AppName[选填]，为空则关闭自动注册 &ndash;&gt;-->
<!--        <property name="appName" value="${xxl.job.app.name}"/>-->
<!--        &lt;!&ndash; 执行器注册中心地址[选填]，为空则关闭自动注册 &ndash;&gt;-->
<!--        <property name="adminAddresses" value="${xxl.job.admin.addresses}"/>-->
<!--        &lt;!&ndash; 执行器日志路径[必填] &ndash;&gt;-->
<!--        <property name="logPath" value="${xxl.job.executor.logpath}"/>-->
<!--        &lt;!&ndash; 访问令牌，非空则进行匹配校验[选填] &ndash;&gt;-->
<!--        <property name="accessToken" value="${xxl.job.accessToken}"/>-->
<!--    </bean>-->
</beans>