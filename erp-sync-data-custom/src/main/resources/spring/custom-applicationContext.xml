<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <import resource="custom-common.xml"/>
    <import resource="custom-dubbo-consumer.xml"/>
    <import resource="erp-sync-data-custom-rest-client.xml"/>
    <import resource="custom-mq-producer.xml"/>
    <import resource="custom-fileserver.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/syncmain-context.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/admin-context.xml"/>
    <import resource="classpath*:spring/erp-preprocess-data.xml"/>
    <import resource="classpath*:spring/erp-apiproxy-data.xml"/>
    <import resource="classpath*:spring/dispatcher-mongo.xml"/>
    <import resource="classpath*:spring/common-spring.xml"/>
    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>
</beans>