<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/fs-app.log</File>
        <!-- 可让每天产生一个日志文件，最多 15 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/fs-app-%d{yyyyMMdd}.log.zip</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} [%class:%line] %msg%rEx{
                full,
                }%n
            </pattern>
        </encoder>
    </appender>

    <!-- 异步输出日志避免阻塞服务 -->
    <appender name="ASYNC_ROLLING" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="RollingFile"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!--记录warn级别以上日志-->
    <appender name="ErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File> ${catalina.home}/logs/error.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread]- %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>15</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <!-- 配置基础组件为ERROR级别，避免打印过多影响服务自己日志 -->
    <logger name="org.apache" level="WARN"/>
    <logger name="com.github" level="OFF"/>
    <logger name="Access" level="INFO"/>

    <logger level="INFO" name="com.fxiaoke.open.erpsyncdata" additivity="true"/>

    <root level="INFO">
        <appender-ref ref="ASYNC_ROLLING"/>
        <appender-ref ref="ErrorLog"/>
    </root>

</configuration>