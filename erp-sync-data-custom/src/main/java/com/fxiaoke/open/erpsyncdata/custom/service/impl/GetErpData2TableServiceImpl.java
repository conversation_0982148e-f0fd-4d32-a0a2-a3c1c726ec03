package com.fxiaoke.open.erpsyncdata.custom.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.custom.arg.GetErpData2TableArg;
import com.fxiaoke.open.erpsyncdata.custom.service.ErpDataInterfaceService;
import com.fxiaoke.open.erpsyncdata.custom.service.GetErpData2TableService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 9:55 2021/2/5
 * @Desc:
 */
@Slf4j
@Service
public class GetErpData2TableServiceImpl implements GetErpData2TableService {
    @Autowired
    private ErpDataInterfaceService erpDataInterfaceService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;

    @Override
    public Result<String> pushErpData2Table(String tenantId, String userId, GetErpData2TableArg arg) {
        String erpFakeObjApiName=arg.getErpDataFilter().getErpFakeObjApiName();
        String dataCenterId=idFieldConvertManager.getDataCenterId(tenantId,erpFakeObjApiName);
        if (arg == null || arg.getErpDataFilter() == null || StringUtils.isBlank(erpFakeObjApiName)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId,dataCenterId);
        if (connectInfo == null) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        String realApiName = idFieldConvertManager.getRealObjApiName(tenantId, erpFakeObjApiName);
        if (realApiName == null) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        if (arg.getErpDataFilter() != null && CollectionUtils.isNotEmpty(arg.getErpDataFilter().getErpField2Values())) {//指定erp筛选字段和值列表
            GetErpData2TableArg.ErpDataFilter erpDataFilter = arg.getErpDataFilter();
            if (CollectionUtils.isNotEmpty(arg.getErpDataFilter().getErpField2Values())) {
                TimeFilterArg timeFilterArg = new TimeFilterArg();
                timeFilterArg.setTenantId(tenantId);
                timeFilterArg.setObjAPIName(realApiName);
                timeFilterArg.setOperationType(1);
                timeFilterArg.setStartTime(erpDataFilter.getStartTime() == null ? System.currentTimeMillis() : erpDataFilter.getStartTime());
                timeFilterArg.setEndTime(erpDataFilter.getEndTime() == null ? System.currentTimeMillis() : erpDataFilter.getEndTime());
                timeFilterArg.setIncludeDetail(true);
                for (Map<String, String> kv : arg.getErpDataFilter().getErpField2Values()) {
                    List<FilterData> filterDataList = Lists.newArrayList();
                    for (String key : kv.keySet()) {
                        FilterData filterData = new FilterData();
                        filterData.setFieldApiName(key);
                        filterData.setOperate("EQ");
                        filterData.setFieldValue(Lists.newArrayList(kv.get(key)));
                        filterDataList.add(filterData);
                    }
                    List<List<FilterData>> filterList = Lists.newArrayList();
                    filterList.add(filterDataList);
                    timeFilterArg.setFilters(filterList);
                    Result<List<StandardData>> erpDataResult = erpDataInterfaceService.getAllErpObjectData(timeFilterArg, connectInfo);
                    if (erpDataResult == null || !erpDataResult.isSuccess()) {
                        log.info("erpDataInterfaceService.getAllErpObjectData failed timeFilterArg={},connectInfo={}", timeFilterArg, connectInfo);
                        continue;
                    }
                    if (CollectionUtils.isEmpty(erpDataResult.getData())) {
                        continue;
                    }
                    List<StandardData> erpDataList = erpDataResult.getData();
                    for(StandardData standardData:erpDataList){
                        standardData.getMasterFieldVal().put("virtualUserId",Lists.newArrayList(userId));
                    }
                    setStandardDataIdField(tenantId,erpFakeObjApiName,erpDataList);
                    if(CollectionUtils.isNotEmpty(erpDataList)){
                        erpObjDataPushManager.pushErpData2Table(tenantId,realApiName,erpFakeObjApiName,"1",erpDataList,null,dataCenterId);
                    }
                    //删除创建时间是30天前且更新时间是15天前的erpData
                    erpObjDataPushManager.deleteErpDataByCreateAndUpdateTime(tenantId,realApiName,
                            System.currentTimeMillis()-1000*3600*24*30,System.currentTimeMillis()-1000*3600*24*15);
                }
            }
        } else if (arg.getCrmDataFilter() != null && arg.getCrmDataFilter().getErpField2CrmField() != null) {//指定crm对象范围和取crm字段值为筛选列表

            //todo
        } else {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        return Result.newSuccess();
    }


    private void setStandardDataIdField(String tenantId, String erpFakeObjApiName, List<StandardData> erpDataList) {
        ErpObjectFieldEntity idField = idFieldConvertManager.getIdField(tenantId, erpFakeObjApiName);
        if(idField==null){//如果没有id字段，清空列表
            log.warn("setStandardDataIdField tenantId={},erpFakeObjApiName={} idField is null",tenantId,erpFakeObjApiName);
            erpDataList.clear();
            return;
        }
        for(StandardData standardData:erpDataList){
            if(standardData.getMasterFieldVal().get(idField.getFieldApiName())==null){
                log.warn("setStandardDataIdField standardData not contain idField tenantId={},idField={},data={}",tenantId,idField,standardData);
                continue;
            }
            standardData.getMasterFieldVal().put("id",standardData.getMasterFieldVal().get(idField.getFieldApiName()));
        }
    }
}
