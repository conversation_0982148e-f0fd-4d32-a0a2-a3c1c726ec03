package com.fxiaoke.open.erpsyncdata.custom.service;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:28 2021/2/4
 * @Desc:
 */
public interface CrmDataInterfaceService {
    Result<List<ObjectData>> getAllCrmObjectData(String tenantId, String objApiName, SearchTemplateQuery searchTemplateQuery);
    Result<List<ObjectData>> getLimitCrmObjectData(String tenantId, String objApiName, SearchTemplateQuery searchTemplateQuery);
}
