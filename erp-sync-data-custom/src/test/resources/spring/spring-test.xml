<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <import resource="classpath:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/syncmain-context.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/admin-context.xml"/>
    <import resource="classpath*:spring/erp-preprocess-data.xml"/>
    <import resource="classpath*:spring/erp-apiproxy-data.xml"/>
<!--    <import resource="classpath*:spring/datacenter-applicationContext.xml"/>-->
    <import resource="classpath*:spring/custom-common.xml"/>
    <import resource="classpath*:spring/custom-mq-test.xml"/>
    <import resource="classpath*:spring/custom-fileserver.xml"/>
    <import resource="classpath*:spring/custom-dubbo-consumer.xml"/>
<!--    <import resource="classpath*:spring/oa-sync-data.xml"/>-->
    <import resource="classpath*:spring/spring-test-fara.xml" />


</beans>