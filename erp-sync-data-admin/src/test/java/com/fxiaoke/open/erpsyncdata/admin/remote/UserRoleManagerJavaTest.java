package com.fxiaoke.open.erpsyncdata.admin.remote;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UserRoleManagerJavaTest {

    @InjectMocks
    private UserRoleManager userRoleManager;

    private MockedStatic<OkHttpUtils> mockedOkHttpUtils;

    @BeforeEach
    public void setUp() {
        mockedOkHttpUtils = Mockito.mockStatic(OkHttpUtils.class);
    }

    @AfterEach
    void tearDown() {
        if (mockedOkHttpUtils != null) {
            mockedOkHttpUtils.close(); // 显式关闭静态 mock
        }
    }

    @Test
    void testCheckAdminAuth_WithCrmManagerRole() throws IOException {
        // 准备测试数据
        String tenantId = "89306";
        String employeeId = "1000";
        Set<String> roles = new HashSet<>(Collections.singletonList("31")); // CRM管理员角色

        // Mock HTTP响应
        HttpResponse response = new HttpResponse();
        response.setCode(200);
        JSONObject result = new JSONObject();
        result.put("errCode", 0);
        result.put("result", roles);
        response.setBody(result.toString());

        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(any(), any(), any()))
                .thenReturn(response);

        // 执行测试
        boolean isAdmin = userRoleManager.checkAdminAuth(tenantId, employeeId);

        // 验证结果
        assertTrue(isAdmin);
    }

    @Test
    void testHasFuncPermission() {
        String tenantId = "89306";
        Integer userId = 1000;
        Set<String> funcCodes = Sets.newHashSet("FUNC_CODE_1", "FUNC_CODE_2");

        // Mock checkAdminAuth 返回false
        HttpResponse roleResponse = new HttpResponse();
        roleResponse.setCode(200);
        JSONObject roleResult = new JSONObject();
        roleResult.put("errCode", 0);
        roleResult.put("result", new HashSet<>());
        roleResponse.setBody(roleResult.toString());

        // Mock funcPermissionCheck 返回结果
        HttpResponse permissionResponse = new HttpResponse();
        permissionResponse.setCode(200);
        JSONObject permissionResult = new JSONObject();
        Map<String, Boolean> permissions = new HashMap<>();
        permissions.put("FUNC_CODE_1", true);
        permissions.put("FUNC_CODE_2", false);
        permissionResult.put("errCode", 0);
        permissionResult.put("result", permissions);
        permissionResponse.setBody(permissionResult.toString());

        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(any(), any(), any()))
                .thenReturn(roleResponse)
                .thenReturn(permissionResponse);

        // 执行测试
        boolean hasPermission = userRoleManager.hasFuncPermission(tenantId, userId, funcCodes);

        // 验证结果
        assertTrue(hasPermission);
    }

    @Test
    void testGetLackRoles() {
        String tenantId = "89306";
        List<String> roles = Arrays.asList("ROLE_1", "ROLE_2", "ROLE_3");

        // Mock HTTP响应
        HttpResponse response = new HttpResponse();
        response.setCode(200);
        JSONObject result = new JSONObject();
        List<UserRoleManager.RolePojo> existingRoles = Arrays.asList(
                createRolePojo("ROLE_1", "角色1"),
                createRolePojo("ROLE_2", "角色2")
        );
        result.put("errCode", 0);
        result.put("result", existingRoles);
        response.setBody(result.toString());

        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(any(), any(), any()))
                .thenReturn(response);

        // 执行测试
        List<String> lackRoles = userRoleManager.getLackRoles(tenantId, roles);

        // 验证结果
        assertEquals(1, lackRoles.size());
        assertTrue(lackRoles.contains("ROLE_3"));
    }

    @Test
    void testQueryRoleNameByRoleCode() {
        String tenantId = "89306";
        List<String> roles = Arrays.asList("ROLE_1", "ROLE_2");

        // Mock HTTP响应
        HttpResponse response = new HttpResponse();
        response.setCode(200);
        JSONObject result = new JSONObject();
        List<UserRoleManager.RolePojo> rolePojos = Arrays.asList(
                createRolePojo("ROLE_1", "角色1"),
                createRolePojo("ROLE_2", "角色2")
        );
        result.put("errCode", 0);
        result.put("result", rolePojos);
        response.setBody(result.toString());

        mockedOkHttpUtils.when(() -> OkHttpUtils.postNonblocking(any(), any(), any()))
                .thenReturn(response);

        // 执行测试
        Map<String, String> roleNameMap = userRoleManager.queryRoleNameByRoleCode(tenantId, roles);

        // 验证结果
        assertEquals(2, roleNameMap.size());
        assertEquals("角色1", roleNameMap.get("ROLE_1"));
        assertEquals("角色2", roleNameMap.get("ROLE_2"));
    }

    private UserRoleManager.RolePojo createRolePojo(String roleCode, String roleName) {
        UserRoleManager.RolePojo rolePojo = new UserRoleManager.RolePojo();
        rolePojo.setRoleCode(roleCode);
        rolePojo.setRoleName(roleName);
        return rolePojo;
    }
} 