<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">


<!--    &lt;!&ndash;配置中心 &ndash;&gt;-->
<!--    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"-->
<!--          c:placeholderConfigurer-ref="autoConf"/>-->
<!--    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"-->
<!--          p:fileEncoding="UTF-8"-->
<!--          p:ignoreResourceNotFound="true"-->
<!--          p:ignoreUnresolvablePlaceholders="false"-->
<!--          p:configName="erp-sync-data-all"/>-->


<!--    <dubbo:application name="erp-sync-data-web"/>-->
<!--    <dubbo:registry address="${dubbo.registry.address}"/>-->
<!--    <dubbo:protocol port="20881"/>-->

<!--    <dubbo:consumer timeout="30000" filter="tracerpc" retries="0"/>-->
<!--    <import resource="classpath:spring/ei-ea-converter.xml"/>-->


    <!--  智能客服推荐解决方案  -->
    <bean id="eserviceRetrofitFactory" class="com.github.zhxing.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory" p:configNames="fs-eservice-rest" init-method="init"/>
    <bean class="com.github.zhxing.retrofitspring.RetrofitSpringFactoryBean"
          p:type="com.facishare.eservice.rest.online.service.KnowledgeService" p:factory-ref="eserviceRetrofitFactory"/>

    <bean class="com.fxiaoke.open.erpsyncdata.admin.remote.KnowledgeManager"/>
    <bean class="com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter"/>
</beans>