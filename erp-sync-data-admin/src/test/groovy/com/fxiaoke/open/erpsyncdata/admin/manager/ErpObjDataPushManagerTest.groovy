package com.fxiaoke.open.erpsyncdata.admin.manager

import com.fasterxml.jackson.core.type.TypeReference
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService
import spock.lang.Specification

class ErpObjDataPushManagerTest extends Specification {
    String snapt = "{\"id\":\"74bad5c24c8a465c925a388c1673c4e4\",\"sourceTenantId\":\"88521\",\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destTenantId\":\"88521\",\"destObjectApiName\":\"SalesOrderObj\",\"status\":1,\"syncPloyId\":\"88521\",\"syncPloyDetailId\":\"9a9fad94e9d243fc94e8e7b178e55d76\",\"syncPloyDetailData\":{\"id\":\"9a9fad94e9d243fc94e8e7b178e55d76\",\"syncPloyId\":\"88521\",\"status\":2,\"sourceTenantType\":2,\"destTenantType\":1,\"sourceTenantIds\":[\"88521\"],\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destTenantIds\":[\"88521\"],\"destObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"sourceApiName\":\"FDate\",\"sourceType\":\"date\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"order_time\",\"destType\":\"date\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":false},{\"sourceApiName\":\"FCustId.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_Customer.BillHead\",\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"account_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"AccountObj\",\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":false},{\"sourceApiName\":\"FSalerId.FNumber\",\"sourceType\":\"employee\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"owner\",\"destType\":\"employee\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":2,\"function\":null,\"value\":\"FNumber\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":false},{\"sourceApiName\":\"FSaleOrderFinance.FBillAllAmount\",\"sourceType\":\"currency\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"order_amount\",\"destType\":\"currency\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":false},{\"sourceApiName\":\"FNote\",\"sourceType\":\"long_text\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"remark\",\"destType\":\"long_text\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":false}],\"detailObjectMappings\":[{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"sourceApiName\":\"fake_master_detail\",\"sourceType\":\"master_detail\",\"sourceTargetApiName\":\"SAL_SaleOrder.BillHead\",\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"order_id\",\"destType\":\"master_detail\",\"destTargetApiName\":\"SalesOrderObj\",\"destQuoteFieldType\":null,\"optionMappings\":null,\"mappingType\":1,\"function\":null,\"value\":null,\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FTaxPrice\",\"sourceType\":\"currency\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"sales_price\",\"destType\":\"currency\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FQty\",\"sourceType\":\"number\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"quantity\",\"destType\":\"number\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FAmount\",\"sourceType\":\"currency\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"product_price\",\"destType\":\"currency\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FAllAmount\",\"sourceType\":\"currency\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"subtotal\",\"destType\":\"number\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FEntryNote\",\"sourceType\":\"text\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"remark\",\"destType\":\"long_text\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FUnitID.FNumber\",\"sourceType\":\"select_one\",\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"unit\",\"destType\":\"quote\",\"destTargetApiName\":null,\"destQuoteFieldType\":\"select_one\",\"optionMappings\":[{\"sourceOption\":\"002\",\"destOption\":\"1\"}],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_MATERIAL.BillHead\",\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"field_eh821__c\",\"destType\":\"text\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null},{\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_MATERIAL.BillHead\",\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"product_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"ProductObj\",\"destQuoteFieldType\":null,\"optionMappings\":[],\"mappingType\":1,\"function\":null,\"value\":\"\",\"defaultValue\":null,\"valueType\":null,\"notUpdateField\":null}]}],\"syncRules\":{\"events\":[1,2,3,5],\"syncTypeList\":[\"get\"],\"syncDependForce\":false},\"syncConditions\":{\"apiName\":\"SAL_SaleOrder.BillHead\",\"filters\":[[{\"fieldApiName\":\"FNote\",\"fieldType\":\"long_text\",\"label\":\"备注\",\"operate\":\"LIKE\",\"fieldValue\":[\"同步到crm\"],\"quoteFieldType\":null,\"quoteRealField\":null,\"quoteFieldTargetObjectApiName\":null,\"quoteFieldTargetObjectField\":null,\"type\":null,\"variableType\":null,\"isVariableBetween\":false}]],\"isSyncForce\":false,\"noDetailDataNotSync\":false},\"detailObjectSyncConditions\":[{\"apiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"filters\":[],\"isSyncForce\":true,\"noDetailDataNotSync\":false}],\"integrationStreamNodes\":{\"reverseWriteNode\":null,\"checkSyncDataMappingNode\":null,\"queryCrmObject2DestNodeBySource\":null,\"queryCrmObject2DestNodeByDest\":null,\"syncConditionsQueryDataNode\":{\"queryObjectMappingData\":[],\"syncCondition\":1},\"objApiName2NotUpdateFieldApiName\":{\"SalesOrderProductObj\":[],\"SalesOrderObj\":[\"discount\"]},\"objApiName2NeedReturnFieldApiName\":{},\"reSyncErrorDataNode\":null},\"beforeFuncApiName\":null,\"duringFuncApiName\":null,\"afterFuncApiName\":null,\"sourceDataCenterId\":\"643f7322b54ea80001767d86\",\"destDataCenterId\":\"643f7326b54ea8000176a191\"},\"syncConditionsExpression\":\"((string.contains(FNote,\\\"同步到crm\\\")))\",\"detailObjectSyncConditionsExpressions\":{\"SAL_SaleOrder.SaleOrderEntry\":\"true\"},\"newest\":null}"
    String dataListStr = "[{\"errCode\":0,\"errMsg\":\"成功\",\"sourceEventType\":2,\"sourceData\":{\"erp_id\":\"112956\",\"erp_num\":\"XSDD010778\",\"id\":112956,\"FBillNo\":\"XSDD010778\",\"FDate\":1699545600000,\"FCustId.FNumber\":\"20230308-000006\",\"FSalerId.FNumber\":[\"1001\"],\"FSaleOrderFinance.FBillAllAmount\":25,\"FApproveDate\":null,\"FBillTypeID.Id\":\"eacb50844fc84a10b03d7b841f3a6278\",\"FNote\":\" \",\"mongo_id\":\"654da3bf1cb35b087ae6fd81\",\"object_describe_api_name\":\"SAL_SaleOrder.BillHead\",\"tenant_id\":\"88521\",\"ComId\":\"112956#XSDD010778\",\"_id\":\"112956#XSDD010778\",\"last_modified_by\":[\"-10000\"],\"name\":\"XSDD010778\"},\"detailData\":{\"SAL_SaleOrder.SaleOrderEntry\":[{\"DetailId\":122665,\"FMaterialId.FNumber\":\"CH2145\",\"FUnitID.FNumber\":\"Pcs\",\"FTaxPrice\":5,\"FAllAmount\":25,\"FAllAmount_LC\":25,\"FAmount\":22.12,\"FEntryNote\":\"同步到erp\",\"FQty\":5,\"FAccountBalanceId\":0,\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"_id\":\"122665\",\"tenant_id\":\"88521\",\"last_modified_by\":[\"-10000\"],\"name\":\"122665\",\"fake_master_detail\":\"112956#XSDD010778\"}],\"SAL_SaleOrder.SaleOrderClause\":[],\"SAL_SaleOrder.SaleOrderPlan\":[],\"SAL_SaleOrder.SalOrderTrace\":[]},\"syncLogId\":\"J-E.0.0.VIRTUAL_API_LOG_NAME.846aed28e1a84fba80ddd2c3c033f8ea\",\"dataVersion\":*************,\"skyWalkingApiName\":null,\"dataReceiveType\":1,\"ployDetailSnapshotId\":null,\"sourceTenantType\":null,\"syncDependForce\":null,\"dataVersionList\":null,\"delayDispatcherTime\":null,\"syncDataId\":null,\"tenantId\":null,\"destTenantType\":null,\"destTenantId\":null,\"destObjectApiName\":null,\"updateData\":null,\"isMatchUpdateData\":null,\"syncPloyDetailSnapshotId\":null,\"mainObjApiName\":null,\"streamId\":null,\"masterMappingsData\":null,\"detailObjectDatasMap\":{},\"syncDataData\":null,\"detailSyncDataDataMap\":null,\"sourceTenantId\":null,\"destEventType\":null,\"destDataId\":null,\"destData\":null,\"destDetailSyncDataIdAndDestDataMap\":null,\"destDetailObjMasterDetailFieldApiName\":null,\"stop\":false,\"requestId\":null,\"syncDataMap\":null,\"syncDataEntityStr\":null,\"syncLogBaseInfo\":null,\"objectApiName\":null,\"dataId\":null,\"version\":null,\"syncPloyDetailId\":null,\"writeResult\":null,\"detailWriteResults\":[],\"finish\":false,\"msg\":null,\"doProcess\":false,\"doWrite\":false,\"completeDataProcess\":false,\"reverseWrite2Crm\":false,\"completeDataWrite\":false,\"queryCrmObject2DestNode\":false,\"debugRecordIfDetailCauseMaterSync\":false,\"success\":true}]"
    SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData2 = JacksonUtil.fromJson(snapt, SyncPloyDetailSnapshotData2.class)
    List<SyncDataContextEvent> dataList = JacksonUtil.fromJson(dataListStr, new TypeReference<List<SyncDataContextEvent>>() {
    })
    def eventType;
    Result<List<SyncDataContextEvent>> erpObjDataResult = Result.newSuccess(dataList)
    Result2<ObjectDataSyncMsg> mainResult = Result2.newSuccess()
    Result2<ObjectDataSyncMsg> detailResult = Result2.newSuccess()
    AllModelDubboService allModelDubboService = Mock(AllModelDubboService) {
        syncDataMain(_) >> { args ->
            if (1 == eventType) {
                mainResult.getData().getDataSyncResultList().addAll(detailResult.getData().getDataSyncResultList())
                return mainResult
            } else {
                if ("SAL_SaleOrder.BillHead".equals(((SyncDataContextEvent) args[0]).getSourceData().getApiName())) {
                    return mainResult
                } else {
                    return detailResult
                }
            }

        }
    }
    ErpObjDataPushManager monitor = new ErpObjDataPushManager(
            erpDataPreprocessService: Mock(ErpDataPreprocessService) {
                getReSyncObjDataById(_) >> { return erpObjDataResult }
            },
            allModelDubboServiceRest: allModelDubboService
    )

    def "DirectSyncPushData2Dest"() {
//        Map<String,List<String>> objApiName2NeedReturnFieldApiName= Maps.newHashMap()
//        objApiName2NeedReturnFieldApiName.put(needObj,JacksonUtil.fromJson(needField,List.class))
        syncPloyDetailSnapshotData2.getSyncPloyDetailData().getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName().put(needObj, JacksonUtil.fromJson(needField, List.class))
        ObjectDataSyncMsg objectDataSyncMsg = JacksonUtil.fromJson(masterDataSyncMsgStr, ObjectDataSyncMsg.class)
        ObjectDataSyncMsg detailDataSyncMsg = JacksonUtil.fromJson(detail, ObjectDataSyncMsg.class)
        mainResult.setData(objectDataSyncMsg)
        detailResult.setData(detailDataSyncMsg)
        eventType=operationType

        when:
        Result<StandardData> result = monitor.directSyncPushData2Dest("88521", "", "SAL_SaleOrder", "SAL_SaleOrder.BillHead", "112956#XSDD010778", "SalesOrderObj", operationType, syncPloyDetailSnapshotData2, null)
        then:
        result.isSuccess()
        for (String field : needField) {
            result.getData().getMasterFieldVal().containsKey(field)
        }
        where:
        needObj         | needField  | operationType | masterDataSyncMsgStr                                                                                                                                                                       | detail
        "SalesOrderObj" | "[\"f1\"]" | 1           | "{\"dataSyncResultList\":[{\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destObjectApiName\":\"SalesOrderObj\",\"writeDestSucc\":true,\"needReturnDestObjectData\":{\"f1\":123}}]}" | "{\"dataSyncResultList\":[{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectApiName\":\"SalesOrderProductObj\",\"writeDestSucc\":true,\"needReturnDestObjectData\":{\"f1\":123}}]}"
        "SalesOrderObj" | "[\"f1\"]" | 2           | "{\"dataSyncResultList\":[{\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destObjectApiName\":\"SalesOrderObj\",\"writeDestSucc\":true,\"needReturnDestObjectData\":{\"f1\":123}}]}" | "{\"dataSyncResultList\":[{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectApiName\":\"SalesOrderProductObj\",\"writeDestSucc\":true,\"needReturnDestObjectData\":{\"f1\":123}}]}"

    }
}
