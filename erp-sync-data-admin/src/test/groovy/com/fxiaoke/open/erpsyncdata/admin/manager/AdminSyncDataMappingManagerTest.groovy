package com.fxiaoke.open.erpsyncdata.admin.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.oasyncdata.constant.SyncDataStatusEnum
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class AdminSyncDataMappingManagerTest extends Specification {


    // 模拟 SyncDataMappingsDao 依赖
    def syncDataMappingsDao = Mock(SyncDataMappingsDao)

    // 测试类实例
    def updateMapping2SuccessService = new AdminSyncDataMappingManager(
            syncDataMappingsDao:syncDataMappingsDao
    )

    /**
     * 测试 updateMapping2Success 方法，当没有数据需要处理时
     */
    def "updateMapping2Success 在无数据时返回正确的记录"() {
        given: "设置初始条件，无数据需要处理"
        String tenantId = "tenant1"
        String sourceObjApiName = "SourceAPI"
        String destObjectApiName = "DestAPI"
        boolean onlyCreated = false
        long limit = 10

        // 当调用 listFailedMappingLtTime 方法时返回空列表
        syncDataMappingsDao.listFailedMappingLtTime(_, _, _, _) >> []

        when: "调用 updateMapping2Success 方法"
        def result = updateMapping2SuccessService.updateMapping2Success(tenantId, sourceObjApiName, destObjectApiName, onlyCreated, limit)

        then: "返回的记录总查询和总更新数均为0"
        result.totalQuery == 0
        result.totalUpdate == 0
    }

    /**
     * 测试 updateMapping2Success 方法，处理单批数据
     */
    def "updateMapping2Success 处理单批数据并正确更新记录"() {
        given: "设置初始条件，有一批数据需要处理"
        String tenantId = "tenant1"
        String sourceObjApiName = "SourceAPI"
        String destObjectApiName = "DestAPI"
        boolean onlyCreated = false
        long limit = 10

        // 模拟第一次查询返回一批数据
        def firstBatch = [
                new SyncDataMappingsEntity(id: "1", isCreated: true, updateTime: 1000),
                new SyncDataMappingsEntity(id: "2", isCreated: false, updateTime: 1000)
        ]

        // 第一次调用 listFailedMappingLtTime 返回第一批数据
        syncDataMappingsDao.listFailedMappingLtTime(_, _, _, _) >> firstBatch >>[]
        syncDataMappingsDao.listFailedMappingEqTime(*_) >> []

        // 模拟 batchUpdateByIds 方法无异常
        syncDataMappingsDao.batchUpdateByIds(_, _, _, _, _, _) >> {}

        when: "调用 updateMapping2Success 方法"
        def result = updateMapping2SuccessService.updateMapping2Success(tenantId, sourceObjApiName, destObjectApiName, onlyCreated, limit)

        then: "总查询数为2，总更新数为2"
        result.totalQuery == 2
        result.totalUpdate == 2

        and: "batchUpdateByIds 被正确调用"
        1 * syncDataMappingsDao.batchUpdateByIds(tenantId, ["1", "2"], SyncDataStatusEnum.WRITE_SUCCESS.getStatus(), _, _, _)
    }

    /**
     * 测试 updateMapping2Success 方法，处理多批数据直到达到限制
     */
    def "updateMapping2Success 处理多批数据并在达到限制时停止"() {
        given: "设置初始条件，有多批数据需要处理，但达到限制后停止"
        String tenantId = "tenant1"
        String sourceObjApiName = "SourceAPI"
        String destObjectApiName = "DestAPI"
        boolean onlyCreated = true
        long limit = 4

        // 模拟第1次查询返回1条数据
        def firstBatch = [
                new SyncDataMappingsEntity(id: "1", isCreated: true, updateTime: 1000)
        ]

        // 模拟第2、3次查询返回2条数据
        def secondBatch = [
                new SyncDataMappingsEntity(id: "2", isCreated: false, updateTime: 1000),
                new SyncDataMappingsEntity(id: "3", isCreated: true, updateTime: 1000)
        ]

        // 第一次调用 listFailedMappingLtTime 返回第一批数据
        syncDataMappingsDao.listFailedMappingLtTime(_, _, _, _) >>> [firstBatch, secondBatch]
        syncDataMappingsDao.listFailedMappingEqTime(*_) >> []

        // 模拟 batchUpdateByIds 方法无异常
        syncDataMappingsDao.batchUpdateByIds(_, _, _, _, _, _) >> {}

        when: "调用 updateMapping2Success 方法"
        def result = updateMapping2SuccessService.updateMapping2Success(tenantId, sourceObjApiName, destObjectApiName, onlyCreated, limit)

        then: "总查询数为5，总更新数为3"
        result.totalQuery == 5
        result.totalUpdate == 3

        and: "batchUpdateByIds 被正确调用两次"
        3 * syncDataMappingsDao.batchUpdateByIds(_, _, _, _, _, _)
    }
}
