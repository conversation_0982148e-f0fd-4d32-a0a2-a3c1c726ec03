package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.alibaba.fastjson2.JSON
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CrmSpecialFieldExcelVo
import com.fxiaoke.open.erpsyncdata.admin.remote.EgressApiManager
import com.fxiaoke.open.erpsyncdata.admin.service.impl.matchingdistrict.MatchingDistrictService
import com.fxiaoke.open.erpsyncdata.admin.service.impl.matchingdistrict.MatchingDistrictServiceHelp
import com.fxiaoke.open.erpsyncdata.admin.service.impl.matchingdistrict.RegexMatchingService
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.tuple.Triple
import spock.lang.Specification
import spock.lang.Unroll

import java.util.stream.Collectors

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ExcelSystemFieldTypeEnum.*

/**
 * <AUTHOR> 
 * @date 2023/6/19 19:10:36
 */
class SystemFieldServiceImplTest extends Specification {

    @Unroll
    def "解析地址"() {
        when:
        def p = RegexMatchingService.DistrictFunctionEnum.getFunction(districtEnum).apply(key);

        then:
        p == result

        where:
        key                                            | districtEnum || result
        // 省
        "广东省"                                       | province     || "广东"
        "上海市"                                       | province     || "上海"
        "香港"                                         | province     || "香港"

        "中国广西省"                                   | province     || "广西"
        "中华人民共和国广西省"                         | province     || "广西"
        "中国内蒙古自治区"                             | province     || "内蒙古"
        "中国新疆维吾尔自治区"                         | province     || "新疆维吾尔"

        // 市
        "上海市"                                       | city         || "上海"
        "香港特别行政区"                               | city         || "香港"

        "上海市上海市"                                 | city         || "上海"
        "海南省五指山市"                               | city         || "五指山"
        "新疆维吾尔自治区博尔塔拉蒙古自治州"           | city         || "博尔塔拉蒙古"
        "甘肃省临夏回族自治州"                         | city         || "临夏回族"
        "黑龙江省大兴安岭地区"                         | city         || "大兴安岭"

        "中国香港香港特别行政区"                       | city         || "香港"

        // 区
        "南山区"                                       | county       || "南山"

        "深圳市南山区"                                 | county       || "南山"
        "上海市黄埔区"                                 | county       || "黄埔"

        "西藏自治区拉萨市当雄县"                       | county       || "当雄"
        "青海省玉树藏族自治州王树市"                   | county       || "王树"
        "上海市上海市黄埔区"                           | county       || "黄埔"
        "海南省儋州市国营八一总场"                     | county       || "国营八一总"
        "海南省信州市国营蓝洋农场"                     | county       || "国营蓝洋农"
        "海南省五指山市常好乡xxx地址"                  | county       || "常好"
        "内蒙古自治区锡林郭勒盟西乌珠穆沁旗xx地址"     | county       || "西乌珠穆沁"

        // 街道/乡镇
        "xxx街道"                                      | town         || "xxx"

        "黄埔区xxx街道"                                | town         || "xxx"
        "王树市xxx村"                                  | town         || "xxx"

        "拉萨市当雄县xxx村"                            | town         || "xxx"

        "上海市上海市黄埔区xxx街道"                    | town         || "xxx"
        "河南省郑州市中原区xxx村"                      | town         || "xxx"
        "西藏自治区拉萨市当雄县xxx村"                  | town         || "xxx"
        "中国香港香港特别行政区中西区xxx街道"          | town         || "xxx"
        "宁夏回族自治区银川市临武市xxx村"              | town         || "xxx"
        "青海省玉树藏族自治州王树市xxx村"              | town         || "xxx"
        "海南省三沙市西沙群岛xxx村"                    | town         || "xxx"
        "海南省琼海市嘉积镇xxx村"                      | town         || "xxx"
        "海南省安定县定城镇xxx村"                      | town         || "xxx"
        "甘肃省临夏回族自治州临夏市xxx村"              | town         || "xxx"
        "新疆维吾尔自治区博尔塔拉蒙古自治州精河县xx村" | town         || "xx"
        "黑龙江省大兴安岭地区呼玛县xx村"               | town         || "xx"
    }

//    @Unroll
//    def "匹配地名-#id"() {
//        when:
//        new MatchingDistrictServiceHelp().setGaoDeMapManager(Mock(EgressApiManager) {
//            getDistrict(*_) >> gaode
//        })
//        def collect = crmData.stream().collect(Collectors.toMap({ it }, { it }));
//        final List<MatchingDistrictService> matchingDistrictServices = MatchingDistrictServiceHelp.getMatchingDistrictServices(districtEnum, collect);
//
//        def matchData = matchingDistrictServices.stream()
//                .map({ service -> service.matchingWithRemoveCrmData(erpDataName, collect) })
//                .filter({ it != null })
//                .findFirst()
//                .orElse(Triple.of(null, null, "未匹配"));
//        then:
//        matchData.getLeft() == crmLabel
//        matchData.getRight() == result
//
//        where:
//        // id | 高德返回值 | 查询类型 | paas行政区数据 | 匹配的erp地名 || 匹配的crm地名 | 匹配结果
//        id           | gaode    | districtEnum | crmData              | erpDataName    || crmLabel           | result
//        "匹配不到"   | "中国"   | country      | ["美国", "德国"]     | "中国"         || null               | "未匹配"
//        "匹配不到2"  | null     | city         | ["深圳市"]           | "上海市"       || null               | "未匹配"
//
//        "国全匹配"   | _        | country      | ["中国"]             | "中国"         || "中国"             | "完全匹配"
//        "省全匹配"   | _        | province     | ["广东省"]           | "广东省"       || "广东省"           | "完全匹配"
//        "市全匹配"   | _        | city         | ["上海市"]           | "上海市"       || "上海市"           | "完全匹配"
//        "区全匹配"   | _        | county       | ["南山区"]           | "南山区"       || "南山区"           | "完全匹配"
//        "镇全匹配"   | _        | town         | ["xxx镇"]            | "xxx镇"        || "xxx镇"            | "完全匹配"
//
//        "正则匹配"   | _        | province     | ["广东省", "广西省"] | "中国广东"     || "广东省"           | "部分匹配"
//        "正则匹配2"  | _        | province     | ["中国广东"]         | "广东省"       || "中国广东"         | "部分匹配"
//        "正则匹配3"  | _        | province     | ["中国广东"]         | "广东"         || "中国广东"         | "部分匹配"
//        "正则匹配4"  | _        | province     | ["广东省", "广西省"] | "广东"         || "广东省"           | "部分匹配"
//
//        "正则匹配5"  | _        | city         | ["广东省深圳市"]     | "广东省深圳"   || "广东省深圳市"     | "部分匹配"
//        "正则匹配6"  | _        | city         | ["广东省深圳"]       | "深圳市"       || "广东省深圳"       | "部分匹配"
//        "正则匹配7"  | _        | city         | ["广东省深圳市"]     | "深圳"         || "广东省深圳市"     | "部分匹配"
//        "正则匹配8"  | _        | city         | ["中国广东省深圳市"] | "深圳"         || "中国广东省深圳市" | "部分匹配"
//        "正则匹配9"  | _        | city         | ["深圳"]             | "广东省深圳市" || "深圳"             | "部分匹配"
//
//        "正则匹配10" | _        | county       | ["南山"]             | "深圳市南山区" || "南山"             | "部分匹配"
//        "正则匹配11" | _        | county       | ["广东省深圳市南山"] | "深圳市南山区" || "广东省深圳市南山" | "部分匹配"
//        "正则匹配12" | _        | county       | ["广东省深圳市南山"] | "南山区"       || "广东省深圳市南山" | "部分匹配"
//        "正则匹配13" | _        | county       | ["南山区"]           | "南山"         || "南山区"           | "部分匹配"
//
//        "正则匹配14" | _        | town         | ["撒旦法镇"]         | "撒旦法村"     || "撒旦法镇"         | "部分匹配"
//        "正则匹配15" | _        | town         | ["撒旦法村"]         | "撒旦法镇"     || "撒旦法村"         | "部分匹配"
//
//        "高德匹配"   | "广东省" | province     | ["广东省", "广西省"] | "中国广东123"  || "广东省"           | "部分匹配"
//    }
//
//    def "测试生成excel文件"() {
//        when:
//        new MatchingDistrictServiceHelp().setGaoDeMapManager(Mock(EgressApiManager) {
//            getDistrict(*_) >> { args -> args[0] }
//        })
//        def fieldService = new SystemFieldServiceImpl()
//        def vos = JSON.parseArray(JSON.toJSONString([["type": "市", "label": "深圳", "code": "a"], ["type": "市", "label": "广州市", "code": "b"], ["type": "市", "label": "茂名", "code": "c"], ["type": "市", "label": "广东省汕头市", "code": "d"], ["type": "市", "label": "没有", "code": "e"], ["type": "市", "label": "重名", "code": "f"], ["type": "市", "label": "重名", "code": "g"]]), CrmSpecialFieldExcelVo.class)
//        def erpData = [["1", "广东省深圳市"], ["2", "广州市"], ["3", "茂名"], ["4", "汕头市"], ["5", "对不上"], ["6", "重名"], ["7", "重名"], ["8", "重名"]]
//        def arg = fieldService.getDistrictExcelSheetArg(tenantId, city, vos, erpData, lang)
//
//        def manager = new DBFileManager()
//        def stream = manager.createExcelStream(tenantId, lang, "测试.xlsx", [arg])
//
//        then:
//        FileUtils.writeByteArrayToFile(new File("/Users/<USER>/workspaces/java/fs-erp-sync-data-git/erp-sync-data-admin/src/test/resources/测试.xlsx"), stream.toByteArray())
//    }
}
