package com.fxiaoke.open.erpsyncdata.admin.manager

import com.alibaba.fastjson.JSON
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import com.fxiaoke.open.erpsyncdata.admin.arg.UpdateIntegrationStreamArg
import com.fxiaoke.open.erpsyncdata.admin.data.DataCenterData
import com.fxiaoke.open.erpsyncdata.admin.result.*
import com.fxiaoke.open.erpsyncdata.admin.service.*
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.*
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/10/24 16:37:24
 */
class IntegrationStreamManagerTest extends Specification {
    ObjectDescribeService objectDescribeService = Mock()
    ProxyHttpClient proxyHttpClient = Mock()
    FsCrmObjectService fsCrmObjectService = Mock()
    ErpObjectFieldsService erpObjectFieldsService = Mock()
    ErpObjectService erpObjectService = Mock()
    AdminSyncPloyDetailService adminSyncPloyDetailService = Mock()
    SyncPloyDetailAdminManager syncPloyDetailAdminManager = Mock()
    AdminNodeSettingService adminNodeSettingService = Mock()
    I18NStringManager i18NStringManager = Mock()

    IntegrationStreamManager integrationStreamManager = new IntegrationStreamManager(objectDescribeService: objectDescribeService
            , proxyHttpClient: proxyHttpClient
            , fsCrmObjectService: fsCrmObjectService
            , erpObjectFieldsService: erpObjectFieldsService
            , erpObjectService: erpObjectService
            , adminSyncPloyDetailService: adminSyncPloyDetailService
            , syncPloyDetailAdminManager: syncPloyDetailAdminManager
            , adminNodeSettingService: adminNodeSettingService
            , i18NStringManager: i18NStringManager)

    def setup() {

    }


    def "test common Verify Stream Detail"() {
        given:
        objectDescribeService.getDescribe(*_) >> new Result<ControllerGetDescribeResult>(data: new ControllerGetDescribeResult(describe: new ObjectDescribe(active: true)))
        proxyHttpClient.postUrl(*_) >> new HttpRspLimitLenUtil.ResponseBodyModel.ResponseBodyModelBuilder().body(JSON.toJSONString([errCode: '0', result: [function: [is_active: true]]])).build()
        fsCrmObjectService.listObjectFieldsWithFilterBlackList(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(new ListObjectFieldsResult())
        erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess([new ErpObjectFieldResult(fieldApiName: "fieldApiName", fieldDefineType: ErpFieldTypeEnum.text)])
        erpObjectService.queryErpObjectByObjApiName(*_) >> new com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<ErpObjectDescResult>(new ErpObjectDescResult(erpObjectApiName: 'erpObjectApiName'))

        when:
        Map<String, UpdateIntegrationStreamResult.ErrorMsg> result = integrationStreamManager.commonVerifyStreamDetail('88521', new SyncPloyDetailEntity(sourceTenantType: TenantType.CRM, syncConditions: new SyncConditionsData(filters: [[new FilterData()]]), detailObjectMappings: [new DetailObjectMappingsData.DetailObjectMappingData()] as DetailObjectMappingsData, detailObjectSyncConditions: [new SyncConditionsData()] as SyncConditionsListData, fieldMappings: [new FieldMappingData(sourceApiName: "sourceApiName", destApiName: "destApiName")] as FieldMappingsData, beforeFuncApiName: 'beforeFuncApiName', duringFuncApiName: 'duringFuncApiName', afterFuncApiName: 'afterFuncApiName', integrationStreamNodes: new IntegrationStreamNodesData(reverseWriteNode: new IntegrationStreamNodesData.ReverseWriteNode())), "lang")

        then:
        result == [afterFunctionNode      : new UpdateIntegrationStreamResult.ErrorMsg(false, '[]'),
                   beforeFunctionNode     : new UpdateIntegrationStreamResult.ErrorMsg(false, '[]'),
                   durationFunctionApiNode: new UpdateIntegrationStreamResult.ErrorMsg(false, '[]'),
                   sourceSystemNode       : new UpdateIntegrationStreamResult.ErrorMsg(false, '[]'),
                   reverseWriteNode       : new UpdateIntegrationStreamResult.ErrorMsg(false, '[]'),
                   fieldMappingNode       : new UpdateIntegrationStreamResult.ErrorMsg(false, '[]'),
                   syncConditionsNode     : new UpdateIntegrationStreamResult.ErrorMsg(false, '')]
    }

    def "test verify Crm Obj Life Status"() {
        given:
        objectDescribeService.getDescribe(*_) >> new Result<ControllerGetDescribeResult>(data: new ControllerGetDescribeResult(describe: new ObjectDescribe(active: false)))

        when:
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<Boolean> result = integrationStreamManager.verifyCrmObjLifeStatus('88521', "crmApiName")

        then:
        result == com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(true)
    }

    def "test verify Function Status"() {
        given:
        proxyHttpClient.postUrl(*_) >> new HttpRspLimitLenUtil.ResponseBodyModel.ResponseBodyModelBuilder().body(JSON.toJSONString([errCode: '0', result: [function: [is_active: true]]])).build()

        when:
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<Boolean> result = integrationStreamManager.verifyFunctionStatus('88521', "functionName")

        then:
        result == com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(false)
    }

    def "test verify Field Mapping"() {
        given:
        fsCrmObjectService.listObjectFieldsWithFilterBlackList(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(new ListObjectFieldsResult(fields: [new ObjectFieldResult(apiName: "apiName")]))
        erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess([new ErpObjectFieldResult()])
        erpObjectService.queryErpObjectByObjApiName(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(new ErpObjectDescResult())

        when:
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<List<IntegrationFieldVerifyResult>> result = integrationStreamManager.verifyFieldMapping('88521', new SyncPloyDetailEntity(sourceTenantType: TenantType.CRM, detailObjectMappings: [new DetailObjectMappingsData.DetailObjectMappingData()] as DetailObjectMappingsData, fieldMappings: [new FieldMappingData(sourceApiName: "sourceApiName", destApiName: "destApiName")] as FieldMappingsData), "lang")

        then:
        result == com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess([new IntegrationFieldVerifyResult(ResultCodeEnum.CRM_OBJECT_FIELD_INVALID, null, 'sourceApiName')])
    }

    def "test check Object Mapping Fields Status"() {
        when:
        List<IntegrationFieldVerifyResult> result = integrationStreamManager.checkObjectMappingFieldsStatus(["mappingObjObjSet"] as Set<String>, new ListObjectFieldsResult(), [new IntegrationFieldVerifyResult(ResultCodeEnum.SUCCESS, "objectApiName", "fieldApiName")])

        then:
        result == [new IntegrationFieldVerifyResult(ResultCodeEnum.SUCCESS, "objectApiName", "fieldApiName")]
    }

    def "test check Object Field Required"() {
        when:
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<List<IntegrationFieldVerifyResult>> result = integrationStreamManager.checkObjectFieldRequired([new FieldMappingData()], new ListObjectFieldsResult(fields: [new ObjectFieldResult(isRequired: true)]), [new IntegrationFieldVerifyResult(ResultCodeEnum.SUCCESS, "objectApiName", "fieldApiName")])


        def ret = new com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<List<IntegrationFieldVerifyResult>>("s106240000", "成功", [new IntegrationFieldVerifyResult(ResultCodeEnum.SUCCESS, "objectApiName", "fieldApiName"), new IntegrationFieldVerifyResult(ResultCodeEnum.OBJECT_REQUIRED_FIELD_NOT_MAPPING, null, null)])
        ret.i18nKey = 'erpdss.global.global.s6'
        then:
        ret.data == result.data
        result == ret
    }

    def "test verify Condition Fields"() {
        given:
        fsCrmObjectService.listObjectFieldsWithFilterBlackList(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(new ListObjectFieldsResult())
        erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess([null])
        erpObjectService.queryErpObjectByObjApiName(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(new ErpObjectDescResult())

        when:
        List<IntegrationFieldVerifyResult> result = integrationStreamManager.verifyConditionFields('88521', [new SyncConditionsData()], true, "erpDcId", "lang")

        then:
        result == []
    }

    def "test query Erp Obj Field Mapping"() {
        given:
        erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess([new ErpObjectFieldResult(fieldApiName: "fieldApiName", fieldDefineType: ErpFieldTypeEnum.text)])
        erpObjectService.queryErpObjectByObjApiName(*_) >> new com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<ErpObjectDescResult>(new ErpObjectDescResult(erpObjectApiName: 'erpObjectApiName'))


        when:
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<ListObjectFieldsResult> result = integrationStreamManager.queryErpObjFieldMapping('88521', "dataCenterId", 0, "erpObjectApiName")

        then:
        result == com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(new ListObjectFieldsResult(objectApiName: 'erpObjectApiName', fields: [new ObjectFieldResult(apiName: 'fieldApiName', type: 'text', isRequired: false)]))
    }

    def "test all Update Integration Stream"() {
        given:
        adminSyncPloyDetailService.updateSyncRules(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(null)
        adminSyncPloyDetailService.updateSyncConditions(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(null)
        adminSyncPloyDetailService.updateUsedQueryField(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(null)
        adminNodeSettingService.updateFunction(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(null)
        adminNodeSettingService.updateFieldMapping(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(null)
        adminNodeSettingService.updateIntegrationStreamNodes(*_) >> com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess("data")
        i18NStringManager.get(*_) >> "getResponse"

        when:
        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<UpdateIntegrationStreamResult> result = integrationStreamManager.allUpdateIntegrationStream('88521', new UpdateIntegrationStreamArg(sourceSystemNode: new QueryIntegrationDetailResult.SourceSystemNode(sourceObjectApiName: "sourceObjectApiName", sourceDc: new DataCenterData(dcChannel: ErpChannelEnum.CRM), sourceTenantType: TenantType.CRM), fieldMappingNode: new QueryIntegrationDetailResult.FieldMappingNode(fieldMappings: new ObjectMappingResult(fieldMappings: [new FieldMappingResult()])), destSystemNode: new QueryIntegrationDetailResult.DestSystemNode(destObjectApiName: "destObjectApiName")), "erpDcId", "lang")

        then:
        result == com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess()
    }
}
