package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService
import com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent.BaseLinkServiceImpl
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHInterfaceMonitorDao
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryInterfaceMonitorLogArg
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import org.apache.ibatis.cursor.Cursor
import org.bson.types.ObjectId
import org.mockito.Mockito
import spock.lang.Specification

import java.text.SimpleDateFormat

class ErpInterfaceMonitorServiceImplTest extends Specification {

    def "queryInterfaceMonitorLogList"() {
        given:
        def adminSyncPloyDetailService = Mock(AdminSyncPloyDetailServiceImpl) {
            getByIdWithCache(*_) >> {
                String json = "{\"ployId\":88521,\"ployName\":\"物料 ERP往CRM\",\"ployTenantId\":88521,\"id\":\"61b4c18e0b25401bb55d94078fabf699\",\"sourceTenantDatas\":[{\"tenantId\":88521,\"tenantName\":null}],\"sourceTenantType\":2,\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceObjectName\":null,\"destTenantDatas\":[{\"tenantId\":88521,\"tenantName\":null}],\"destTenantType\":1,\"destObjectApiName\":\"ProductObj\",\"destObjectName\":null,\"detailObjectMappings\":[],\"status\":1,\"statusName\":\"启用\",\"isValid\":true,\"syncType\":null,\"syncTypeList\":[\"get\"],\"beforeFuncApiName\":null,\"duringFuncApiName\":null,\"afterFuncApiName\":\"JDYManagerErpAPL2__c\",\"hadReverseWriteNode\":false,\"fieldMappings\":[{\"sourceApiName\":null,\"sourceType\":null,\"sourceTargetApiName\":null,\"sourceQuoteFieldType\":null,\"sourceQuoteRealField\":null,\"sourceQuoteFieldTargetObjectApiName\":null,\"sourceQuoteFieldTargetObjectField\":null,\"destApiName\":\"unit\",\"destType\":\"select_one\",\"destTargetApiName\":null,\"destQuoteFieldType\":null,\"optionMappings\":null,\"mappingType\":3,\"function\":null,\"value\":1}]}";
                SyncPloyDetailResult ployDetail = GsonUtil.fromJson(json, SyncPloyDetailResult.class);
                com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<SyncPloyDetailResult> result = com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result.newSuccess(ployDetail)
                return result
            }
        }

        def baseLinkService = Mock(BaseLinkServiceImpl) {
            getRealApiName(*_) >> "ProductObj"
        }

//        def chInterfaceMonitorManager = Mock(CHInterfaceMonitorManager) {
//            cursorInterfaceMonitorByInterfaceMonitorDataInType(*_) >> {
//                List<CHInterfaceMonitorEntity> entities = new ArrayList<>();
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
//                CHInterfaceMonitorEntity entity = new CHInterfaceMonitorEntity()
//                entity.setId("6710defb8ed19776083b4122");
//                entity.setTenantId("88521");
//                entity.setAppName("fs-erp-sync-data");
//                entity.setArg("{\"object_data\":{\"tenant_id\":\"88521\",\"owner\":[\"-10000\"],\"product_status\":\"2\",\"remark\":\"哈哈哈1233\",\"product_code\":\"CH540z\",\"record_type\":\"default__c\",\"product_category_id\":\"646ca28ebe3218000161e296\",\"off_shelves_time\":1635436800000,\"price\":\"5\",\"object_describe_api_name\":\"ProductObj\",\"name\":\"K3C20211028物料002更新\",\"_id\":\"66db21c3e345810006b2747b\"},\"details\":{},\"fillOutOwner\":false}");
//                entity.setCallTime(1729158907528L);
//                entity.setCostTime(308L);
//                entity.setCreateTime(sdf.parse("2024-10-17T17:55:07.837+08:00"));
//                entity.setUpdateTime(sdf.parse("2024-10-17T17:55:07.837+08:00"));
//                entity.setExpireTime(sdf.parse("2024-11-16T17:55:07.837+08:00"));
//                entity.setLogType("interface_monitor");
//                entity.setDcId("643f7322b54ea80001767d86");
//                entity.setObjApiName("ProductObj");
//                entity.setInterfaceMonitorType("crmUpdate");
//                entity.setInterfaceMonitorStatus(1);
//                entity.setLogId("J-E.88521.0.BD_MATERIAL.2cqBA5Momv6.55.0.0");
//                entity.setRemark("v1/rest/object/ProductObj/action/Edit?isSpecifyCreatedBy=false&isSpecifyTime=false&triggerFlow=true&triggerWorkflow=true");
//                entity.setResult("{\"code\":0,\"message\":\"OK\",\"data\":{\"objectData\":{\"is_saleable\":true,\"is_multiple_unit\":false,\"product_category_id\":\"646ca28ebe3218000161e296\",\"off_shelves_time\":1635436800000,\"extend_obj_data_id\":\"66db21c5ea09d90001f711a0\",\"price\":\"5\",\"searchAfterId\":[\"1729158658309\",\"66db21c3e345810006b2747b\"],\"lock_status\":\"0\",\"package\":\"CRM\",\"is_giveaway\":\"0\",\"create_time\":1725637061197,\"version\":6,\"created_by\":[\"-10000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"-10000\"],\"teamMemberType\":\"0\",\"teamMemberRole\":\"1\",\"teamMemberPermissionType\":\"2\",\"teamMemberDeptCascade\":\"0\"}],\"data_own_department\":[\"999999\"],\"name\":\"K3C20211028物料002更新\",\"_id\":\"66db21c3e345810006b2747b\",\"tenant_id\":\"88521\",\"product_status\":\"2\",\"remark\":\"哈哈哈1233\",\"product_code\":\"CH540z\",\"on_shelves_time\":1725637061176,\"is_deleted\":false,\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"-10000\"],\"product_category_id__relation_ids\":\"646ca28ebe3218000161e296\",\"is_package\":false,\"last_modified_time\":1729158658309,\"life_status\":\"normal\",\"last_modified_by\":[\"-10000\"],\"record_type\":\"default__c\",\"category\":\"1684841102584\"},\"isDuplicate\":false}}");
//                entity.setReturnTime(1729158907836L);
//                entity.setServerIp("***********");
//                entity.setSyncDataId("6710defa8ed19776083b411f");
//                entity.setTimeFilterArgEndTime(0L);
//                entity.setTimeFilterArgLimit(0);
//                entity.setTimeFilterArgOffset(0);
//                entity.setTimeFilterArgStartTime(0L);
//                entity.setTraceId("J-E.88521.0.BD_MATERIAL.2cqBA5Momv6.55.0.0");
//                entities.add(entity)
//                Result<List<InterfaceMonitorData>> result = Result.newSuccess(entities)
//                return result
//            }
//        }

        def chInterfaceMonitorManager = Mock(CHInterfaceMonitorManager) {
            cursorInterfaceMonitorByInterfaceMonitorDataInType(*_) >> {
                List<InterfaceMonitorData> entities = new ArrayList<>();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
                InterfaceMonitorData entity = new InterfaceMonitorData()
                entity.setId(new ObjectId("6710defb8ed19776083b4122"));
                entity.setTenantId("88521");
                entity.setArg("{\"object_data\":{\"tenant_id\":\"88521\",\"owner\":[\"-10000\"],\"product_status\":\"2\",\"remark\":\"哈哈哈1233\",\"product_code\":\"CH540z\",\"record_type\":\"default__c\",\"product_category_id\":\"646ca28ebe3218000161e296\",\"off_shelves_time\":1635436800000,\"price\":\"5\",\"object_describe_api_name\":\"ProductObj\",\"name\":\"K3C20211028物料002更新\",\"_id\":\"66db21c3e345810006b2747b\"},\"details\":{},\"fillOutOwner\":false}");
                entity.setCallTime(1729158907528L);
                entity.setCostTime(308L);
                entity.setCreateTime(1729158907529L);
                entity.setExpireTime(sdf.parse("2024-11-16T17:55:07.837+08:00"));
                entity.setDcId("643f7322b54ea80001767d86");
                entity.setObjApiName("ProductObj");
                entity.setType("crmUpdate");
                entity.setLogId("J-E.88521.0.BD_MATERIAL.2cqBA5Momv6.55.0.0");
                entity.setRemark("v1/rest/object/ProductObj/action/Edit?isSpecifyCreatedBy=false&isSpecifyTime=false&triggerFlow=true&triggerWorkflow=true");
                entity.setResult("{\"code\":0,\"message\":\"OK\",\"data\":{\"objectData\":{\"is_saleable\":true,\"is_multiple_unit\":false,\"product_category_id\":\"646ca28ebe3218000161e296\",\"off_shelves_time\":1635436800000,\"extend_obj_data_id\":\"66db21c5ea09d90001f711a0\",\"price\":\"5\",\"searchAfterId\":[\"1729158658309\",\"66db21c3e345810006b2747b\"],\"lock_status\":\"0\",\"package\":\"CRM\",\"is_giveaway\":\"0\",\"create_time\":1725637061197,\"version\":6,\"created_by\":[\"-10000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"-10000\"],\"teamMemberType\":\"0\",\"teamMemberRole\":\"1\",\"teamMemberPermissionType\":\"2\",\"teamMemberDeptCascade\":\"0\"}],\"data_own_department\":[\"999999\"],\"name\":\"K3C20211028物料002更新\",\"_id\":\"66db21c3e345810006b2747b\",\"tenant_id\":\"88521\",\"product_status\":\"2\",\"remark\":\"哈哈哈1233\",\"product_code\":\"CH540z\",\"on_shelves_time\":1725637061176,\"is_deleted\":false,\"object_describe_api_name\":\"ProductObj\",\"owner\":[\"-10000\"],\"product_category_id__relation_ids\":\"646ca28ebe3218000161e296\",\"is_package\":false,\"last_modified_time\":1729158658309,\"life_status\":\"normal\",\"last_modified_by\":[\"-10000\"],\"record_type\":\"default__c\",\"category\":\"1684841102584\"},\"isDuplicate\":false}}");
                entity.setReturnTime(1729158907836L);
                entity.setSyncDataId("6710defa8ed19776083b411f");
                entity.setTraceId("J-E.88521.0.BD_MATERIAL.2cqBA5Momv6.55.0.0");
                entity.setStatus(1)
                entities.add(entity)
                Result<List<InterfaceMonitorData>> result = Result.newSuccess(entities)
                return result
            }

            countByInterfaceMonitorDataLimitSize(*_) >> 1
        }

        def i18NStringManager = Mock(I18NStringManager) {
            get(I18NStringEnum.s6,"en","88521") >> "success"
            get(I18NStringEnum.s7,"en","88521") >> "fail"
        }

        def erpInterfaceMonitorService = new ErpInterfaceMonitorServiceImpl(
                adminSyncPloyDetailService:adminSyncPloyDetailService,
                baseLinkService:baseLinkService,
                chInterfaceMonitorManager:chInterfaceMonitorManager,
                i18NStringManager:i18NStringManager)
        QueryInterfaceMonitorLogArg arg = new QueryInterfaceMonitorLogArg()
        arg.setDataCenterId("643f7322b54ea80001767d86")
        arg.setPloyDetailId("61b4c18e0b25401bb55d94078fabf699")
        arg.setStartTime(1729077947000L)
        arg.setEndTime(1729164347000L)
        def result = erpInterfaceMonitorService.queryInterfaceMonitorLogList("88521", arg, "en")

        expect:
        result.success

    }
}
