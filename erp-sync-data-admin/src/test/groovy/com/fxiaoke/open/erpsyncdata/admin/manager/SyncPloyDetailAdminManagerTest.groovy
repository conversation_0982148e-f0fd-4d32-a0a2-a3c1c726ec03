package com.fxiaoke.open.erpsyncdata.admin.manager

import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData
import com.fxiaoke.open.erpsyncdata.admin.remote.ErRemoteManager
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum
import com.fxiaoke.open.erpsyncdata.converter.manager.PloyBreakManager
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.*
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData
import com.fxiaoke.otherrestapi.function.result.FunctionResult
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult
import spock.lang.Specification

import java.util.stream.Collectors

import static com.fxiaoke.open.erpsyncdata.common.constant.CheckAndUpdatePloyValidErrorTypeEnum.*

/**
 * <AUTHOR> 
 * @date 2024/10/24 16:37:53
 */
class SyncPloyDetailAdminManagerTest extends Specification {
    AdminSyncPloyDetailService adminSyncPloyDetailService = Mock()
    CustomFunctionService customFunctionService = Mock()
    ErpDataPreprocessService erpDataPreprocessService = Mock()
    SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao = Mock()
    AdminSyncPloyDetailDao adminSyncPloyDetailDao = Mock()
    ErRemoteManager erRemoteManager = Mock()
    RedisCacheManager redisCacheManager = Mock()
    SyncDataMappingsDao syncDataMappingsDao = Mock()
    PloyBreakManager ployBreakManager = Mock()
    ErpObjManager erpObjManager = Mock()
    I18NStringManager i18NStringManager = Mock()

    SyncPloyDetailAdminManager syncPloyDetailAdminManager = new SyncPloyDetailAdminManager(
            adminSyncPloyDetailService: adminSyncPloyDetailService
            , customFunctionService: customFunctionService
            , erpDataPreprocessService: erpDataPreprocessService
            , syncPloyDetailSnapshotDao: syncPloyDetailSnapshotDao
            , adminSyncPloyDetailDao: adminSyncPloyDetailDao
            , erRemoteManager: erRemoteManager
            , redisCacheManager: redisCacheManager
            , syncDataMappingsDao: syncDataMappingsDao
            , ployBreakManager: ployBreakManager
            , erpObjManager: erpObjManager
            , i18NStringManager: i18NStringManager
    )

    def setup() {
        syncPloyDetailSnapshotDao.setTenantId(*_) >> syncPloyDetailSnapshotDao
        adminSyncPloyDetailDao.setTenantId(*_) >> adminSyncPloyDetailDao
        syncDataMappingsDao.setTenantId(*_) >> syncDataMappingsDao
    }


    def "test check And Update Ploy Valid Status"() {
        given:
        adminSyncPloyDetailService.getById(*_) >> Result.newSuccess(new SyncPloyDetailResult(status: SyncPloyDetailStatusEnum.ENABLE.getStatus()))
        adminSyncPloyDetailService.updateValid(*_) >> new Result<Void>("errCode": ResultCodeEnum.SUCCESS.getErrCode())
        customFunctionService.find(*_) >> new Result2<FunctionResult<FunctionServiceFindResult>>(data: new FunctionServiceFindResult())

        erRemoteManager.listTenantNamesByIds(*_) >> ["listTenantNamesByIdsResponse": "listTenantNamesByIdsResponse"]


        def ployDetailEntity = new SyncPloyDetailEntity(fieldMappings: [new FieldMappingData(sourceApiName: "sourceApiName")] as FieldMappingsData,
                status: SyncPloyDetailStatusEnum.ENABLE.getStatus(),
                detailObjectMappings: [
                        new DetailObjectMappingsData.DetailObjectMappingData(sourceObjectApiName: "detailDestObjectApiName", fieldMappings: [new FieldMappingData(sourceApiName: "destApiName")] as FieldMappingsData),
                        new DetailObjectMappingsData.DetailObjectMappingData(sourceObjectApiName: "detailDestObjectApiName2")
                ] as DetailObjectMappingsData,
                sourceDataCenterId: 'sourceDataCenterId',
                destDataCenterId: 'destDataCenterId',
                detailObjectSyncConditions: [new SyncConditionsData(apiName: 'apiName')] as SyncConditionsListData,
                id: '123',
                integrationStreamNodes: new IntegrationStreamNodesData(
                        reverseWriteNode: new IntegrationStreamNodesData.ReverseWriteNode(fieldMappings: [new FieldMappingData(destApiName: "sourceApiName")] as FieldMappingsData, detailObjectMappings: [new DetailObjectMappingsData.DetailObjectMappingData(destObjectApiName: "detailDestObjectApiName", fieldMappings: [new FieldMappingData(destApiName: "destApiName")])] as DetailObjectMappingsData),
                        checkSyncDataMappingNode: new IntegrationStreamNodesData.CheckSyncDataMappingNode(queryObjectMappingData: new QueryObjectMappingData(sourceObjectApiName: 'sourceObjectApiName'), detailCheckSyncDataMappingData: [new DetailQueryObject2SyncDataMappingsData.DetailQueryObject2SyncDataMappingData(queryObjectMappingData: new QueryObjectMappingData(destObjectApiName: "detailDestObjectApiName"))] as DetailQueryObject2SyncDataMappingsData)
                )
        )
        adminSyncPloyDetailDao.getById(*_) >> ployDetailEntity

        redisCacheManager.getCache(*_) >> "getCacheResponse"
        i18NStringManager.get2(*_) >> "get2Response"
        i18NStringManager.get(*_) >> "getResponse"

        when:
        Set<CheckAndUpdatePloyValidStatusDetailData> result = syncPloyDetailAdminManager.checkAndUpdatePloyValidStatus('88521', "id", "lang")

        def collect = result.stream().map { it.getCheckAndUpdatePloyValidErrorType() }.collect(Collectors.toSet())
        then:
        collect == [PLOY_BREAK_BY_SYSTEM.getType(), SYNC_RULES_NOT_EXISTS.getType(), FUNCTION_NOT_VALID.getType(), MAPPING_NOT_EXISTS.getType()] as Set<Integer>
    }

    def "test check And Update Ploy Valid Status 2"() {
        given:
        adminSyncPloyDetailService.updateValid(*_) >> new Result<Void>("errCode", "errMsg", null)
        customFunctionService.find(*_) >> new Result2<FunctionResult<FunctionServiceFindResult>>(data: new FunctionServiceFindResult())
        erRemoteManager.listTenantNamesByIds(*_) >> ["listTenantNamesByIdsResponse": "listTenantNamesByIdsResponse"]
        redisCacheManager.getCache(*_) >> "getCacheResponse"
        i18NStringManager.get2(*_) >> "get2Response"

        def ployDetailEntity = new SyncPloyDetailEntity(fieldMappings: [new FieldMappingData(sourceApiName: "sourceApiName")] as FieldMappingsData,
                status: SyncPloyDetailStatusEnum.ENABLE.getStatus(),
                detailObjectMappings: [
                        new DetailObjectMappingsData.DetailObjectMappingData(sourceObjectApiName: "detailDestObjectApiName", fieldMappings: [new FieldMappingData(sourceApiName: "destApiName")] as FieldMappingsData),
                        new DetailObjectMappingsData.DetailObjectMappingData(sourceObjectApiName: "detailDestObjectApiName2")
                ] as DetailObjectMappingsData,
                sourceDataCenterId: 'sourceDataCenterId',
                destDataCenterId: 'destDataCenterId',
                detailObjectSyncConditions: [new SyncConditionsData(apiName: 'apiName')] as SyncConditionsListData,
                id: '123',
                integrationStreamNodes: new IntegrationStreamNodesData(
                        reverseWriteNode: new IntegrationStreamNodesData.ReverseWriteNode(fieldMappings: [new FieldMappingData(destApiName: "sourceApiName")] as FieldMappingsData, detailObjectMappings: [new DetailObjectMappingsData.DetailObjectMappingData(destObjectApiName: "detailDestObjectApiName", fieldMappings: [new FieldMappingData(destApiName: "destApiName")])] as DetailObjectMappingsData),
                        checkSyncDataMappingNode: new IntegrationStreamNodesData.CheckSyncDataMappingNode(queryObjectMappingData: new QueryObjectMappingData(sourceObjectApiName: 'sourceObjectApiName'), detailCheckSyncDataMappingData: [new DetailQueryObject2SyncDataMappingsData.DetailQueryObject2SyncDataMappingData(queryObjectMappingData: new QueryObjectMappingData(destObjectApiName: "detailDestObjectApiName"))] as DetailQueryObject2SyncDataMappingsData)
                )
        )
        when:
        Set<CheckAndUpdatePloyValidStatusDetailData> result = syncPloyDetailAdminManager.checkAndUpdatePloyValidStatus('88521', ployDetailEntity, "lang", Boolean.TRUE)


        def collect = result.stream().map { it.getCheckAndUpdatePloyValidErrorType() }.collect(Collectors.toSet())
        then:
        collect == [SYNC_RULES_NOT_EXISTS.getType(), FUNCTION_NOT_VALID.getType(), MAPPING_NOT_EXISTS.getType()] as Set<Integer>
    }

    def "test check Query Interface Status"() {
        given:
        erpDataPreprocessService.listErpObjDataByTime(*_) >> new Result<ListErpObjDataResult>("errCode", "errMsg", new ListErpObjDataResult())
        syncPloyDetailSnapshotDao.listEnableSnapshotsBySyncPloyDetailsId(*_) >> [new SyncPloyDetailSnapshotEntity()]
        adminSyncPloyDetailDao.getById(*_) >> new SyncPloyDetailEntity()
        redisCacheManager.setCache(*_) >> true
        i18NStringManager.get2(*_) >> "get2Response"
        i18NStringManager.get(*_) >> "getResponse"

        when:
        Result<Void> result = syncPloyDetailAdminManager.checkQueryInterfaceStatus('88521', "ployDetailId", "lang")

        then:
        result == new Result<Void>("errCode", "errMsg", null)
    }

    def "test check Query Interface Status 2"() {
        given:
        erpDataPreprocessService.listErpObjDataByTime(*_) >> new Result<ListErpObjDataResult>("errCode", "errMsg", new ListErpObjDataResult())
        redisCacheManager.setCache(*_) >> true
        i18NStringManager.get2(*_) >> "get2Response"
        i18NStringManager.get(*_) >> "getResponse"

        when:
        Result<Void> result = syncPloyDetailAdminManager.checkQueryInterfaceStatus('88521', "snapshotEntityId", new SyncPloyDetailEntity(sourceTenantType: TenantType.ERP, syncRules: new SyncRulesData(syncTypeList: ['get'], events: [1, 2, 3])), "lang")

        then:
        result == new Result<Void>("errCode", "errMsg", null)
    }

    def "test check Function"() {
        given:
        customFunctionService.find(*_) >> Result2.newSuccess(new FunctionResult(result: new FunctionServiceFindResult(function: new FunctionServiceFindData(body: '{"type":"Fx.object.create", "sourceApiName":"sourceApiName"}'))))
        i18NStringManager.get2(*_) >> "get2Response"

        when:
        List<CheckAndUpdatePloyValidStatusDetailData> result = syncPloyDetailAdminManager.checkFunction('88521', "funcApiName", [new com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData(sourceApiName: "sourceApiName")], "lang")

        then:
        result == []
    }

    def "test get Erp Real Obj Api Name By Crm Obj Api Name"() {
        given:
        adminSyncPloyDetailDao.listByDestTenantTypeAndObjApiName(*_) >> [new SyncPloyDetailEntity()]
        erpObjManager.getRelation(*_) >> new ErpObjectRelationshipEntity("id", '88521', "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)

        when:
        String result = syncPloyDetailAdminManager.getErpRealObjApiNameByCrmObjApiName('88521', "crmObjApiName")

        then:
        result == "erpRealObjectApiname"
    }
}
