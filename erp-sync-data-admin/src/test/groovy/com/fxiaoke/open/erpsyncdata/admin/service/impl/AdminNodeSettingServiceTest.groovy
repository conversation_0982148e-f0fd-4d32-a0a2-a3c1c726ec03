package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.admin.arg.UpdateIntegrationStreamArg
import com.fxiaoke.open.erpsyncdata.admin.result.DetailQueryData2SyncDataMappingResult
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult
import com.fxiaoke.open.erpsyncdata.admin.result.QueryIntegrationDetailResult
import com.fxiaoke.open.erpsyncdata.admin.result.QueryObjectMappingResult
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import io.swagger.annotations.ApiModelProperty
import com.google.common.collect.Lists;
import spock.lang.Specification

class AdminNodeSettingServiceTest extends Specification {

    def "UpdateIntegrationStreamNodes"() {
        String crmMainObj = "SalesOrderObj"
        String erpMainObj = "SAL_SaleOrder.BillHead"
        //erp->crm
        UpdateIntegrationStreamArg updateIntegrationStreamArg1 = new UpdateIntegrationStreamArg()
        //数据范围查询crm节点
        //数据范围查询crm节点
        QueryIntegrationDetailResult.SyncConditionsQueryDataNode syncConditionsQueryDataNode = new QueryIntegrationDetailResult.SyncConditionsQueryDataNode();
        QueryObjectMappingResult queryObjectMappingResult = new QueryObjectMappingResult();
        queryObjectMappingResult.setSourceObjectApiName("AccountObj");
        queryObjectMappingResult.setDestObjectApiName("SalesOrderObj");
        FilterData filterData = new FilterData();
        filterData.setFieldApiName("account_no.FNumber");
        filterData.setFieldValue(com.google.common.collect.Lists.newArrayList("FCustId.FNumber"));
        FilterData filterData1 = new FilterData();
        filterData1.setFieldApiName("remark");
        filterData1.setFieldValue(com.google.common.collect.Lists.newArrayList("FSaleOrgId.FNumber"));
        List<List<FilterData>> queryFieldMappings = com.google.common.collect.Lists.newArrayList();
        queryFieldMappings.add(com.google.common.collect.Lists.newArrayList(filterData, filterData1));
        queryObjectMappingResult.setQueryFieldMappings(queryFieldMappings);
        syncConditionsQueryDataNode.setQueryObjectMappingData(com.google.common.collect.Lists.newArrayList(queryObjectMappingResult));
        syncConditionsQueryDataNode.setSyncCondition(1);


        String str = "{\"checkSyncDataMappingNode\":{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SalesOrderObj\",\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FBillNo\"],\"operate\":\"EQ\"}]]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}],\"detailCheckSyncDataMappingData\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FID\"],\"operate\":\"EQ\"}]]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"},{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destApiName\":\"masterDataId\",\"sourceApiName\":\"master_id\"}]}]},\"queryCrmObject2DestNodeBySource\":{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FCustId.FNumber\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]},\"detailQueryData2DestDataMapping\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FCustId.FNumber\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]}}]},\"queryCrmObject2DestNodeByDest\":{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderObj\",\"queryFieldMappings\":[[{\"fieldApiName\":\"_id\",\"fieldValue\":[\"account_id\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]},\"detailQueryData2DestDataMapping\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderProductObj\",\"queryFieldMappings\":[[{\"fieldApiName\":\"_id\",\"fieldValue\":[\"account_id\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]}}]}}";
        JSONObject jsonObject = JSONObject.parseObject(str);
        String check=jsonObject.getJSONObject("checkSyncDataMappingNode").toJSONString();
        String source=jsonObject.getJSONObject("queryCrmObject2DestNodeBySource").toJSONString();
        String dest=jsonObject.getJSONObject("queryCrmObject2DestNodeByDest").toJSONString();
        UpdateIntegrationStreamArg arg=new UpdateIntegrationStreamArg();
        //检查中间表节点
        QueryIntegrationDetailResult.CheckSyncDataMappingNode checkSyncDataMappingNode= JacksonUtil.fromJson(check,QueryIntegrationDetailResult.CheckSyncDataMappingNode.class);
        //通过源数据查询crm节点
        QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeBySource=JacksonUtil.fromJson(source,QueryIntegrationDetailResult.QueryCrmObject2DestNode.class);
        //通过目标数据查询crm节点
        QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeByDest=JacksonUtil.fromJson(dest,QueryIntegrationDetailResult.QueryCrmObject2DestNode.class);


        updateIntegrationStreamArg1.setSyncConditionsQueryDataNode(syncConditionsQueryDataNode)
        updateIntegrationStreamArg1.setCheckSyncDataMappingNode(checkSyncDataMappingNode)
        updateIntegrationStreamArg1.setQueryCrmObject2DestNodeBySource(queryCrmObject2DestNodeBySource)
        updateIntegrationStreamArg1.setQueryCrmObject2DestNodeByDest(queryCrmObject2DestNodeByDest)
        AdminNodeSettingServiceImpl adminNodeSettingService = new AdminNodeSettingServiceImpl();
        adminNodeSettingService.updateIntegrationStreamNodes("81243", updateIntegrationStreamArg1)
    }

    private List<ObjectMappingResult> getMainSource2SyncDataMapping() {

        return Lists.newArrayList()
    }
}
