import com.fxiaoke.functions.FunctionContext
import com.fxiaoke.functions.Fx
import com.fxiaoke.functions.http.HttpResult
import com.fxiaoke.functions.interfaces.Cache
import com.fxiaoke.functions.time.DateTime
import com.fxiaoke.functions.utils.Maps

/**
 * <AUTHOR>
 * @codeName JDY_MANAGER
 * @description JDY的api的调用
 * @createTime 2023-10-16
 */


/**
 * @type classes
 * @returntype
 * @namespace library
 */
class JDYManagerErpAPL {

    static List DEFAULT_SIGNHEADERS = ["X-Api-Nonce", "X-Api-TimeStamp"];
    //---------可以修改的数据，需要填写真实数据----------
    String clientId="";//应用id
    String clientSecret="";//应用Secret
    String appKey="";//appkey
    String appSecret="appSecret";//随意填，不能为空
    String TENANT_ID="";//企业id的账号
    String OUT_INSTANCE_ID="";//第三方实例ID;
    //上查源单为销售订单的类型
    Map UP_QUERY_SALES_ORDER_FIELD=["sal_bill_outbound":"material_entity","ar_creditbill":"src_bill_type_id"]
    //虚拟的联系人，因为云星辰的联系人是在客户的从对象
    Map NEED_SPLIT_ERP_OBJ=["VIRContactObj":["source_erp_obj_apiname":"bd_customer","source_erp_detail_apiname":"bomentity","erp_detail_list_apiname":"contact_persons"]]
    //erp对象的相关调用信息
    Map JDY_OBJ_URL_MAPPING = [
            "bd_material" : ["jdyObjectApiName": "bd_material", "queryDetailUrl": "/jdy/v2/bd/material_detail", "queryListUrl": "/jdy/v2/bd/material", "createUrl": "/jdy/v2/bd/material", "detailObjApiName": ["aux_entity", "barcode_entity", "price_entity"]],
            "bd_customer" : ["jdyObjectApiName": "bd_customer", "queryDetailUrl": "/jdy/v2/bd/customer_detail", "queryListUrl": "/jdy/v2/bd/customer", "createUrl": "/jdy/v2/bd/customer","detailObjApiName": ["bomentity"]],
            "bd_materialgroup" : ["jdyObjectApiName": "bd_materialgroup", "queryDetailUrl": "/jdy/v2/bd/material_group_detail", "queryListUrl": "/jdy/v2/bd/material_group", "createUrl": "/jdy/v2/bd/material_group"],
            "sal_bill_order": ["jdyObjectApiName": "sal_bill_order", "queryDetailUrl": "/jdy/v2/scm/sal_order_detail", "queryListUrl": "/jdy/v2/scm/sal_order", "createUrl": "/jdy/v2/scm/sal_order", "detailObjApiName": ["material_entity"],"masterId":"bill_no"],
            "bd_store": ["jdyObjectApiName": "bd_store", "queryDetailUrl": "/jdy/v2/bd/store_detail", "queryListUrl": "/jdy/v2/bd/store", "createUrl": "/jdy/v2/bd/store"],
            "inv_inventory_entity": ["jdyObjectApiName": "inv_inventory_entity",  "queryListUrl": "/jdy/v2/scm/inventory","needQueryDetail":"false","queryListKey":"material_id","complexIdKey":"stock_id|material_id"],
            "sal_bill_outbound": ["jdyObjectApiName": "sal_bill_outbound", "queryDetailUrl": "/jdy/v2/scm/sal_out_bound_detail", "queryListUrl": "/jdy/v2/scm/sal_out_bound", "createUrl": "/jdy/v2/scm/sal_out_bound","detailObjApiName": ["material_entity"]],
            "ar_creditbill": ["jdyObjectApiName": "ar_creditbill", "queryDetailUrl": "/jdy/v2/arap/ar_credit_detail", "queryListUrl": "/jdy/v2/arap/ar_credit", "createUrl": "/jdy/v2/arap/ar_credit","detailObjApiName": ["source_bill_entry_list"]],
            "sal_invoice": ["jdyObjectApiName": "sal_invoice", "queryDetailUrl": "/jdy/v2/scm/sal_invoice_detail", "queryListUrl": "/jdy/v2/scm/sal_invoice_list", "createUrl": "/jdy/v2/scm/sal_invoice","detailObjApiName": ["material_entity"]],
    ]
    //不能修改
    String domain = "https://api.kingdee.com";
    String APP_SIGNATURE = "app_signature";//app_signature的key
    String APP_KEY = "app_key";//app_key
    String OUT_INSTANCE_KEY = "outerInstanceId";//外部实例id的key
    String APL_JDY_TOKEN_KEY = "APL_JDY_TOKEN_KEY" + TENANT_ID;//app_key
    String VIR_MASTER_ID="vir_master_id";//虚拟的主从字段
    String VIR_SALES_ORDER_ID="vir_sales_order_id";
    String SRC_BILL_TYPE_SALES_ORDER="sal_bill_order";
    String COMPLEX_ID="complexId";
    //缓存上查数据
    Cache upQueryCacheMap = Fx.cache.getDefaultCache();
    //


    // 仅查询指定的字段，供字段查询工具使用
    Map queryList(Map arg, Map connectInfo) {
        Map resultMap = [:];//返回的结果数据
        Map requestMap = [:];
        Map paramsMap = [:];
        Map headersMap = [:];

        String appToken = executeGetToken();
        String url = arg.get("url")

        def page = arg.get("page")
        paramsMap.put("page", page == null? 1: page)
        def perPage = arg.get("perPage")
        paramsMap.put("page_size", perPage == null? 50: perPage)
        headersMap = getHeader(clientId, clientSecret, "GET", url, paramsMap)
        headersMap.put("app-token", appToken);
        headersMap.put("X-GW-Router-Addr", "https://tf.jdy.com");

        Fx.log.info("queryList requestMap: " + Fx.json.toJson(requestMap))
        HttpResult returnData = GETJdy(domain + url, headersMap, paramsMap);
        //失败返回的数据
        if (returnData["statusCode"] != 200 || returnData["content"]["errcode"] != 0) {
            resultMap.put("code", returnData["statusCode"]);
            resultMap.put("message", returnData["content"]);
            log.info("查询失败：" + resultMap);
            return resultMap
        }

        List rowsData = returnData["content"]["data"]["rows"] as List;
        Map listData = [:]
        List objDataList = []
        List fields = arg.get("fieldNames") as List
        rowsData.each { item ->
            Map row = item as Map;
            def objData = [:]
            fields.each { field ->
                def fieldName = field as String;
                objData.put(fieldName, row[fieldName])
            }
            Map standardMap=[:];
            standardMap.put("masterFieldVal",objData);
            objDataList.add(standardMap)
        }
        listData.put("totalNum", returnData["content"]["data"]["count"]);
        listData.put("dataList", objDataList);

        resultMap.put("code", returnData["content"]["errcode"]);
        resultMap.put("data", listData);
        // 返回需要的数据字段列表
        Fx.log.info("resultMap："+Fx.json.toJson(resultMap))
        return resultMap;
    }

    //列表的接口
    Map queryMasterBatch(Map timeFilterArg, Map connectInfo) {
        Fx.log.info("listErpObjDataByTime" + timeFilterArg);
        Map listData = [:];
        Map listDataResult = [:];
        String timeFilterArgApiName=timeFilterArg.get("objAPIName");
        boolean spilitErpObj=false;
        if(NEED_SPLIT_ERP_OBJ.get(timeFilterArg.get("objAPIName"))!=null){
            Map needSplitErpMap=NEED_SPLIT_ERP_OBJ.get(timeFilterArg.get("objAPIName")) as Map;
            timeFilterArg.put("objAPIName",needSplitErpMap["source_erp_obj_apiname"]);
            spilitErpObj=true;
        }
        if(JDY_OBJ_URL_MAPPING[timeFilterArg["objAPIName"]]==null){
            Fx.message.throwErrorMessage("云星辰函数没有配置该对象url：${timeFilterArg["objAPIName"]}")
        }
        // 根据apiname去拿url
        String url = JDY_OBJ_URL_MAPPING[timeFilterArg["objAPIName"]]["queryListUrl"];

        Map paramsMap = [:];
        Map headersMap = [:];
        String appToken = executeGetToken();
        paramsMap.put("modify_end_time", timeFilterArg["endTime"]);
        paramsMap.put("modify_start_time", timeFilterArg["startTime"]);
        paramsMap.put("page_size", timeFilterArg["limit"]);
        def page = Integer.valueOf(timeFilterArg["offset"].toString()) / Integer.valueOf(timeFilterArg["limit"].toString()) + 1;
        paramsMap.put("page", page);
        headersMap = getHeader(clientId, clientSecret, "GET", url, paramsMap)
        headersMap.put("app-token", appToken);
        headersMap.put("X-GW-Router-Addr", "https://tf.jdy.com");
        HttpResult returnData = GETJdy(domain + url, headersMap, paramsMap);
        //失败返回的数据
        if (returnData["statusCode"] != 200 || returnData["content"]["errcode"] != 0) {
            listDataResult.put("code", returnData["statusCode"]);
            listDataResult.put("message", returnData["content"]);
            return listDataResult;
        }

        List rowsData = returnData["content"]["data"]["rows"] as List;
        List objDataIdList = [];
        List objDataList = [];
        String listKey=JDY_OBJ_URL_MAPPING[timeFilterArg["objAPIName"]]["queryListKey"];
        if(listKey!=null){
            //返回列表的id key不一定是id.需要配置
            rowsData.each { item -> objDataIdList.add(item[listKey]) };
        }else {
            rowsData.each { item -> objDataIdList.add(item["id"]) };
        }
        //有些数据是不需要再重新查询单条数据的
        String needQueryDetail=JDY_OBJ_URL_MAPPING[timeFilterArg["objAPIName"]]["needQueryDetail"] as String;
        if(needQueryDetail==null){
            objDataIdList.each { item ->
                Map itemMap = ["dataId": item, "objAPIName": timeFilterArg["objAPIName"]]
                Map objDetail = queryMasterById(itemMap, null);
                if(spilitErpObj){
                    //拆分从对象的数据
                    log.info("split detail:{}"+objDetail["data"])
                    if(objDetail["data"]["detailFieldVals"]!=null){
                        String detailData= NEED_SPLIT_ERP_OBJ[timeFilterArgApiName]["source_erp_detail_apiname"];
                        List convertObjList=objDetail["data"]["detailFieldVals"][detailData] as List;
                        List standardObJList=[]
                        convertObjList.each{detailItem ->
                            Map detailItemMap=detailItem as Map;//从对象
                            if(timeFilterArgApiName.equals("VIRContactObj")){
                                Map standardMap=[:];
                                //添加客户id.业务员id
                                String vir_account_id=objDetail["data"]["masterFieldVal"]["id"];
                                String vir_saler_id=objDetail["data"]["masterFieldVal"]["saler_id"];
                                detailItemMap.put("vir_account_id",vir_account_id);
                                if(!vir_saler_id==null){
                                  detailItemMap.put("vir_saler_id",vir_saler_id);
                                } /*else {
                                  detailItemMap.put("vir_saler_id","-10000");
                                }*/
                                standardMap.put("masterFieldVal",detailItemMap);
                                standardMap.put("objAPIName",timeFilterArgApiName);
                                standardMap.put("detailFieldVals",[:])
                                standardObJList.add(standardMap)
                            }
                        }
                        objDetail["data"]=standardObJList;
                    }
                }
                objDataList.addAll(objDetail["data"]);
            }
        }else {
            rowsData.each {item ->
                Map standardMap=[:];
                standardMap.put("objAPIName",timeFilterArgApiName);
                standardMap.put("masterFieldVal",item);
                //拼接混合的id 库存：仓库id-商品id
                String complexIdKey=JDY_OBJ_URL_MAPPING[timeFilterArg["objAPIName"]]["complexIdKey"] as String;
                if(complexIdKey!=null){
                    String[] splitIdKey= complexIdKey.split("\\|");
                    String complexIdValue="";
                    Map itemDataMap=item as Map;
                    splitIdKey.each {valueKey ->
                        String subitem=itemDataMap[valueKey] as String;
                        complexIdValue= complexIdValue.concat(subitem).concat("-")}
                    if(complexIdValue.endsWith("-")){
                        Integer endIndex= complexIdValue.lastIndexOf("-");
                        complexIdValue=complexIdValue.substring(0,endIndex);
                    }

                    itemDataMap.put(COMPLEX_ID,complexIdValue)
                }
                standardMap.put("detailFieldVals",[:])
                objDataList.add(standardMap)
            }
        }
        listData.put("totalNum", objDataList.size());
        listData.put("dataList", objDataList);

        listDataResult.put("code", returnData["content"]["errcode"]);
        listDataResult.put("data", listData);
        //拿到返回的数据id列表
        Fx.log.info("listDataResult："+Fx.json.toJson(listDataResult))
        return listDataResult;
    }

    //
    Map queryMasterById(Map map, Map connectInfo) {
        Map resultMap = [:];
        //返回的结果数据
        if(JDY_OBJ_URL_MAPPING[map["objAPIName"]]==null){
            Fx.message.throwErrorMessage("云星辰函数不支持该对象url单条重试：${map["objAPIName"]}")
        }
        String url = JDY_OBJ_URL_MAPPING[map["objAPIName"]]["queryDetailUrl"];
        //有些对象的getById接口的入参不是id.需要配置其他key
        String queryDataField = JDY_OBJ_URL_MAPPING[map["objAPIName"]]["queryIdKey"] as String;

        String dataId = map["dataId"];
        String dataNumber = map["dataNumber"];
        Map paramsMap = [:];
        if(dataId!=null){
            //有些对象不是用id当做key查询字段
            if(queryDataField!=null){
                paramsMap.put(queryDataField, dataId)
            }else{
                paramsMap.put("id", dataId)
            }
            if(url == "/jdy/v2/inv/inv_inventory_ims") {
                log.info("库存详情查询")
                String stock_id = (dataId.split("-") as List)[0] as String; //库存id
                String material_id = (dataId.split("-") as List)[1] as String; //库存id
                paramsMap.put("filter_stock_id", stock_id)
                paramsMap.put("filter_material_id", material_id)
            } else {
                //有些对象不是用id当做key查询字段
                if(queryDataField!=null){
                    paramsMap.put(queryDataField, dataId)
                }else{
                    paramsMap.put("id", dataId)
                }
            }
        }
        if(dataNumber!=null){
            paramsMap.put("number", dataNumber)
        }
        Map headersMap = [:];
        String appToken = executeGetToken();
        headersMap = getHeader(clientId, clientSecret, "GET", url, paramsMap)
        headersMap.put("app-token", appToken);
        headersMap.put("X-GW-Router-Addr", "https://tf.jdy.com");
        HttpResult returnData = GETJdy(domain + url, headersMap, paramsMap);
        //失败返回的数据
        if (returnData["statusCode"] != 200 || returnData["content"]["errcode"] != 0) {
            resultMap.put("code", returnData["statusCode"]);
            resultMap.put("message", returnData["content"]);
            return resultMap;
        }
        Map objData = returnData["content"]["data"] as Map;
        Map standardDataObjMap = [:]
        Map detailFieldVals = [:]
        // 将objData中的从对象拆解到detailFieldVals中
        List detailMap = (List) JDY_OBJ_URL_MAPPING[map["objAPIName"]]["detailObjApiName"]
        String masterId=objData.get(JDY_OBJ_URL_MAPPING[map["objAPIName"]]["masterId"])
        //填充主对象id
        detailMap.each { item ->
            List detailMapItem=objData[item] as List;
            log.info("queryMasterById - 处理从对象" + item + ": \n" + Fx.json.toJson(detailMapItem))
            detailMapItem.each{detailItem ->
                Map detailItemMap=  detailItem as Map;
                // 从对象为单据时，执行上查源单的逻辑
                String srcBillNo= detailItemMap.get("src_bill_no")
                String srcBillType = detailItemMap.get("src_bill_type_id") as String;
                if(srcBillNo != null && srcBillType != null){
                    //上查源单前先查询缓存
                    String virSalesOrderId=upQueryCacheMap.get(srcBillType+srcBillNo);
                    if(virSalesOrderId != null){
                        Fx.log.info("cache vaule："+srcBillNo+srcBillType)
                    }
                    if(virSalesOrderId == null){
                        virSalesOrderId=upQuerySalesOrderBillNo(detailItemMap)
                        log.info("更新缓存：" + srcBillType+srcBillNo + " ---- " + virSalesOrderId)
                        upQueryCacheMap.put(srcBillType+srcBillNo,virSalesOrderId,200)
                    }
                    detailItemMap.put(VIR_MASTER_ID,masterId)
                    detailItemMap.put(VIR_MASTER_ID,virSalesOrderId)
                    detailItemMap.put(VIR_MASTER_ID,virSalesOrderId)
                    objData.put(VIR_SALES_ORDER_ID,virSalesOrderId)//主对象的销售订单id
                }
            }
            detailFieldVals.put(item, objData[item])

            objData.remove(item)
        }
        standardDataObjMap.put("detailFieldVals", detailFieldVals);
        standardDataObjMap.put("masterFieldVal", objData);
        standardDataObjMap.put("objAPIName", map["objAPIName"]);
        Fx.log.info("queryMasterById result:" + Fx.json.toJson(standardDataObjMap));
        resultMap.put("code", returnData["content"]["errcode"]);
        resultMap.put("data", standardDataObjMap)
        return resultMap;
    }

    //指定的单据上查源单为销售订单的数据
    public String upQuerySalesOrderBillNo(Map objectData) {
        if(objectData[VIR_SALES_ORDER_ID]!=null){
            return objectData[VIR_SALES_ORDER_ID];
        }

        String virSalesOrderId=null;
        if (objectData != null && objectData.size() != 0) {
            if(virSalesOrderId!=null){
                return virSalesOrderId;
            }
            String srcBillNo= objectData.get("src_bill_no")
            String srcBillType = objectData.get("src_bill_type_id") as String;
            log.debug("上查源单 ---- objectData：" + Fx.json.toJson(objectData))

            log.info("上查源单 ---- SRC_BILL_TYPE_SALES_ORDER: "+srcBillType+" ---- srcBillNo: "+srcBillNo)
            Map queryUpOrderMap = [:];
            if(srcBillType==null||srcBillNo==null||srcBillType.isEmpty()||srcBillNo.isEmpty()){
                log.info("上查源单为空，暂不做处理")
                return objectData;
            }
            List queryList=[0,0,0]
            queryList.each { item ->
                queryUpOrderMap.put("dataNumber",srcBillNo)
                queryUpOrderMap.put("objAPIName", srcBillType)
                if(SRC_BILL_TYPE_SALES_ORDER.equals(srcBillType)){
                    log.info("upquery sales order info：" + Fx.json.toJson(objectData))
                    if(virSalesOrderId==null){
                        virSalesOrderId = objectData.get("src_inter_id")  // 出货单的src_inter_id就是销售订单的id，若存在就无需再上查
                        if(virSalesOrderId==null){
                            queryUpOrderMap.put("dataNumber",srcBillNo)
                            queryUpOrderMap.put("objAPIName", srcBillType)
                            log.info("上查源单，virSalesOrderId为空 ---- queryUpOrderMap：" + queryUpOrderMap)
                            Map queryData = queryMasterById(queryUpOrderMap, [:])
                            virSalesOrderId=queryData["data"]["masterFieldVal"]["id"];
                        }
                        log.info("上查时更新缓存：" + srcBillType+srcBillNo + " ---- " + virSalesOrderId)
                        upQueryCacheMap.put(srcBillType+srcBillNo,virSalesOrderId,200)
                    }
                    objectData.put(VIR_SALES_ORDER_ID, virSalesOrderId);//拿到销售订单id
                    log.info("virSalesOrderId sales order info：" + virSalesOrderId)
                    return virSalesOrderId;
                }
                if(!UP_QUERY_SALES_ORDER_FIELD.containsKey(srcBillType)){//需要配置单据的查询条件
                    log.info("upqeurySalesOrderBillNo not support srcBillType:" + srcBillType)
                    return virSalesOrderId;
                }
                log.info("上查源单 ---- queryUpOrderMap：" + queryUpOrderMap)
                Map queryData = queryMasterById(queryUpOrderMap, [:])
                if(queryData["data"]["detailFieldVals"]!=null||queryData["data"]["detailFieldVals"]["detailApiName"]!=null){
                    log.debug("upquery queryData"+Fx.json.toJson(queryData))
                    String detailApiName=UP_QUERY_SALES_ORDER_FIELD[srcBillType];
                    List detailData=queryData["data"]["detailFieldVals"][detailApiName] as List;
                    if(detailData==null){
                        return virSalesOrderId;
                    }
                    log.info("上查源单 ---- detail data: "+Fx.json.toJson(detailData))
                    Map detailDataMap=detailData[0] as Map;
                    srcBillType = detailDataMap["src_bill_type_id"];
                    srcBillNo = detailDataMap["src_bill_no"];
                    // 因为从对象是递归处理的，此处可能已经得到了销售订单id
                    virSalesOrderId = queryData["data"]["masterFieldVal"][VIR_SALES_ORDER_ID];
                    log.info("上查源单 ---- 得到VIR_SALES_ORDER_ID：" + virSalesOrderId)
                }
            }
        }
        return virSalesOrderId;
    }

    //调用erp的创建数据
    Map create(Map standardData, Map connectInfo) {
        Map resultMap = [:];//返回的结果数据
        boolean needQueryDetail = false;//创建完数据需要反查明细id
        boolean spilitErpObj = false;   //是否需要拆分erp对象

        String objApiName = standardData.get("objAPIName");
        if(NEED_SPLIT_ERP_OBJ.get(objApiName)!=null){
            Map needSplitErpMap=NEED_SPLIT_ERP_OBJ.get(objApiName) as Map;
            standardData.put("objAPIName",needSplitErpMap["source_erp_obj_apiname"]);
            spilitErpObj=true;
            log.info("需要反查主对象, " + objApiName)
        }
        String postUrl = JDY_OBJ_URL_MAPPING[standardData["objAPIName"]]["createUrl"];
        if(postUrl==null){
            Fx.message.throwErrorMessage("云星辰函数没有配置该对象url：${standardData["objAPIName"]}")
        }
        Map requestMap = [:];
        Map paramsMap = [:];
        Map headersMap = [:];
        String appToken = executeGetToken();
        headersMap = getHeader(clientId, clientSecret, "POST", postUrl, paramsMap)
        headersMap.put("app-token", appToken);
        headersMap.put("X-GW-Router-Addr", "https://tf.jdy.com");
        Map bodyMap = standardData["masterFieldVal"] as Map;

        Map detailMapList = standardData["detailFieldVals"] as Map;
        if (detailMapList != null) {
            needQueryDetail = true;
            Fx.log.info("detailMapList：" + Fx.json.toJson(detailMapList))
            detailMapList.each { item -> requestMap.put(item.getKey(), item.getValue()) }
        }

        Set originaErpDetailIds = []
        if (spilitErpObj) { //反查主对象，构造完整主从结构
            Map itemMap = ["dataId": bodyMap["vir_account_id"], "objAPIName": standardData["objAPIName"]]
            Map objDetail = queryMasterById(itemMap, null);
            if (objDetail == null || objDetail.size() == 0) {
                Fx.message.throwErrorMessage("找不到其对应的主对象：" + itemMap)
            }
            //主对象
            requestMap.put("id", objDetail["data"]["masterFieldVal"]["id"])
            requestMap.put("name", objDetail["data"]["masterFieldVal"]["name"])
            //从对象
            Map detailDataMap = objDetail["data"]["detailFieldVals"] as Map;
            Fx.log.info("objDetail: " + Fx.json.toJson(detailDataMap))

            Map needSplitErpMap=NEED_SPLIT_ERP_OBJ.get(objApiName) as Map;
            List detailMap = (List) JDY_OBJ_URL_MAPPING[standardData["objAPIName"]]["detailObjApiName"]
            //遍历从对象apiname
            detailMap.each { item ->
                List entityList = detailDataMap[item] as List
                List erpDetails = [];
                entityList.each { entityItem ->
                    String erpDetailId = entityItem["id"]
                    originaErpDetailIds.add(erpDetailId)
                    // 更新从对象
                    if (item.equals(needSplitErpMap["source_erp_detail_apiname"]) && erpDetailId.equals(bodyMap["id"])) {
                      log.info("更新从对象" + bodyMap)
                      erpDetails.add(bodyMap)
                    } else {
                      erpDetails.add(["id":erpDetailId])
                    }
                }
                if (item.equals(needSplitErpMap["source_erp_detail_apiname"]) && bodyMap["id"] == null) {
                    // 新增从对象
                    log.info("新增从对象" + bodyMap)
                    erpDetails.add(bodyMap)
                } else {
                    log.info("更新从对象" + bodyMap)
                    needQueryDetail = false
                }
                //从对象的ids
                requestMap.put(needSplitErpMap["erp_detail_list_apiname"], erpDetails)
            }
        } else {
            requestMap.putAll(bodyMap);
        }
        Fx.log.info("create ---- requestMap: " + requestMap)

        HttpResult returnData = postJdy(domain + postUrl, headersMap, paramsMap, requestMap)
        //失败返回的数据
        if (returnData["statusCode"] != 200 || returnData["content"]["errcode"] != 0) {
            resultMap.put("code", returnData["statusCode"]);
            resultMap.put("message", returnData["content"]);
            return resultMap;
        }
        List objData = returnData["content"]["data"]["ids"] as List;
        String masterDataId = objData[0];
        Map detailIds = [:]
        if (needQueryDetail) {
            Map itemMap = ["dataId": masterDataId, "objAPIName": standardData["objAPIName"]]
            Map objDetail = queryMasterById(itemMap, null);
            //从对象
            Map detailDataMap = objDetail["data"]["detailFieldVals"] as Map;
            Fx.log.info("objDetail: " + Fx.json.toJson(detailDataMap))

            List detailMap = (List) JDY_OBJ_URL_MAPPING[standardData["objAPIName"]]["detailObjApiName"]
            //遍历从对象apiname
            detailMap.each { item ->
                List entityList = detailDataMap[item] as List;
                Fx.log.info(Fx.json.toJson(entityList[0]))
                List erpDetailIds = [];
                entityList.each { entityItem ->
                    erpDetailIds.add(entityItem["id"])
                }
                //从对象的ids
                detailIds.put(item, erpDetailIds)
            }
        }

        if (spilitErpObj) {
            Map needSplitErpMap=NEED_SPLIT_ERP_OBJ.get(objApiName) as Map;
            log.info("同步后detailIds: " + detailIds)
            def l = detailIds[needSplitErpMap["source_erp_detail_apiname"]] as List
            def ids = []
            log.info("同步前originaErpDetailIds: " + originaErpDetailIds)
            l.each { v ->
              if (!originaErpDetailIds.contains(v)) {
                ids.add(v)
              }
            }
            log.info("从对象ids: " + l + " ---- 新增的ids: " + ids)
            if (ids == null || ids.size() == 0) { // 从对象更新
                masterDataId = bodyMap["id"]
            }
            if (ids.size() == 1) {
                masterDataId = ids.get(0)
            }
            if (ids.size() > 1) {
                Fx.message.throwErrorMessage("获取从对象主键时出错，只新增一个对象却得到ids：${ids}")
            }
            detailIds = null
        }
        Map dataIdMapping = [:];
        dataIdMapping.put("masterDataId", masterDataId);
        dataIdMapping.put("detailDataIds", detailIds);
        resultMap.put("data", dataIdMapping)
        resultMap.put("code", returnData["content"]["errcode"]);
        Fx.log.info("create返回数据: " + Fx.json.toJson(resultMap))
        return resultMap;
    }
    //修改erp的数据，会传递id
    Map update(Map standardData, Map connectInfo) {
        return create(standardData, connectInfo);
    }


    /**
     * POST请求数据的
     * @param url
     * @param headersMap
     * @param paramsMap
     * @return
     */

    HttpResult postJdy(String url, Map headersMap, Map paramsMap, Map bodyMap) {
        if (paramsMap != null && paramsMap.size() != 0) {
            url = url.concat("?")
            paramsMap.each { item ->
                String value = Fx.crypto.URL.encode(item.getValue().toString()).data
                url = url.concat(item.getKey().toString()).concat("=").concat(value).concat("&")
            }
            if (url.lastIndexOf("&") != -1) {
                Fx.log.info("postJdy:" + url.lastIndexOf("&"))
                url = url.substring(0, url.lastIndexOf("&"))
            }

        }
        Fx.log.info("postJdy url:\n" + url + "headersMap:\n" + headersMap + "paramsMap:\n" + paramsMap + "bodyMap:\n" + Fx.json.toJson(bodyMap))
        def (Boolean error, HttpResult result, String errorMessage) = Fx.http.post(url, headersMap, bodyMap, 10000, true, 2, false);

        Fx.log.info("postJdy error:" + error)
        Fx.log.info("postJdy errorMessage:" + errorMessage)
        Fx.log.info("postJdy result:" + result)

        if (error || result == null || result.statusCode != 200) {
           Fx.log.info("postJdy error:${result} msg:${errorMessage}")
           if (result == null) {
              def res = new HttpResult()
              res.statusCode = 504;
              res.content = "访问${url}超时：${errorMessage}";
              return res
           }
           return result;
        } else {
           Fx.log.info("GETJdy success :" + result)
           return result;
        }

        return result;
    }

    /**
     * GET请求数据的
     * @param url
     * @param headersMap
     * @param paramsMap
     * @return
     */
    HttpResult GETJdy(String url, Map headersMap, Map paramsMap) {
        if (paramsMap != null && paramsMap.size() != 0) {
            url = url.concat("?")
            paramsMap.each { item ->
                String value = Fx.crypto.URL.encode(item.getValue().toString()).data
                url = url.concat(item.getKey().toString()).concat("=").concat(value).concat("&")
            }
        }
        if (url.lastIndexOf("&") != -1) {
            url = url.substring(0, url.lastIndexOf("&"))
        }
        Fx.log.info("GETJdy url" + url + "headersMap" + headersMap + "paramsMap" + paramsMap)

        def (Boolean error, HttpResult result, String errorMessage) = Fx.http.get(url, headersMap, 10000, true, 2, false);
        Fx.log.info("GETJdy error:" + error)
        Fx.log.info("GETJdy errorMessage:" + errorMessage)
        Fx.log.info("GETJdy result:" + result)

        if (error || result == null || result.statusCode != 200) {
            Fx.log.info("GETJdy error:${result} msg:${errorMessage}")
            if (result == null) {
                def res = new HttpResult()
                res.statusCode = 504;
                res.content = "访问${url}超时：${errorMessage}";
                return res
            }
            return result;
        } else {
            //TODO 频次限制，需要重试
            Fx.log.info("GETJdy success :" + result)
            return result;
        }
    }

    /**
     * 获取最新appsecret的数据。
     * @return
     */
    String executeGetAppSecret() {
        log.info("executeGetAppSecret starting...")
        String interfacePath = "/jdyconnector/app_management/push_app_authorize";
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put(OUT_INSTANCE_KEY, OUT_INSTANCE_ID);
        String reqUrl = domain + interfacePath;
        Map<String, String> headerMap = getHeader(clientId, clientSecret, "POST", interfacePath, paramMap);
        HttpResult jdySecretResult = this.postJdy(reqUrl, headerMap, paramMap,[:]);
        //失败返回的数据
        if (jdySecretResult["statusCode"] != 200 || jdySecretResult["content"]["code"] != 200) {
            Fx.message.throwErrorMessage("调用execute token接口异常：${jdySecretResult}")
        }
        def appSecretList=jdySecretResult["content"]["data"]["appSecret"] as List
        appSecret=appSecretList[0]
        log.info("executeGetAppSecret: "+appSecretList[0])
        return appSecret;
    }


    /**
     * 获取token的数据。
     * @return
     */
    String executeGetToken() {
        Cache cache = Fx.cache.getDefaultCache();
        // cache.remove(APL_JDY_TOKEN_KEY);
        if (cache.get(APL_JDY_TOKEN_KEY) == null) {
            Fx.log.info("appSecret-------------buildTokenRequest")
            HttpResult tokenResult=buildTokenRequest();
            //"授权密钥校验失败，请获取最新密钥加密
            if( tokenResult["content"]["errcode"] != 0&&tokenResult["content"]["errcode"]==1030002006){
                executeGetAppSecret();//获取最新的appsecret
                Fx.log.info("appSecret-------------buildTokenRequest2")
                tokenResult =buildTokenRequest();
                Fx.log.info("retry tokenResult: ${tokenResult}")
                if (tokenResult["statusCode"] != 200 ) {
                    Fx.message.throwErrorMessage("调用execute token again接口异常：${tokenResult}")
                }
            }
            String token = tokenResult["content"]["data"]["app-token"] as String
            if (token != null) {
                Fx.log.info("token: " + token)
                cache.put(APL_JDY_TOKEN_KEY, token, 60 * 60 * 24)//24小时缓存
            }
            return token;
        }
        return cache.get(APL_JDY_TOKEN_KEY);

    }

    private HttpResult buildTokenRequest(){
        String interfacePath = "/jdyconnector/app_management/kingdee_auth_token";
        Map<String, Object> paramMap = Maps.newHashMap();
        Fx.log.info("appSecret-------------"+appSecret)
        String app_signature = calculateHmac(appKey, appSecret);
        paramMap.put(APP_KEY, appKey);
        paramMap.put(APP_SIGNATURE, app_signature);
        Fx.log.info("params map"+paramMap)
        String reqUrl = domain + interfacePath;
        Map<String, String> headerMap = getHeader(clientId, clientSecret, "GET", interfacePath, paramMap);

        Fx.log.info("headermap:{}"+paramMap)
        HttpResult tokenResult = this.GETJdy(reqUrl, headerMap, paramMap);
        //失败返回的数据
        if (tokenResult["statusCode"] != 200 ) {
            Fx.message.throwErrorMessage("调用execute token接口异常：${tokenResult}")

        }
        return tokenResult;
    }


    static Map<String, String> getHeader(String clientId, String clientSecret, String method, String path, Map<String, Object> params) {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("X-Api-ClientID", clientId);
        headerMap.put("X-Api-Auth-Version", "2.0");
        headerMap.put("X-Api-TimeStamp", DateTime.now().toTimestamp().toString());
        headerMap.put("X-Api-Nonce", DateTime.now().toTimestamp().toString());
        headerMap.put("X-Api-SignHeaders", "X-Api-Nonce,X-Api-TimeStamp");
        headerMap.put("X-Api-Signature",
                getSignature(method, getPathEncode(path), getQueryEncode(params), DEFAULT_SIGNHEADERS, headerMap, clientSecret));

        headerMap.put("Content-Type", "application/json");

        return headerMap;
    }

    static String getPathEncode(String path) {
        return Fx.crypto.URL.encode(path).data;
    }

    public static String getQueryEncode(Map<String, Object> querys) {
        if (querys==null) {
            return "";
        } else {
            def list = [];
            querys.entrySet().each { item ->
                def (boolean error, String resultKey, String errorMessage) = Fx.crypto.URL.encode((String) item.getKey())
                def (boolean errorValue, String resultValue, String errorMessageValue) = Fx.crypto.URL.encode((String) item.getValue())
                Fx.log.debug("${error},${errorMessage},${errorValue},${errorMessageValue}")
                list.add(resultKey + "=" + resultValue);
            }
            list.sort();
            List outList = [];
            list.each { item ->
                int index = ((String) item).indexOf("=")
                if (index >= 1) {
                    String listItem = item;
                    def (boolean error, String resultKey, String errorMessage) = Fx.crypto.URL.encode(listItem.substring(0, index))
                    def (boolean errorValue, String resultValue, String errorMessageValue) = Fx.crypto.URL.encode(listItem.substring(index + 1, listItem.length()))
                    Fx.log.debug("${error},${errorMessage},${errorValue},${errorMessageValue}")
                    outList.add(resultKey + "=" + resultValue);
                }
            }
            String rawQueryString = String.join("&", outList);
            return rawQueryString;
        }

    }


    static String getSignature(String method, String path, String query, List signatureHeaders, Map<String, String> headers, String clientSecret) {
        String signature = "";

        signature = signature.concat(method);
        signature = signature.concat("\n");
        signature = signature.concat(path);
        signature = signature.concat("\n");
        signature = signature.concat(query);
        signature = signature.concat("\n");
        List<String> var7 = signatureHeaders;
        var7.each { item ->
            signature = signature.concat(item.toLowerCase());
            signature = signature.concat(":");
            signature = signature.concat((String) headers.get(item));
            signature = signature.concat("\n");
        }
        String hmac = calculateHmac(signature.toString(), clientSecret);
        return hmac;
    }


    /**
     * 云星辰HamcSHA256，以16进制输出，再进行Base64加密工具类
     * @param appKey
     * @param appSecret
     * @return
     * @throws Exception
     */
    private static String calculateHmac(String appKey, String appSecret) throws Exception {
        Fx.log.info("appKey" + appKey)
        Fx.log.info("appSecret" + appSecret)
        byte[] hmacBytes = Fx.crypto.SHA.sha256HMAC(appSecret, appKey)
        String encodeHexValue = Fx.utils.encodeHex(hmacBytes)
        String base64Encoded = Fx.crypto.getBase64().encode(encodeHexValue.getBytes());
        return base64Encoded;
    }


    //debug 时候的入口方法
    void debug(FunctionContext context, Map arg) {

        // def param =[:];

        //   param.put("app_signature","ZTQ0MzdiNDExYWVkZDgzY2Q3NTcwYTNiOWFjYTA5N2U3MDViM2VjM2U1MGFiZWFmZDA0NmJhYzQwODhkMTIwNw==");
        //   param.put("app_key","3qBt3ABo");
        // Map<String,String> headersMap=Maps.newHashMap();
        //   headersMap.put("X-Api-Nonce","7855507040");
        //   headersMap.put("X-Api-TimeStamp","1697474428529");
        // String data= getSignature("GET","%2Fjdyconnector%2Fapp_management%2Fkingdee_auth_token","app_key=3qBt3ABo&app_signature=ZmMwNzQyZjAzZjUxNzdiZmRlZjdmY2M0NDczMDkxNGMyZjkzZTI2NGJkN2RlNDY3YWYyMGQzMzBmODAzZDE3MA%253D%253D",DEFAULT_SIGNHEADERS,headersMap,"46d02b40c25a0784bba9708fa9b474ec");
        // String url="https://api.kingdee.com/jdy/v2/bd/material";
        // def param =[:];
        // param.put("page","1");
        // param.put("page_size","200");
        // String appToken=executeGetToken();
        // Map headersMap=getHeader("242159","46d02b40c25a0784bba9708fa9b474ec","GET","/jdy/v2/bd/material",param)
        // headersMap.put("app-token",appToken);
        // headersMap.put("X-GW-Router-Addr","https://tf.jdy.com");
        // def data=GETJdy(url,headersMap,param)
        // Fx.log.info(data)
        // def page = 0 / 100;
        // Fx.log.info(page)
        // executeGetAppSecret();
        // Map queryDataMap=[:]
        // queryDataMap.put("src_bill_type_id","ar_creditbill")
        // queryDataMap.put("src_bill_no","SKD-20231119-00001")
        // upqeurySalesOrderBillNo(queryDataMap)
        // log.info("upqeurySalesOrderBillNo queryDataMap"+queryDataMap)
        // String sales_id="";
        // log.info(sales_id.isEmpty())
        // String data="stock_id|material_id";
        // String[] dataList=data.split("\\|")
        // log.info(Fx.json.toJson(dataList))
        // Map timeFilterArg = ["startTime": "1700409600000", "endTime": "1700496000000", "objAPIName": "ar_creditbill", "limit": "100", "offset": "0"]
        // def data=queryMasterBatch(timeFilterArg, null)
        // Fx.log.info(data)
        // Map JDY_OBJ_URL_MAPPING = ["material":["jdyObjectApiName":"material"]]
        // def data=JDY_OBJ_URL_MAPPING["material"]["jdyObjectApiName"]
        // Map masterFieldVal = ["customer_id": "1",];
        // Map detailValue = ["material_id": "1798744514349767680", "qty": "19", "price": "19", "unit_id": "1"]
        // List detailList = [];
        // detailList.add(detailValue)
        // Map detailValues = ["material_entity": detailList]

        // Map standardData = ["masterFieldVal": masterFieldVal, "detailFieldVals": detailValues, "objAPIName": "sal_order"]

        // Map data = createErpObjData(standardData, null)
        // Fx.log.info("standardData:" + Fx.json.toJson(data))
        // def data="{\"kf_period\":\"0\",\"is_serial\":false,\"is_subpart\":true,\"aux_unit_name\":\"cm\",\"check_type\":\"1\",\"aux_entity\":[],\"net_weight\":\"0\",\"kf_period_type\":\"\",\"tax_rate\":\"0\",\"number\":\"SP00002\",\"refistration_number\":\"\",\"weight_unit_id\":\"\",\"is_batch\":false,\"high\":\"0\",\"barcode_entity\":[{\"barcode_remark\":\"000000000001\",\"id\":\"1798030217659316225\",\"barcode_unit_name\":\"cm\",\"barcode\":\"000000000001\",\"barcode_unit_number\":\"cm\",\"barcode_unit_id\":\"9\"}],\"price_entity\":[{\"price_barcode\":\"000000000001\",\"price_near_sal_price\":\"0\",\"price_trade_price\":\"0\",\"price_min_sales_price\":\"0\",\"price_retail_price\":\"0\",\"price_near_sal_tax_price\":\"0\",\"price_near_pur_tax_price\":\"0\",\"price_aux_name5\":\"\",\"price_aux_number1\":\"\",\"price_aux_name4\":\"\",\"price_aux_id5\":\"\",\"price_aux_id4\":\"\",\"price_near_pur_unit_cost\":\"0\",\"price_sale_price10\":\"0\",\"price_aux_id1\":\"\",\"price_near_pur_price\":\"0\",\"price_aux_id3\":\"\",\"id\":\"1798030217659317250\",\"price_aux_id2\":\"\",\"price_aux_name1\":\"\",\"price_aux_number4\":\"\",\"price_unit_id\":\"9\",\"price_aux_number5\":\"\",\"price_aux_number2\":\"\",\"price_aux_name3\":\"\",\"price_aux_name2\":\"\",\"price_aux_number3\":\"\",\"price_purchase_price\":\"0\",\"price_sale_price9\":\"0\",\"price_sale_price8\":\"0\",\"price_sale_price7\":\"0\",\"price_min_pur_tax_price\":\"0\",\"price_distribution_price\":\"0\",\"price_cost_price\":\"0\",\"price_outsourceprice\":\"0\",\"price_max_purchase_price\":\"0\",\"price_unit_number\":\"cm\",\"price_unit_name\":\"cm\",\"price_aux_id\":\"\",\"price_sale_price2\":\"0\",\"price_near_supplier\":\"\",\"price_sale_price1\":\"0\",\"price_sale_price6\":\"0\",\"price_sale_price5\":\"0\",\"price_sale_price4\":\"0\",\"price_sale_price3\":\"0\"}],\"enable\":\"1\",\"model\":\"\",\"is_multi_unit\":false,\"id\":\"1798030217659316224\",\"gross_weight\":\"0\",\"barcode\":\"000000000001\",\"is_sale\":true,\"images\":[],\"aux_unit_number\":\"cm\",\"purchase_id\":\"\",\"purchase_unit_id\":\"9\",\"brand_id\":\"\",\"is_kf_period\":false,\"volume\":\"0\",\"producing_pace\":\"\",\"is_assembly\":true,\"name\":\"测试001\",\"coefficient3\":0,\"coefficient2\":0,\"coefficient1\":0,\"pro_license\":\"\",\"remark\":\"\",\"fetch_category_id\":\"\",\"mul_label\":[],\"vender_id\":\"\",\"sale_unit_id\":\"9\",\"parent_number\":\"\",\"inv_mgr_type\":\"0\",\"base_unit_name\":\"cm\",\"is_weight\":false,\"cost_method\":\"1\",\"conversion_unit_id1\":\"\",\"conversion_unit_id2\":\"\",\"conversion_unit_id3\":\"\",\"fix_unit_id2\":\"\",\"fix_unit_id3\":\"\",\"fix_unit_id1\":\"\",\"max_inventory_qty\":\"0\",\"custom_field\":{},\"wide\":\"0\",\"help_code\":\"cs001\",\"length\":\"0\",\"base_unit_id\":\"9\",\"store_unit_id\":\"9\",\"stock_id\":\"\",\"is_purchase\":true,\"alarm_day\":\"0\",\"aux_unit_id\":\"9\",\"min_inventory_qty\":\"0\",\"sec_inventory_qty\":\"0\",\"space_id\":\"\",\"is_asst_attr\":false}";

        // Map objData=Fx.json.parse(data)
        // Map standardDataObjMap=[:]
        // Map detailFieldVals=[:]


        Map erpIdArg = ["dataId": "1821922871434309632", "objAPIName": "ar_creditbill"]
        def getObjData=queryMasterById(erpIdArg,null)
        Fx.log.info(getObjData)
        // List detailMap=(List)JDY_OBJ_URL_MAPPING["material"]["detailObjApiName"]


        // detailMap.each{item ->
        // detailFieldVals.put(item,objData[item])
        // objData.remove(item)
        //   }

        //   standardDataObjMap.put("detailFieldVals",detailFieldVals);
        //   standardDataObjMap.put("masterFieldVal",objData);
        //   standardDataObjMap.put("objAPIName","material");
        //   Fx.log.info(Fx.json.toJson(standardDataObjMap));
        // def type = "JDYQueryInfoService";
        // def params = [:];
        // def argData = ["type":type,"params":Fx.json.toJson(params)];
        // def ret = Fx.proxy.callAPI("erp.syncData.executeCustomFunction", [:], argData);
        // log.info(ret)
        // Map jdyInfoAPP=ret as Map;

    }

}
