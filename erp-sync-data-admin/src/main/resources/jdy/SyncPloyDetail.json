[{"createTime": 1700478382522, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "WarehouseObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [], "detailObjectSyncConditions": [], "fieldMappings": [{"destApiName": "is_enable", "destType": "select_one", "mappingType": 1, "optionMappings": [{"destOption": "2", "sourceOption": "0"}, {"destOption": "1", "sourceOption": "1"}], "sourceApiName": "enable", "sourceType": "select_one", "value": ""}, {"destApiName": "number", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "number", "sourceType": "text", "value": ""}, {"destApiName": "owner", "destType": "employee", "mappingType": 2, "optionMappings": [], "sourceApiName": "storekeeper_id", "sourceType": "employee", "value": "storekeeper_id"}, {"destApiName": "is_default", "destType": "true_or_false", "mappingType": 3, "optionMappings": [], "value": "false", "valueType": 1}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "name", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "name", "sourceType": "text", "value": ""}], "id": "6565a5275541d10001cce4d8", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"WarehouseObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "bd_store_1hfjn3p7g", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 2, "syncConditions": {"apiName": "bd_store_1hfjn3p7g", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "0/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": *************}, {"createTime": *************, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "AccountObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [], "detailObjectSyncConditions": [], "fieldMappings": [{"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "name", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "name", "sourceType": "text", "value": ""}, {"destApiName": "remark", "destType": "long_text", "mappingType": 1, "optionMappings": [], "sourceApiName": "remark", "sourceType": "text", "value": ""}, {"destApiName": "address", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "addr", "sourceType": "text", "value": ""}, {"destApiName": "owner", "destType": "employee", "mappingType": 3, "optionMappings": [], "value": "1000", "valueType": 1}], "id": "6565b4255541d10001cceac7", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"AccountObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "bd_customer_1hdj35l42", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 2, "syncConditions": {"apiName": "bd_customer_1hdj35l42", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "1/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "limitValues": [], "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": *************}, {"createTime": *************, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "ProductObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [], "detailObjectSyncConditions": [], "fieldMappings": [{"destApiName": "owner", "destType": "employee", "mappingType": 3, "optionMappings": [], "value": "1000", "valueType": 1}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "name", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "name", "sourceType": "text", "value": ""}, {"destApiName": "product_category_id", "destTargetApiName": "ProductCategoryObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "parent_number", "sourceTargetApiName": "bd_materialgroup_1hdgnds3j", "sourceType": "object_reference", "value": ""}], "id": "6565b4255541d10001cceac8", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"ProductObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "bd_material_1hdghefj2", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 2, "syncConditions": {"apiName": "bd_material_1hdghefj2", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "0/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "limitValues": [], "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": 1701164965060}, {"createTime": 1700560865936, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "ContactObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [], "detailObjectSyncConditions": [], "fieldMappings": [{"destApiName": "name", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "contact_person", "sourceType": "text", "value": ""}, {"destApiName": "owner", "destType": "employee", "mappingType": 2, "optionMappings": [], "sourceApiName": "vir_saler_id", "sourceType": "employee", "value": "vir_saler_id"}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "account_id", "destTargetApiName": "AccountObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "vir_account_id", "sourceTargetApiName": "bd_customer_1hdj35l42", "sourceType": "object_reference", "value": ""}, {"destApiName": "email", "destType": "email", "mappingType": 1, "optionMappings": [], "sourceApiName": "email", "sourceType": "email", "value": ""}, {"destApiName": "tel", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "mobile", "sourceType": "phone_number", "value": ""}, {"destApiName": "add", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "contact_address", "sourceType": "text", "value": ""}], "id": "ce175b44f283487eb09a039e5a80bf9f", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"ContactObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "VIRContactObj_1hfjmern1", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 2, "syncConditions": {"apiName": "VIRContactObj_1hfjmern1", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "3/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "limitValues": [], "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": 1701170069037}, {"createTime": 1700635954868, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "StockObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [], "detailObjectSyncConditions": [], "fieldMappings": [{"destApiName": "name", "destType": "auto_number", "mappingType": 1, "optionMappings": [], "sourceApiName": "complexId", "sourceType": "id", "value": ""}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "owner", "destType": "employee", "mappingType": 3, "optionMappings": [], "value": "1000", "valueType": 1}, {"destApiName": "product_id", "destTargetApiName": "ProductObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "material_id", "sourceTargetApiName": "bd_material_1hdghefj2", "sourceType": "object_reference", "value": ""}, {"destApiName": "real_stock", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "qty", "sourceType": "number", "value": ""}, {"destApiName": "available_stock", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "valid_qty", "sourceType": "number", "value": ""}, {"destApiName": "warehouse_id", "destTargetApiName": "WarehouseObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "stock_id", "sourceTargetApiName": "bd_store_1hfjn3p7g", "sourceType": "object_reference", "value": ""}], "id": "6565b4255541d10001cceac4", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"StockObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "inv_inventory_entity_1hfk4qcq9", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 2, "syncConditions": {"apiName": "inv_inventory_entity_1hfk4qcq9", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "4/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "limitValues": [], "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": 1700720578060}, {"createTime": 1698147429088, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "ProductCategoryObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [], "detailObjectSyncConditions": [], "fieldMappings": [{"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "code", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "id", "sourceType": "text", "value": ""}, {"destApiName": "category_code", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "number", "sourceType": "id", "value": ""}, {"destApiName": "name", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "name", "sourceType": "text", "value": ""}, {"destApiName": "order_field", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "level", "sourceType": "number", "value": ""}, {"destApiName": "pid", "destTargetApiName": "ProductCategoryObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "parent_id", "sourceTargetApiName": "bd_materialgroup_1hdgnds3j", "sourceType": "object_reference", "value": ""}], "id": "8d59ec7be9cd4f58a98acbcdcfdadf2f", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"ProductCategoryObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "bd_materialgroup_1hdgnds3j", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 2, "syncConditions": {"apiName": "bd_materialgroup_1hdgnds3j", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "3/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "limitValues": [], "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": 1700722072044}, {"createTime": 1701171617910, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "PaymentObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [{"destObjectApiName": "OrderPaymentObj", "fieldMappings": [{"destApiName": "payment_id", "destTargetApiName": "PaymentObj", "destType": "master_detail", "mappingType": 1, "sourceApiName": "vir_master_id", "sourceTargetApiName": "ar_creditbill_1hgar1hfu", "sourceType": "master_detail"}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "payment_amount", "destType": "currency", "mappingType": 1, "optionMappings": [], "sourceApiName": "cur_settle_amount", "sourceType": "currency", "value": ""}, {"destApiName": "name", "destType": "auto_number", "mappingType": 1, "optionMappings": [], "sourceApiName": "id", "sourceType": "id", "value": ""}, {"destApiName": "order_id", "destTargetApiName": "SalesOrderObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "vir_sales_order_id", "sourceTargetApiName": "sal_bill_order_1hdj9h07p", "sourceType": "object_reference", "value": ""}], "sourceObjectApiName": "ar_creditbill_1hgar1hh0"}], "detailObjectSyncConditions": [{"apiName": "ar_creditbill_1hgar1hh0", "filters": [], "isSyncForce": true}], "fieldMappings": [{"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "payment_time", "destType": "date", "mappingType": 1, "optionMappings": [], "sourceApiName": "bill_date", "sourceType": "date", "value": ""}, {"destApiName": "owner", "destType": "employee", "mappingType": 2, "optionMappings": [], "sourceApiName": "emp_id", "sourceType": "employee", "value": "emp_id"}, {"destApiName": "amount", "destType": "currency", "mappingType": 1, "optionMappings": [], "sourceApiName": "current_total_instant_amount", "sourceType": "currency", "value": ""}, {"destApiName": "account_id", "destTargetApiName": "AccountObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "customer_id", "sourceTargetApiName": "bd_customer_1hdj35l42", "sourceType": "object_reference", "value": ""}, {"destApiName": "name", "destType": "auto_number", "mappingType": 1, "optionMappings": [], "sourceApiName": "id", "sourceType": "id", "value": ""}], "id": "36df912072b2463d816479f15a641d87", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NeedReturnFieldApiName": {"PaymentObj": [], "OrderPaymentObj": []}, "objApiName2NotUpdateFieldApiName": {"PaymentObj": [], "OrderPaymentObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "ar_creditbill_1hgar1hfu", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 1, "syncConditions": {"apiName": "ar_creditbill_1hgar1hfu", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "1/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "limitValues": [], "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": 1701433015722}, {"createTime": 1700725943673, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "DeliveryNoteObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [{"destObjectApiName": "DeliveryNoteProductObj", "fieldMappings": [{"destApiName": "delivery_note_id", "destTargetApiName": "DeliveryNoteObj", "destType": "master_detail", "mappingType": 1, "sourceApiName": "vir_master_id", "sourceTargetApiName": "sal_bill_outbound_1hfr09f86", "sourceType": "master_detail"}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "name", "destType": "auto_number", "mappingType": 1, "optionMappings": [], "sourceApiName": "id", "sourceType": "id", "value": ""}, {"destApiName": "product_id", "destTargetApiName": "ProductObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "material_id", "sourceTargetApiName": "bd_material_1hdghefj2", "sourceType": "object_reference", "value": ""}, {"destApiName": "sales_order_id", "destTargetApiName": "SalesOrderObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "vir_sales_order_id", "sourceTargetApiName": "sal_bill_order_1hdj9h07p", "sourceType": "object_reference", "value": ""}, {"destApiName": "sales_order_product_id", "destTargetApiName": "SalesOrderProductObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "src_entry_id", "sourceTargetApiName": "sal_bill_order_1hdj9ifgk", "sourceType": "object_reference", "value": ""}, {"destApiName": "delivery_num", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "qty", "sourceType": "number", "value": ""}], "sourceObjectApiName": "sal_bill_outbound_1hfr0in82"}], "detailObjectSyncConditions": [{"apiName": "sal_bill_outbound_1hfr0in82", "filters": [], "isSyncForce": true}], "fieldMappings": [{"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "owner", "destType": "employee", "mappingType": 3, "optionMappings": [], "value": "1000", "valueType": 1}, {"destApiName": "sales_order_id", "destTargetApiName": "SalesOrderObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "vir_sales_order_id", "sourceTargetApiName": "sal_bill_order_1hdj9h07p", "sourceType": "object_reference", "value": ""}], "id": "7bf85389ad18404b90030905d31ee44b", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"DeliveryNoteProductObj": [], "DeliveryNoteObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "sal_bill_outbound_1hfr09f86", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 1, "syncConditions": {"apiName": "sal_bill_outbound_1hfr09f86", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "0/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": 1701433122214}, {"createTime": 1698234107268, "destDataCenterId": "643f7326b54ea8000176a191", "destObjectApiName": "SalesOrderObj", "destTenantIds": ["88521"], "destTenantType": 1, "detailObjectMappings": [{"destObjectApiName": "SalesOrderProductObj", "fieldMappings": [{"destApiName": "order_id", "destTargetApiName": "SalesOrderObj", "destType": "master_detail", "mappingType": 1, "sourceApiName": "vir_master_id", "sourceTargetApiName": "sal_bill_order_1hdj9h07p", "sourceType": "master_detail"}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "product_price", "destType": "currency", "mappingType": 1, "optionMappings": [], "sourceApiName": "amount", "sourceType": "number", "value": "", "valueType": 1}, {"destApiName": "quantity", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "qty", "sourceType": "number", "value": ""}, {"destApiName": "product_id", "destTargetApiName": "ProductObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "material_id", "sourceTargetApiName": "bd_material_1hdghefj2", "sourceType": "object_reference", "value": ""}, {"destApiName": "name", "destType": "auto_number", "mappingType": 1, "optionMappings": [], "sourceApiName": "id", "sourceType": "id", "value": ""}, {"destApiName": "sales_price", "destType": "currency", "mappingType": 1, "optionMappings": [], "sourceApiName": "tax_price", "sourceType": "currency", "value": ""}, {"destApiName": "subtotal", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "all_amount", "sourceType": "currency", "value": ""}], "sourceObjectApiName": "sal_bill_order_1hdj9ifgk"}], "detailObjectSyncConditions": [{"apiName": "sal_bill_order_1hdj9ifgk", "filters": [], "isSyncForce": true}], "fieldMappings": [{"destApiName": "owner", "destType": "employee", "mappingType": 3, "optionMappings": [], "value": "1000", "valueType": 1}, {"destApiName": "record_type", "destType": "record_type", "mappingType": 3, "optionMappings": [], "value": "default__c", "valueType": 1}, {"destApiName": "name", "destType": "auto_number", "mappingType": 1, "optionMappings": [], "sourceApiName": "bill_no", "sourceType": "text", "value": ""}, {"destApiName": "account_id", "destTargetApiName": "AccountObj", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "customer_id", "sourceTargetApiName": "bd_customer_1hdj35l42", "sourceType": "object_reference", "value": ""}, {"destApiName": "order_time", "destType": "date", "mappingType": 1, "optionMappings": [], "sourceApiName": "bill_date", "sourceType": "date", "value": ""}], "id": "6565a5275541d10001cce4db", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"SalesOrderProductObj": [], "SalesOrderObj": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "653623d5b4660c00014c129a", "sourceObjectApiName": "sal_bill_order_1hdj9h07p", "sourceTenantIds": ["88521"], "sourceTenantType": 2, "status": 1, "syncConditions": {"apiName": "sal_bill_order_1hdj9h07p", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"cronExpression": "3/6 * * * *", "dayLimitType": "EVERY_DAY", "endDataTime": "23:59", "intervalQuantity": 6, "limitValues": [], "startDataTime": "00:00", "timeUnit": "minutes"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": 1701433258150}, {"createTime": 1698227195230, "destDataCenterId": "653623d5b4660c00014c129a", "destObjectApiName": "bd_customer_1hdj35l42", "destTenantIds": ["88521"], "destTenantType": 2, "detailObjectMappings": [], "detailObjectSyncConditions": [], "fieldMappings": [{"destApiName": "name", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "name", "sourceType": "text", "value": ""}, {"destApiName": "remark", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "remark", "sourceType": "long_text", "value": ""}, {"destApiName": "number", "destType": "id", "mappingType": 1, "optionMappings": [], "sourceApiName": "account_no", "sourceType": "auto_number", "value": ""}], "id": "e4daa957e5a54e23b606be1bbc71d619", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"bd_customer_1hdj35l42": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "643f7326b54ea8000176a191", "sourceObjectApiName": "AccountObj", "sourceTenantIds": ["88521"], "sourceTenantType": 1, "status": 2, "syncConditions": {"apiName": "AccountObj", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 7], "pollingInterval": {"dayLimitType": "EVERY_DAY", "limitValues": [], "startDataTime": "00:00", "timeUnit": "once"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": *************}, {"createTime": *************, "destDataCenterId": "653623d5b4660c00014c129a", "destObjectApiName": "sal_bill_order_1hdj9h07p", "destTenantIds": ["88521"], "destTenantType": 2, "detailObjectMappings": [{"destObjectApiName": "sal_bill_order_1hdj9ifgk", "fieldMappings": [{"destApiName": "vir_master_id", "destTargetApiName": "sal_bill_order_1hdj9h07p", "destType": "master_detail", "mappingType": 1, "sourceApiName": "order_id", "sourceTargetApiName": "SalesOrderObj", "sourceType": "master_detail"}, {"destApiName": "qty", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "quantity", "sourceType": "number", "value": ""}, {"destApiName": "material_id", "destTargetApiName": "bd_material_1hdghefj2", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "product_id", "sourceTargetApiName": "ProductObj", "sourceType": "object_reference", "value": ""}, {"destApiName": "id", "destType": "id", "mappingType": 1, "optionMappings": [], "sourceApiName": "_id", "sourceType": "text", "value": ""}, {"destApiName": "amount", "destType": "number", "mappingType": 1, "optionMappings": [], "sourceApiName": "sales_price", "sourceType": "currency", "value": ""}, {"destApiName": "price", "destType": "currency", "mappingType": 1, "optionMappings": [], "sourceApiName": "product_price", "sourceType": "currency", "value": ""}], "sourceObjectApiName": "SalesOrderProductObj"}], "detailObjectSyncConditions": [{"apiName": "SalesOrderProductObj", "filters": [], "isSyncForce": true}], "fieldMappings": [{"destApiName": "bill_no", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "name", "sourceType": "auto_number", "value": ""}, {"destApiName": "dispatcher_address", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "ship_to_add", "sourceType": "text", "value": ""}, {"destApiName": "cost_fee", "destType": "text", "mappingType": 1, "optionMappings": [], "sourceApiName": "order_amount", "sourceType": "currency", "value": ""}, {"destApiName": "customer_id", "destTargetApiName": "bd_customer_1hdj35l42", "destType": "object_reference", "mappingType": 1, "optionMappings": [], "sourceApiName": "account_id", "sourceTargetApiName": "AccountObj", "sourceType": "object_reference", "value": ""}], "id": "bb1a8655b7cb4038b41747f9875c849b", "integrationStreamName": "未命名集成流", "integrationStreamNodes": {"objApiName2NotUpdateFieldApiName": {"sal_bill_order_1hdj9ifgk": [], "sal_bill_order_1hdj9h07p": []}, "syncConditionsQueryDataNode": {}}, "isValid": true, "sourceDataCenterId": "643f7326b54ea8000176a191", "sourceObjectApiName": "SalesOrderObj", "sourceTenantIds": ["88521"], "sourceTenantType": 1, "status": 2, "syncConditions": {"apiName": "SalesOrderObj", "filters": [], "isSyncForce": true}, "syncPloyId": "88521", "syncRules": {"events": [1, 2, 3, 5, 7], "pollingInterval": {"dayLimitType": "EVERY_DAY", "limitValues": [], "startDataTime": "00:00", "timeUnit": "once"}, "syncDependForce": false, "syncType": "get", "syncTypeList": ["get"]}, "tenantId": "88521", "updateTime": *************}]