<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.admin"/>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxUploadSize" value="104857600"/>
        <property name="maxInMemorySize" value="40960"/>
    </bean>

    <import resource="classpath:spring/license-client.xml"/>

    <!--  智能客服推荐解决方案  -->
    <import resource="classpath:spring/eservice-rest.xml"/>

    <bean class="com.sharecrm.egress.sdk.config.EgressApiSdkConfig"></bean>

    <!--更改集-->
    <import resource="classpath*:spring/fs-change-set-api.xml"/>
</beans>