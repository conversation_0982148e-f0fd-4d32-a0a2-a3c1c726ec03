package com.fxiaoke.open.erpsyncdata.admin.task;

import com.fxiaoke.open.erpsyncdata.admin.result.CheckQuotaResult;
import com.fxiaoke.open.erpsyncdata.admin.service.SyncQuotaService;
import com.fxiaoke.open.erpsyncdata.admin.utils.SyncQuotaHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-11-10
 */
@Slf4j
public class SyncQuotaTask extends LocalSchedulingTask {
    @Autowired
    private SyncQuotaService syncQuotaService;


    public SyncQuotaTask() {
        super("check quota", SyncQuotaHelper.LAZY_HOLDER.checkQuotaTaskCron);
        log.info("init bean syncQuotaTask");
    }


    /**
     *
     */
    @SneakyThrows
    @Override
    public void runTask() {
        //不发送告警
        Result<List<CheckQuotaResult>> checkQuotaResult = syncQuotaService.checkQuotaTask(null, false, true, true,null);
        log.info("check quota task end,result:{}", checkQuotaResult);
    }
}
