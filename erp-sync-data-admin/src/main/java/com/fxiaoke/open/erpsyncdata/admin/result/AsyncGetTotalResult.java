package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class AsyncGetTotalResult implements Serializable {
    @ApiModelProperty("数据总条数")
    private int totalCount;
    @ApiModelProperty("是否成功获取到总数")
    private Boolean isSuccess;
    @ApiModelProperty("获取数据总条数任务id")
    private String taskId;
}
