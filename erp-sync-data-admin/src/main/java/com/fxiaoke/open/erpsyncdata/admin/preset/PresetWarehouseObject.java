package com.fxiaoke.open.erpsyncdata.admin.preset;

import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailCreateArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PresetWarehouseObject extends AbstractPresetObject {
    @Override
    protected List<String> getFormId() {
        return Lists.newArrayList(K3CloudForm.BD_STOCK);
    }

    @Override
    public List<String> getFieldMappingJson() {
        String json = "{\"id\":\"\",\"masterObjectMapping\":{\"sourceObjectApiName\":\"BD_STOCK.BillHead\",\"destObjectApiName\":\"WarehouseObj\",\"fieldMappings\":[{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":false,\"optionMappings\":[],\"destApiName\":\"is_default\",\"destType\":\"true_or_false\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"1000\",\"optionMappings\":[],\"destApiName\":\"owner\",\"destType\":\"employee\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"1\",\"optionMappings\":[],\"destApiName\":\"is_enable\",\"destType\":\"select_one\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceType\":\"id\",\"destApiName\":\"number\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceType\":\"text\",\"destApiName\":\"name\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parentWarehouseNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_STOCK.BillHead\",\"destApiName\":\"parent_warehouse\",\"destType\":\"object_reference\",\"destTargetApiName\":\"WarehouseObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[{\"sourceOption\":\"false\",\"destOption\":false},{\"sourceOption\":\"true\",\"destOption\":true}],\"sourceApiName\":\"isLoc\",\"sourceType\":\"true_or_false\",\"destApiName\":\"whether_position\",\"destType\":\"true_or_false\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAddress\",\"sourceType\":\"long_text\",\"destApiName\":\"address\",\"destType\":\"text\"}]},\"detailObjectMappings\":[]}";
        return Lists.newArrayList(json);
    }

    @Override
    public List<SyncPloyDetailCreateArg> getSyncPloyDetailCreateArg() {
        SyncPloyDetailCreateArg arg = new SyncPloyDetailCreateArg();
        arg.setPloyId(tenantId);
        arg.setSourceTenantIds(Lists.newArrayList(tenantId));
        arg.setSourceTenantType(TenantType.ERP);
        arg.setSourceObjectApiName("BD_STOCK.BillHead");
        arg.setDestTenantIds(Lists.newArrayList(tenantId));
        arg.setDestTenantType(TenantType.CRM);
        arg.setDestObjectApiName("WarehouseObj");
        arg.setDcId(dataCenterId);
        return Lists.newArrayList(arg);
    }
}
