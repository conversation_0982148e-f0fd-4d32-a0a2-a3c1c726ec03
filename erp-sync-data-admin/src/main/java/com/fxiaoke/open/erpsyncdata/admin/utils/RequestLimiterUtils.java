package com.fxiaoke.open.erpsyncdata.admin.utils;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import lombok.Data;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class RequestLimiterUtils {

    private static final TimedCache<String, Counter> countersMap = CacheUtil.newTimedCache(1000 * 60 * 5);//为防止对象随企业越来越多，设置过期时间

    static {
        //定时清理，不然超时后不会自动清理。10分钟
        countersMap.schedulePrune(TimeUnit.MINUTES.toMillis(10));
    }

    public static boolean isAllowed(String tenantId, int limit, int interval) {
        long currentTime = System.currentTimeMillis();
        if (!countersMap.containsKey(tenantId)) {
            initCounter(tenantId);
        }
        Counter counter = countersMap.get(tenantId);
        if (counter != null) {
            if (currentTime - counter.getLastResetTime() >= interval) {
                // 如果已经过了interval，则重置计数器和时间
                counter.getCounter().set(0);
                counter.setLastResetTime(currentTime);
            }
            return counter.getCounter().incrementAndGet() <= limit;
        }
        return false;
    }

    private static synchronized void initCounter(String tenantId) {
        if (!countersMap.containsKey(tenantId)) {
            countersMap.put(tenantId, new Counter());
        }
    }

    @Data
    private static class Counter {
        private AtomicInteger counter = new AtomicInteger(0);
        private Long lastResetTime = System.currentTimeMillis();
    }

}
