package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 9:40 2020/10/14
 * @Desc:
 */
@Data
@Accessors(chain = false)
public class ObjectDataMappingExcelVo {

    /**
     * 源数据id
     */
    @ExcelProperty(value = "erpdss.global.global.s69",index = 0)
    private String sourceDataId;

    /**
     * 源数据name
     */
    @ExcelProperty(value = "erpdss.global.global.s1034",index = 1)
    private String sourceDataName;

    /**
     * 目标数据id
     */
    @ExcelProperty(value = "erpdss.global.global.s71",index = 2)
    private String destDataId;

    /**
     * 目标数据name
     */
    @ExcelProperty(value = "erpdss.global.global.s1035",index = 3)
    private String destDataName;

    /**
     * 主对象源数据id
     */
    @ExcelProperty(value = "erpdss.global.global.s1036",index = 4)
    private String masterDataId;

    /**
     * 备注
     */
    @ExcelProperty(value = "erpdss.global.global.s19",index = 5)
    private String remark;

}
