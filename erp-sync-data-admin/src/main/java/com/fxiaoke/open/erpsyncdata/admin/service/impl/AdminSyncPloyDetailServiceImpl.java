package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmFieldTypeContants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperationTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.data.CheckAndUpdatePloyValidStatusDetailData;
import com.fxiaoke.open.erpsyncdata.admin.data.ObjectApiNameData;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSkuSpuProcessManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.IntegrationStreamManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.SwitchStreamStatusManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.SyncPloyDetailAdminManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.dataScreen.BIManager;
import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.admin.service.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorConfigHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CheckStreamEnableArg;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.model.TriggerConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.REDIS_KEY_EXCEPTION_PLOY_DETAIL;

@Slf4j
@Service
public class AdminSyncPloyDetailServiceImpl implements AdminSyncPloyDetailService {
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @ReloadableProperty("eip.type.mapping.white.list")
    private String typeMappingWhiteListJson;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private AdminSkuSpuProcessManager adminSkuSpuProcessManager;
    @Autowired
    private InitLastSyncTimeService initLastSyncTimeService;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private SwitchStreamStatusManager switchStreamStatusManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private SyncPloyManager syncPloyManager;
    @Autowired
    private SyncPloyDetailAdminManager syncPloyDetailAdminManager;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;
    @Autowired
    private SyncQuotaService syncQuotaService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private IntegrationStreamManager integrationStreamManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;
    @Autowired
    private BIManager biManager;
    @Autowired
    private ErpConnectInfoDao connectInfoDao;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    private final Set<String> K3ChannelIDField = Sets.newHashSet("FNumber", "Number", "FBillNo");
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;

    @Override
    public Result<ListDistinctApiNameByTypeResult> listDistinctApiNamesByType(String tenantId, String dataCenterId, Integer type, Integer status, String apiName, Integer pageNumber, Integer pageSize) {
        List<String> apiNames = null;
        int totalCount = 0;
        List<String> erpFakeObjApiNames = getErpFakeObjApiNames(tenantId, dataCenterId);
        if (CollectionUtils.isEmpty(erpFakeObjApiNames)) {
            ListDistinctApiNameByTypeResult result = new ListDistinctApiNameByTypeResult();
            result.setPageNumber(pageNumber);
            result.setPageSize(pageSize);
            result.setTotalCount(totalCount);
            result.setObjectApiNameDataList(Lists.newArrayList());
            return Result.newSuccess(result);
        }
        if (SyncPloyTypeEnum.INPUT.getType().equals(type)) {
            //数据在向我输入的时候，我是目标企业
            apiNames = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listDistinctApiNamesByDestTenantId(tenantId, erpFakeObjApiNames, status, apiName, (pageNumber - 1) * pageSize, pageSize);
            totalCount = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .countDistinctApiNamesByDestTenantId(tenantId, erpFakeObjApiNames, apiName, status);
        }
        if (SyncPloyTypeEnum.OUTPUT.getType().equals(type)) {
            //由我输出的时候，我是源企业
            apiNames = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listDistinctApiNamesBySourceTenantId(tenantId, erpFakeObjApiNames, status, apiName, (pageNumber - 1) * pageSize, pageSize);
            totalCount = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .countDistinctApiNamesBySourceTenantId(tenantId, erpFakeObjApiNames, apiName, status);
        }
        ListDistinctApiNameByTypeResult result = new ListDistinctApiNameByTypeResult();
        result.setPageNumber(pageNumber);
        result.setPageSize(pageSize);
        result.setTotalCount(totalCount);
        if (!CollectionUtils.isEmpty(apiNames)) {
            result.setObjectApiNameDataList(apiNames.stream().map(ObjectApiNameData::newData).collect(Collectors.toList()));
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<SyncObjectResult>> listMasterSyncObjects(String tenantId, String lang) {
        List<ObjectDescribe> allObjectDescribeAndFields = crmRemoteManager.listAllObjectAndFieldsByTenant(tenantId);
        Set<ObjectApiNameData> masterCrmObj = this.getMasterCrmObj(tenantId,allObjectDescribeAndFields);

        List<SyncObjectResult> results = new ArrayList<>();
        boolean hasProductGroup = false, hasDepartment = false, hasMultiUnit = false;
        for (ObjectApiNameData objectDescribe : masterCrmObj) {

            SyncObjectResult syncObjectResult = new SyncObjectResult();
            if (objectDescribe.getApiName().equals(ObjectApiNameEnum.FS_PRODUCTGROUPOBJ.getObjApiName())) {
                hasProductGroup = true;
            }
            if (objectDescribe.getApiName().equals(ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName())) {
                hasDepartment = true;
            }
            if (objectDescribe.getApiName().equals(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName())) {
                hasMultiUnit = true;
            }
            syncObjectResult.setApiName(objectDescribe.getApiName());
            syncObjectResult.setName(objectDescribe.getDisplayName());
            results.add(syncObjectResult);
        }
        //v1/rest/object/describe/service/findDescribeManageList接口查不到产品组和部门对象，固定添加
        if (!hasProductGroup) {
            SyncObjectResult productGroup = new SyncObjectResult();
            productGroup.setApiName(ObjectApiNameEnum.FS_PRODUCTGROUPOBJ.getObjApiName());
            productGroup.setName(ObjectApiNameEnum.FS_PRODUCTGROUPOBJ.getObjName(i18NStringManager, lang, tenantId));
            results.add(productGroup);
        }
        if (!hasDepartment) {
            SyncObjectResult department = new SyncObjectResult();
            department.setApiName(ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName());
            department.setName(ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjName(i18NStringManager, lang, tenantId));
            results.add(department);
        }
        Result<JSONObject> jsonObjectResult = crmRemoteService.checkModuleStatus(tenantId, CrmConfigKeyConstants.MULTIPLE_UNIT);
        if (!hasMultiUnit && jsonObjectResult != null && jsonObjectResult.isSuccess() && jsonObjectResult.getData().getJSONObject("value") != null &&
                StatusEnum.IsMultipleUnitOpen.getValue().equals(jsonObjectResult.getData().getJSONObject("value").getString("openStatus"))) {
            SyncObjectResult productGroup = new SyncObjectResult();
            productGroup.setApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
            productGroup.setName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjName(i18NStringManager, lang, tenantId));
            results.add(productGroup);
        }
        //如果开启了CPQ并且开启了产品选配实例，添加产品选配实例对象供客户对接使用
        Result<JSONObject> bomInstanceStatus = crmRemoteService.checkModuleStatus(tenantId, CrmConfigKeyConstants.BOM_INSTANCE);
        if (bomInstanceStatus != null
                && bomInstanceStatus.isSuccess()
                && bomInstanceStatus.getData().getJSONObject("value") != null
                && StringUtils.equalsIgnoreCase(bomInstanceStatus.getData().getJSONObject("value").getString("openStatus"), "1")) {
            SyncObjectResult productGroup = new SyncObjectResult();
            productGroup.setApiName(ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName());
            productGroup.setName(ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjName(i18NStringManager, lang, tenantId));
            results.add(productGroup);
        }
        return Result.newSuccess(results);
    }

    private Set<ObjectApiNameData> getMasterCrmObj(String tenantId,List<ObjectDescribe> allObjectDescribeAndFields) {
        Set<ObjectApiNameData> results = new HashSet<>();
        List<String> result=tenantConfigurationManager.getCrmNeedSingleSync(tenantId);
        for (ObjectDescribe objectDescribe : allObjectDescribeAndFields) {
            if (!objectDescribe.isActive() || objectDescribe.isDelete()) {
                continue;
            }
            boolean isDetailObjectByMasterApiName = true;
            for (FieldDescribe fieldDescribe : objectDescribe.getFields().values()) {
                if ("master_detail".equals(fieldDescribe.getType())) {
                    isDetailObjectByMasterApiName = false;
                }
            }
            if(result.contains(objectDescribe.getApiName())){
                isDetailObjectByMasterApiName = true;
            }
            //不支持客户地址作为主对象
//            if (StringUtils.equalsIgnoreCase(objectDescribe.getApiName(), CrmObjectApiName.ACCOUNT_ADDR_API_NAME)) {
//                isDetailObjectByMasterApiName = true;
//            }
            if (isDetailObjectByMasterApiName) {
                ObjectApiNameData syncObjectResult = ObjectApiNameData.newData(objectDescribe.getApiName(), objectDescribe.getDisplayName());
                results.add(syncObjectResult);
            }
        }
        return results;
    }


    @Override
    public Result<Void> insertPollingTime(String tenantId, String syncPloyDetailId, PollingIntervalApiDto pollingInterval) {
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, syncPloyDetailId);
        if (ObjectUtils.isEmpty(syncPloyDetailEntity)) {
            return Result.newErrorByI18N(ResultCodeEnum.PRESET_PLOY_DETAIL_FAILED.getErrCode(),
                    I18NStringEnum.s565.getI18nValue(),
                    I18NStringEnum.s565.getI18nKey(),
                    null);
        }
        syncPloyDetailEntity.getSyncRules().setPollingInterval(pollingInterval);
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .updateSyncRulesById(syncPloyDetailId, syncPloyDetailEntity.getSyncRules());

        return Result.newSuccess();
    }

    /**
     * 重复方法
     */
    @Override
    public Result<Void> startRulesAddOrRemoveApiNames(String tenantId, String sourceObjectApiName, String erpDataCenterId, List<String> syncTypeList) {

        ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, sourceObjectApiName);
        Result<ConnectInfoResult> connectInfo = connectInfoService
                .getConnectInfoByDataCenterId(tenantId, -10000, erpDataCenterId);
        ConnectInfoResult connectInfoResult = connectInfo.getData();
        if (syncTypeList.contains(SyncTypeEnum.push.name()) && syncTypeList.size() == 1) {//推送
            if (connectInfoResult.getPushDataApiNames() == null) {
                connectInfoResult.setPushDataApiNames(Lists.newArrayList(relationshipEntity.getErpRealObjectApiname()));
                connectInfoService.updateConnectInfo(tenantId, -10000, connectInfoResult, null, false);
            } else if (!connectInfoResult.getPushDataApiNames().contains(relationshipEntity.getErpRealObjectApiname())) {
                connectInfoResult.getPushDataApiNames().add(relationshipEntity.getErpRealObjectApiname());//增加apiName
                connectInfoService.updateConnectInfo(tenantId, -10000, connectInfoResult, null, false);
            }
        } else {//非推送
            if (connectInfoResult.getPushDataApiNames() != null && connectInfoResult.getPushDataApiNames().contains(relationshipEntity.getErpRealObjectApiname())) {
                connectInfoResult.getPushDataApiNames().remove(relationshipEntity.getErpRealObjectApiname());//去除apiName
                connectInfoService.updateConnectInfo(tenantId, -10000, connectInfoResult, null, false);
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateUsedQueryField(String tenantId, String dataCenterId, String sourceApiName, ErpChannelEnum erpChannelEnum) {
        if (!ErpChannelEnum.ERP_K3CLOUD.name().equals(erpChannelEnum.name())) {
            //只支持源对象是K3的修改usedQuery
            return Result.newSuccess();
        }
        ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne("0", "0", ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.APPROVAL_TENANT_QUERY_ALL_FIELD.name());
        if (ObjectUtils.isNotEmpty(configurationEntity)) {
            List<String> denyEnterPrise = Splitter.on(";").splitToList(configurationEntity.getConfiguration());
            if (denyEnterPrise.contains(tenantId)) {
                //名单内的企业不允许修改
                return Result.newSuccess();
            }
        }
        this.updateUsedQueryFieldByObjectApiName(tenantId, dataCenterId, sourceApiName);
        return null;
    }

    /**
     * 更新需要查询的字段
     *
     * @param tenantId
     * @param erpSourceApiName
     * @return
     */
    @Override
    public Result<Void> updateUsedQueryFieldByObjectApiName(String tenantId, String dataCenterId, String erpSourceApiName) {

        String actualApiName = idFieldConvertManager.getRealObjApiName(tenantId, erpSourceApiName);
        List<ErpObjectRelationshipEntity> splitObjApiName = erpObjManager.getSplitObjRelations(tenantId, actualApiName);
        List<String> erpSourceApiNames = Lists.newArrayList(erpSourceApiName);
        if (CollectionUtils.isNotEmpty(splitObjApiName)) {
            erpSourceApiNames.addAll(splitObjApiName.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList()));
        }
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantTypeAndObjApiNameList(tenantId, TenantType.ERP, erpSourceApiNames);

        Map<String, Set<String>> allFieldApiName = Maps.newHashMap();
        Set<String> masterFieldSet = Sets.newHashSet();
        syncPloyDetailEntities.forEach(item -> {
            String sourceObjectApiName = item.getSourceObjectApiName();
            String realApiName = idFieldConvertManager.getRealObjApiName(tenantId, sourceObjectApiName);
            //主对象
            for (FieldMappingData fieldMapping : item.getFieldMappings()) {
                if (StringUtils.isNotBlank(fieldMapping.getSourceApiName())) {
                    masterFieldSet.add(fieldMapping.getSourceApiName());
                }
            }
            //主对象的数据范围
            if (ObjectUtils.isNotEmpty(item.getSyncConditions()) && CollectionUtils.isNotEmpty(item.getSyncConditions().getFilters())) {
                for (List<FilterData> filter : item.getSyncConditions().getFilters()) {
                    for (FilterData filterData : filter) {
                        masterFieldSet.add(filterData.getFieldApiName());

                    }
                }
            }
            //其他节点-主
            if (item.getIntegrationStreamNodes() != null) {
                IntegrationStreamNodesData integrationStreamNodes = item.getIntegrationStreamNodes();
                if (integrationStreamNodes.getSyncConditionsQueryDataNode() != null) {
                    IntegrationStreamNodesData.SyncConditionsQueryDataNode syncConditionsQueryDataNode = integrationStreamNodes.getSyncConditionsQueryDataNode();
                    if (syncConditionsQueryDataNode.getQueryObjectMappingData() != null) {
                        for (QueryObjectMappingData queryObjectToDestObjectData : syncConditionsQueryDataNode.getQueryObjectMappingData()) {
                            for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filter : queryObjectToDestObjectData.getQueryFieldMappings()) {
                                for (FilterData filterData : filter) {
                                    if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null && StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())) {
                                        masterFieldSet.add(filterData.getFieldValue().get(0).toString());
                                    }
                                }
                            }
                        }
                    }
                }
                if (integrationStreamNodes.getCheckSyncDataMappingNode() != null) {
                    IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode = integrationStreamNodes.getCheckSyncDataMappingNode();
                    if (checkSyncDataMappingNode.getQueryObjectMappingData() != null && checkSyncDataMappingNode.getQueryObjectMappingData().getQueryFieldMappings() != null) {
                        for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filter : checkSyncDataMappingNode.getQueryObjectMappingData().getQueryFieldMappings()) {
                            for (FilterData filterData : filter) {
                                if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null && StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())) {
                                    masterFieldSet.add(filterData.getFieldValue().get(0).toString());
                                }
                            }
                        }
                    }
                    if (checkSyncDataMappingNode.getSource2SyncDataMapping() != null) {
                        for (DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData data : checkSyncDataMappingNode.getSource2SyncDataMapping()) {
                            if (sourceObjectApiName.equals(data.getSourceObjectApiName()) && StringUtils.isNotBlank(data.getSourceApiName())) {
                                masterFieldSet.add(data.getSourceApiName());
                            }
                        }
                    }
                }
                if (integrationStreamNodes.getQueryCrmObject2DestNodeBySource() != null) {
                    IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNode = integrationStreamNodes.getQueryCrmObject2DestNodeBySource();
                    if (queryCrmObject2DestNode.getQueryObjectToDestObject() != null) {
                        for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryCrmObject2DestNode.getQueryObjectToDestObject()) {
                            for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filter : queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings()) {
                                for (FilterData filterData : filter) {
                                    if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null && StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())) {
                                        masterFieldSet.add(filterData.getFieldValue().get(0).toString());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            allFieldApiName.put(realApiName, masterFieldSet);
            if (ObjectUtils.isNotEmpty(item.getDetailObjectMappings())) {
                List<String> detailName = item.getDetailObjectMappings().stream().map(DetailObjectMappingsData.DetailObjectMappingData::getSourceObjectApiName).collect(Collectors.toList());
                List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, detailName);
                Map<String, String> detailNameMap = erpObjectEntities.stream().collect(Collectors.toMap(ErpObjectEntity::getErpObjectApiName, ErpObjectEntity::getErpObjectExtendValue, (key1, key2) -> key2));
                //从对象
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : item.getDetailObjectMappings()) {
                    Set<String> detailSet = Sets.newHashSet();
                    detailObjectMapping.getFieldMappings().forEach(detailTemp -> {
                        if (StringUtils.isNotEmpty(detailTemp.getSourceApiName())) {
                            detailSet.add(detailTemp.getSourceApiName());
                        }
                    });
                    String detailRealApiName = detailNameMap.get(detailObjectMapping.getSourceObjectApiName());
                    allFieldApiName.putIfAbsent(detailRealApiName, Sets.newHashSet());
                    allFieldApiName.get(detailRealApiName).addAll(detailSet);
                }
                //从对象的数据范围
                if (ObjectUtils.isNotEmpty(item.getDetailObjectSyncConditions())) {
                    log.info("item detailConditions{}", item.getId());
                    for (SyncConditionsData detailObjectSyncCondition : item.getDetailObjectSyncConditions()) {
                        Set<String> detailSet = Sets.newHashSet();
                        for (List<FilterData> filter : detailObjectSyncCondition.getFilters()) {
                            for (FilterData filterData : filter) {
                                detailSet.add(filterData.getFieldApiName());
                            }
                        }
                        String detailRealApiName = detailNameMap.get(detailObjectSyncCondition.getApiName());
                        allFieldApiName.putIfAbsent(detailRealApiName, Sets.newHashSet());
                        allFieldApiName.get(detailRealApiName).addAll(detailSet);
                    }
                }
                //其他节点-从
                if (item.getIntegrationStreamNodes() != null) {
                    IntegrationStreamNodesData integrationStreamNodes = item.getIntegrationStreamNodes();
                    if (integrationStreamNodes.getCheckSyncDataMappingNode() != null) {
                        IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode = integrationStreamNodes.getCheckSyncDataMappingNode();
                        if (checkSyncDataMappingNode.getDetailCheckSyncDataMappingData() != null) {
                            for (DetailQueryObject2SyncDataMappingsData.DetailQueryObject2SyncDataMappingData detail : checkSyncDataMappingNode.getDetailCheckSyncDataMappingData()) {
                                Set<String> detailSet = Sets.newHashSet();
                                String streamSourceObjApiName = detail.getQueryObjectMappingData().getDestObjectApiName();
                                for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filter : detail.getQueryObjectMappingData().getQueryFieldMappings()) {
                                    for (FilterData filterData : filter) {
                                        if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null && StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())) {
                                            detailSet.add(filterData.getFieldValue().get(0).toString());
                                        }
                                    }
                                }
                                for (DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData data : detail.getSource2SyncDataMapping()) {
                                    if (streamSourceObjApiName.equals(data.getSourceObjectApiName()) && StringUtils.isNotBlank(data.getSourceApiName())) {
                                        detailSet.add(data.getSourceApiName());
                                    }
                                }
                                String detailRealApiName = detailNameMap.get(streamSourceObjApiName);
                                allFieldApiName.putIfAbsent(detailRealApiName, Sets.newHashSet());
                                allFieldApiName.get(detailRealApiName).addAll(detailSet);
                            }
                        }
                    }
                    if (integrationStreamNodes.getQueryCrmObject2DestNodeBySource() != null) {
                        IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNode = integrationStreamNodes.getQueryCrmObject2DestNodeBySource();
                        if (queryCrmObject2DestNode.getDetailQueryData2DestDataMapping() != null) {
                            for (List<QueryObjectToDestObjectData> detail : queryCrmObject2DestNode.getDetailQueryData2DestDataMapping()) {
                                Set<String> detailSet = Sets.newHashSet();
                                String streamSourceObjApiName = detail.get(0).getQueryObjectMappingData().getDestObjectApiName();
                                for (QueryObjectToDestObjectData queryObjectToDestObjectData : detail) {
                                    for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingData filter : queryObjectToDestObjectData.getQueryObjectMappingData().getQueryFieldMappings()) {
                                        for (FilterData filterData : filter) {
                                            if (CollectionUtils.isNotEmpty(filterData.getFieldValue()) && filterData.getFieldValue().get(0) != null && StringUtils.isNotBlank(filterData.getFieldValue().get(0).toString())) {
                                                detailSet.add(filterData.getFieldValue().get(0).toString());
                                            }
                                        }
                                    }
                                }
                                String detailRealApiName = detailNameMap.get(streamSourceObjApiName);
                                allFieldApiName.putIfAbsent(detailRealApiName, detailSet).addAll(detailSet);
                                allFieldApiName.get(detailRealApiName).addAll(detailSet);
                            }
                        }
                    }
                }
            }

        });
        //查询研发内部固定的字段
        ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne("all", "0", ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.OBJECT_FIXED_FIELD_NAME.name());

        Map<String, List<String>> configurationMap = Maps.newHashMap();
        if (ObjectUtils.isNotEmpty(configurationEntity) && StringUtils.isNotEmpty(configurationEntity.getConfiguration())) {
            configurationMap = JacksonUtil.fromJson(configurationEntity.getConfiguration(), new TypeReference<Map<String, List<String>>>() {
            });
        }
        //更新对应的字段 配置的字段的queryCode
        List<String> objApiNames = allFieldApiName.keySet().stream().collect(Collectors.toList());
        for (String objApiName : objApiNames) {

            if (CollectionUtils.isNotEmpty(configurationMap.get(objApiName))) {
                Set<String> menuSet = configurationMap.get(objApiName).stream().collect(Collectors.toSet());
                allFieldApiName.get(objApiName).addAll(menuSet);
            }
            List<ErpFieldExtendEntity> result = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .getAllNeedQueryFieldExtend(tenantId, dataCenterId, objApiName);
            Set<String> dbFieldSet = result.stream().map(ErpFieldExtendEntity::getFieldApiName).collect(Collectors.toSet());
            Set<String> frontFieldSet = allFieldApiName.get(objApiName);
            Set<String> backFrontFieldSet = Sets.newHashSet();
            backFrontFieldSet.addAll(frontFieldSet);
            //前端传来字段删除db已更新true的字段=需要更新为启用的字段
            frontFieldSet.removeAll(dbFieldSet);
            //需要更新为停用的字段
            dbFieldSet.removeAll(backFrontFieldSet);
            if (CollectionUtils.isNotEmpty(frontFieldSet)) {
                //激活
                erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .batchUpdateFieldExtendStatus(tenantId, dataCenterId, frontFieldSet, objApiName, true);
            }
            if (CollectionUtils.isNotEmpty(dbFieldSet)) {
                //停用  getName的依赖这个。详情见 com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager.convertIdField2Crm
                dbFieldSet.removeAll(K3ChannelIDField);
                erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .batchUpdateFieldExtendStatus(tenantId, dataCenterId, dbFieldSet, objApiName, false);
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<SyncPloyDetailResult> getSyncPloyDetailAndSourceDestApiName(String tenantId, String sourceObjectApiName, String destObjectApiName, Integer tenantType) {
        //返回值，说明传递的对象是从对象
        String sourceMasterObjectApiName = outerServiceFactory.get(tenantType).getMasterObjectApiName(tenantId, tenantType, sourceObjectApiName).getData();
        if (StringUtils.isEmpty(sourceMasterObjectApiName)) {
            sourceMasterObjectApiName = sourceObjectApiName;
        }
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listBySourceTenantTypeAndObjApiName(tenantId, tenantType, sourceMasterObjectApiName);
        SyncPloyDetailResult result = null;
        if (CollectionUtils.isNotEmpty(syncPloyDetailEntities)) {
            for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
                if (syncPloyDetailEntity.getSourceObjectApiName().equals(sourceObjectApiName)) {
                    //传递的是主对象的apiName
                    if (syncPloyDetailEntity.getDestObjectApiName().equals(destObjectApiName)) {
                        result = SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, null, tenantId, Lists.newArrayList(syncPloyDetailEntity)).get(0);
                        return Result.newSuccess(result);
                    }
                } else {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailEntity.getDetailObjectMappings()) {
                        if (detailObjectMapping.getSourceObjectApiName().equals(sourceObjectApiName) && detailObjectMapping.getDestObjectApiName().equals(destObjectApiName)) {
                            result = SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, null, tenantId, Lists.newArrayList(syncPloyDetailEntity)).get(0);
                            return Result.newSuccess(result);
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Result<List<SyncObjectResult>> listTenantAllCrmObjects(String tenantId) {
        List<ObjectDescribe> allCrmObject = crmRemoteManager.listAllObjectByTenant(tenantId);
        List<SyncObjectResult> results = Lists.newArrayList();
        for (ObjectDescribe objectDescribe : allCrmObject) {
            SyncObjectResult syncObjectResult = new SyncObjectResult();
            syncObjectResult.setApiName(objectDescribe.getApiName());
            syncObjectResult.setName(objectDescribe.getDisplayName());
            results.add(syncObjectResult);
        }
        return Result.newSuccess(results);
    }

    @Override
    public Result<List<Result<?>>> batchCheckAndUpdatePloyStatus(String tenantId, Integer userId, SyncPloyDetailUpdateStatusArg.BatchArg arg) {
        String dcId = arg.getDcId();
        String lang = I18nUtil.getLocaleFromTrace();
        SyncPloyDetailStatusEnum newStatus = SyncPloyDetailStatusEnum.checkStatus(arg.getStatus());
        SyncPloyDetailStatusEnum oldStatus = newStatus == SyncPloyDetailStatusEnum.ENABLE ? SyncPloyDetailStatusEnum.DISABLE : SyncPloyDetailStatusEnum.ENABLE;
        if (arg.isUpdateAllStreams()) {
            //更新所有集成流
            List<String> ids = adminSyncPloyDetailDao.listIdByCond(tenantId, dcId, oldStatus.getStatus());
            arg.setIds(ids);
        }
        List<String> ids = arg.getIds();
        if (CollUtil.isEmpty(ids)) {
            return Result.newSuccess(new ArrayList<>());
        }
        List<Result<?>> resultList = new ArrayList<>();
        for (String id : ids) {
            try {
                Result<Set<CheckAndUpdatePloyValidStatusDetailData>> oneResult = checkAndUpdatePloyStatus(tenantId, userId, dcId, id, newStatus.getStatus(), arg.isNeedSyncDuringStop(), lang);
                log.info("batchCheckAndUpdatePloyStatus checkAndUpdatePloyStatus,id:{},newStatus:{},result:{}", id, newStatus, oneResult);
                resultList.add(oneResult);
            } catch (Exception e) {
                log.error("batchCheckAndUpdatePloyStatus checkAndUpdatePloyStatus error,id:{},newStatus:{}", id, newStatus, e);
                resultList.add(Result.wrapException(e));
            }
        }
        return Result.newSuccess(resultList);
    }


    private List<String> getErpFakeObjApiNames(String tenantId, String dataCenterId) {
        ErpObjectEntity arg = new ErpObjectEntity();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(dataCenterId);
        arg.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        if (CollectionUtils.isEmpty(erpObjectEntities)) {
            return Lists.newArrayList();
        }
        List<String> erpFakeObjApiNames = erpObjectEntities.stream().map(ErpObjectEntity::getErpObjectApiName).collect(Collectors.toList());
        return erpFakeObjApiNames;
    }

    @Override
    public Result<ListBaseInfosByTypeResult> listByType(String tenantId,
                                                        String dataCenterId,
                                                        Integer type,
                                                        Integer status,
                                                        String apiName,
                                                        String searchText,
                                                        int pageNumber,
                                                        int pageSize,
                                                        String lang) {
        //策略明细
        List<SyncPloyDetailEntity> syncPloyDetailEntities = new ArrayList<>();
        //总条数
        int totalCount = 0;
        //获取一页策略明细和总条数
        List<String> erpFakeObjApiNames = getErpFakeObjApiNames(tenantId, dataCenterId);
        if (CollectionUtils.isEmpty(erpFakeObjApiNames)) {
            ListBaseInfosByTypeResult result = new ListBaseInfosByTypeResult();
            result.setSyncPloyDetailInfos(Lists.newArrayList());
            result.setTotalCount(0);
            result.setPageNumber(pageNumber);
            result.setPageSize(pageSize);
            return Result.newSuccess(result);
        }
        if (SyncPloyTypeEnum.INPUT.getType().equals(type)) {
            //数据在向我输入的时候，我是目标企业
            syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listByDestTenantId(tenantId, erpFakeObjApiNames, status, apiName, searchText, (pageNumber - 1) * pageSize, pageSize);
            totalCount = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .countByDestTenantId(tenantId, erpFakeObjApiNames, apiName, status, searchText);
        }
        if (SyncPloyTypeEnum.OUTPUT.getType().equals(type)) {
            //由我输出的时候，我是源企业
            syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listBySourceTenantId(tenantId, erpFakeObjApiNames, status, apiName, searchText, (pageNumber - 1) * pageSize, pageSize);
            totalCount = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .countBySourceTenantId(tenantId, erpFakeObjApiNames, apiName, status, searchText);
        }

        if (CollectionUtils.isEmpty(syncPloyDetailEntities)) {
            ListBaseInfosByTypeResult result = new ListBaseInfosByTypeResult();
            result.setSyncPloyDetailInfos(Lists.newArrayList());
            result.setTotalCount(0);
            result.setPageNumber(pageNumber);
            result.setPageSize(pageSize);
            return Result.newSuccess(result);
        }

        List<SyncPloyDetailResult> syncPloyDetailResults = SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, lang, tenantId, syncPloyDetailEntities);
        List<SyncPloyDetailInfoResult> syncPloyDetailBaseInfos = BeanUtil2.deepCopyList(syncPloyDetailResults, SyncPloyDetailInfoResult.class);

        Map<String, String> objectAndTenantsToPloyDetailIdMap = new HashMap<>();
        List<SyncObjectAndTenantMappingData> objectApiNameMappings = new ArrayList<>();
        for (SyncPloyDetailInfoResult syncPloyDetailInfoResult : syncPloyDetailBaseInfos) {
            for (TenantData sourceTenantData : syncPloyDetailInfoResult.getSourceTenantDatas()) {
                for (TenantData destTenantData : syncPloyDetailInfoResult.getDestTenantDatas()) {
                    SyncObjectAndTenantMappingData syncObjectAndTenantMappingData = new SyncObjectAndTenantMappingData();
                    syncObjectAndTenantMappingData.setSourceTenantId(sourceTenantData.getTenantId());
                    syncObjectAndTenantMappingData.setSourceObjectApiName(syncPloyDetailInfoResult.getSourceObjectApiName());
                    syncObjectAndTenantMappingData.setDestTenantId(destTenantData.getTenantId());
                    syncObjectAndTenantMappingData.setDestObjectApiName(syncPloyDetailInfoResult.getDestObjectApiName());
                    objectApiNameMappings.add(syncObjectAndTenantMappingData);
                    objectAndTenantsToPloyDetailIdMap.put(syncObjectAndTenantMappingData.toString(), syncPloyDetailInfoResult.getId());
                }
            }
            if (syncPloyDetailInfoResult.getDetailObjectMappings() == null || syncPloyDetailInfoResult.getDetailObjectMappings().size() == 0) {
                continue;
            }
            for (SyncPloyDetailInfoResult.DetailObjectMappingInfo detailObjectMapping : syncPloyDetailInfoResult.getDetailObjectMappings()) {
                for (TenantData sourceTenantData : syncPloyDetailInfoResult.getSourceTenantDatas()) {
                    for (TenantData destTenantData : syncPloyDetailInfoResult.getDestTenantDatas()) {
                        SyncObjectAndTenantMappingData detailObjectAndTenantMappingData = new SyncObjectAndTenantMappingData();
                        detailObjectAndTenantMappingData.setSourceObjectApiName(detailObjectMapping.getSourceObjectApiName());
                        detailObjectAndTenantMappingData.setSourceTenantId(sourceTenantData.getTenantId());
                        detailObjectAndTenantMappingData.setDestObjectApiName(detailObjectMapping.getDestObjectApiName());
                        detailObjectAndTenantMappingData.setDestTenantId(destTenantData.getTenantId());
                        objectApiNameMappings.add(detailObjectAndTenantMappingData);
                        objectAndTenantsToPloyDetailIdMap.put(detailObjectAndTenantMappingData.toString(), syncPloyDetailInfoResult.getId());
                    }
                }
            }
        }

        List<SyncDataMappingsFailedCountData> failedSyncDataMappings = syncDataMappingsDao.setTenantId(tenantId).countSyncFailed(tenantId, objectApiNameMappings);

        for (SyncDataMappingsFailedCountData countData : failedSyncDataMappings) {
            String key = countData.getSourceObjectApiName() + "-" + "-" + countData.getDestObjectApiName();
            String ployDetailId = objectAndTenantsToPloyDetailIdMap.get(key);
            for (SyncPloyDetailInfoResult syncPloyDetailInfoResult : syncPloyDetailBaseInfos) {
                if (syncPloyDetailInfoResult.getId().equals(ployDetailId)) {
                    Integer newSyncFailedDataCount = syncPloyDetailInfoResult.getSyncFailedDataCount() + countData.getFailedCount();
                    syncPloyDetailInfoResult.setSyncFailedDataCount(newSyncFailedDataCount);
                }
            }
        }

        ListBaseInfosByTypeResult result = new ListBaseInfosByTypeResult();
        result.setSyncPloyDetailInfos(syncPloyDetailBaseInfos);
        result.setTotalCount(totalCount);
        result.setPageNumber(pageNumber);
        result.setPageSize(pageSize);
        return Result.newSuccess(result);
    }

    @Override
    public Result<SyncPloyDetailResult> getById(String tenantId, String id, String lang) {
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        //校验该企业是否有权限查看该策略
        if (!entity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        SyncPloyDetailResult result = SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, lang, tenantId, Lists.newArrayList(entity)).get(0);
        return Result.newSuccess(result);
    }

    @Override
    @Cached(cacheType = CacheType.LOCAL, expire = 10)
    public Result<SyncPloyDetailResult> getByIdWithCache(String tenantId, String id, String lang) {
        Result<SyncPloyDetailResult> byId = this.getById(tenantId, id, lang);
        return byId;
    }

    @Override
    public Result<Void> updateValid(String tenantId, String id, Boolean valid) {
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateValid(id, valid);
        return Result.newSuccess();
    }

    @Override
    public Result<SyncPloyDetailListByPloyIdResult> listByPloyId(String tenantId,
                                                                 String ployId,
                                                                 int pageNumber,
                                                                 int pageSize,
                                                                 String dataCenterId,
                                                                 String lang) {
        List<String> erpFakeObjApiNames = getErpFakeObjApiNames(tenantId, dataCenterId);
        if (CollectionUtils.isEmpty(erpFakeObjApiNames)) {
            SyncPloyDetailListByPloyIdResult result = new SyncPloyDetailListByPloyIdResult();
            result.setSyncPloyDetails(Lists.newArrayList());
            result.setTotalCount(0);
            result.setPageNumber(pageNumber);
            result.setPageSize(pageSize);
            return Result.newSuccess(result);
        }
        int totalCount = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .countByPloyIdAndErpObjApiNames(tenantId, ployId, erpFakeObjApiNames);

        //获取策略明细
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByPloyIdAndErpObjApiNames(tenantId, ployId, erpFakeObjApiNames, (pageNumber - 1) * pageSize, pageSize);

        if (syncPloyDetailEntities == null || syncPloyDetailEntities.size() == 0) {
            SyncPloyDetailListByPloyIdResult result = new SyncPloyDetailListByPloyIdResult();
            result.setSyncPloyDetails(Lists.newArrayList());
            result.setTotalCount(totalCount);
            result.setPageNumber(pageNumber);
            result.setPageSize(pageSize);
            return Result.newSuccess(result);
        }

        List<SyncPloyDetailResult> syncPloyDetailResults = SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, lang, tenantId, syncPloyDetailEntities);

        SyncPloyDetailListByPloyIdResult result = new SyncPloyDetailListByPloyIdResult();
        result.setSyncPloyDetails(syncPloyDetailResults);
        result.setTotalCount(totalCount);
        result.setPageNumber(pageNumber);
        result.setPageSize(pageSize);
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<SyncPloyDetailResult>> listByTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByTenantId(tenantId);
        List<SyncPloyDetailResult> results = SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, null, tenantId, syncPloyDetailEntities);
        return Result.newSuccess(results);
    }

    @Override
    public Result<FieldMappingsResult> getFieldMappingsById(String tenantId, String id) {
        FieldMappingsResult result = new FieldMappingsResult();

        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);

        //校验该企业是否有权限查看该策略
        if (!syncPloyDetailEntity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        List<FieldMappingResult> fieldMappings = new ArrayList<>();
        for (FieldMappingData fieldMappingData : syncPloyDetailEntity.getFieldMappings()) {
            fieldMappings.add(this.buildFieldMappingResult(fieldMappingData));
        }

        List<ObjectMappingResult> detailObjectMappings = null;
        if (syncPloyDetailEntity.getDetailObjectMappings() != null) {
            detailObjectMappings = new ArrayList<>();
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : syncPloyDetailEntity.getDetailObjectMappings()) {
                ObjectMappingResult objectMappingResult = new ObjectMappingResult();
                objectMappingResult.setSourceObjectApiName(detailObjectMappingData.getSourceObjectApiName());
                objectMappingResult.setDestObjectApiName(detailObjectMappingData.getDestObjectApiName());

                List<FieldMappingResult> detailObjectFieldMappings = new ArrayList<>();
                if (detailObjectMappingData.getFieldMappings() != null) {
                    for (FieldMappingData fieldMappingData : detailObjectMappingData.getFieldMappings()) {
                        detailObjectFieldMappings.add(this.buildFieldMappingResult(fieldMappingData));
                    }
                }
                objectMappingResult.setFieldMappings(detailObjectFieldMappings);
                detailObjectMappings.add(objectMappingResult);
            }
        }

        ObjectMappingResult masterObjectMappings = new ObjectMappingResult();
        masterObjectMappings.setSourceObjectApiName(syncPloyDetailEntity.getSourceObjectApiName());
        masterObjectMappings.setDestObjectApiName(syncPloyDetailEntity.getDestObjectApiName());
        masterObjectMappings.setFieldMappings(fieldMappings);
        result.setMasterObjectMappings(masterObjectMappings);
        result.setDetailObjectMappings(detailObjectMappings);
        result.setSourceTenantIds(Lists.newArrayList(syncPloyDetailEntity.getTenantId()));
        result.setDestTenantIds(Lists.newArrayList(syncPloyDetailEntity.getTenantId()));
        return Result.newSuccess(result);
    }

    @Override
    public Result<SyncRulesResult> getSyncRules(String tenantId, String id) {
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        if (!syncPloyDetailEntity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        SyncRulesData syncRulesData = syncPloyDetailEntity.getSyncRules();
        if (syncRulesData == null) {

            //兼容老的同步策略，默认设置为轮询
            SyncRulesWebData syncRulesData2 = new SyncRulesWebData();
            syncRulesData2.setEvents(Sets.newHashSet(1, 2));
            syncRulesData2.setSyncTypeList(Lists.newArrayList(SyncTypeEnum.get.name()));

            SyncPloyDetailUpdateSyncRulesArg updateSyncRulesArg = new SyncPloyDetailUpdateSyncRulesArg();
            updateSyncRulesArg.setId(id);
            updateSyncRulesArg.setSyncRules(syncRulesData2);

            updateSyncRules(tenantId, updateSyncRulesArg);

            syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
            syncRulesData = syncPloyDetailEntity.getSyncRules();
        }
        SyncRulesResult rulesResult = BeanUtil2.deepCopy(syncRulesData, SyncRulesResult.class);
        if (rulesResult.getDestEvents() == null) {
            rulesResult.setDestEvents(Lists.newArrayList());
        }
        if (rulesResult.getEvents() == null) {
            rulesResult.setEvents(Sets.newHashSet());
        }
        return Result.newSuccess(rulesResult);
    }

    @Override
    public Result<SyncConditionsResult> getSyncConditions(String tenantId, String id) {
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        if (!syncPloyDetailEntity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        SyncConditionsData syncConditionsData = syncPloyDetailEntity.getSyncConditions();
        com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData conditionsDataResult;
        List<com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData> detailObjectSyncConditions = Lists.newArrayList();
        if (syncConditionsData == null) {
            conditionsDataResult = new com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData();
            conditionsDataResult.setFilters(Lists.newArrayList());
        } else {
            conditionsDataResult = BeanUtil2.deepCopy(syncConditionsData, com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData.class);
            if (conditionsDataResult.getFilters() == null) {
                conditionsDataResult.setFilters(Lists.newArrayList());
            }
        }
        if (CollectionUtils.isEmpty(syncPloyDetailEntity.getDetailObjectSyncConditions()) && !CollectionUtils.isEmpty(syncPloyDetailEntity.getDetailObjectMappings())) {
            for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailEntity.getDetailObjectMappings()) {
                com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData data = new com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData();
                data.setFilters(Lists.newArrayList());
                data.setApiName(detailObjectMapping.getSourceObjectApiName());
                detailObjectSyncConditions.add(data);
            }
        } else {
            detailObjectSyncConditions = BeanUtil2.deepCopyList(syncPloyDetailEntity.getDetailObjectSyncConditions(), com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData.class);
        }
        SyncConditionsResult syncConditionsResult = new SyncConditionsResult();
        syncConditionsResult.setSyncConditions(conditionsDataResult);
        syncConditionsResult.setDetailObjectSyncConditions(detailObjectSyncConditions);
        return Result.newSuccess(syncConditionsResult);
    }

    private FieldMappingResult buildFieldMappingResult(FieldMappingData fieldMappingData) {
        FieldMappingResult fieldMappingResult = new FieldMappingResult();
        fieldMappingResult.setSourceApiName(fieldMappingData.getSourceApiName());
        fieldMappingResult.setSourceTargetApiName(fieldMappingData.getSourceTargetApiName());
        fieldMappingResult.setSourceType(fieldMappingData.getSourceType());
        fieldMappingResult.setSourceQuoteFieldType(fieldMappingData.getSourceQuoteFieldType());
        fieldMappingResult.setSourceQuoteRealField(fieldMappingData.getSourceQuoteRealField());
        fieldMappingResult.setSourceQuoteFieldTargetObjectField(fieldMappingData.getSourceQuoteFieldTargetObjectField());
        fieldMappingResult.setSourceQuoteFieldTargetObjectApiName(fieldMappingData.getSourceQuoteFieldTargetObjectApiName());
        fieldMappingResult.setDestApiName(fieldMappingData.getDestApiName());
        fieldMappingResult.setDestTargetApiName(fieldMappingData.getDestTargetApiName());
        fieldMappingResult.setDestType(fieldMappingData.getDestType());
        fieldMappingResult.setDestQuoteFieldType(fieldMappingData.getDestQuoteFieldType());

        fieldMappingResult.setOptionMappings(BeanUtil2.deepCopyList(fieldMappingData.getOptionMappings(), FieldMappingResult.OptionMappingResult.class));
        fieldMappingResult.setMappingType(fieldMappingData.getMappingType());
        fieldMappingResult.setFunction(fieldMappingData.getFunction());
        fieldMappingResult.setValue(fieldMappingData.getValue());
        fieldMappingResult.setValueType(fieldMappingData.getValueType());
        fieldMappingResult.setDefaultValue(fieldMappingData.getDefaultValue());
        return fieldMappingResult;
    }

    @Override
    public Result<String> create(String tenantId, SyncPloyDetailCreateArg arg, String lang) {
        if ((!arg.getSourceTenantIds().contains(tenantId)) && (!arg.getDestTenantIds().contains(tenantId))) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        ErpConnectInfoEntity crmDc = connectInfoService.getOrCreateCrmDc(tenantId, lang).getData();
        CreateObjectMappingArg arg1 = new CreateObjectMappingArg();
        arg1.setIntegrationStreamName(i18NStringManager.get(I18NStringEnum.s767, lang, tenantId) + arg.getSourceObjectApiName() + "->" + arg.getDestObjectApiName());
        arg1.setSourceObjectApiName(arg.getSourceObjectApiName());
        arg1.setDestObjectApiName(arg.getDestObjectApiName());
        if (TenantType.CRM == arg.getSourceTenantType()) {
            arg1.setSourceDataCenterId(crmDc.getId());
            arg1.setDestDataCenterId(arg.getDcId());
            //默认执行动作127
            arg1.setSyncRules(new SyncRulesWebData());
            arg1.getSyncRules().setEvents(Sets.newHashSet(1, 2, 7));
        } else {
            arg1.setSourceDataCenterId(arg.getDcId());
            arg1.setDestDataCenterId(crmDc.getId());
            //默认每15分钟轮询，默认执行动作127
            arg1.setSyncRules(new SyncRulesWebData());
            arg1.getSyncRules().setSyncTypeList(Lists.newArrayList(SyncTypeEnum.get.name()));
            arg1.getSyncRules().setEvents(Sets.newHashSet(1, 2, 7));
            arg1.getSyncRules().setPollingInterval(tenantId, new PollingIntervalApiDto(), configCenterConfig.getCronBeginMinute(tenantId, arg.getSourceObjectApiName()));
            arg1.getSyncRules().getPollingInterval().setIntervalQuantity(15);
            arg1.getSyncRules().getPollingInterval().setTimeUnit(IntervalTimeUnitEnum.minutes);
            arg1.getSyncRules().getPollingInterval().setStartDataTime("00:00");
            arg1.getSyncRules().getPollingInterval().setEndDataTime("23:59");
        }
        if (CollectionUtils.isNotEmpty(arg.getDetailObjectMappings())) {
            for (SyncPloyDetailCreateArg.DetailObjectMappingCreateArg detailObj : arg.getDetailObjectMappings()) {
                CreateObjectMappingArg.ObjectMapping objectMapping = new CreateObjectMappingArg.ObjectMapping();
                objectMapping.setSourceObjectApiName(detailObj.getSourceObjectApiName());
                objectMapping.setDestObjectApiName(detailObj.getDestObjectApiName());
                arg1.getDetailObjectMappings().add(objectMapping);
            }
        }
        return integrationStreamService.createObjectMapping(tenantId, arg1, lang);
    }

    @Override
    public Result<Void> delete(String tenantId, String id) {
        if (!checkPloyDetailAuth(tenantId, id)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        if (SyncPloyDetailStatusEnum.ENABLE.getStatus() == entity.getStatus()) {
            return Result.newError(ResultCodeEnum.ENABLED_PLOY_CAN_NOT_DELETE);
        }
        if(hasMapping(tenantId,entity)){
            return Result.newError(I18NStringEnum.s5113);
        }
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByTenantAndId(tenantId, id);
        tenantConfigurationManager.deleteByTenantIdWithDataCenterId(tenantId, id);
        dataIntegrationNotificationManager.deleteByPloyDetailId(tenantId, id);
        return Result.newSuccess();
    }

    private boolean hasMapping(String tenantId, SyncPloyDetailEntity entity) {
        SyncDataMappingsEntity dataMappingsEntity = syncDataMappingsDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getBySourceAndDestObjApiNameLimit1(tenantId, entity.getSourceObjectApiName(), entity.getDestObjectApiName());
        if (dataMappingsEntity != null) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
            for (DetailObjectMappingsData.DetailObjectMappingData detailObj : entity.getDetailObjectMappings()) {
                dataMappingsEntity = syncDataMappingsDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .getBySourceAndDestObjApiNameLimit1(tenantId, detailObj.getSourceObjectApiName(), detailObj.getDestObjectApiName());
                if (dataMappingsEntity != null) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Result<Void> update(String tenantId, SyncPloyDetailUpdateArg arg) {
        if (!checkPloyDetailAuth(tenantId, arg.getId())) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        SyncPloyDetailEntity entity = BeanUtil2.deepCopy(arg, SyncPloyDetailEntity.class);
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdSelective(entity);
        return Result.newSuccess();
    }

    @Override
    @Transactional
    public Result<Void> updateStatus(String tenantId, Integer userId, String id, Integer status) {
        if (!SyncPloyDetailStatusEnum.ENABLE.getStatus().equals(status) && !SyncPloyDetailStatusEnum.DISABLE.getStatus().equals(status)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        //校验修改权限
        if (!syncPloyDetailEntity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        if (syncPloyDetailEntity.getStatus().equals(status)) {
            return Result.newSuccess();
        }
        Integer oldStatus = syncPloyDetailEntity.getStatus();
        syncPloyDetailEntity.setStatus(status);
        SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = this.buildPloyDetailSnapshot(syncPloyDetailEntity, syncPloyDetailEntity.getTenantId(), syncPloyDetailEntity.getTenantId());

        if (SyncPloyDetailStatusEnum.DISABLE.getStatus().equals(status)) {
            final boolean success = syncPloyManager.disablePloyDetailByStreamId(tenantId, id, syncPloyDetailEntity.getSourceObjectApiName());
            if (!success) {
                return Result.newError(ResultCodeEnum.SERVER_BUSY);
            }

            String dcId = syncPloyDetailEntity.getSourceDataCenterId();
            String ployDetailId = syncPloyDetailEntity.getId();
            syncPloyManager.deletePloyDetailAlertData(tenantId, dcId, ployDetailId);
        } else {
            int result = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateStatusById(tenantId, id, status, oldStatus);
            if (result == 0) {
                return Result.newError(ResultCodeEnum.SERVER_BUSY);
            }
            int rows = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(syncPloyDetailSnapshotEntity);
            if (rows == 0) {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            dataIntegrationNotificationManager.updateAlarmStatus(tenantId, id, AlarmType.INTEGRATION_STREAM_BREAK, true);
        }
        return Result.newSuccess();
    }

    /**
     * 为策略明细创建一个快照
     *
     * @param ployDetail     策略明细
     * @param sourceTenantId 源企业id
     * @param destTenantId   目标企业id
     */
    private SyncPloyDetailSnapshotEntity buildPloyDetailSnapshot(SyncPloyDetailEntity ployDetail, String sourceTenantId, String destTenantId) {
        if (!ployDetail.getTenantId().equals(sourceTenantId)) {
            return null;
        }
        SyncPloyDetailSnapshotEntity snapshotEntity = new SyncPloyDetailSnapshotEntity();
        snapshotEntity.setSyncPloyData(new SyncPloyData());//默认，必填
        snapshotEntity.setId(IdUtil.generateId());
        snapshotEntity.setStatus(ployDetail.getStatus());
        snapshotEntity.setSourceTenantId(sourceTenantId);
        snapshotEntity.setSourceObjectApiName(ployDetail.getSourceObjectApiName());
        snapshotEntity.setDestTenantId(destTenantId);
        snapshotEntity.setDestObjectApiName(ployDetail.getDestObjectApiName());
        snapshotEntity.setSyncPloyId(ployDetail.getSyncPloyId());
        snapshotEntity.setSyncPloyDetailId(ployDetail.getId());
        if (ployDetail.getSyncConditions() == null) {
            snapshotEntity.setSyncConditionsExpression(ConditionUtil.getTrueExpression());
        } else {
            snapshotEntity.setSyncConditionsExpression(ConditionUtil.parseToOrExpression(ployDetail.getSyncConditions().getFilters()));
        }
        if (!CollectionUtils.isEmpty(ployDetail.getDetailObjectSyncConditions())) {
            for (SyncConditionsData detailObjectSyncCondition : ployDetail.getDetailObjectSyncConditions()) {
                if (detailObjectSyncCondition.getIsSyncForce()) {
                    snapshotEntity.getDetailObjectSyncConditionsExpressions().put(detailObjectSyncCondition.getApiName(), ConditionUtil.getTrueExpression());
                } else if (CollectionUtils.isEmpty(detailObjectSyncCondition.getFilters())) {
                    //筛选条件为空同样同步
                    snapshotEntity.getDetailObjectSyncConditionsExpressions().put(detailObjectSyncCondition.getApiName(), ConditionUtil.getTrueExpression());
                } else {
                    snapshotEntity.getDetailObjectSyncConditionsExpressions().put(detailObjectSyncCondition.getApiName(), ConditionUtil.parseToOrExpression(detailObjectSyncCondition.getFilters()));
                }
            }
        }
        if (!CollectionUtils.isEmpty(ployDetail.getDetailObjectMappings())) {
            Set<String> sourceDetailObjectApiNames = ployDetail.getDetailObjectMappings().stream()
                    .map(DetailObjectMappingsData.DetailObjectMappingData::getSourceObjectApiName).collect(Collectors.toSet());
            sourceDetailObjectApiNames.removeAll(snapshotEntity.getDetailObjectSyncConditionsExpressions().keySet());
            for (String sourceDetailObjectApiName : sourceDetailObjectApiNames) {
                snapshotEntity.getDetailObjectSyncConditionsExpressions().put(sourceDetailObjectApiName, ConditionUtil.getTrueExpression());
            }
        }
        snapshotEntity.setCreateTime(System.currentTimeMillis());


        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(sourceTenantId)).getById(sourceTenantId, ployDetail.getId());
        SyncPloyDetailData syncPloyDetailData = BeanUtil2.deepCopy(syncPloyDetailEntity, SyncPloyDetailData.class);
        adminSkuSpuProcessManager.processSkuSpu(snapshotEntity, ployDetail, syncPloyDetailData, destTenantId);
        snapshotEntity.setSyncPloyDetailData(syncPloyDetailData);
        log.debug("buildPloyDetailSnapshot snapshotEntity:{}", snapshotEntity);
        return snapshotEntity;
    }

    @Override
    public Result<String> updateFieldMappings(String tenantId, SyncPloyDetailUpdateFieldMappingsArg arg) {
        if (ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(arg.getMasterObjectMapping().getDestObjectApiName()) ||
                ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(arg.getMasterObjectMapping().getDestObjectApiName())) {//针对部门人员对象的特殊逻辑
            //TODO 更换mappingType，如果前端做了这个逻辑就可以去掉了
            for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMappingArg : arg.getMasterObjectMapping().getFieldMappings()) {
                if (ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(fieldMappingArg.getDestTargetApiName()) ||
                        ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(fieldMappingArg.getDestTargetApiName())) {//针对查找关联部门人员的字段的mappingType做特殊处理
                    if (3 == fieldMappingArg.getMappingType()) {
                        continue;
                    }
                    fieldMappingArg.setMappingType(FieldMappingTypeEnum.DEPARTMENT_EMPLOYEE_FROM_MAPPING_TABLE.getType());
                }
            }
        }
        //处理主对象附件
        for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping : arg.getMasterObjectMapping().getFieldMappings()) {
            if (ErpFieldTypeEnum.isAttachmentType(fieldMapping.getDestType()) && ErpFieldTypeEnum.isAttachmentType(fieldMapping.getSourceType())) {
                fieldMapping.setMappingType(FieldMappingTypeEnum.ATTACHMENT_MAPPING_TABLE.getType());
            }
        }
        //处理从对象附件
        for (int i = 0; i < arg.getDetailObjectMappings().size(); i++) {
            for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping2 : arg.getDetailObjectMappings().get(i).getFieldMappings()) {
                if (ErpFieldTypeEnum.isAttachmentType(fieldMapping2.getDestType()) && ErpFieldTypeEnum.isAttachmentType(fieldMapping2.getSourceType())) {
                    fieldMapping2.setMappingType(FieldMappingTypeEnum.ATTACHMENT_MAPPING_TABLE.getType());
                }
            }
        }


        if (arg.getMasterObjectMapping() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getId());

        if (!syncPloyDetailEntity.getSourceObjectApiName().equals(arg.getMasterObjectMapping().getSourceObjectApiName()) || !syncPloyDetailEntity.getDestObjectApiName()
                .equals(arg.getMasterObjectMapping().getDestObjectApiName()) || syncPloyDetailEntity.getDetailObjectMappings().size() != arg.getDetailObjectMappings().size()) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }


        //校验该企业是否有权限在该策略下添加明细
        if (!syncPloyDetailEntity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }

        if (CollectionUtils.isEmpty(arg.getMasterObjectMapping().getFieldMappings())) {
            return Result.newError(ResultCodeEnum.NOT_SET_FIELD);
        }
        if (!syncPloyDetailEntity.getDetailObjectMappings().isEmpty()) {
            for (SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg objectMappingArg : arg.getDetailObjectMappings()) {
                if (CollectionUtils.isEmpty(objectMappingArg.getFieldMappings())) {
                    return Result.newError(ResultCodeEnum.NOT_SET_FIELD);
                }
            }
        }

        Map<String, List<String>> typeMappingWhiteList = JSON.parseObject(typeMappingWhiteListJson, Map.class);
        for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping : arg.getMasterObjectMapping().getFieldMappings()) {
            if (fieldMapping.getMappingType() != FieldMappingTypeEnum.SOUCRE_QUOTE_VAULE.getType()) {
                continue;
            }
            if (FieldType.EMPLOYEE.equals(fieldMapping.getSourceType()) && "mobile".equals(fieldMapping.getValue())) {
                if (!typeMappingWhiteList.containsKey(FieldType.PHONE_NUMBER) || !typeMappingWhiteList.get(FieldType.PHONE_NUMBER).contains(fieldMapping.getDestType())) {
                    log.info("unsupported type mapping, sourceFieldApiName : {}, destFieldApiName : {}, sourceType : {}, destType : {}", fieldMapping.getSourceApiName(), fieldMapping.getDestApiName(),
                            fieldMapping.getSourceType(), fieldMapping.getDestType());
                    return Result.newError(ResultCodeEnum.UNSUPPORTED_TYPE_MAPPING.getErrCode(),
                            "employee.mobile->" + fieldMapping.getDestApiName());
                }
            } else {
                if (!typeMappingWhiteList.containsKey(fieldMapping.getSourceType()) || !typeMappingWhiteList.get(fieldMapping.getSourceType()).contains(fieldMapping.getDestType())) {
                    log.info("unsupported type mapping, sourceFieldApiName : {}, destFieldApiName : {}, sourceType : {}, destType : {}", fieldMapping.getSourceApiName(), fieldMapping.getDestApiName(),
                            fieldMapping.getSourceType(), fieldMapping.getDestType());
                    return Result.newError(ResultCodeEnum.UNSUPPORTED_TYPE_MAPPING.getErrCode(),
                            ResultCodeEnum.UNSUPPORTED_TYPE_MAPPING.getErrMsg() + fieldMapping.getSourceApiName() + "->" + fieldMapping.getDestApiName());
                }
            }
        }

        FieldMappingsData fieldMappingsData = new FieldMappingsData();
        fieldMappingsData.addAll(BeanUtil2.deepCopyList(arg.getMasterObjectMapping().getFieldMappings(), FieldMappingData.class));

        DetailObjectMappingsData detailObjectMappingsData = new DetailObjectMappingsData();
        if (CollectionUtils.isNotEmpty(arg.getDetailObjectMappings())) {
            detailObjectMappingsData.addAll(BeanUtil2.deepCopyList(arg.getDetailObjectMappings(), DetailObjectMappingsData.DetailObjectMappingData.class));
        }

        SyncPloyDetailEntity entity = new SyncPloyDetailEntity();
        entity.setId(arg.getId());
        entity.setFieldMappings(fieldMappingsData);
        entity.setDetailObjectMappings(detailObjectMappingsData);
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdSelective(entity);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateSyncRules(String tenantId, SyncPloyDetailUpdateSyncRulesArg arg) {
        if (!checkPloyDetailAuth(tenantId, arg.getId())) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }

        final SyncRulesWebData syncRules = arg.getSyncRules();
        final TriggerConfig triggerConfig = syncRules.getTriggerConfig();
        boolean isCustomTriggerConfig = Objects.nonNull(triggerConfig) && Objects.equals(triggerConfig.getTriggerType(), SyncPloyDetailTriggerEnum.CUSTOM.getStatus());
        if (isCustomTriggerConfig) {
            if (triggerConfig.getTriggerObjectFields().values().stream()
                    .anyMatch(CollectionUtils::isEmpty)) {
                // 自定义触发字段需要校验有配置字段
                return Result.newError(ResultCodeEnum.NEED_SET_TRIGGER_FIELD);
            }
        }

        SyncRulesData syncRulesData = BeanUtil2.deepCopy(syncRules, SyncRulesData.class);
        SyncPloyDetailEntity entity = new SyncPloyDetailEntity();
        entity.setId(arg.getId());
        entity.setSyncRules(syncRulesData);
        entity.setUpdateTime(System.currentTimeMillis());
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdSelective(entity);
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getId());
        if (TenantType.ERP.equals(syncPloyDetailEntity.getSourceTenantType())) {
            if (syncPloyDetailEntity.getSyncRules().getEvents() != null
                    && syncPloyDetailEntity.getSyncRules().getEvents().contains(EventTypeEnum.DELETE_DIRECT.getType())
                    && syncPloyDetailEntity.getSyncRules().getSyncTypeList() != null
                    && syncPloyDetailEntity.getSyncRules().getSyncTypeList().contains(SyncTypeEnum.get.name())) {
                return Result.newError(I18NStringEnum.s5112);
            }
        }
        if (TenantType.ERP == syncPloyDetailEntity.getSourceTenantType()) { //如果是erp->crm,初始化轮询时间为当前时间
            doLastSyncTime(tenantId, arg.getDataCenterId(), syncPloyDetailEntity.getSourceObjectApiName(), BeanUtil2.deepCopy(syncRules, SyncRulesData.class));
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Void> doLastSyncTime(String tenantId, String dataCenterId, String sourceObjApiName,
                                       @Deprecated SyncRulesData rulesDataNew) {
        //频率从新数据取
        PollingIntervalApiDto pollingInterval = rulesDataNew.getPollingInterval();
        initLastSyncTime(tenantId, sourceObjApiName, pollingInterval);
        addOrRemovePushObjApiName(tenantId, dataCenterId, sourceObjApiName, rulesDataNew);
        return Result.newSuccess();
    }

    private void initLastSyncTime(String tenantId, String sourceObjApiName, PollingIntervalApiDto pollingInterval) {

        Set<Integer> newEvents = getSyncRuleEvents(tenantId, sourceObjApiName);
        if (pollingInterval == null) {
            pollingInterval = new PollingIntervalApiDto();
            pollingInterval.setTimeUnit(IntervalTimeUnitEnum.minutes);
            pollingInterval.setIntervalQuantity(15);//默认时间间隔为15分钟
            pollingInterval.setStartDataTime("00:00");
            pollingInterval.setEndDataTime("23:59");
        }
        initLastSyncTimeService.initLastSyncTime(tenantId, sourceObjApiName, newEvents, System.currentTimeMillis(), pollingInterval);
    }

    private Set<Integer> getSyncRuleEvents(String tenantId, String sourceObjApiName) {
        //事件从所有集成流取
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setGlobalTenant(tenantId).listBySourceTenantTypeAndObjApiName(tenantId, TenantType.ERP, sourceObjApiName);
        List<SyncRulesData> rulesDataList = syncPloyDetailEntities.stream().map(v -> v.getSyncRules()).collect(Collectors.toList());
        final List<String> syncTypeList = rulesDataList.stream()
                //默认值为仅轮询
                .flatMap(r -> CollUtil.defaultIfEmpty(r.getSyncTypeList(), Lists.newArrayList("get")).stream())
                .collect(Collectors.toList());
        final Set<Integer> events = rulesDataList.stream()
                .flatMap(r -> CollUtil.defaultIfEmpty(r.getEvents(), Lists.newArrayList()).stream())
                .collect(Collectors.toSet());
        if (syncTypeList.size() == 1 && syncTypeList.contains(SyncTypeEnum.push.name())) {
            //仅仅包含推送
            Set<Integer> newEvents = new HashSet<>();
            if (events.contains(ExtraEventTypeEnum.ADD.getRealType())) {
                newEvents.add(ExtraEventTypeEnum.PUSH_ADD.getExtraType());
            }
            if (events.contains(ExtraEventTypeEnum.UPDATE.getRealType())) {
                newEvents.add(ExtraEventTypeEnum.PUSH_UPDATE.getExtraType());
            }
            if (events.contains(ExtraEventTypeEnum.INVALID.getRealType())) {
                newEvents.add(ExtraEventTypeEnum.PUSH_INVALID.getExtraType());
            }
            if (events.contains(ExtraEventTypeEnum.DELETE_DIRECT.getRealType())) {
                newEvents.add(ExtraEventTypeEnum.PUSH_DELETE_DIRECT.getExtraType());
            }
            return newEvents;
        }
        return events;
    }

    private void addOrRemovePushObjApiName(String tenantId, String dataCenterId, String sourceObjApiName, SyncRulesData rulesData) {
        ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, sourceObjApiName);
        Result<ConnectInfoResult> connectInfo = connectInfoService
                .getConnectInfoByDataCenterId(tenantId, -10000, dataCenterId);
        ConnectInfoResult connectInfoResult = connectInfo.getData();
        if (rulesData.getSyncTypeList() != null && rulesData.getSyncTypeList().contains(SyncTypeEnum.push.name()) && rulesData.getSyncTypeList().size() == 1) {//推送
            if (connectInfoResult.getPushDataApiNames() == null) {
                connectInfoResult.setPushDataApiNames(Lists.newArrayList(relationshipEntity.getErpRealObjectApiname()));
                connectInfoService.updateConnectInfo(tenantId, -10000, connectInfoResult, null, false);
            } else if (!connectInfoResult.getPushDataApiNames().contains(relationshipEntity.getErpRealObjectApiname())) {
                connectInfoResult.getPushDataApiNames().add(relationshipEntity.getErpRealObjectApiname());//增加apiName
                connectInfoService.updateConnectInfo(tenantId, -10000, connectInfoResult, null, false);
            }
        } else {//非推送
            if (connectInfoResult.getPushDataApiNames() != null && connectInfoResult.getPushDataApiNames().contains(relationshipEntity.getErpRealObjectApiname())) {
                connectInfoResult.getPushDataApiNames().remove(relationshipEntity.getErpRealObjectApiname());//去除apiName
                connectInfoService.updateConnectInfo(tenantId, -10000, connectInfoResult, null, false);
            }
        }
    }

    @Override
    public Result<Void> updateSyncConditions(String tenantId, SyncPloyUpdateSyncConditionsArg arg) {
        if (!checkPloyDetailAuth(tenantId, arg.getId())) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        SyncConditionsData syncConditionsData = BeanUtil2.deepCopy(arg.getSyncConditions(), SyncConditionsData.class);
        if (StringUtils.isNotEmpty(syncConditionsData.getApiName()) && arg.getType() != null && arg.getRelationTenantId() != null && arg.getType() == SyncPloyTypeEnum.INPUT.getType()) {
            processUppserEi(tenantId, arg, syncConditionsData.getApiName(), syncConditionsData.getFilters());
        }
        if (syncConditionsData != null && CollectionUtils.isEmpty(syncConditionsData.getFilters())) {
            syncConditionsData.setIsSyncForce(true);//如果筛选条件为空，默认同步所有
        }
        if (CollectionUtils.isNotEmpty(arg.getDetailObjectSyncConditions()) && arg.getType() != null && arg.getType() == SyncPloyTypeEnum.INPUT.getType() && arg.getRelationTenantId() != null) {
            for (com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData conditionsData : arg.getDetailObjectSyncConditions()) {
                processUppserEi(tenantId, arg, conditionsData.getApiName(), conditionsData.getFilters());
            }
        }
        if (CollectionUtils.isNotEmpty(arg.getDetailObjectSyncConditions())) {
            for (com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData conditionsData : arg.getDetailObjectSyncConditions()) {
                if (conditionsData != null && CollectionUtils.isEmpty(conditionsData.getFilters())) {
                    conditionsData.setIsSyncForce(true);//如果筛选条件为空，默认同步所有
                }
            }
        }
        SyncPloyDetailEntity entity = new SyncPloyDetailEntity();
        entity.setId(arg.getId());
        entity.setSyncConditions(syncConditionsData);
        entity.setDetailObjectSyncConditions(SyncConditionsListData.convert(BeanUtil2.deepCopyList(arg.getDetailObjectSyncConditions(), SyncConditionsData.class)));
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdSelective(entity);
        return Result.newSuccess();
    }

    private void processUppserEi(String tenantId, SyncPloyUpdateSyncConditionsArg arg, String apiName, List<List<FilterData>> filters) {
        ObjectDescribe srcDescribe = objectDescribeService.getDescribe(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(arg.getRelationTenantId(), SuperUserConstants.USER_ID), apiName)
                .getData().getDescribe();
        FieldDescribe fieldDescribe = srcDescribe.getFields().get("upper_ei");
        if (fieldDescribe != null && fieldDescribe.getDefineType().equals("package")) {
            for (List<FilterData> filterData : filters) {
                Boolean isExist = false;
                for (FilterData filterDatum : filterData) {
                    if (filterDatum.getFieldApiName().equals("upper_ei")) {
                        filterDatum.setFieldValue(Lists.newArrayList(tenantId));
                        isExist = true;
                        break;
                    }
                }
                if (!isExist) {
                    FilterData data = FilterData.builder().fieldApiName("upper_ei").fieldType(FieldType.TEXT).fieldValue(Lists.newArrayList(tenantId)).operate(FilterOperatorEnum.EQ.getValue())
                            .build();
                    filterData.add(data);
                }
            }
        }
    }

    private boolean checkPloyDetailAuth(String tenantId, String syncPloyDetailId) {
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, syncPloyDetailId);
        if (syncPloyDetailEntity == null) {
            return false;
        }
        //校验该企业是否有权限在该策略下添加明细
        return syncPloyDetailEntity.getTenantId().equals(tenantId);
    }

    @Override
    public Result<Void> saveOrUpdateCustomFunction(Integer tenantId, Integer userId, CustomFunctionCreateArg arg) {
        if (!CustomFunctionTypeEnum.listAllTypes().contains(arg.getCustomFuncType())) {
            return Result.newError(ResultCodeEnum.CUSTOM_FUNC_APINAME_TYPE_NOT_EXIST);
        }
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(String.valueOf(tenantId)))
                .updateFuncApiName(arg.getId(), arg.getCustomFuncType(), arg.getCustomFuncApiName(), System.currentTimeMillis());
        return Result.newSuccess();
    }

    @NotNull
    @Override
    public Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkAndUpdatePloyStatus(String tenantId, Integer userId, String dcId, String id, Integer status, boolean needSyncDuringStop, String lang) {
        if (!SyncPloyDetailStatusEnum.ENABLE.getStatus().equals(status) && !SyncPloyDetailStatusEnum.DISABLE.getStatus().equals(status)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        if (SyncPloyDetailStatusEnum.DISABLE.getStatus().equals(status)) {
            return checkAndDisablePloy(tenantId, userId, dcId, id, lang);
        }

        //获取策略明细和策略，里面校验企业权限，对集成平台暂时无用
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        //校验该企业是否有权限查看该策略
        if (!entity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }

        if (Objects.equals(entity.getStatus(), status)) {
            return Result.newSuccess();
        }

        return checkAndEnablePloy(tenantId, userId, dcId, entity, needSyncDuringStop, lang);
    }

    @Override
    public Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkAndDisablePloy(String tenantId,
                                                                                    Integer userId,
                                                                                    String dcId,
                                                                                    String id,
                                                                                    String lang) {
        Result<Void> result = updateStatus(tenantId, userId, id, SyncPloyDetailStatusEnum.DISABLE.getStatus());

        UserOperationTypeEnum action = UserOperationTypeEnum.STOP;

        if (!result.isSuccess()) {
            UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                    id, userId, action.name(), action.getDescription(i18NStringManager, lang, tenantId) + i18NStringManager.getByEi(I18NStringEnum.s801, tenantId) + action.getDescription(i18NStringManager, lang, tenantId) + i18NStringManager.getByEi(I18NStringEnum.s802, tenantId) + result.getErrMsg(), null));
            return Result.copy(result);
        }

        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                id, userId, action.name(), action.getDescription(i18NStringManager, lang, tenantId) + i18NStringManager.getByEi(I18NStringEnum.s801, tenantId) + action.getDescription(i18NStringManager, lang, tenantId) + i18NStringManager.getByEi(I18NStringEnum.s6, tenantId), null));

        return Result.newSuccess();
    }

    private Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkAndEnablePloy(String tenantId, Integer userId, String dcId, SyncPloyDetailEntity entity, boolean needSyncDuringStop, String lang) {
        ErpConnectInfoEntity erpConnectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        if(StringUtils.isBlank(erpConnectInfo.getConnectParams())){
            return Result.newError(I18NStringEnum.s542);
        }
        if (TenantType.ERP.equals(entity.getSourceTenantType())) {
            if (entity.getSyncRules().getEvents() != null
                    && entity.getSyncRules().getEvents().contains(EventTypeEnum.DELETE_DIRECT.getType())
                    && entity.getSyncRules().getSyncTypeList() != null
                    && entity.getSyncRules().getSyncTypeList().contains(SyncTypeEnum.get.name())) {
                return Result.newError(I18NStringEnum.s5112);
            }
        }
        //启用集成流的时候，校验主键字段和主从字段
        Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkIntegrationStreamMapping = checkIntegrationStreamMapping(tenantId, entity, lang);
        if (checkIntegrationStreamMapping != null) {
            return checkIntegrationStreamMapping;
        }

        Result<Set<CheckAndUpdatePloyValidStatusDetailData>> newError = checkPloyDetail(tenantId, dcId, entity, lang, Boolean.FALSE);
        if (newError != null) {
            return newError;
        }
        //upsert监控看板的描述
        biManager.upsertDestByPloyDetail(tenantId, entity);
        SyncPloyDetailResult ployDetailResult = SyncPloyDetailResult.buildSyncPloyDetailResultByEntity(i18NStringManager, lang, tenantId, entity);
        final String id = ployDetailResult.getId();

        Result<Void> result = updateStatus(tenantId, userId, id, SyncPloyDetailStatusEnum.ENABLE.getStatus());

        UserOperationTypeEnum action = UserOperationTypeEnum.START;

        String actionName = i18NStringManager.get(action.getI18nKey(), lang, tenantId, action.getDescription(i18NStringManager, lang, tenantId));
        if (!result.isSuccess()) {
            UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                    id, userId, action.name(),
                    actionName + i18NStringManager.get(I18NStringEnum.s801, lang, tenantId) + actionName + i18NStringManager.get(I18NStringEnum.s802, lang, tenantId) + i18NStringManager.get2(result.getI18nKey(), lang, tenantId, result.getErrMsg(), result.getI18nExtra()),
                    null));
            return Result.copy(result);
        }

        //启用成功后，再校验轮询接口是否异常，因为需要使用策略快照Id
        Result<Void> checkQueryInterfaceStatus = syncPloyDetailAdminManager.checkQueryInterfaceStatus(tenantId, id, lang);
        if (!checkQueryInterfaceStatus.isSuccess()) {
            //更新为异常状态
            updateValid(tenantId, id, false);

            updateStatus(tenantId, userId, id, SyncPloyDetailStatusEnum.DISABLE.getStatus());//停用
            UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                    id, userId, UserOperationTypeEnum.START.name(),
                    i18NStringManager.get(I18NStringEnum.s800, lang, tenantId) + i18NStringManager.get2(checkQueryInterfaceStatus.getI18nKey(), lang, tenantId, checkQueryInterfaceStatus.getErrMsg(), checkQueryInterfaceStatus.getI18nExtra()),
                    null));
            return Result.copy(checkQueryInterfaceStatus);
        }

        processAfterEnablePloy(tenantId, needSyncDuringStop, ployDetailResult, id);

        UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                id, userId, action.name(), action.getDescription(i18NStringManager, lang, tenantId) + i18NStringManager.getByEi(I18NStringEnum.s801, tenantId) + action.getDescription(i18NStringManager, lang, tenantId) + i18NStringManager.getByEi(I18NStringEnum.s6, tenantId), null));

        return Result.newSuccess();
    }

    private void processAfterEnablePloy(String tenantId, boolean needSyncDuringStop, SyncPloyDetailResult ployDetailResult, String id) {
        //如果配置了产品选配实例BomInstanceObj对象的策略，默认设置聚合时间
        if (StringUtils.equalsIgnoreCase(ployDetailResult.getSourceObjectApiName(), ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName())) {
            updateAggregationConfig(tenantId);
        }

        //由于启用后才能检测接口，而检测接口失败又需要停用策略，所以在一切成功后，再去执行移除集成流熔断状态
        String breakSetTopic = String.format(CommonConstant.REDIS_KEY_PLOY_BREAK_SET, tenantId);
        Long srem = redisDataSource.get(this.getClass().getSimpleName()).srem(breakSetTopic, id);
        log.info("srem broke set,result:{}", srem);
        //创建补偿同步任务
        switchStreamStatusManager.asyncProcessAfterEnablePloy(tenantId, needSyncDuringStop, ployDetailResult);
    }

    @Nullable
    private Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkPloyDetail(String tenantId, String dcId, SyncPloyDetailEntity entity, String lang, Boolean updateInEnabled) {
        SyncPloyDetailResult ployDetailResult = SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, lang, tenantId, Lists.newArrayList(entity)).get(0);
        final String id = ployDetailResult.getId();

        if (!Objects.equals(ployDetailResult.getStatus(), SyncPloyDetailStatusEnum.ENABLE.getStatus())) {
            //校验配额
            SyncQuota syncQuota = syncQuotaService.getQuota(tenantId, false, false).safeData();
            if (syncQuota.getStreamQuota() - syncQuota.getStreamUsed() < 0) {
                return Result.newError(i18NStringManager.getByEi(I18NStringEnum.s792, tenantId));
            }
            //启用策略时，校验前删除熔断异常信息
            String key = String.format(REDIS_KEY_EXCEPTION_PLOY_DETAIL, tenantId, id);
            Long aLong = redisCacheManager.delCache(key, this.getClass().getSimpleName());
            log.info("delete exception cache,{}", aLong);
        }
        //校验数据
        Set<CheckAndUpdatePloyValidStatusDetailData> allCheckAndUpdatePloyValidStatusDetailDatas =
                syncPloyDetailAdminManager.checkAndUpdatePloyValidStatus(tenantId, entity, lang, updateInEnabled);
        //详情校验
        allCheckAndUpdatePloyValidStatusDetailDatas.addAll(checkDetail(tenantId, entity, lang));
        //检查是轮询且时间轮询间隔是否已经预置
        checkInitPollingIntervalAndSyncTime(dcId, id, tenantId, ployDetailResult);
        //接口实现的校验, 根据连接器配置是否执行
        Result<Void> checkEnableStreamRes = interfaceCheckEnableStream(tenantId, dcId, entity);
        if (!checkEnableStreamRes.isSuccess()) {
            //接口校验出异常
            allCheckAndUpdatePloyValidStatusDetailDatas.add(CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, 5, checkEnableStreamRes.getText()));
        }
        if (CollectionUtils.isNotEmpty(allCheckAndUpdatePloyValidStatusDetailDatas)) {
            return Result.newErrorWithData(ResultCodeEnum.PLOY_NOT_VALID, allCheckAndUpdatePloyValidStatusDetailDatas);
        }
        return null;
    }

    /**
     * 接口对集成流进行校验，根据配置判断是否执行
     */
    public Result<Void> interfaceCheckEnableStream(String tenantId, String dcId, SyncPloyDetailEntity entity) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        Connector connector = AllConnectorUtil.getByChannelAndConnectParam(connectInfo.getChannel(), connectInfo.getConnectParams());
        ImmutableSet<String> whiteList = tenantConfigurationManager.getWhiteList(TenantConfigurationTypeEnum.NEED_INTERFACE_CHECK_ENABLE_STREAM);
        if (whiteList.contains(connector.getKey())
                || whiteList.contains(tenantId)
                || whiteList.contains(tenantId + "." + dcId)) {
            //检查
            ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandler(connector.getConnectorHandlerType());
            if (configHandler!=null){
                CheckStreamEnableArg arg = new CheckStreamEnableArg();
                Integer sourceTenantType = entity.getSourceTenantType();
                String crmObjApiName = TenantType.CRM.equals(sourceTenantType) ? entity.getSourceObjectApiName():entity.getDestObjectApiName();
                String erpSplitObjApiName = TenantType.CRM.equals(sourceTenantType) ? entity.getDestObjectApiName():entity.getSourceObjectApiName();
                //中间对象查出真实对象
                String erpRealObjApiName = erpObjManager.getRealObjApiName(tenantId, erpSplitObjApiName);
                arg.setCrmObjApiName(crmObjApiName)
                        .setErpSplitObjApiName(erpSplitObjApiName)
                        .setErpRealObjApiName(erpRealObjApiName)
                        .setSourceTenantType(sourceTenantType);
                Result<Void> voidResult = configHandler.checkEnableStream(connectInfo, arg);
                return voidResult;
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Set<CheckAndUpdatePloyValidStatusDetailData>> updateIntegrationStreamInEnabled(String tenantId,
                                                                                                 Integer userId,
                                                                                                 String dcId,
                                                                                                 UpdateIntegrationStreamArg arg,
                                                                                                 String lang) {
        final String id = arg.getId();
        SyncPloyDetailEntity oldEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getId());
//        IntegrationStreamServiceImpl.allUpdateIntegrationStream 逻辑太多太复杂,抽取逻辑太复杂且风险太大,先直接使用,后面失败在直接回滚
//        使用事务耗时较久,不确定是否会导致其他问题,且嘉裕说事务有问题,最好不要用
        boolean callback = true;
        String newSnapshotId = null;
        String oldSnapshotId = null;
        try {
            final Result<UpdateIntegrationStreamResult> updateIntegrationStreamResultResult = integrationStreamService.allUpdateIntegrationStream(tenantId, arg, lang);
            if (!updateIntegrationStreamResultResult.isSuccess()) {
                return Result.copy(updateIntegrationStreamResultResult);
            }

            log.info("updateIntegrationStreamInEnabled UpdateIntegrationStream tenantId:{} id:{}", tenantId, arg.getId());
            SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getId());

            Result<Set<CheckAndUpdatePloyValidStatusDetailData>> newError = checkPloyDetail(tenantId, dcId, entity, lang, Boolean.TRUE);
            if (newError != null) {
                return newError;
            }

            SyncPloyDetailResult ployDetailResult = SyncPloyDetailResult.buildSyncPloyDetailResultByEntity(i18NStringManager, lang, tenantId, entity);

//            创建新快照
            SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = this.buildPloyDetailSnapshot(entity, tenantId, tenantId);
            newSnapshotId = syncPloyDetailSnapshotEntity.getId();
            //这里插入的快照就是启用
            int rows = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(syncPloyDetailSnapshotEntity);
            if (rows == 0) {
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
            }
            log.info("updateIntegrationStreamInEnabled createNewSnapshot tenantId:{} newSnapshotId:{}", tenantId, newSnapshotId);

            //启用成功后，再校验轮询接口是否异常，因为需要使用策略快照Id
            Result<Void> checkQueryInterfaceStatus = syncPloyDetailAdminManager.checkQueryInterfaceStatus(tenantId, newSnapshotId, entity, lang);
            if (!checkQueryInterfaceStatus.isSuccess()) {
                UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(),
                        id, userId, UserOperationTypeEnum.START.name(), i18NStringManager.getByEi(I18NStringEnum.s2010, tenantId) + checkQueryInterfaceStatus.getErrMsg(), null));
                return Result.copy(checkQueryInterfaceStatus);
            }

            //需要停用旧的快照
            //之前会出现并发的时候，出现互相停用对方的集成流快照。这里保证会存在一个正常的集成流快照
            final List<SyncPloyDetailSnapshotEntity> syncPloyDetailSnapshotEntities = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listEnableSnapshotOffsetOne(tenantId, id, SyncPloyDetailStatusEnum.ENABLE.getStatus());
            final List<String> list = syncPloyDetailSnapshotEntities.stream().map(SyncPloyDetailSnapshotEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                log.info("update currency list :{},newSnapshotId:{},oldSnapshotId:{}", JSONObject.toJSONString(list), newSnapshotId, oldSnapshotId);
                oldSnapshotId = list.get(0);
                list.forEach(oldId -> adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateStatusById(oldId, SyncPloyDetailStatusEnum.DISABLE.getStatus()));
                log.info("updateIntegrationStreamInEnabled disableOldSnapshot tenantId:{} oldSnapshotId:{}", tenantId, list);
            }

            callback = false;
        } finally {
            if (callback) {
//                回滚数据
                adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).update(oldEntity);
                log.info("updateIntegrationStreamInEnabled callback tenantId:{} id:{}", tenantId, arg.getId());
                if (Objects.nonNull(oldSnapshotId)) {
                    adminSyncPloyDetailSnapshotDao.updateStatusById(oldSnapshotId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
                    log.info("updateIntegrationStreamInEnabled callback oldSnapshotId:{} tenantId:{} id:{}", oldSnapshotId, tenantId, arg.getId());
                }
                if (Objects.nonNull(newSnapshotId)) {
                    adminSyncPloyDetailSnapshotDao.updateStatusById(newSnapshotId, SyncPloyDetailStatusEnum.DISABLE.getStatus());
                    log.info("updateIntegrationStreamInEnabled callback newSnapshotId:{} tenantId:{} id:{}", newSnapshotId, tenantId, arg.getId());
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<SyncPloyDetailData>> getTop1000ByDcId(String tenantId, String dcId) {
        final List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceOrDestDcIdAndObjApiName(tenantId, dcId, null,
                        null, null, 0, 1000);
        final List<SyncPloyDetailData> ployDetailData = BeanUtil2.deepCopyList(syncPloyDetailEntities, SyncPloyDetailData.class);

        return Result.newSuccess(ployDetailData);
    }

    @Override
    public Result<Integer> countModifiedCrmData(String tenantId, String objApiName, Long beginTime, String lang) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), -10000);
        searchTemplateQuery.addFilter("last_modified_time", Lists.newArrayList(String.valueOf(beginTime)), FilterOperatorEnum.GTE);
        searchTemplateQuery.addFilter("last_modified_time", Lists.newArrayList(String.valueOf(System.currentTimeMillis())), FilterOperatorEnum.LT);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setSearchSource("es");
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setNeedReturnCountNum(true);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objApiName);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchTemplateQuery));
        findV3Arg.setNeedCount(true);
        //指定返回字段
        findV3Arg.setSelectFields(CollUtil.newArrayList("_id"));
        try {
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> result = objectDataServiceV3.queryList(headerObj, findV3Arg);
            if (result.isSuccess()) {
                return Result.newSuccess(result.getData().getQueryResult().getTotalNumber());
            } else {
                return Result.newError(i18NStringManager.get(I18NStringEnum.s793, lang, tenantId) + result.getMessage());
            }
        } catch (Exception e) {
            log.info("get count exception", e);
            return Result.newError(i18NStringManager.get(I18NStringEnum.s794, lang, tenantId) + ExceptionUtil.getSimpleMessage(e));
        }
    }


    private void updateAggregationConfig(String tenantId) {
        ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne(tenantId,
                "0",
                "ALL",
                TenantConfigurationTypeEnum.MONGO_DISPATCHER_DATA_AGGREGATION_TIME.name());

        Map<String, Integer> configMap = new HashMap<>();
        configMap.put("BomInstanceObj", 2000);
        configMap.put("SalesOrderObj", 60 * 1000);

        if (configurationEntity == null) {
            configurationEntity = ErpTenantConfigurationEntity.builder()
                    .id(idGenerator.get())
                    .tenantId(tenantId)
                    .dataCenterId("0")
                    .channel("ALL")
                    .type(TenantConfigurationTypeEnum.MONGO_DISPATCHER_DATA_AGGREGATION_TIME.name())
                    .configuration(JSONObject.toJSONString(configMap))
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .build();
            int count = tenantConfigurationManager.insert(tenantId, configurationEntity);
            log.info("SyncPloyDetailController.updateAggregationConfig,insert default aggregation time,success={}", count == 1);
        } else {
            JSONObject configJson = null;
            try {
                configJson = JSONObject.parseObject(configurationEntity.getConfiguration());
            } catch (Exception e) {
                configJson = new JSONObject();
            }
            configJson.putAll(configMap);
            configurationEntity.setConfiguration(configJson.toJSONString());
            int count = tenantConfigurationManager.updateById(tenantId, configurationEntity);
            log.info("SyncPloyDetailController.updateAggregationConfig,update default aggregation time,success={}", count == 1);
        }
    }

    @Override
    public Result<Void> checkInitPollingIntervalAndSyncTime(String dcId, String id, String tenantId, SyncPloyDetailResult ployDetailResult) {
        if (!ployDetailResult.getSourceTenantType().equals(TenantType.ERP)) {
            return Result.newSuccess();
        }
        SyncRulesResult syncRulesResult = ployDetailResult.getSyncRulesResult();
        SyncRulesResult.PollingInterval pollingIntervalRes = syncRulesResult.getPollingInterval();
        PollingIntervalApiDto pollingInterval = new PollingIntervalApiDto();
        if (pollingIntervalRes == null) {
            pollingInterval.setTimeUnit(IntervalTimeUnitEnum.minutes);
            pollingInterval.setCronExpression(configCenterConfig.getCronBeginMinute(tenantId, ployDetailResult.getSourceObjectApiName()) + "/6 * * * *");
            pollingInterval.setIntervalQuantity(6);//默认时间间隔为6分钟
            pollingInterval.setStartDataTime("00:00");
            pollingInterval.setEndDataTime("23:59");
            //插入轮询时间
            insertPollingTime(tenantId, id, pollingInterval);
        } else {
            BeanUtils.copyProperties(pollingIntervalRes, pollingInterval);
        }
        Collection<Integer> events = getSyncRuleEvents(tenantId,ployDetailResult.getSourceObjectApiName());
        if (events.isEmpty()) {
            events = Lists.newArrayList(1, 2);
        }
        events.stream()
                .filter(ExtraEventTypeEnum.permittedType::contains)
                .filter(event -> Objects.isNull(erpSyncTimeDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByUnique(tenantId, ployDetailResult.getSourceObjectApiName(), event)))
                .findFirst()
                //初始化轮询任务
                .ifPresent(entity -> initLastSyncTime(tenantId, ployDetailResult.getSourceObjectApiName(), pollingInterval));

        List<String> syncTypeList = ployDetailResult.getSyncTypeList();
        startRulesAddOrRemoveApiNames(tenantId, ployDetailResult.getSourceObjectApiName(), dcId, syncTypeList);
        return Result.newSuccess();
    }

    private Set<CheckAndUpdatePloyValidStatusDetailData> checkDetail(String tenantId, SyncPloyDetailEntity syncPloyDetailEntity, String lang) {
        //校验详情细节
        Set<CheckAndUpdatePloyValidStatusDetailData> sets = Sets.newHashSet();
        Map<String, UpdateIntegrationStreamResult.ErrorMsg> mapResult = integrationStreamManager.commonVerifyStreamDetail(tenantId, syncPloyDetailEntity, lang);
        if (ObjectUtils.isNotEmpty(mapResult)) {
            for (String key : mapResult.keySet()) {
                UpdateIntegrationStreamResult.ErrorMsg item = mapResult.get(key);
                if (!item.getInvalid()) {
                    continue;
                }
                switch (key) {
                    case "sourceSystemNode":
                    case "destSystemNode":
                        sets.add(CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.API_NAMES_NOT_EXISTS.getType(), item.getErrMsg()));
                        break;
                    case "beforeFunctionNode":
                    case "durationFunctionApiNode":
                    case "afterFunctionNode":
                        sets.add(CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.FUNCTION_NOT_VALID.getType(), item.getErrMsg()));
                    case "syncConditionsNode":
                    case "fieldMappingNode":
                        List<IntegrationFieldVerifyResult> integrationFieldVerifyResults = JSONArray.parseArray(item.getErrMsg(), IntegrationFieldVerifyResult.class);
                        integrationFieldVerifyResults.removeIf(integrationFieldVerifyResult -> integrationFieldVerifyResult.getErrorCode().getErrCode().equals(ResultCodeEnum.OBJECT_REQUIRED_FIELD_NOT_MAPPING.getErrCode()));
                        if (CollectionUtils.isNotEmpty(integrationFieldVerifyResults)) {
                            sets.add(CheckAndUpdatePloyValidStatusDetailData.newData(tenantId, CheckAndUpdatePloyValidErrorTypeEnum.MAPPING_COMMON_FIELDS_ERROR.getType(), item.getErrMsg()));
                        }
                    default:
                        break;

                }
            }
        }
        return sets;
    }

    @Override
    public Result<Map<String, Map<String, Set<Object>>>> getAllObjectAndFieldApiNameByPloy(List<SyncPloyDetailData> ployDetails) {

        Map<String, Map<String, Set<Object>>> objectAndFieldApiName = new HashMap<>();
        ployDetails.forEach(detail -> {
            final boolean sourceCrm = Objects.equals(detail.getSourceTenantType(), TenantTypeEnum.CRM.getType());
            addMasterFields(detail, objectAndFieldApiName, sourceCrm);
            addDetailFields(detail, objectAndFieldApiName, sourceCrm);

            addQueryCrmNodeFields(detail, objectAndFieldApiName, sourceCrm);
        });

        return Result.newSuccess(objectAndFieldApiName);
    }

    @Override
    public Result<List<SyncWalkingNodeResult>> getPloyDetailNodeSettings(String tenantId, String id, String lang) {
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id);
        if (ObjectUtils.isEmpty(entity)) {
            return Result.newSuccess();
        }
        List<SyncWalkingNodeResult> results = convertPloyDetailResult(entity, lang, tenantId);
        return Result.newSuccess(results);
    }

    public List<SyncWalkingNodeResult> convertPloyDetailResult(SyncPloyDetailEntity entity, String lang, String tenantId) {
        //目前集成流节点跟日志节点还对应不上，集成流相关的节点先写死
        List<SyncWalkingNodeResult> results = Lists.newArrayList();
        IntegrationStreamNodesData integrationStreamNodes = entity.getIntegrationStreamNodes();
        List<SyncWalkingNodeResult> sourceChildNodes = Lists.newArrayList();
        List<SyncWalkingNodeResult> destChildNodes = Lists.newArrayList();
        List<SyncWalkingNodeResult> tempChildNodes = Lists.newArrayList();
        for (PloyDetailNodeEnum value : PloyDetailNodeEnum.values()) {
            if(value.getType().equals(PloyDetailNodeEnum.DATA_MAPPING.getType())){//中间表不返回由前端写死
                continue;
            }
            if (TenantType.CRM == entity.getSourceTenantType()&&value.getType().equals(PloyDetailNodeEnum.TEMP_NODE.getType())) {//源为crm，没有临时库
                continue;
            }
            if (value.getType().equals(PloyDetailNodeEnum.ID_MAPPING.getType())) {
                if (integrationStreamNodes == null || integrationStreamNodes.getCheckSyncDataMappingNode() == null
                        || integrationStreamNodes.getCheckSyncDataMappingNode().getQueryObjectMappingData() == null) {
                    continue;
                }
            }
            if (value.getType().equals(PloyDetailNodeEnum.PRE_FUNCTION.getType()) && ObjectUtils.isEmpty(entity.getBeforeFuncApiName())) {
                continue;
            }
            if (value.getType().equals(PloyDetailNodeEnum.CRM_QUERY_BY_SOURCE.getType())) {
                if (integrationStreamNodes == null || integrationStreamNodes.getQueryCrmObject2DestNodeBySource() == null
                        || integrationStreamNodes.getQueryCrmObject2DestNodeBySource().getQueryObjectToDestObject() == null) {
                    continue;
                }
            }
            if (value.getType().equals(PloyDetailNodeEnum.CRM_QUERY_BY_DEST.getType())) {
                if (integrationStreamNodes == null || integrationStreamNodes.getQueryCrmObject2DestNodeByDest() == null
                        || integrationStreamNodes.getQueryCrmObject2DestNodeByDest().getQueryObjectToDestObject() == null) {
                    continue;
                }
            }
            if (value.getType().equals(PloyDetailNodeEnum.MID_FUNCTION.getType()) && ObjectUtils.isEmpty(entity.getDuringFuncApiName())) {
                continue;
            }
            if (value.getType().equals(PloyDetailNodeEnum.REVERSE_WRITE.getType())) {
                if(TenantType.ERP == entity.getSourceTenantType()){
                    continue;
                }else {
                    if (integrationStreamNodes == null || integrationStreamNodes.getReverseWriteNode() == null) {
                        continue;
                    }
                }

            }
            if (value.getType().equals(PloyDetailNodeEnum.AFTER_FUNCTION.getType()) && ObjectUtils.isEmpty(entity.getAfterFuncApiName())) {
                continue;
            }
            if (value.getType().equals(PloyDetailNodeEnum.CONDITIONAL_RETRY.getType())) {
                if (integrationStreamNodes == null || integrationStreamNodes.getReSyncErrorDataNode() == null) {
                    continue;
                }
            }
            if (value.getType().equals(PloyDetailNodeEnum.CRM_NOTIC.getType())) {
                if (integrationStreamNodes == null || integrationStreamNodes.getNotifyComplementNode() == null) {
                    continue;
                }
            }

            SyncWalkingNodeResult nodeResult = SyncWalkingNodeResult.builder().nodeName(i18NStringManager.get(value.getI18nKey(), lang, tenantId, value.getDescription()))
                    .nodeType(value.getType()).nodeOrder(value.getOrder()).icon(value.getIcon()).canQueryLog(value.getCanQueryLog()).build();
            if (value.getType().equals(PloyDetailNodeEnum.SOURCE_SYSTEM.getType())) {
                nodeResult.setChildNodes(sourceChildNodes);
            }
            if (value.getType().equals(PloyDetailNodeEnum.DEST_SYSTEM.getType())) {
                nodeResult.setChildNodes(destChildNodes);
            }
            if (value.getType().equals(PloyDetailNodeEnum.TEMP_NODE.getType())) {
                nodeResult.setChildNodes(tempChildNodes);
            }
            if (value.getType().equals(PloyDetailNodeEnum.READ.getType()) || value.getType().equals(PloyDetailNodeEnum.CRM_TRIGGER.getType())) {//源系统的子节点
                if (TenantType.CRM == entity.getSourceTenantType()) {//CRM->erp只有数据触发节点
                    if (value.getType().equals(PloyDetailNodeEnum.CRM_TRIGGER.getType())) {
                        sourceChildNodes.add(nodeResult);
                    }
                } else {
                    if (!value.getType().equals(PloyDetailNodeEnum.CRM_TRIGGER.getType())) {
                        sourceChildNodes.add(nodeResult);
                    }
                }

            } else if (value.getType().equals(PloyDetailNodeEnum.WRITE.getType())) {//目标系统的子节点
                destChildNodes.add(nodeResult);
            } else if (value.getType().equals(PloyDetailNodeEnum.TEMP.getType()) || value.getType().equals(PloyDetailNodeEnum.TEMP_DATA.getType())) {//临时库的子节点
                tempChildNodes.add(nodeResult);
            } else {
                results.add(nodeResult);
            }
        }
        return results;
    }

    private static void addQueryCrmNodeFields(SyncPloyDetailData detail, Map<String, Map<String, Set<Object>>> objectAndFieldApiName, boolean sourceCrm) {
        Optional.ofNullable(detail.getIntegrationStreamNodes())
                .map(IntegrationStreamNodesData::getQueryCrmObject2DestNodeBySource)
                .ifPresent(queryCrmNode -> addQueryCrmNodeFields(objectAndFieldApiName, queryCrmNode, sourceCrm));

        Optional.ofNullable(detail.getIntegrationStreamNodes())
                .map(IntegrationStreamNodesData::getQueryCrmObject2DestNodeByDest)
                .ifPresent(queryCrmNode -> addQueryCrmNodeFields(objectAndFieldApiName, queryCrmNode, !sourceCrm));
    }

    private static void addQueryCrmNodeFields(Map<String, Map<String, Set<Object>>> objectAndFieldApiName, IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmNode, boolean sourceCrm) {
        Optional.ofNullable(queryCrmNode.getQueryObjectToDestObject())
                .ifPresent(queryObjectToDestObject -> addQueryCrmNodeFields(objectAndFieldApiName, queryObjectToDestObject, sourceCrm));

        // 明细查找
        Optional.ofNullable(queryCrmNode.getDetailQueryData2DestDataMapping())
                .ifPresent(queryData2DestDataMapping ->
                        queryData2DestDataMapping.forEach(objectToDestObjectData -> addQueryCrmNodeFields(objectAndFieldApiName, objectToDestObjectData, sourceCrm)));
    }

    public static void addQueryCrmNodeFields(Map<String, Map<String, Set<Object>>> objectAndFieldApiName, List<QueryObjectToDestObjectData> queryObjectToDestObject, boolean sourceCrm) {
        queryObjectToDestObject.forEach(queryObjectToDestObjectData -> {
            // 查询条件
            Optional.ofNullable(queryObjectToDestObjectData.getQueryObjectMappingData()).ifPresent(queryObjectMappingData -> addFieldMappingData(objectAndFieldApiName, queryObjectMappingData, sourceCrm));
            // 查询crm后的设置配置
            Optional.ofNullable(queryObjectToDestObjectData.getQueryData2DestDataMapping()).ifPresent(detailObjectMappingData -> {
                // 查询crm节点的source一定是crm,sourceCrm给true
                addFieldMappingData(objectAndFieldApiName, detailObjectMappingData.getSourceObjectApiName(), detailObjectMappingData.getFieldMappings(), true);
                if (sourceCrm) {
                    // 查询的值赋给crm,所以crm的字段在dest,sourceCrm给false
                    addFieldMappingData(objectAndFieldApiName, detailObjectMappingData.getDestObjectApiName(), detailObjectMappingData.getFieldMappings(), false);
                }
            });
        });
    }

    private static void addDetailFields(SyncPloyDetailData detail, Map<String, Map<String, Set<Object>>> objectAndFieldApiName, boolean source) {
        // 字段映射
        Optional.ofNullable(detail.getDetailObjectMappings()).ifPresent(data -> data.forEach(detailObjectMappingData -> {
            final String objectApiName = source ? detailObjectMappingData.getSourceObjectApiName() : detailObjectMappingData.getDestObjectApiName();
            if (StringUtils.isEmpty(objectApiName)) {
                return;
            }
            objectAndFieldApiName.computeIfAbsent(objectApiName, k -> new HashMap<>());
            addFieldMappingData(objectAndFieldApiName, objectApiName, detailObjectMappingData.getFieldMappings(), source);
        }));

        if (source) {
            // 数据范围
            Optional.ofNullable(detail.getDetailObjectSyncConditions())
                    .ifPresent(data -> data.forEach(syncConditionsData ->
                            addFieldMappingData(objectAndFieldApiName, syncConditionsData.getApiName(), syncConditionsData.getFilters())));
            // 反写节点
            Optional.ofNullable(detail.getIntegrationStreamNodes())
                    .map(IntegrationStreamNodesData::getReverseWriteNode)
                    .map(IntegrationStreamNodesData.ReverseWriteNode::getDetailObjectMappings)
                    .ifPresent(data -> data.forEach(detailObjectMappingData ->
                            addFieldMappingData(objectAndFieldApiName, detailObjectMappingData.getSourceObjectApiName(), detailObjectMappingData.getFieldMappings(), source)));
        }
    }

    private static void addMasterFields(SyncPloyDetailData detail, Map<String, Map<String, Set<Object>>> objectAndFieldApiName, boolean source) {
        final String sourceObjectApiName = source ? detail.getSourceObjectApiName() : detail.getDestObjectApiName();
        if (StringUtils.isEmpty(sourceObjectApiName)) {
            return;
        }

        objectAndFieldApiName.computeIfAbsent(sourceObjectApiName, k -> new HashMap<>());

        // 字段映射
        Optional.ofNullable(detail.getFieldMappings())
                .ifPresent(fieldMappings -> addFieldMappingData(objectAndFieldApiName, sourceObjectApiName, fieldMappings, source));

        if (source) {
            // 数据范围
            Optional.ofNullable(detail.getSyncConditions())
                    .ifPresent(syncConditionsData ->
                            addFieldMappingData(objectAndFieldApiName, syncConditionsData.getApiName(), syncConditionsData.getFilters()));

            // 反写节点
            Optional.ofNullable(detail.getIntegrationStreamNodes())
                    .map(IntegrationStreamNodesData::getReverseWriteNode)
                    .map(IntegrationStreamNodesData.ReverseWriteNode::getFieldMappings)
                    .ifPresent(fieldMappingData -> addFieldMappingData(objectAndFieldApiName, sourceObjectApiName, fieldMappingData, source));
        }
    }

    public static final Set<String> OPTION_FIELD_TYPE = Sets.newHashSet(CrmFieldTypeContants.SELECT_ONE, CrmFieldTypeContants.SELECT_MANY, CrmFieldTypeContants.MULTI_LEVEL_SELECT_ONE);

    /**
     * 查询条件没有选项
     */
    private static void addFieldMappingData(Map<String, Map<String, Set<Object>>> objectAndFieldApiName, String sourceObjectApiName, QueryObjectOrFilterFieldMappingsData queryFieldMappings, Function<FilterData, String> func) {
        Map<String, Set<Object>> fields = objectAndFieldApiName.computeIfAbsent(sourceObjectApiName, k -> new HashMap<>());
        queryFieldMappings.stream().flatMap(Collection::stream).map(func).distinct()
                .forEach(field -> fields.computeIfAbsent(field, k -> new HashSet<>()));
    }

    private static void addFieldMappingData(Map<String, Map<String, Set<Object>>> objectAndFieldApiName, QueryObjectMappingData queryObjectMappingData, boolean sourceCrm) {
        addFieldMappingData(objectAndFieldApiName, queryObjectMappingData.getSourceObjectApiName(), queryObjectMappingData.getQueryFieldMappings(), FilterData::getFieldApiName);
        if (sourceCrm) {
            addFieldMappingData(objectAndFieldApiName, queryObjectMappingData.getDestObjectApiName(), queryObjectMappingData.getQueryFieldMappings(), filterData -> filterData.getFieldValue().get(0).toString());
        }
    }

    public static void addFieldMappingData(Map<String, Map<String, Set<Object>>> objectAndFieldApiName, String sourceObjectApiName, List<List<FilterData>> fieldMappings) {
        Map<String, Set<Object>> fields = objectAndFieldApiName.computeIfAbsent(sourceObjectApiName, k -> new HashMap<>());
        fieldMappings.stream()
                .flatMap(Collection::stream)
                .map(filterData -> {
                    final String fieldApiName = filterData.getFieldApiName();
                    if (Objects.equals(filterData.getFieldType(), FieldType.SELECT_ONE) || Objects.equals(filterData.getFieldType(), FieldType.SELECT_MANY)) {
                        return Pair.of(fieldApiName, filterData.getFieldValue());
                    }
                    return Pair.of(fieldApiName, new ArrayList<>());
                }).filter(pair -> StringUtils.isNotBlank(pair.getLeft()))
                .forEach(pair -> fields.computeIfAbsent(pair.getKey(), k -> new HashSet<>()).addAll(pair.getValue()));
    }

    private static void addFieldMappingData(Map<String, Map<String, Set<Object>>> objectAndFieldApiName, String sourceObjectApiName, List<FieldMappingData> fieldMappings, boolean sourceCrm) {
        final Map<String, Set<Object>> fields = objectAndFieldApiName.computeIfAbsent(sourceObjectApiName, k -> new HashMap<>());
        fieldMappings.stream()
                .map(fieldMappingData -> getFieldMappingCrmFieldWithOptions(fieldMappingData, sourceCrm))
                .filter(pair -> StringUtils.isNotBlank(pair.getLeft()))
                .forEach(pair -> fields.computeIfAbsent(pair.getKey(), k -> new HashSet<>()).addAll(pair.getValue()));
    }

    @NotNull
    public static Pair<String, Set<Object>> getFieldMappingCrmFieldWithOptions(FieldMappingData fieldMappingData, boolean sourceCrm) {
        final String apiName;
        Set<Object> options = new HashSet<>();
        if (sourceCrm) {
            apiName = fieldMappingData.getSourceApiName();
            if (OPTION_FIELD_TYPE.contains(fieldMappingData.getSourceType()) && Objects.equals(FieldMappingTypeEnum.SOURCE_VALUE.getType(), fieldMappingData.getMappingType()) && CollectionUtils.isNotEmpty(fieldMappingData.getOptionMappings())) {
                options = fieldMappingData.getOptionMappings().stream()
                        .map(OptionMappingData::getSourceOption)
                        .collect(Collectors.toSet());
            }
        } else {
            apiName = fieldMappingData.getDestApiName();
            if (OPTION_FIELD_TYPE.contains(fieldMappingData.getDestType())) {
                if (Objects.equals(FieldMappingTypeEnum.SOURCE_VALUE.getType(), fieldMappingData.getMappingType()) && CollectionUtils.isNotEmpty(fieldMappingData.getOptionMappings())) {
                    options = fieldMappingData.getOptionMappings().stream()
                            .map(OptionMappingData::getDestOption)
                            .collect(Collectors.toSet());
                } else if (Objects.equals(FieldMappingTypeEnum.FIXED_VAULE.getType(), fieldMappingData.getMappingType())) {
                    final String value = Objects.equals(fieldMappingData.getValueType(), 1) ? fieldMappingData.getValue() : fieldMappingData.getDefaultValue();
                    options.add(value);
                }
            }
        }

        return Pair.of(apiName, options);
    }

    private Result<Set<CheckAndUpdatePloyValidStatusDetailData>> checkIntegrationStreamMapping(String tenantId, SyncPloyDetailEntity entity, String lang) {
        ErpConnectInfoEntity sourceConnectInfo = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, entity.getSourceDataCenterId());
        final boolean crmSource = ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel());
        //校验主键字段
        List<String> noIdFieldObjApiNames = Stream.concat(
                        entity.getDetailObjectMappings().stream()
                                .map(mapping -> crmSource ? mapping.getDestObjectApiName() : mapping.getSourceObjectApiName()),
                        Stream.of(crmSource ? entity.getDestObjectApiName() : entity.getSourceObjectApiName())
                ).distinct()
                .filter(name -> Objects.isNull(erpFieldManager.findIdField(tenantId, name)))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noIdFieldObjApiNames)) {
            return Result.newError(i18NStringManager.get2(I18NStringEnum.s2064.getI18nKey(), lang, tenantId, I18NStringEnum.s2064.getI18nValue(), Lists.newArrayList(String.join(",", noIdFieldObjApiNames))));
        }

        //检查从对象的主从字段
        if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
            List<String> erpDetailObjApiNames;
            String erpDcId;
            if (ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {
                erpDetailObjApiNames = entity.getDetailObjectMappings().stream().map(DetailObjectMappingsData.DetailObjectMappingData::getDestObjectApiName).collect(Collectors.toList());
                erpDcId = entity.getDestDataCenterId();
            } else {
                erpDetailObjApiNames = entity.getDetailObjectMappings().stream().map(DetailObjectMappingsData.DetailObjectMappingData::getSourceObjectApiName).collect(Collectors.toList());
                erpDcId = entity.getSourceDataCenterId();
            }
            List<ErpObjectFieldEntity> masterDetailFields = erpObjectFieldDao.findByObjsAndType(tenantId, erpDcId, erpDetailObjApiNames, ErpFieldTypeEnum.master_detail);
            Set<String> validObjApiNames = masterDetailFields.stream().map(ErpObjectFieldEntity::getErpObjectApiName).collect(Collectors.toSet());
            Collection<String> subtract = CollUtil.subtract(erpDetailObjApiNames, validObjApiNames);
            if (!subtract.isEmpty()) {
                return Result.newError(i18NStringManager.get(I18NStringEnum.s757, lang, tenantId) + subtract);
            }
        }
        return null;
    }
}
