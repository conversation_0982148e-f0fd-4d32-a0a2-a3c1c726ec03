package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:24 2023/5/19
 * @Desc:
 */
@Data
public class DetailQueryData2SyncDataMappingResult implements Serializable {
    @ApiModelProperty("查询crm")
    private QueryObjectMappingResult queryObjectMappingData; //查询crm
    @ApiModelProperty("对象字段映射到目标")
    private List<IdFieldMappingResult> source2SyncDataMapping;//集成流源对象字段映射到中间表、查出来的对象映射到中间表
}
