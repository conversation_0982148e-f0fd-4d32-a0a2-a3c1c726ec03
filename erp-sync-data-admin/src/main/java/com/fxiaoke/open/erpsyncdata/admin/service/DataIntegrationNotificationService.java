package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.arg.GetDataIntegrationNotificationArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.apache.kafka.common.protocol.types.Field;

import java.util.List;
import java.util.Map;

public interface DataIntegrationNotificationService {
    /**
     * 获取数据集成通知数据列表
     *
     * @param arg
     * @return
     */
    Result<DataIntegrationNotificationModel> getDataList(GetDataIntegrationNotificationArg arg);

    /**
     * 获取连接器列表，去重
     *
     * @return
     */
    Result<List<DataCenterModel>> getDcList(String tenantId);

    /**
     * 检查用户是否有CRM管理员权限或系统管理员权限
     * @param tenantId
     * @param userId
     * @return
     */
    Result<Boolean> checkAdmin(String tenantId,String userId);

    /**
     * 清空数据集成通知表的数据
     * @return
     */
    Result<Long> clearData();

    /**
     * 判断当前企业是否有数据集成通知入口的权限
     * @param userId
     * @return
     */
    Result<Boolean> hasData(String tenantId, Integer userId);
}
