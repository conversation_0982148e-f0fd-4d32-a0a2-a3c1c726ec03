package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateObjectMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.UpdateIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.data.DataCenterData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.fxiaoke.open.erpsyncdata.admin.model.ErpAnalysisField;
import com.fxiaoke.open.erpsyncdata.admin.model.InitK3Obj;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.QueryIntegrationDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.UpdateIntegrationStreamResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjPresetService;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorConfigHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorMetaDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.MetaDataInfoManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateMetaDataInfoManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ErpObjectDescribe;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ObjectDescArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SubDetailExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.InitDcResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/20
 */
@Slf4j
@Service
public class ErpObjPresetServiceImpl implements ErpObjPresetService {
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao relationshipDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private MetaDataInfoManager metaDataInfoManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private K3UltimateMetaDataInfoManager k3UltimateMetaDataInfoManager;


    @Override
    public Result<String> preSetK3Obj(String tenantId, String dcId, InitK3Obj.PresetObjArg arg, String lang) {
        String formId = arg.getErpObjectApiName();
        ErpObjectDescribe erpObjectDescribe = analyzeK3ObjDescCache(tenantId, dcId, formId, lang).safeData();
        Set<String> needInitObjs = arg.getErpObjectApiNames();
        ErpConnectInfoEntity tenantInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dcId);
        //主表未存在，加上主表
        String mainSplitObjApiName = formId + ".BillHead";
        needInitObjs.add(mainSplitObjApiName);
        //仅保留传参的对象以及主对象
        erpObjectDescribe.getSplitObjDescribes().keySet().retainAll(needInitObjs);
        //只允许使用了分批单据查询的对象增加孙对象
        boolean hasSubDetail = erpObjectDescribe.getSplitObjDescribes().values().stream().anyMatch(v -> ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL.equals(v.getObjRelation().getSplitType()));
        if (hasSubDetail) {
            Boolean useplitDetailInterface = !tenantConfigurationManager.getUseViewInterFace(tenantId, dcId, formId);
            if (!useplitDetailInterface) {
                return Result.newSystemError(I18NStringEnum.s385);
            }
        }
        ErpObjectDescribe.SplitObjDescribe mainDescribe = erpObjectDescribe.getSplitObjDescribes().get(mainSplitObjApiName);
        if (mainDescribe != null) {
            if (arg.isExist()) {
                //主表已经存在时，移除除了明细字段之外的其他字段
                mainDescribe.getFields().keySet().retainAll(needInitObjs);
                mainDescribe.getFieldExtends().keySet().retainAll(needInitObjs);
            } else {
                //主表未存在，明细字段值保留需要预置的对象。
                mainDescribe.getFields().values().removeIf(v -> ErpFieldTypeEnum.detail.equals(v.getFieldDefineType()) && !needInitObjs.contains(v.getFieldApiName()));
                mainDescribe.getFieldExtends().values().removeIf(v -> ErpFieldTypeEnum.detail.equals(v.getFieldDefineType()) && !needInitObjs.contains(v.getFieldApiName()));
            }
        }

        Result<String> incrementResult = incrementInitObjField(tenantId, dcId, tenantInfo.getChannel(), erpObjectDescribe);
        log.info("increment init obj,{}", incrementResult);
        return incrementResult;
    }

    /**
     * 统一走缓存
     *
     * @param tenantId
     * @param dcId
     * @param formId
     * @return
     */
    private Result<ErpObjectDescribe> analyzeK3ObjDescCache(String tenantId, String dcId, String formId, String lang) {
        String analyzingNowStr = "analyzing now";
        String cacheKey = String.format(CommonConstant.REDIS_CACHE_ANALYSE_K3_OBJ, tenantId + dcId, formId);
        //从缓存取
        String cacheStr = redisCacheManager.getCache(cacheKey, this.getClass().getSimpleName());
        Result<ErpObjectDescribe> erpObjectDescribeRes;
        if (cacheStr != null) {
            if (Objects.equals(cacheStr, analyzingNowStr)) {
                //正在解析中，稍候重试。
                return Result.newError(ResultCodeEnum.ANALYZING_K3_OBJ);
            }
            erpObjectDescribeRes = JacksonUtil.fromJson(cacheStr, new TypeReference<Result<ErpObjectDescribe>>() {
            });
            return erpObjectDescribeRes;
        }

        //无缓存,先修改为解析进行中
        redisCacheManager.setCache(cacheKey, analyzingNowStr, TimeUnit.MINUTES.toSeconds(10L), this.getClass().getSimpleName());
        try {
            erpObjectDescribeRes = analyzeK3ObjDesc(tenantId, dcId, formId, lang);
        } catch (Exception e) {
            log.warn("analyze exception", e);
            erpObjectDescribeRes = Result.wrapException(e);
        }
        if (!erpObjectDescribeRes.isSuccess()) {
            erpObjectDescribeRes.setErrMsg(i18NStringManager.get(I18NStringEnum.s727, lang, tenantId) + erpObjectDescribeRes.getErrMsg());
            //缓存结果,失败缓存10分钟
            redisCacheManager.setCache(cacheKey, JacksonUtil.toJson(erpObjectDescribeRes), TimeUnit.MINUTES.toSeconds(10L), this.getClass().getSimpleName());
        } else {
            //缓存结果,成功缓存2小时
            redisCacheManager.setCache(cacheKey, JacksonUtil.toJson(erpObjectDescribeRes), TimeUnit.HOURS.toSeconds(2L), this.getClass().getSimpleName());
        }
        return erpObjectDescribeRes;
    }

    /**
     * 解析K3描述
     * 先用客户的连接信息，差不多则使用测试系统的连接信息
     *
     * @param tenantId
     * @param dcId
     * @param formId
     * @return
     */
    private Result<ErpObjectDescribe> analyzeK3ObjDesc(String tenantId, String dcId, String formId, String lang) {
        preCheck(tenantId, dcId, formId);
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dcId);
        if (connectInfo == null || connectInfo.getConnectParams() == null) {
            return Result.newError(ResultCodeEnum.CONNECT_INFO_NOT_EXISTS);
        }
        if(ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.equals(connectInfo.getChannel())){
            return k3UltimateMetaDataInfoManager.getErpObjectDescribe(tenantId,dcId,formId);
        }else{
            K3CloudConnectParam k3CloudConnectParam = JacksonUtil.fromJson(connectInfo.getConnectParams(), K3CloudConnectParam.class);
            Result<ErpObjectDescribe> customerK3Result;
            customerK3Result = tryGetK3Describe(tenantId, dcId, formId, k3CloudConnectParam, false, lang);
            if (!customerK3Result.isSuccess()) {
                //尝试使用测试账套的K3连接信息来查询
                k3CloudConnectParam = new K3CloudConnectParam();
                k3CloudConnectParam.setBaseUrl("http://172.31.100.60/k3cloud/");
                k3CloudConnectParam.setDbId("5ec229fad54306");
                k3CloudConnectParam.setDbName(i18NStringManager.getByEi(I18NStringEnum.s3732, tenantId));
                k3CloudConnectParam.setAuthType(1);
                k3CloudConnectParam.setUserName("ces2");
                k3CloudConnectParam.setPassword("8888888");
                Result<ErpObjectDescribe> testK3Result = tryGetK3Describe(tenantId, dcId, formId, k3CloudConnectParam, true, lang);
                if (!testK3Result.isSuccess()) {
                    return Result.copy(customerK3Result);
                } else {
                    customerK3Result = testK3Result;
                }
            }
            return customerK3Result;
        }

    }

    private Result<ErpObjectDescribe> tryGetK3Describe(final String tenantId,
                                                       String dcId,
                                                       String formId,
                                                       K3CloudConnectParam k3CloudConnectParam,
                                                       boolean ignoreCustomField,
                                                       String lang) {
        Result<ErpObjectDescribe> customerK3Result;
        try {
            K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, k3CloudConnectParam, dcId);
            customerK3Result = metaDataInfoManager.getErpObjectDescribe(apiClient, new QueryBusinessInfoArg(formId), ignoreCustomField, lang);
        } catch (ErpSyncDataException e) {
            //异常
            customerK3Result = Result.newError(e.getErrCode(), e.getErrMsg());
        }
        return customerK3Result;
    }

    @Override
    public Result<ErpObjTreeNode> analyzeK3Obj(String tenantId, String dcId, InitK3Obj.AnalyzeObjArg analyzeObjArg, String lang) {
        String formId = analyzeObjArg.getFormId();
        //查询已有的对象
        List<ErpObjectEntity> existObjs = erpObjectDao.queryByRealObjApiName(tenantId, dcId, formId);
        if (analyzeObjArg.isCheckExist() && !existObjs.isEmpty()) {
            return Result.newError(ResultCodeEnum.OBJECT_WANNA_ADD_ALREADY_EXISTS);
        }
        //需要将中间对象apiName转换为解析的不带dcSeq的
        Map<String, String> existObjApiNameMap = existObjs.stream().collect(Collectors.toMap(v -> removeDcSeq(tenantId, dcId, v.getErpObjectApiName()), v -> v.getErpObjectName(), (u, v) -> u));
        Result<ErpObjectDescribe> erpObjectDescribeRes = analyzeK3ObjDescCache(tenantId, dcId, formId, lang);
        //转换结果
        if (!erpObjectDescribeRes.isSuccess()) {
            return Result.copy(erpObjectDescribeRes);
        }
        ErpObjectDescribe erpObjectDescribe = erpObjectDescribeRes.getData();
        ErpObjectEntity realObj = erpObjectDescribe.getRealObj();
        ErpObjTreeNode vo = ErpObjTreeNode.of(realObj.getErpObjectApiName(), realObj.getErpObjectName());
        vo.setExist(!existObjs.isEmpty());
        Map<String, ErpObjTreeNode> detailMap = new HashMap<>();
        erpObjectDescribe.getSplitObjDescribes().forEach((objApiName, desc) -> {
            if (!desc.isMain()) {
                String erpObjectName = desc.getSplitObj().getErpObjectName();
                if (ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL.equals(desc.getObjRelation().getSplitType())) {
                    //明细子结构
                    ErpObjTreeNode detailTree = detailMap.get(desc.getParentObjApiName());
                    if (detailTree != null) {
                        ErpObjTreeNode subDetailTree = ErpObjTreeNode.of(objApiName, erpObjectName);
                        if (existObjApiNameMap.containsKey(objApiName)) {
                            subDetailTree.setExist(true).setErpObjectName(existObjApiNameMap.get(objApiName));
                        }
                        detailTree.getChildren().add(subDetailTree);
                    }
                }
                if (ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT.equals(desc.getObjRelation().getSplitType())) {
                    //明细
                    ErpObjTreeNode detailTree = ErpObjTreeNode.of(objApiName, erpObjectName);
                    if (existObjApiNameMap.containsKey(objApiName)) {
                        detailTree.setExist(true).setErpObjectName(existObjApiNameMap.get(objApiName));
                    }
                    vo.getChildren().add(detailTree);
                    detailMap.put(objApiName, detailTree);
                }

            }
        });
        return Result.newSuccess(vo);
    }

    @Override
    public Result<ErpAnalysisField> analyzeK3ObjField(String tenantId, String dcId, InitK3Obj.AnalysisFieldArg arg, String lang) {
        ErpAnalysisField erpAnalysisField = new ErpAnalysisField();
        if (StrUtil.isBlank(arg.getActualObjApiName())) {
            //尝试自动填充真实对象apiName
            String realObjApiName = erpObjManager.getRealObjApiName(tenantId, arg.getSplitObjApiName());
            arg.setActualObjApiName(realObjApiName);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dcId);
        ErpObjectDescribe erpObjectDescribe = analyzeK3ObjDescCache(tenantId, dcId, arg.getActualObjApiName(), lang).safeData();
        addDcSeq(tenantId, dcId, erpObjectDescribe, !ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())&&!ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.equals(connectInfo.getChannel()));
        String splitObjApiName = arg.getSplitObjApiName();
        ErpObjectDescribe.SplitObjDescribe splitObjDescribe = erpObjectDescribe.getSplitObjDescribes().get(splitObjApiName);
        if (splitObjDescribe == null) {
            return Result.newSuccess(erpAnalysisField);
        }
        List<ErpObjectFieldResult> resultList = erpAnalysisField.getErpObjectFields();
        if (StrUtil.isNotBlank(arg.getFieldApiName())) {
            //指定字段
            String fieldApiName = arg.getFieldApiName();
            final ErpObjectFieldEntity erpObjectFieldEntity = splitObjDescribe.getFields().get(fieldApiName);
            final ErpFieldExtendEntity erpFieldExtendEntity = splitObjDescribe.getFieldExtends().get(fieldApiName);
            if (erpObjectFieldEntity != null) {
                ErpObjectFieldResult fieldResult = convert2FieldResult(splitObjApiName, erpObjectFieldEntity, erpFieldExtendEntity);
                resultList.add(fieldResult);
            }
        } else {
            //查找s所有字段字段，去除原有字段
            List<ErpObjectFieldEntity> existFields = erpObjectFieldDao.findData(tenantId, dcId, splitObjApiName);
            for (ErpObjectFieldEntity existField : existFields) {
                splitObjDescribe.getFields().remove(existField.getFieldApiName());
            }
            splitObjDescribe.getFields().forEach((fieldApi, erpObjectFieldEntity) -> {
                ErpObjectFieldResult fieldResult = convert2FieldResult(splitObjApiName, erpObjectFieldEntity, splitObjDescribe.getFieldExtends().get(fieldApi));
                resultList.add(fieldResult);
            });
        }
        return Result.newSuccess(erpAnalysisField);
    }

    @Override
    public Result<List<ErpObjTreeNode>> parseObjectList(String tenantId, String dcId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dcId);
        ConnectorMetaDataHandler metaDataHandler = ConnectorHandlerFactory.getMetaDataHandlerNotNull(connectInfo.getChannel(), connectInfo.getConnectParams());
        Result<List<ErpObjTreeNode>> listResult = metaDataHandler.listObjects(connectInfo);
        if (!listResult.isSuccess()) {
            return listResult;
        }
        //标记是否已经存在,只判断主
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.listByDcId(tenantId, dcId);
        Set<String> existObjApiNames = erpObjectEntities.stream()
                .filter(v -> v.getErpObjectType() == ErpObjectTypeEnum.REAL_OBJECT)
                .map(v -> v.getErpObjectApiName()).collect(Collectors.toSet());
        for (ErpObjTreeNode datum : listResult.getData()) {
            if (existObjApiNames.contains(datum.getErpObjectApiName())) {
                datum.setExist(true);
            }
        }
        return listResult;
    }

    @Override
    public Result<ErpObjTreeNode> parseObject(String tenantId, String dcId, ObjectDescArg.ParseObjTree arg) {
        String realObjectApiName = arg.getRealObjectApiName();
        //查询已有的对象
        List<ErpObjectEntity> existObjs = erpObjectDao.queryByRealObjApiName(tenantId, dcId, realObjectApiName);
        if (arg.isCheckExist() && !existObjs.isEmpty()) {
            return Result.newError(ResultCodeEnum.OBJECT_WANNA_ADD_ALREADY_EXISTS);
        }
        Map<String, String> existObjApiNameMap = existObjs.stream().collect(Collectors.toMap(v -> removeDcSeq(tenantId, dcId, v.getErpObjectApiName()), v -> v.getErpObjectName(), (u, v) -> u));
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dcId);
        ConnectorMetaDataHandler metaDataHandler = ConnectorHandlerFactory.getMetaDataHandlerNotNull(connectInfo.getChannel(), connectInfo.getConnectParams());
        ErpObjTreeNode treeNode = metaDataHandler.getObjectTree(connectInfo, arg).safeData();
        //遍历树
        setExist(existObjApiNameMap, treeNode);
        return Result.newSuccess(treeNode);
    }

    @Override
    public Result<ErpAnalysisField> parseObjectField(String tenantId, String dcId, ObjectDescArg.ParseObjFieldBySplit arg) {
        String splitObjApiName = arg.getSplitObjectApiName();
        //查找对象
        ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, splitObjApiName);
        ObjectDescArg.ParseObjField parseObjFieldArg = new ObjectDescArg.ParseObjField();
        parseObjFieldArg.setRealObjectApiName(relation.getErpRealObjectApiname());
        //一些系统需要传输需要哪些明细对象，所以查出所有对象
        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectFieldDao.queryObjExtendDTO(tenantId, dcId, relation.getErpRealObjectApiname());
        erpObjExtendDtos.stream()
                .filter(v -> !ErpObjSplitTypeEnum.NOT_SPLIT.equals(v.getSplitType()))
                .forEach(v -> {
                    //明细需要填充明细ApiName
                    String realDetailApiName = v.parseRealApiCode();
                    parseObjFieldArg.getRealChildObjectApiName().add(realDetailApiName);
                });
        ErpObjectDescribe erpObjectDescribe = parseObjDescCache(tenantId, dcId, parseObjFieldArg).safeData();
        //增加数据中心序号
        addDcSeq(tenantId, dcId, erpObjectDescribe, true);
        ErpObjectDescribe.SplitObjDescribe splitObjDescribe = erpObjectDescribe.getSplitObjDescribes().get(splitObjApiName);
        ErpAnalysisField erpAnalysisField = new ErpAnalysisField();
        if (splitObjDescribe == null) {
            return Result.newSuccess(erpAnalysisField);
        }
        List<ErpObjectFieldResult> resultList = erpAnalysisField.getErpObjectFields();
        if (StrUtil.isNotBlank(arg.getFieldApiName())) {
            //指定字段
            String fieldApiName = arg.getFieldApiName();
            final ErpObjectFieldEntity erpObjectFieldEntity = splitObjDescribe.getFields().get(fieldApiName);
            final ErpFieldExtendEntity erpFieldExtendEntity = splitObjDescribe.getFieldExtends().get(fieldApiName);
            if (erpObjectFieldEntity != null) {
                ErpObjectFieldResult fieldResult = convert2FieldResult(splitObjApiName, erpObjectFieldEntity, erpFieldExtendEntity);
                resultList.add(fieldResult);
            }
        } else {
            //查找s所有字段字段，去除原有字段
            List<ErpObjectFieldEntity> existFields = erpObjectFieldDao.findData(tenantId, dcId, splitObjApiName);
            for (ErpObjectFieldEntity existField : existFields) {
                splitObjDescribe.getFields().remove(existField.getFieldApiName());
            }
            splitObjDescribe.getFields().forEach((fieldApi, erpObjectFieldEntity) -> {
                ErpObjectFieldResult fieldResult = convert2FieldResult(splitObjApiName, erpObjectFieldEntity, splitObjDescribe.getFieldExtends().get(fieldApi));
                resultList.add(fieldResult);
            });
        }
        return Result.newSuccess(erpAnalysisField);
    }

    private static void setExist(Map<String, String> existObjApiNameMap, ErpObjTreeNode treeNode) {
        boolean exist = existObjApiNameMap.containsKey(treeNode.getErpObjectApiName());
        treeNode.setExist(exist);
        if (treeNode.getChildren() != null) {
            for (ErpObjTreeNode child : treeNode.getChildren()) {
                setExist(existObjApiNameMap, child);
            }
        }
    }

    /**
     * 统一走缓存
     *
     * @param tenantId
     * @param dcId
     * @return
     */
    private Result<ErpObjectDescribe> parseObjDescCache(String tenantId, String dcId, ObjectDescArg.ParseObjField arg) {
        String realObjApiName = arg.getRealObjectApiName();
        String joinObjApiName = realObjApiName;
        if (CollUtil.isNotEmpty(arg.getRealChildObjectApiName())) {
            String detailApis = StrUtil.join(".", arg.getRealChildObjectApiName());
            joinObjApiName = joinObjApiName + "." + detailApis;
        }
        String cacheStr = getFromCache(tenantId, dcId, joinObjApiName);
        Result<ErpObjectDescribe> erpObjectDescribeRes;
        if (cacheStr != null) {
            erpObjectDescribeRes = JacksonUtil.fromJson(cacheStr, new TypeReference<Result<ErpObjectDescribe>>() {
            });
            return erpObjectDescribeRes;
        }
        try {
            ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dcId);
            ConnectorMetaDataHandler metaDataHandler = ConnectorHandlerFactory.getMetaDataHandlerNotNull(connectInfo.getChannel(), connectInfo.getConnectParams());
            ErpObjTreeNode treeNode = metaDataHandler.getObjectDesc(connectInfo, arg).safeData();
            ErpObjectDescribe erpObjectDescribe = transTree2Desc(realObjApiName, treeNode);
            erpObjectDescribeRes = Result.newSuccess(erpObjectDescribe);
        } catch (Exception e) {
            log.warn("analyze exception", e);
            erpObjectDescribeRes = Result.wrapException(e);
        }
        //成功缓存
        setCache(tenantId, dcId, joinObjApiName, erpObjectDescribeRes);
        return erpObjectDescribeRes;
    }

    private void setCache(String tenantId, String dcId, String joinObjApiName, Result<ErpObjectDescribe> erpObjectDescribeRes) {
        if (!erpObjectDescribeRes.isSuccess()) {
            //失败不缓存
            return;
        }
        if (ConfigCenter.disableParseObjCache) {
            //支持配置不缓存
            return;
        }
        //缓存结果,成功缓存10分钟
        String cacheKey2 = String.format(CommonConstant.REDIS_CACHE_PARSE_OBJ, tenantId + dcId, joinObjApiName);
        redisCacheManager.setCache(cacheKey2, JacksonUtil.toJson(erpObjectDescribeRes), TimeUnit.MINUTES.toSeconds(10L), this.getClass().getSimpleName());
    }

    private String getFromCache(String tenantId, String dcId, String joinObjApiName) {
        String cacheKey = String.format(CommonConstant.REDIS_CACHE_PARSE_OBJ, tenantId + dcId, joinObjApiName);
        //从缓存取
        String cacheStr = redisCacheManager.getCache(cacheKey, this.getClass().getSimpleName());
        return cacheStr;
    }

    private void delCache(String tenantId, String dcId, ObjectDescArg.ParseObjField arg) {
        String realObjApiName = arg.getRealObjectApiName();
        String joinObjApiName = realObjApiName;
        if (CollUtil.isNotEmpty(arg.getRealChildObjectApiName())) {
            String detailApis = StrUtil.join(".", arg.getRealChildObjectApiName());
            joinObjApiName = joinObjApiName + "." + detailApis;
        }
        String cacheKey2 = String.format(CommonConstant.REDIS_CACHE_PARSE_OBJ, tenantId + dcId, joinObjApiName);
        redisCacheManager.delCache(cacheKey2, this.getClass().getSimpleName());
    }

    /**
     * 转换，兼容有字段和无字段
     *
     * @param realObjApiName
     * @param treeNode
     * @return
     */
    private static ErpObjectDescribe transTree2Desc(String realObjApiName, ErpObjTreeNode treeNode) {
        //转换
        ErpObjectDescribe erpObjectDescribe = new ErpObjectDescribe();
        ErpObjectEntity realObj = new ErpObjectEntity();
        LinkedHashMap<String, ErpObjectDescribe.SplitObjDescribe> splitObjDescribes = new LinkedHashMap<>();
        erpObjectDescribe.setRealObj(realObj);
        erpObjectDescribe.setSplitObjDescribes(splitObjDescribes);
        //真实对象信息
        realObj.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        realObj.setErpObjectApiName(realObjApiName);
        String objectName = treeNode.getErpObjectName();
        realObj.setErpObjectName(objectName);
        //别问为什么是这个，以前前端一直这么传。。。
        realObj.setErpObjectExtendValue("DETAIL2DETAIL_SPLIT");
        //填充主对象
        String mainSplitApiName = realObjApiName;
        setObjDesc(true, mainSplitApiName, objectName, realObjApiName, ErpObjSplitTypeEnum.NOT_SPLIT, treeNode.getFields(), splitObjDescribes, "");
        //填充明细
        if (treeNode.getChildren() != null) {
            for (ErpObjTreeNode child : treeNode.getChildren()) {
                String detailSplitApiName = child.getErpObjectApiName();
                setObjDesc(false, detailSplitApiName, child.getErpObjectName(), realObjApiName, ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT, child.getFields(), splitObjDescribes, child.getErpObjectApiName());
            }
        }
        return erpObjectDescribe;
    }

    private static void setObjDesc(boolean isMain,
                                   String mainSplitApiName,
                                   String objectName,
                                   String realObjApiName,
                                   ErpObjSplitTypeEnum mainType,
                                   List<ErpObjTreeNode.Field> fields,
                                   LinkedHashMap<String, ErpObjectDescribe.SplitObjDescribe> splitObjDescribes,
                                   String extendValue) {
        ErpObjectDescribe.SplitObjDescribe splitObjDescribe = new ErpObjectDescribe.SplitObjDescribe();
        //拆分对象
        ErpObjectEntity splitObj = new ErpObjectEntity();
        //对象关系
        ErpObjectRelationshipEntity relation = new ErpObjectRelationshipEntity();
        splitObjDescribe.setSplitObj(splitObj);
        splitObjDescribe.setObjRelation(relation);
        splitObjDescribe.setMain(isMain);
        splitObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        splitObj.setErpObjectApiName(mainSplitApiName);
        splitObj.setErpObjectName(objectName);
        splitObj.setErpObjectExtendValue(extendValue);
        relation.setSplitSeq(1);
        relation.setErpRealObjectApiname(realObjApiName);
        relation.setErpSplitObjectApiname(mainSplitApiName);
        relation.setSplitType(mainType);
        //填充字段
        if (fields != null) {
            for (ErpObjTreeNode.Field field : fields) {
                ErpObjectFieldEntity fieldEntity = BeanUtil.copy(field, ErpObjectFieldEntity.class);
                splitObjDescribe.getFields().put(field.getFieldApiName(), fieldEntity);
            }
        }
        splitObjDescribes.put(mainSplitApiName, splitObjDescribe);
    }

    @Override
    public Result<String> presetObject(String tenantId, String dcId, ObjectDescArg.ParseObjField arg) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dcId);
        ErpObjectDescribe erpObjectDescribe = parseObjDescCache(tenantId, dcId, arg).safeData();
        Set<String> needInitObjs = arg.getRealChildObjectApiName();
        //仅保留传参的对象以及主对象
        ErpObjectDescribe.SplitObjDescribe mainSplitObj = erpObjectDescribe.getSplitObjDescribes().values().stream()
                .filter(v -> v.isMain()).findAny().orElseThrow(() -> new ErpSyncDataException(ResultCodeEnum.PARAM_ERROR, tenantId));
        String mainSplitObjApiName = mainSplitObj.getSplitObj().getErpObjectApiName();
        needInitObjs.add(mainSplitObjApiName);
        erpObjectDescribe.getSplitObjDescribes().keySet().retainAll(needInitObjs);
        ErpObjectDescribe.SplitObjDescribe mainDescribe = erpObjectDescribe.getSplitObjDescribes().get(mainSplitObjApiName);
        if (arg.isExist()) {
            //主表已经存在时，移除除了明细字段之外的其他字段
            mainDescribe.getFields().keySet().retainAll(needInitObjs);
            mainDescribe.getFieldExtends().keySet().retainAll(needInitObjs);
        }
        Result<String> incrementResult = incrementInitObjField(tenantId, dcId, connectInfo.getChannel(), erpObjectDescribe);
        log.info("presetObject obj result,{}", incrementResult);
        if (!incrementResult.isSuccess()) {
            //失败则清除describe缓存
            delCache(tenantId, dcId, arg);
        }
        return incrementResult;
    }

    @Override
    public Result<InitDcResult> initDcPresetObjAndStream(String tenantId, String dcId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dcId);
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorConfigHandler configHandler = ConnectorHandlerFactory.getConfigHandlerNotNull(channel, connectInfo.getConnectParams());

        List<ErpObjTreeNode> erpObjTreeNodes = configHandler.getObjsNeedPreset(connectInfo).safeData();
        log.info("init dc preset obj getObjsNeedPreset {},{}", dcId, erpObjTreeNodes);
        for (ErpObjTreeNode erpObjTreeNode : erpObjTreeNodes) {
            ObjectDescArg.ParseObjField arg = new ObjectDescArg.ParseObjField();
            arg.setRealObjectApiName(erpObjTreeNode.getErpObjectApiName());
            if (CollUtil.isNotEmpty(erpObjTreeNode.getChildren())) {
                for (ErpObjTreeNode child : erpObjTreeNode.getChildren()) {
                    arg.getRealChildObjectApiName().add(child.getErpObjectApiName());
                }
            }
            Result<String> result = presetObject(tenantId, dcId, arg);
            log.info("init dc preset obj {},{}", arg, result);
        }
        InitDcResult initDcResult = new InitDcResult().setPresetObjs(erpObjTreeNodes);

        String streamInfosStr = configHandler.getStreamInfoNeedPreset(connectInfo).safeData();
        if (StrUtil.isNotBlank(streamInfosStr)) {
            Map<String, Result<?>> presetStreamInfo = presetStreamInfo(tenantId, dcId, connectInfo, streamInfosStr);
            initDcResult.setPresetStreams(presetStreamInfo);
        }
        return Result.newSuccess(initDcResult);
    }

    private Map<String,Result<?>> presetStreamInfo(String tenantId, String dcId, ErpConnectInfoEntity connectInfo, String streamInfosStr) {
        Map<String,Result<?>> presetResultMap = new LinkedHashMap<>();
        List<UpdateIntegrationStreamArg> args = JacksonUtil.fromJson(streamInfosStr, new TypeReference<List<UpdateIntegrationStreamArg>>() {
        });
        //查找crmdc
        ErpConnectInfoEntity crmConnectInfo = erpConnectInfoDao.getCRMConnectInfo(tenantId, ErpChannelEnum.CRM.name());
        DataCenterData crmDc = DataCenterData.newDataByEntity(crmConnectInfo);
        DataCenterData erpDc = DataCenterData.newDataByEntity(connectInfo);
        int dcSeq = dataCenterManager.getDataCenterSeq(tenantId, dcId);
        List<TenantData> tenantData = new ArrayList<>();
        tenantData.add(TenantData.newDataByTenantId(tenantId));
        log.info("init dc preset stream getStreamInfoNeedPreset {},{}", dcId, streamInfosStr);
        for (UpdateIntegrationStreamArg updateArg : args) {
            QueryIntegrationDetailResult.SourceSystemNode sourceSystemNode = updateArg.getSourceSystemNode();
            QueryIntegrationDetailResult.DestSystemNode destSystemNode = updateArg.getDestSystemNode();
            QueryIntegrationDetailResult.FieldMappingNode fieldMappingNode = updateArg.getFieldMappingNode();
            ObjectMappingResult fieldMappings = fieldMappingNode.getFieldMappings();
            List<ObjectMappingResult> detailObjectMappings = fieldMappingNode.getDetailObjectMappings();
            sourceSystemNode.setSourceTenantDatas(tenantData);
            destSystemNode.setDestTenantDatas(tenantData);
            boolean is2ERP = sourceSystemNode.getSourceTenantType().equals(TenantType.CRM);
            if (is2ERP) {
                sourceSystemNode.setSourceDc(crmDc);
                destSystemNode.setDestDc(erpDc);
                String destObjectApiName = replaceSeq(destSystemNode.getDestObjectApiName(), dcSeq);
                destSystemNode.setDestObjectApiName(destObjectApiName);
                replaceDestObjMapping(fieldMappings, dcSeq);
                for (ObjectMappingResult detailObjectMapping : detailObjectMappings) {
                    replaceDestObjMapping(detailObjectMapping, dcSeq);
                }
            } else {
                sourceSystemNode.setSourceDc(erpDc);
                destSystemNode.setDestDc(crmDc);
                String sourceObjectApiName = replaceSeq(sourceSystemNode.getSourceObjectApiName(), dcSeq);
                sourceSystemNode.setSourceObjectApiName(sourceObjectApiName);
                replaceSourceObjMapping(fieldMappings, dcSeq);
                for (ObjectMappingResult detailObjectMapping : detailObjectMappings) {
                    replaceSourceObjMapping(detailObjectMapping, dcSeq);
                }
            }
            // 新增
            CreateObjectMappingArg createArg = new CreateObjectMappingArg();
            createArg.setIntegrationStreamName(updateArg.getIntegrationStreamName());
            createArg.setSourceDataCenterId(updateArg.getSourceSystemNode().getSourceDc().getDcId());
            createArg.setDestDataCenterId(updateArg.getDestSystemNode().getDestDc().getDcId());
            createArg.setSourceObjectApiName(updateArg.getSourceSystemNode().getSourceObjectApiName());
            createArg.setDestObjectApiName(updateArg.getDestSystemNode().getDestObjectApiName());
            createArg.setSyncRules(updateArg.getSyncRules());
            createArg.setRemark(i18NStringManager.getByEi(I18NStringEnum.s3750, tenantId));
            List<CreateObjectMappingArg.ObjectMapping> detailMapping = detailObjectMappings.stream().map(v -> {
                CreateObjectMappingArg.ObjectMapping objectMapping = new CreateObjectMappingArg.ObjectMapping();
                objectMapping.setSourceObjectApiName(v.getSourceObjectApiName());
                objectMapping.setDestObjectApiName(v.getDestObjectApiName());
                return objectMapping;
            }).collect(Collectors.toList());
            createArg.setDetailObjectMappings(detailMapping);
            Result<String> createResult = integrationStreamService.createObjectMapping(tenantId, createArg, I18nUtil.getLocaleFromTrace());
            log.info("init dc preset stream create ,{},{}",createArg,createResult);
            if (createResult.isSuccess()) {
                String streamId = createResult.getData();
                updateArg.setId(streamId);
                Result<UpdateIntegrationStreamResult> updateResult = integrationStreamService.allUpdateIntegrationStream(tenantId, updateArg, I18nUtil.getLocaleFromTrace());
                log.info("init dc preset stream update ,{},{}", updateArg, updateResult);
                presetResultMap.put(updateArg.getIntegrationStreamName(), updateResult);
            } else {
                presetResultMap.put(updateArg.getIntegrationStreamName(), createResult);
            }
        }
        return presetResultMap;
    }

    private static void replaceDestObjMapping(ObjectMappingResult fieldMappings, int dcSeq) {
        fieldMappings.setDestObjectApiName(replaceSeq(fieldMappings.getDestObjectApiName(), dcSeq));
        if (fieldMappings.getFieldMappings() != null) {
            for (FieldMappingResult fieldMapping : fieldMappings.getFieldMappings()) {
                if (StrUtil.isNotBlank(fieldMapping.getDestTargetApiName())) {
                    fieldMapping.setDestTargetApiName(replaceSeq(fieldMapping.getDestTargetApiName(), dcSeq));
                }
            }
        }
    }


    private static void replaceSourceObjMapping(ObjectMappingResult fieldMappings, int dcSeq) {
        fieldMappings.setSourceObjectApiName(replaceSeq(fieldMappings.getSourceObjectApiName(), dcSeq));
        if (fieldMappings.getFieldMappings() != null) {
            for (FieldMappingResult fieldMapping : fieldMappings.getFieldMappings()) {
                if (StrUtil.isNotBlank(fieldMapping.getSourceTargetApiName())) {
                    fieldMapping.setSourceTargetApiName(replaceSeq(fieldMapping.getSourceTargetApiName(), dcSeq));
                }
            }
        }
    }

    @NotNull
    private static String replaceSeq(String objectApiName, int dcSeq) {
        objectApiName = objectApiName.replaceAll("_\\d+$", "") + "_" + dcSeq;
        return objectApiName;
    }

    private ErpObjectFieldResult convert2FieldResult(String splitObjApiName,
                                                     ErpObjectFieldEntity erpObjectFieldEntity,
                                                     ErpFieldExtendEntity erpFieldExtendEntity) {
        ErpObjectFieldResult fieldResult = new ErpObjectFieldResult();
        BeanUtils.copyProperties(erpObjectFieldEntity, fieldResult);
        fieldResult.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        fieldResult.setErpObjectApiName(splitObjApiName);
        String fieldExtendValueStr = erpObjectFieldEntity.getFieldExtendValue();
        if (StringUtils.isNotEmpty(fieldExtendValueStr)) {
            Object fieldExtendValue = GsonUtil.fromJson(fieldExtendValueStr, Object.class);
            fieldResult.setFieldExtendValue(fieldExtendValue);
        }
        if (Objects.nonNull(erpFieldExtendEntity)) {
            fieldResult.setViewCode(erpFieldExtendEntity.getViewCode());
            fieldResult.setQueryCode(erpFieldExtendEntity.getQueryCode());
            fieldResult.setSaveCode(erpFieldExtendEntity.getSaveCode());
        }
        return fieldResult;
    }


    /**
     * 增量初始化K3对象和字段
     *
     * @param tenantId
     * @param dataCenterId
     * @param channel
     * @param erpObjectDescribe
     * @return
     */
    private Result<String> incrementInitObjField(String tenantId,
                                                 String dataCenterId,
                                                 ErpChannelEnum channel,
                                                 ErpObjectDescribe erpObjectDescribe) {

        Long now = System.currentTimeMillis();
        StringBuilder content = new StringBuilder();
        addDcSeq(tenantId, dataCenterId, erpObjectDescribe, channel != ErpChannelEnum.ERP_K3CLOUD&&!ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.equals(channel));

        //增量对象处理
        ErpObjectRelationshipEntity queryArg = new ErpObjectRelationshipEntity();
        queryArg.setTenantId(tenantId);
        queryArg.setDataCenterId(dataCenterId);
        queryArg.setErpRealObjectApiname(erpObjectDescribe.getRealObj().getErpObjectApiName());
        //获取已有的数据
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = relationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryArg);
        //当存在批次不为1时，直接修正。
        for (ErpObjectRelationshipEntity erpObjectRelationshipEntity : erpObjectRelationshipEntities) {
            if (erpObjectRelationshipEntity.getSplitSeq() > 1) {
                erpObjectRelationshipEntity.setSplitSeq(1);
                relationshipDao.updateById(erpObjectRelationshipEntity);
            }
        }
        Set<String> existRelationApiNames = erpObjectRelationshipEntities.stream()
                .map(ErpObjectRelationshipEntity::getErpSplitObjectApiname)
                .collect(Collectors.toSet());
        List<ErpObjectEntity> existObjs = erpObjectDao.listByDcId(tenantId, dataCenterId);
        Set<String> existObjApiNames = existObjs.stream().map(v -> v.getErpObjectApiName()).collect(Collectors.toSet());
        if (existRelationApiNames.isEmpty()) {
            //为空时需要插入真实对象
            ErpObjectEntity realObj = erpObjectDescribe.getRealObj();
            if (!existObjApiNames.contains(realObj.getErpObjectApiName())) {
                realObj.setId(IdGenerator.get());
                realObj.setTenantId(tenantId);
                realObj.setDataCenterId(dataCenterId);
                realObj.setChannel(channel);
                realObj.setDeleteStatus(false);
                realObj.setCreateTime(now);
                realObj.setUpdateTime(now);
                int insert = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(realObj);
                content.append("add realObj:").append(insert).append(";");
            }
        }
        //拆分对象
        LinkedHashMap<String, ErpObjectDescribe.SplitObjDescribe> splitObjDescribes = erpObjectDescribe.getSplitObjDescribes();
        splitObjDescribes.forEach((objApiName, desc) -> {
            if (!existRelationApiNames.contains(objApiName)) {
                //新对象
                ErpObjectEntity splitObj = desc.getSplitObj();
                if (!existObjApiNames.contains(splitObj.getErpObjectApiName())) {
                    splitObj.setId(IdGenerator.get());
                    splitObj.setTenantId(tenantId);
                    splitObj.setDataCenterId(dataCenterId);
                    splitObj.setChannel(channel);
                    splitObj.setErpObjectApiName(objApiName);
                    splitObj.setCreateTime(now);
                    splitObj.setUpdateTime(now);
                    erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(splitObj);
                }
                ErpObjectRelationshipEntity objRelation = desc.getObjRelation();
                objRelation.setId(IdGenerator.get());
                objRelation.setTenantId(tenantId);
                objRelation.setDataCenterId(dataCenterId);
                objRelation.setChannel(channel);
                objRelation.setSplitSeq(objRelation.getSplitSeq());
                objRelation.setErpSplitObjectApiname(objApiName);
                objRelation.setCreateTime(now);
                objRelation.setUpdateTime(now);
                relationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(objRelation);
                content.append("add splitObj:").append(objApiName).append(";");
            } else {
                content.append("exist splitObj:").append(objApiName).append(";");
            }
            ErpObjectFieldEntity fieldEntity = new ErpObjectFieldEntity();
            fieldEntity.setTenantId(tenantId);
            fieldEntity.setDataCenterId(dataCenterId);
            fieldEntity.setChannel(channel);
            fieldEntity.setErpObjectApiName(objApiName);
            //获取对象的字段
            List<ErpObjectFieldEntity> erpObjectFieldEntities =
                    erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(fieldEntity);
            Set<String> existFields =
                    erpObjectFieldEntities.stream().map(ErpObjectFieldEntity::getFieldApiName).collect(Collectors.toSet());
            //增量字段
            desc.getFields().keySet().removeAll(existFields);
            //设置一些值
            desc.getFields().forEach((fk, fv) -> {
                fv.setId(com.fxiaoke.api.IdGenerator.get());
                fv.setTenantId(tenantId);
                fv.setDataCenterId(dataCenterId);
                fv.setChannel(channel);
                fv.setErpObjectApiName(objApiName);
                fv.setCreateTime(now);
                fv.setUpdateTime(now);
                if (fv.getRequired() == null) {
                    fv.setRequired(false);
                }
            });
            //批量插入
            ArrayList<ErpObjectFieldEntity> needAddField = new ArrayList<>(desc.getFields().values());
            if (!needAddField.isEmpty()) {
                erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(needAddField);
                content.append("splitObject:").append(objApiName).append(" add field:").append(needAddField.size()).append(";").append("all field:").append(needAddField.size() + existFields.size()).append(";");
            }
            if(ErpChannelEnum.ERP_K3CLOUD.equals(channel)){
                //字段扩展需要先查一遍，不覆盖原来的
                Map<String, List<ErpFieldExtendEntity>> groupByObj = desc.getFieldExtends().values().stream().collect(Collectors.groupingBy(ErpFieldExtendEntity::getObjApiName));
                groupByObj.forEach((obj, fieldExtends) -> {
                    List<ErpFieldExtendEntity> erpFieldExtendEntities = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .queryByObjApiName(tenantId, dataCenterId, obj);
                    Set<String> exist = erpFieldExtendEntities.stream().map(ErpFieldExtendEntity::getFieldApiName).collect(Collectors.toSet());
                    fieldExtends.removeIf(u -> exist.contains(u.getFieldApiName()));
                    fieldExtends.forEach(ev -> {
                        ev.setId(IdGenerator.get());
                        ev.setDataCenterId(dataCenterId);
                        ev.setTenantId(tenantId);
                        ev.setCreateTime(now);
                        ev.setUpdateTime(now);
                    });
                    //批量插入
                    if (!fieldExtends.isEmpty()) {
                        erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(fieldExtends);
                    }
                    content.append(obj).append(" add extend field:").append(fieldExtends.size()).append(";");
                });
            }
        });
        relationshipDao.invalidCacheErpObj(tenantId, dataCenterId);
        return Result.newSuccess(content.toString());
    }

    /**
     * 在对象apiName增加数据中心序号，
     * 在查找关联字段和主从字段，扩展增加序号
     */
    private void addDcSeq(String tenantId, String dataCenterId, ErpObjectDescribe erpObjectDescribe, Boolean addForFirst) {
        int dataCenterSeq = dataCenterManager.getDataCenterSeq(tenantId, dataCenterId);
        if (dataCenterSeq != 0 || addForFirst) {
            //不是第一个，需要在中间对象对象的apiName拼接_+序号;  对于标准的，第一个也增加序号，与真实apiName做区分
            //k3渠道做适配时需要注意 addForFirst传false
            LinkedHashMap<String, ErpObjectDescribe.SplitObjDescribe> newMap = new LinkedHashMap<>();
            erpObjectDescribe.getSplitObjDescribes().forEach((k, v) -> {
                String newObjApiName = k + "_" + dataCenterSeq;
                newMap.put(newObjApiName, v);
                v.getObjRelation().setErpSplitObjectApiname(newObjApiName);
                v.getSplitObj().setErpObjectApiName(newObjApiName);
                for (ErpObjectFieldEntity field : v.getFields().values()) {
                    field.setErpObjectApiName(newObjApiName);
                    //对所有主从查找关联字段增加
                    if (ErpFieldTypeEnum.object_reference.equals(field.getFieldDefineType())
                            || ErpFieldTypeEnum.master_detail.equals(field.getFieldDefineType())) {//查找关联或者主从,关联对象apiName增加后缀
                        field.setFieldExtendValue(field.getFieldExtendValue() + "_" + dataCenterSeq);
                    }
                }
                //孙表的扩展也储存了中间对象apiName
                if (ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL.equals(v.getObjRelation().getSplitType())) {
                    SubDetailExtend parse = SubDetailExtend.parse(v.getSplitObj().getErpObjectExtendValue());
                    if (parse != null) {
                        parse.setDetailObjectApiName(parse.getDetailObjectApiName() + "_" + dataCenterSeq);
                        v.getSplitObj().setErpObjectExtendValue(parse.toJson());
                    }
                }
            });
            erpObjectDescribe.setSplitObjDescribes(newMap);
        }
    }

    private String removeDcSeq(String tenantId, String dataCenterId, String objApiName) {
        int dataCenterSeq = dataCenterManager.getDataCenterSeq(tenantId, dataCenterId);
        if (dataCenterSeq != 0) {
            //不是第一个，需要在中间对象对象的apiName拼接_+序号
            return StrUtil.removeSuffix(objApiName, "_" + dataCenterSeq);
        }
        return objApiName;
    }

    private void preCheck(String tenantId, String dcId, String formId) {
        if (StringUtils.isBlank(tenantId)
                || StringUtils.isBlank(dcId)
                || StringUtils.isBlank(formId)) {
            throw new ErpSyncDataException(ResultCodeEnum.PARAM_ERROR, tenantId);
        }
        if (formId.equals(K3CloudForm.SAL_MATERIALGROUP)) {
            //物料分组检查如果使用旧view接口，抛出异常。
            Boolean useViewInterFace = tenantConfigurationManager.getUseViewInterFace(tenantId, dcId, formId);
            if (useViewInterFace) {
                throw new ErpSyncDataException(I18NStringEnum.s151, tenantId);
            }
        }
    }
}
