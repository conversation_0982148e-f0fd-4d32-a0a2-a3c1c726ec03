package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncDataMappingListByPloyDetailIdResult implements Serializable {
    @ApiModelProperty("数据映射列表")
    private List<SyncDataMappingResult> syncDataMappingList;
    @ApiModelProperty("数据总条数")
    private int totalCount;
    @ApiModelProperty("页长")
    private int pageSize;
    @ApiModelProperty("页码")
    private int pageNumber;
    @ApiModelProperty("是否支持重试")
    private Boolean canReSync;
}
