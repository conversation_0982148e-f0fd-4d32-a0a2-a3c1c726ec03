package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminConnectorInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;

public interface SuperAdminService {
    /**
     * 执行query sql
     *
     * @param tenantId
     * @return
     */
    Result<List<Map<String, Object>>> redoIndex(String tenantId);

    /**
     * 执行insert sql
     *
     * @param tenantId
     * @param sql
     * @return
     */
    Result<Void> superUpdate(String tenantId,String sql);

    /**
     * 未做分页
     *
     * @param requestParam
     */
    Result<Amis.Crud<AdminTenantInfo>> tenantInfoQuery(Map<String,String> requestParam);

    Result<Amis.Crud<AdminConnectorInfo>> connectorInfoQuery(Map<String,String> requestParam);

    Result<Amis.Crud<StreamSimpleInfo>> streamInfoQuery(Map<String,String> requestParam);

    Result<Void> exportToExcel(String tenantId, Integer userId, String ea);

    //禁止service使用非Result返回值
//    Map<String, String> queryEnterpriseLevelLabel(List<String> tenantIds);

    /**
     * 未做分页
     * @param keywords
     */
    Result<List<Dict>> streamInfoQuery(String tenantId, String keywords);

    Result<List<Dict>> dataNodeQuery(Boolean isTop60, String tenantId, String objApiName, String dataId);
}
