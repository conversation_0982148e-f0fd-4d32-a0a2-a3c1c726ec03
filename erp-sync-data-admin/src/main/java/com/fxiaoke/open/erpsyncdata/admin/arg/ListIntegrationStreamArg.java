package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class ListIntegrationStreamArg extends PageArg implements Serializable {
    @ApiModelProperty("数据中心id")
    private String dcId;
    @ApiModelProperty("crm对象")
    private String crmObjApiName;
    @ApiModelProperty("按启用状态筛选")
    private Integer status;
    @ApiModelProperty("按运行状态筛选")
    private Integer runStatus;
    @ApiModelProperty("是否全选")
    private Boolean filterAll=false;
}
