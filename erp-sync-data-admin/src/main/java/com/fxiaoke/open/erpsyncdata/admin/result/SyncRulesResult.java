package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.IntervalTimeUnitEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@ApiModel
public class SyncRulesResult implements Serializable {
    @ApiModelProperty("初始化同步相关信息")
    private InitData initData;
    @ApiModelProperty("同步方式,轮询：get,推送：push，默认轮询")
    private String syncType= SyncTypeEnum.get.name() ;//默认是轮询
    @ApiModelProperty("同步方式,轮询：get,推送：push，默认轮询")
    private List<String> syncTypeList= Lists.newArrayList() ;
    @ApiModelProperty("事件类型， 1新增 2修改 3作废 4手动按钮同步")
    private Set<Integer> events;
    /**目标数据事件类型， 1新增 2修改 3作废**/
    private List<Integer> destEvents;
    @ApiModelProperty("轮询时间间隔")
    private PollingInterval pollingInterval;
    @ApiModelProperty("同步前自定义函数APIName")
    private String beforeFuncApiName;
    @ApiModelProperty("同步中自定义函数APIName")
    private String duringFuncApiName;
    @ApiModelProperty("同步后自定义函数APIName")
    private String afterFuncApiName;

    @Data
    public static class InitData implements Serializable{
        @ApiModelProperty("初始化同步启用状态")
        private Integer status;
        @ApiModelProperty("初始化同步完成时间")
        private Long finishTime;
    }
    @Data
    public static class PollingInterval implements Serializable{
        @ApiModelProperty("间隔数量")
        private Integer intervalQuantity;
        @ApiModelProperty("时间单位")
        private IntervalTimeUnitEnum timeUnit;
        @ApiModelProperty("每天开始时间")
        private String startDataTime;
        @ApiModelProperty("每天结束时间")
        private String endDataTime;
    }
}
