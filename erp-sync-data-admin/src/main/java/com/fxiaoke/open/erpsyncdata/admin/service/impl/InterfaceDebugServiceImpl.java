package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.StrFormatter;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetInterfaceLogIdBySyncDataIdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.FilterDataVo;
import com.fxiaoke.open.erpsyncdata.admin.model.InterfaceDebug;
import com.fxiaoke.open.erpsyncdata.admin.service.InterfaceDebugService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.factory.SpecialBusinessFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewResult;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.SyncTrace;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/23
 */
@Slf4j
@Service
public class InterfaceDebugServiceImpl implements InterfaceDebugService {
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpInterfaceMonitorServiceImpl erpInterfaceMonitorService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private SpecialBusinessFactory specialBusinessFactory;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private CommonBusinessManager commonBusinessManager;

    @Override
    public Result<String> getInterfaceLogIdBySyncDataId(GetInterfaceLogIdBySyncDataIdArg arg) {
        List<InterfaceMonitorData> listResult = chInterfaceMonitorManager.listInterfaceMonitorBySyncDataId(arg.getTenantId(), arg.getSyncDataId(),arg.getStartLogTime(),arg.getEndLogTime());
        if (listResult.isEmpty()) {
            return Result.newError(ResultCodeEnum.SYNC_DATA_NOT_FOUND_INTERFACE_LOG);
        }
        InterfaceMonitorData interfaceMonitorData = listResult.get(0);
        return Result.newSuccess(interfaceMonitorData.getId().toString());
    }

    @Override
    public Result<InterfaceDebug.InfoData> getInterfaceInfo(InterfaceDebug.GetInfoArg arg,String lang) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getDcId());
        String erpObjName = erpObjManager.getErpObj(arg.getTenantId(), arg.getObjApiName()).getErpObjectName();
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.valueOf(arg.getType());
        InterfaceDebug.InfoData infoData = new InterfaceDebug.InfoData();
        infoData.setDataCenterName(connectInfo.getDataCenterName());
        infoData.setFormFullName(StrFormatter.format("【{}】+【{}】", arg.getObjApiName(), erpObjName));
        Pair<String, String> interfaceNameUrlStand = erpInterfaceMonitorService.getInterfaceNameUrlStand(connectInfo, interfaceUrlEnum,lang);
        infoData.setInterfaceName(interfaceNameUrlStand.getKey());
        infoData.setInterfaceUrl(interfaceNameUrlStand.getValue());
        return new Result<>(infoData);
    }


    @Override
    public Result<InterfaceDebug.InfoData> getInterfaceInfoV2(InterfaceDebug.GetInfoArg arg,String lang) {
        String realObjApiName = idFieldConvertManager.getRealObjApiName(arg.getTenantId(), arg.getObjApiName());//810后的页面，前端无法传入真实apiname。需要后端转
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getDcId());
        String erpObjName = erpObjManager.getErpObj(arg.getTenantId(), realObjApiName).getErpObjectName();
        ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.valueOf(arg.getType());
        InterfaceDebug.InfoData infoData = new InterfaceDebug.InfoData();
        infoData.setDataCenterName(connectInfo.getDataCenterName());
        infoData.setFormFullName(StrFormatter.format("【{}】+【{}】", arg.getObjApiName(), erpObjName));
        Pair<String, String> interfaceNameUrlStand = erpInterfaceMonitorService.getInterfaceNameUrlStand(connectInfo, interfaceUrlEnum,lang);
        infoData.setInterfaceName(interfaceNameUrlStand.getKey());
        infoData.setInterfaceUrl(interfaceNameUrlStand.getValue());
        return new Result<>(infoData);
    }


    @Override
    public Result<ErpInterfaceMonitorResult> debugGetData(InterfaceDebug.GetDataArg arg,String lang) {
        if (ConfigCenter.NOT_SAVE_INTERFACE_MONITOR_TENANTS.contains(arg.getTenantId())) {
            return Result.newError(ResultCodeEnum.NO_SAVE_INTERFACE_MONITOR);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getDcId());
        String syncDataId = idGenerator.get();
        //设置线程同步信息，用于关联数据
        SyncTrace.set(SyncTrace.SyncInfo.builder().syncDataId(syncDataId).build());
        switch (connectInfo.getChannel()) {
            case ERP_K3CLOUD:
                //k3获取单条数据为了屏蔽掉连接层针对不同对象的id和编码字段处理，统一使用数据编码，走单独调用接口进行调试
                Result<ViewResult> viewResultResult = debugGetK3Data(arg, connectInfo);
                if (!viewResultResult.isSuccess()) {
                    return Result.copy(viewResultResult);
                }
                break;
            case ERP_SAP:
            case STANDARD_CHANNEL:
            case ERP_U8:
            case ERP_U8_EAI:
            case ERP_DB_PROXY:
            case YXT_MARKETING_ZHIHU:
                ConnectorDataHandler erpManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(),connectInfo.getConnectParams());
                ErpIdArg erpIdArg = new ErpIdArg();
                erpIdArg.setDataId(arg.getDataNumber());
                erpIdArg.setObjAPIName(arg.getObjApiName());
                erpIdArg.setTenantId(arg.getTenantId());
                erpManager.getErpObjData(erpIdArg, connectInfo);
                break;
            default:
                throw new ErpSyncDataException(ResultCodeEnum.UNSUPPORTED_CHANNEL,connectInfo.getTenantId());
        }
        //如果有等保存的日志，需要保存
        BaseErpDataManager.saveWaitingInterfaceMonitor(true);
        Result<ErpInterfaceMonitorResult> result = null;
        if (SyncTrace.get() != null && SyncTrace.get().getInterfaceMonitorData() != null) {
            result = erpInterfaceMonitorService.buildResult(Result.newSuccess(SyncTrace.get().getInterfaceMonitorData()), lang, arg.getTenantId());
        } else {
            result = erpInterfaceMonitorService.getObjInterfaceMonitorBySyncDataId(arg.getTenantId(), syncDataId, lang);
        }
        //确保result为json格式
        formatResult(result);
        SyncTrace.set(null);
        return result;
    }

    @Override
    public Result<ErpInterfaceMonitorResult> debugGetDataV2(InterfaceDebug.GetDataArg arg,String lang) {
        String realObjApiName = idFieldConvertManager.getRealObjApiName(arg.getTenantId(), arg.getObjApiName());//810后的页面，前端无法传入真实apiname。需要后端转
        arg.setObjApiName(realObjApiName);
        if (ConfigCenter.NOT_SAVE_INTERFACE_MONITOR_TENANTS.contains(arg.getTenantId())) {
            return Result.newError(ResultCodeEnum.NO_SAVE_INTERFACE_MONITOR);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getDcId());
        String syncDataId = idGenerator.get();
        syncLogManager.initLogId(arg.getTenantId(),realObjApiName);
        //设置线程同步信息，用于关联数据
        SyncTrace.set(SyncTrace.SyncInfo.builder().syncDataId(syncDataId).build());
        switch (connectInfo.getChannel()) {
            case ERP_K3CLOUD:
                //k3获取单条数据为了屏蔽掉连接层针对不同对象的id和编码字段处理，统一使用数据编码，走单独调用接口进行调试
                Result<ViewResult> viewResultResult = debugGetK3Data(arg, connectInfo);
                if (!viewResultResult.isSuccess()) {
                    return Result.copy(viewResultResult);
                }
                break;
            case ERP_SAP:
            case STANDARD_CHANNEL:
            case ERP_U8:
            case ERP_U8_EAI:
            case ERP_DB_PROXY:
            case YXT_MARKETING_ZHIHU:
                ConnectorDataHandler erpManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
                ErpIdArg erpIdArg = new ErpIdArg();
                erpIdArg.setDataId(arg.getDataNumber());
                erpIdArg.setObjAPIName(arg.getObjApiName());
                erpIdArg.setTenantId(arg.getTenantId());
                erpManager.getErpObjData(erpIdArg, connectInfo);
                break;
            default:
                throw new ErpSyncDataException(ResultCodeEnum.UNSUPPORTED_CHANNEL, connectInfo.getTenantId());
        }
        //如果有等保存的日志，需要保存
        BaseErpDataManager.saveWaitingInterfaceMonitor(true);
        Result<ErpInterfaceMonitorResult> result = null;
        if (SyncTrace.get() != null && SyncTrace.get().getInterfaceMonitorData() != null) {
            result = erpInterfaceMonitorService.buildResult(Result.newSuccess(SyncTrace.get().getInterfaceMonitorData()), lang, arg.getTenantId());
        } else {
            result = erpInterfaceMonitorService.getObjInterfaceMonitorBySyncDataId(arg.getTenantId(), syncDataId, lang);
        }
        //确保result为json格式
        formatResult(result);
        LogIdUtil.clear();
        SyncTrace.set(null);
        return result;
    }

    private void formatResult(Result<ErpInterfaceMonitorResult> result) {
        if (result.isSuccess()) {
            String interfaceResult = result.getData().getResult();
            if (StringUtils.isNotBlank(interfaceResult)) {
                try {
                    JSONObject.parse(interfaceResult);
                } catch (JSONException ignore) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("data", interfaceResult);
                    result.getData().setResult(jsonObject.toJSONString());
                }
            }
        }
    }

    private Result<ViewResult> debugGetK3Data(InterfaceDebug.GetDataArg arg, ErpConnectInfoEntity connectInfo) {
        K3CloudApiClient k3ApiClient = apiClientHolder.getK3ApiClient(connectInfo.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
        ViewArg viewArg = new ViewArg();
        viewArg.setNumber(arg.getDataNumber());
        if (StringUtils.isNotBlank(arg.getOrgNumber())) {
            QueryArg queryArg = new QueryArg();
            queryArg.setFormId("ORG_Organizations");
            queryArg.setFieldKeys("FOrgId");
            queryArg.setFilterString(String.format("FNumber='%s'", arg.getOrgNumber()));
            commonBusinessManager.fillQueryArgByViewExtend(k3ApiClient.getTenantId(), k3ApiClient.getDataCenterId(), queryArg);
            Result<List<K3Model>> orgResult = k3ApiClient.queryReturnMap(queryArg);
            List<K3Model> orgData = orgResult.getData();
            if (!CollectionUtils.isEmpty(orgData)) {
                String orgId = orgData.get(0).getString("FOrgId");
                viewArg.setCreateOrgId(Integer.valueOf(orgId));
            }
        }
        K3DataConverter converter = k3DataManager.buildConverter(arg.getTenantId(),k3ApiClient.getDataCenterId(), arg.getObjApiName());
        //配置
        ErpTenantConfigurationEntity config = tenantConfigurationManager.findOne(arg.getTenantId(), k3ApiClient.getDataCenterId(), ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.USE_BILLQUERY_INTERFACE_TO_VIEW.name());
        List<String> objList = Lists.newArrayList();
        if (config != null && config.getConfiguration() != null) {
            objList = JSONObject.parseArray(config.getConfiguration(), String.class);
        }
        Boolean useViewInterface = false;
        if (objList.contains("*") || objList.contains(arg.getObjApiName())) {//走view接口
            useViewInterface = true;
        }
        Result<ViewResult> viewResult = k3DataManager.getAndLog(arg.getTenantId(), arg, arg.getObjApiName(), k3ApiClient, viewArg, converter, useViewInterface);
        return viewResult;
    }

    @Override
    public Result<ErpInterfaceMonitorResult> debugListData(InterfaceDebug.ListDataArg arg,String lang) {
        if (ConfigCenter.NOT_SAVE_INTERFACE_MONITOR_TENANTS.contains(arg.getTenantId())) {
            return Result.newError(ResultCodeEnum.NO_SAVE_INTERFACE_MONITOR);
        }
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getDcId());
        String syncDataId = idGenerator.get();
        //设置线程同步信息，用于关联数据
        SyncTrace.set(SyncTrace.SyncInfo.builder().syncDataId(syncDataId).build());
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setTenantId(arg.getTenantId());
        timeFilterArg.setObjAPIName(arg.getObjApiName());
        timeFilterArg.setStartTime(arg.getStartTime());
        timeFilterArg.setEndTime(arg.getEndTime());
        timeFilterArg.setOffset(arg.getOffset());
        timeFilterArg.setLimit(arg.getLimit());
        timeFilterArg.setFilters(FilterDataVo.convert2Dto(arg.getFilters()));
        timeFilterArg.setOperationType(EventTypeEnum.UPDATE.getType());
        switch (connectInfo.getChannel()) {
            case ERP_K3CLOUD:
                //k3获取单条数据为了屏蔽掉连接层针对不同对象的id和编码字段处理，统一使用数据编码，走单独调用接口进行调试
                debugListK3Data(timeFilterArg, connectInfo);
                break;
            case ERP_SAP:
            case STANDARD_CHANNEL:
            case ERP_U8:
            case ERP_U8_EAI:
            case ERP_DB_PROXY:
            case YXT_MARKETING_ZHIHU:
                ConnectorDataHandler erpManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(),connectInfo.getConnectParams());
                Result<StandardListData> listDataResult = erpManager.listErpObjDataByTime(timeFilterArg, connectInfo);
                break;
            default:
                throw new ErpSyncDataException(ResultCodeEnum.UNSUPPORTED_CHANNEL,connectInfo.getTenantId());
        }
        //如果有等保存的日志，需要保存
        BaseErpDataManager.saveWaitingInterfaceMonitor(true);
        Result<ErpInterfaceMonitorResult> result=null;
        if(SyncTrace.get()!=null&&SyncTrace.get().getInterfaceMonitorData()!=null){
            result=erpInterfaceMonitorService.buildResult(Result.newSuccess(SyncTrace.get().getInterfaceMonitorData()),lang,arg.getTenantId());
        }else{
            result = erpInterfaceMonitorService.getObjInterfaceMonitorBySyncDataId(arg.getTenantId(), syncDataId,lang);
        }
        //确保result为json格式
        formatResult(result);
        SyncTrace.set(null);
        return result;
    }

    @Override
    public Result<ErpInterfaceMonitorResult> debugListDataV2(InterfaceDebug.@Valid ListDataArg arg,String lang) {
        if (ConfigCenter.NOT_SAVE_INTERFACE_MONITOR_TENANTS.contains(arg.getTenantId())) {
            return Result.newError(ResultCodeEnum.NO_SAVE_INTERFACE_MONITOR);
        }
        String realObjApiName = idFieldConvertManager.getRealObjApiName(arg.getTenantId(), arg.getObjApiName());//810后的页面，前端无法传入真实apiname。需要后端转
        arg.setObjApiName(realObjApiName);
        syncLogManager.initLogId(arg.getTenantId(),realObjApiName);
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(arg.getTenantId(), arg.getDcId());
        String syncDataId = idGenerator.get();
        //设置线程同步信息，用于关联数据
        SyncTrace.set(SyncTrace.SyncInfo.builder().syncDataId(syncDataId).build());
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setTenantId(arg.getTenantId());
        timeFilterArg.setObjAPIName(arg.getObjApiName());
        timeFilterArg.setStartTime(arg.getStartTime());
        timeFilterArg.setEndTime(arg.getEndTime());
        timeFilterArg.setOffset(arg.getOffset());
        timeFilterArg.setLimit(arg.getLimit());
        timeFilterArg.setLastMaxId(arg.getLastMaxId());
        timeFilterArg.setOperationType(Objects.equals(arg.getType(), 2) ? EventTypeEnum.INVALID.getType() : EventTypeEnum.UPDATE.getType());
        final List<List<FilterData>> filters = Objects.nonNull(arg.getFilters()) ?
                FilterDataVo.convert2Dto(arg.getFilters()) :
                erpDataPreprocessService.getFilterData(arg.getTenantId(), arg.getDcId(), connectInfo.getChannel().name(), realObjApiName, Objects.equals(arg.getType(), 2) ? ErpObjInterfaceUrlEnum.queryInvalid : ErpObjInterfaceUrlEnum.queryMasterBatch);
        timeFilterArg.setFilters(filters);
        switch (connectInfo.getChannel()) {
            case ERP_K3CLOUD:
                //k3获取单条数据为了屏蔽掉连接层针对不同对象的id和编码字段处理，统一使用数据编码，走单独调用接口进行调试
                debugListK3Data(timeFilterArg, connectInfo);
                break;
            case ERP_SAP:
            case STANDARD_CHANNEL:
            case ERP_U8:
            case ERP_U8_EAI:
            case YXT_MARKETING_ZHIHU:
            case ERP_DB_PROXY:
                ConnectorDataHandler erpManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
                Result<StandardListData> listDataResult = erpManager.listErpObjDataByTime(timeFilterArg, connectInfo);
                break;
            default:
                throw new ErpSyncDataException(ResultCodeEnum.UNSUPPORTED_CHANNEL, connectInfo.getTenantId());
        }
        //如果有等保存的日志，需要保存
        BaseErpDataManager.saveWaitingInterfaceMonitor(true);
        Result<ErpInterfaceMonitorResult> result = null;
        if (SyncTrace.get() != null && SyncTrace.get().getInterfaceMonitorData() != null) {
            result = erpInterfaceMonitorService.buildResult(Result.newSuccess(SyncTrace.get().getInterfaceMonitorData()), lang, arg.getTenantId());
        } else {
            result = erpInterfaceMonitorService.getObjInterfaceMonitorBySyncDataId(arg.getTenantId(), syncDataId, lang);
        }
        //确保result为json格式
        formatResult(result);
        LogIdUtil.clear();
        SyncTrace.set(null);
        return result;
    }

    private void debugListK3Data(TimeFilterArg arg, ErpConnectInfoEntity connectInfo) {
        K3CloudApiClient k3ApiClient = apiClientHolder.getK3ApiClient(connectInfo.getTenantId(), connectInfo.getConnectParams(), connectInfo.getId());
        //特殊逻辑服务
        SpecialBusiness specialBusiness = specialBusinessFactory.getHandlerByName(arg.getObjAPIName());
        if (specialBusiness.needSpecialGetAndQuery(null)) {
            //单独接口获取列表数据
            specialBusiness.specialListErpObjData(arg, k3ApiClient);
            return;
        }
        ErpFieldExtendEntity idFieldExtend = k3DataManager.buildConverter(arg.getTenantId(), k3ApiClient.getDataCenterId(), arg.getObjAPIName()).getIdFieldExtend();
        String saveCode = idFieldExtend.getSaveCode();
        String viewExtend = idFieldExtend.getViewExtend();
        // 构建查询参数
        QueryArg queryArg = k3DataManager.buildQueryParams(arg, idFieldExtend,k3ApiClient.getDataCenterId());
        Result<List<List<Object>>> listResult = k3DataManager.listAndLog(arg.getTenantId(), arg.getObjAPIName(), k3ApiClient, queryArg, arg);
    }
}
