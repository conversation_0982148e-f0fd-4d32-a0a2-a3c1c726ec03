package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.AplTemplateDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.MultiLanguageText;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> (^_−)☆
 */
public class AplTemplate {

    public static AplTemplate.Info doc2Pojo(AplTemplateDoc aplTemplateDoc) {
        AplTemplate.Info info = BeanUtil.copy(aplTemplateDoc, AplTemplate.Info.class);
        //替换date
        info.setUpdateTime(aplTemplateDoc.getUpdateTime().getTime());
        info.setCreateTime(aplTemplateDoc.getCreateTime().getTime());
        //替换多语字段
        //不使用多语平台了
        info.setName( MultiLanguageText.get(aplTemplateDoc.getName()));
        info.setDescription( MultiLanguageText.get(aplTemplateDoc.getDescription()));
        info.setReadmeStr( MultiLanguageText.get(aplTemplateDoc.getReadmeStr()));
        return info;
    }

    public static AplTemplateDoc pojo2Doc(AplTemplate.Info info) {
        AplTemplateDoc doc = BeanUtil.copy(info, AplTemplateDoc.class);
        doc.getName().set(info.getName());
        doc.getDescription().set(info.getDescription());
        doc.getReadmeStr().set(info.getReadmeStr());
        return doc;
    }

    @Data
    public static class Info {
        /**
         * 默认创建的类名，固定，必须与aplCode的类名一致，并且符合命名规则
         */
        private String aplClassName;
        /**
         * 名称
         */
        private String name;
        /**
         * 版本，固定1.0.0格式
         */
        private String version;

        /**
         * 系统名称
         */
        private String systemName;

        /**
         * 简介
         */
        private String description;

        /**
         * 使用说明
         */
        private String readmeStr;

        /**
         * 代码
         */
        private String aplCode;

        /**
         * 图标
         */
        private String icon;

        /**
         * 排序值
         */
        private Integer order = 65536;

        /**
         * 是否启用
         */
        private boolean enable;

        private Long createTime;
        /**
         * 更新时间
         */
        private Long updateTime;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString(callSuper = true)
    public static class UpsertArg extends Info {
        private String newVersion;
    }

    @Data
    @AllArgsConstructor
    public static class AplClassNameArg{
        private String aplClassName;
    }
}
