package com.fxiaoke.open.erpsyncdata.admin.preset;

import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailCreateArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PresetDeliveryNoteObject extends AbstractPresetObject {
    @Override
    protected List<String> getFormId() {
        return Lists.newArrayList(K3CloudForm.SAL_OUTSTOCK);
    }

    @Override
    public List<String> getFieldMappingJson() {
        String json = "{\"id\":\"\",\"masterObjectMapping\":{\"sourceObjectApiName\":\"SAL_OUTSTOCK.BillHead\",\"destObjectApiName\":\"DeliveryNoteObj\",\"fieldMappings\":[{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"1000\",\"optionMappings\":[],\"destApiName\":\"owner\",\"destType\":\"employee\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":true,\"optionMappings\":[],\"destApiName\":\"field_no_stock_id_integration_flag__c\",\"destType\":\"true_or_false\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"SAL_SaleOrder.BillHead\",\"destApiName\":\"sales_order_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"SalesOrderObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustomerID.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_Customer.BillHead\",\"destApiName\":\"account_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"AccountObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCarriageNO\",\"sourceType\":\"text\",\"destApiName\":\"express_order_id\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNote\",\"sourceType\":\"long_text\",\"destApiName\":\"remark\",\"destType\":\"long_text\"}]},\"detailObjectMappings\":[{\"sourceObjectApiName\":\"SAL_OUTSTOCK.Entity\",\"destObjectApiName\":\"DeliveryNoteProductObj\",\"fieldMappings\":[{\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceType\":\"master_detail\",\"sourceTargetApiName\":\"SAL_OUTSTOCK.BillHead\",\"destApiName\":\"delivery_note_id\",\"destType\":\"master_detail\",\"destTargetApiName\":\"DeliveryNoteObj\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"1000\",\"optionMappings\":[],\"destApiName\":\"owner\",\"destType\":\"employee\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_MATERIAL.BillHead\",\"destApiName\":\"product_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"ProductObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"SAL_SaleOrder.BillHead\",\"destApiName\":\"sales_order_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"SalesOrderObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FRealQty\",\"sourceType\":\"number\",\"destApiName\":\"delivery_num\",\"destType\":\"number\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSOEntryId\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destApiName\":\"sales_order_product_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"SalesOrderProductObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"serialId\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_SerialMainFile.BillHead\",\"destApiName\":\"serial_number_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"SerialNumberObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FLot\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_BatchMainFile.BillHead\",\"destApiName\":\"batch_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"BatchObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockID.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_STOCK.BillHead\",\"destApiName\":\"delivery_warehouse_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"WarehouseObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAmount\",\"sourceType\":\"currency\",\"destApiName\":\"delivery_money\",\"destType\":\"number\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntrynote\",\"sourceType\":\"text\",\"destApiName\":\"remark\",\"destType\":\"long_text\"}]}]}";
        return Lists.newArrayList(json);
    }


    @Override
    public List<SyncPloyDetailCreateArg> getSyncPloyDetailCreateArg() {
        SyncPloyDetailCreateArg arg = new SyncPloyDetailCreateArg();
        arg.setPloyId(tenantId);
        arg.setSourceTenantIds(Lists.newArrayList(tenantId));
        arg.setSourceTenantType(TenantType.ERP);
        arg.setSourceObjectApiName("SAL_OUTSTOCK.BillHead");
        arg.setDestTenantIds(Lists.newArrayList(tenantId));
        arg.setDestTenantType(TenantType.CRM);
        arg.setDestObjectApiName("DeliveryNoteObj");

        SyncPloyDetailCreateArg.DetailObjectMappingCreateArg detailObjectMappingCreateArg
                = new SyncPloyDetailCreateArg.DetailObjectMappingCreateArg();
        detailObjectMappingCreateArg.setSourceObjectApiName("SAL_OUTSTOCK.Entity");
        detailObjectMappingCreateArg.setDestObjectApiName("DeliveryNoteProductObj");
        arg.setDetailObjectMappings(Lists.newArrayList(
                detailObjectMappingCreateArg
        ));
        arg.setDcId(dataCenterId);
        return Lists.newArrayList(arg);
    }
}
