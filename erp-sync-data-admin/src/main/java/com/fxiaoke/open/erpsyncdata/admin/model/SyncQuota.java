package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.admin.utils.SyncQuotaHelper;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/16
 */
//不让其修改
@Data
@Slf4j
public class SyncQuota {

    /**
     * 有效模块code
     */
    private Set<String> validModuleCodes;
    /**
     * 有效渠道
     */
    private Set<String> validConnectorKey;

    /**
     * 有效资源包，数量
     */
    private Map<String, Long> validPkgMap;

    /**
     * 集成流配额
     */
    private Long streamQuota;
    /**
     * 中间表配额
     */
    private Long mappingQuota;
    /**
     * 日志配额
     */
    private Long logQuota;

    /**
     * 已使用集成流配额
     */
    private Long streamUsed = 0L;
    /**
     * 已启用集成流
     */
    private Long streamEnable = 0L;


    /**
     * 已使用中间表
     */
    private MappingCountVo mappingCountVo;
    /**
     * 已使用中间表
     */
    private Long mappingUsed = 0L;
    /**
     * 已使用日志量
     */
    private Long logUsed = 0L;

    /**
     * 日志量计算中
     */
    private boolean logDetailCounting;

    /**
     * 超额后，停用集成流时间
     */
    private Long stopStreamTime;
    /**
     * 渠道对应的集成流配额
     */
    private List<MiscQuota> channelQuotas = new ArrayList<>();
    /**
     * 资源包配额
     */
    private List<MiscQuota> pkgQuotas = new ArrayList<>();

    public void setMappingCountVo(MappingCountVo mappingCountVo) {
        this.mappingCountVo = mappingCountVo;
        this.mappingUsed = mappingCountVo.getStatCount();
    }

    /**
     * 构建配额类
     */
    public static SyncQuota create(Set<String> validModuleCodes, Map<String, Long> validPkgMap) {
        AssertUtil.allNotNull(validModuleCodes, validPkgMap);
        SyncQuota syncQuota = new SyncQuota();
        syncQuota.validModuleCodes = validModuleCodes;
        syncQuota.validConnectorKey = new LinkedHashSet<>();
        syncQuota.validPkgMap = validPkgMap;
        syncQuota.streamQuota = 0L;
        syncQuota.mappingQuota = 0L;
        SyncQuotaHelper syncQuotaHelper = SyncQuotaHelper.LAZY_HOLDER;
        //计算配额和渠道
        Map<String, Connector> code2Connector = AllConnectorUtil.getAllConnectorList().stream().collect(Collectors.toMap(v -> v.getModuleCode(), u -> u, (u, v) -> u));
        for (String moduleCode : validModuleCodes) {
            Connector connector = code2Connector.get(moduleCode);
            if (connector == null) {
                log.error("moduleCode {},not get connector", moduleCode);
                continue;
            }
            syncQuota.validConnectorKey.add(connector.getKey());
            Long aStreamQuota = syncQuotaHelper.moduleStreamQuotaMap.getOrDefault(moduleCode, 0L);
            syncQuota.streamQuota += aStreamQuota;
            Long aMappingQuota = syncQuotaHelper.moduleMappingQuotaMap.getOrDefault(moduleCode, 0L);
            syncQuota.mappingQuota += aMappingQuota;
            MiscQuota channelQuota = new MiscQuota()
                    .setCode(connector.getKey())
                    .setStreamQuota(aStreamQuota)
                    .setMappingQuota(aMappingQuota);
            //.setLogQuota(aMappingQuota * 5);
            syncQuota.channelQuotas.add(channelQuota);
        }
        validPkgMap.forEach((pkg, count) -> {
            Long aStreamQuota = syncQuotaHelper.pkgStreamQuotaMap.getOrDefault(pkg, 0L);
            long streamQuota1 = aStreamQuota * count;
            syncQuota.streamQuota += streamQuota1;
            Long aMappingQuota = syncQuotaHelper.pkgMappingQuotaMap.getOrDefault(pkg, 0L);
            long mappingQuota1 = aMappingQuota * count;
            syncQuota.mappingQuota += mappingQuota1;
            MiscQuota miscQuota = new MiscQuota()
                    .setCode(pkg)
                    .setCount(count)
                    .setStreamQuota(streamQuota1)
                    .setMappingQuota(mappingQuota1);
            //.setLogQuota(mappingQuota1 * 5);
            syncQuota.pkgQuotas.add(miscQuota);
        });
        return syncQuota;
    }
}
