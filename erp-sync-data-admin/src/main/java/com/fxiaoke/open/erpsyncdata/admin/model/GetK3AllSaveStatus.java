package com.fxiaoke.open.erpsyncdata.admin.model;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/11 18:01:45
 */
public interface GetK3AllSaveStatus {
    @Data
    class Arg {

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<SaveStatus> addStatusList;

        private List<SaveStatus> modifyStatusList;

        /**
         * 预置保存参数Key
         */
        private Map<String,List<KeyType>> list;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class AddStatus {
        private Integer status;
        private List<ActionConf> actions;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class SaveStatus {
        private String status;
        private List<ActionConf> actions;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ActionConf {
        private Boolean selected;
        private String action;
        private String url;

        public ActionConf(final String action, final String url) {
            this.action = action;
            this.url = url;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class KeyType {
        private String key;
        /**
         * 1.数组 2.布尔 3.字符串
         */
        private int type;

        public static Map<String,List<GetK3AllSaveStatus.KeyType>> convert(Map<String,Map<String, Integer>> k3PresetSaveConfigKey) {
            Map<String,List<GetK3AllSaveStatus.KeyType>> config= Maps.newHashMap();
            for(String key:k3PresetSaveConfigKey.keySet()){
                List<GetK3AllSaveStatus.KeyType> list=k3PresetSaveConfigKey.get(key).entrySet().stream()
                        .map(entry -> {
                            GetK3AllSaveStatus.KeyType keyType = new GetK3AllSaveStatus.KeyType();
                            keyType.setKey(entry.getKey());
                            keyType.setType(entry.getValue());
                            return keyType;
                        }).collect(Collectors.toList());
                config.put(key,list);
            }
            return config;
        }
    }
}
