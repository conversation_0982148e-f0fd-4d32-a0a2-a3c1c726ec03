package com.fxiaoke.open.erpsyncdata.admin.utils;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 内部调用接口token验证工具类
 * <AUTHOR>
 * @Date 2022/02/28
 */
public class OutTokenVerifyUtils {
    /**
     * 验证token,token 1分钟有效
     * @param token
     * @param tokenKey
     * @return
     */
    public static Result verify(String token,String tokenKey) {
        if (StringUtils.isEmpty(token) || !StringUtils.contains(token, tokenKey)) {
            return Result.newError(ResultCodeEnum.VERIFY_TOKEN_FAILED);
        }
        String dateStr = token.replace(tokenKey, "");
        try {
            Date date = new Date(Long.parseLong(dateStr));
            if (System.currentTimeMillis() - date.getTime() > 60 * 1000) {
                return Result.newError(ResultCodeEnum.VERIFY_TOKEN_FAILED);
            }
        } catch (Exception e) {
            return Result.newError(ResultCodeEnum.VERIFY_TOKEN_FAILED);
        }
        return Result.newSuccess();
    }
}
