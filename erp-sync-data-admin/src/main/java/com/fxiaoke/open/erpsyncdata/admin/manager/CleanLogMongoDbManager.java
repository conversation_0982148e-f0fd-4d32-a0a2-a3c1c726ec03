package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.thread.NamedThreadFactory;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.erpsyncdata.admin.model.CleanLogMongoDb.Record;
import com.fxiaoke.open.erpsyncdata.common.constant.ProcessInfo2;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MonitorLogModule;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.TenantLimitableMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IniChangeListener;
import com.github.autoconf.base.IniConfig;
import com.mongodb.client.MongoCollection;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-11-10
 */
@Slf4j
@Service
public class CleanLogMongoDbManager implements InitializingBean, DisposableBean {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private TenantConfigurationManager configurationManager;
    @Autowired
    private ErpConnectInfoManager connectInfoManager;
    @Autowired
    private List<TenantLimitableMongoDao> tenantLimitableMongoDaoList;
    /**
     * 暂未使用
     * 线程池，最大设置到1个线程.线程用完后会销毁。
     */
    private ThreadPoolExecutor executor = null;
    private boolean parallel = false;


    @Override
    public void afterPropertiesSet() throws Exception {
        ConfigFactory.getInstance().getConfig("erp-sync-data-all", new IniChangeListener("syncQuota") {
            @Override
            public void iniChanged(IniConfig iniConfig) {
                loadConfig(iniConfig);
            }
        });
    }

    private void loadConfig(IConfig config) {
        //允许动态调整线程数,当线程数为1时清空线程池。
        int threads = config.getInt("cleanLogMongo.threads", 1);
        if (threads > 1) {
            if (executor == null) {
                executor = new ThreadPoolExecutor(0, threads, 60L,
                        TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(1024),
                        new NamedThreadFactory("SyncQuotaTask-", false),
                        new BlockPolicy());
            }
            parallel = true;
            if (threads != executor.getMaximumPoolSize()) {
                executor.setMaximumPoolSize(threads);
            }
        } else {
            parallel = false;
            if (executor != null) {
                executor.shutdown();
            }
        }
    }


    @SneakyThrows
    public Result<Map<String, Record>> executeCleanTask(Map<String, Long> tenantLogLimitMap) {
        //检查过滤有效企业
        Set<String> allEis = tenantLogLimitMap.keySet();
        if (allEis.isEmpty()) {
            log.warn("valid eis is empty");
            return Result.newSystemError(I18NStringEnum.s1164);
        }
        //逐个企业执行
        ConcurrentHashMap<String, Record> recordMap = new ConcurrentHashMap<>();
        if (parallel) {
            //多线程执行
            CountDownLatch cdl = new CountDownLatch(allEis.size());
            for (String ei : allEis) {
                Long limit = tenantLogLimitMap.get(ei);
                executor.execute(() -> {
                    try {
                        Record record = executeSingle(ei, limit);
                        recordMap.put(ei, record);
                    } catch (Exception e) {
                        log.warn("execute error", e);
                    } finally {
                        cdl.countDown();
                    }
                });
            }
            cdl.await();
        } else {
            //本线程执行
            for (String ei : allEis) {
                Long limit = tenantLogLimitMap.get(ei);
                try {
                    Record record = executeSingle(ei, limit);
                    recordMap.put(ei, record);
                } catch (Exception e) {
                    log.warn("execute error", e);
                }
            }
        }
        log.info("task completed!");
        return Result.newSuccess(recordMap);
    }

    private void sendTenantBizLog(Record record, String eventId) {
        String action = "erpSync-cleanLogMongoTask";
        AuditLogDTO dto = AuditLogDTO.builder()
                .appName(ProcessInfo2.appName)
                .serverIp(ProcessInfo2.serverIp)
                .profile(ProcessInfo2.profile)
                .action(action)
                .tenantId(record.getTenantId())
                //记录删除数量
                .cost(record.getDeleteCount())
                .step1Cost(record.getAllCount())
                .step2Cost(record.getExceptDeleteCount())
                .extra(JacksonUtil.toJson(record.getStatMap()))
                .eventId(eventId)
                .traceId(TraceUtil.get())
                .build();
        // 发送到kafka的topic名称，默认为biz-audit-log，如果修改，需要配套修改消费kafka的程序，决定存入es的哪个索引
        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    private Record executeSingle(String tenantId, Long limit) {
        log.info("clean log begin tenantId:{},limit:{}", tenantId, limit);
        Record record = new Record();
        record.setTenantId(tenantId);
        Map<String, CollStat> statMap = new HashMap<>();
        for (TenantLimitableMongoDao tenantLimitableMongoDao : tenantLimitableMongoDaoList) {
            CollStat collStat = tenantLimitableMongoDao.getCollStat(tenantId);
            String collPrefix = tenantLimitableMongoDao.getCollPrefix();
            statMap.put(collPrefix, collStat);
            //每个企业上报
            sendBizLog(tenantId, collStat, collPrefix);
        }
        long allCount = statMap.values().stream().mapToLong(v -> v.getCount()).sum();
        record.setStatMap(statMap).setAllCount(allCount);
        if (allCount <= limit * 1.2) {
            //未达到阈值，不执行清理
            record.setExceptDeleteCount(0L);
        } else {
            //计算需要删除的数量
            long needDelete = allCount - limit;
            //获取三个表数据的最早日期
            Map<String, MongoCollection<Document>> collMap = new HashMap<>();
            Map<String, Date> minDateMap = new HashMap<>();
            DateTime minDate = DateUtil.date();
            DateTime oneDayAgo = DateUtil.offsetDay(minDate, -1);
            for (TenantLimitableMongoDao tenantLimitableMongoDao : tenantLimitableMongoDaoList) {
                String collPrefix = tenantLimitableMongoDao.getCollPrefix();
                DateTime groupMinDate = tenantLimitableMongoDao.findMinDate(tenantId);
                minDateMap.put(collPrefix, groupMinDate);
                //获取多个表的最早的时间
                if (groupMinDate.before(minDate)) {
                    minDate = DateUtil.date(groupMinDate);
                }
            }
            Long deleteCount = 0L;
            while (minDate.before(oneDayAgo) && deleteCount < needDelete) {
                //存在一天之前的数据则执行清理
                DateTime endOfDay = DateUtil.endOfDay(minDate);
                //每个集合执行删除
                for (TenantLimitableMongoDao tenantLimitableMongoDao : tenantLimitableMongoDaoList) {
                    String collPrefix = tenantLimitableMongoDao.getCollPrefix();
                    Date groupMinDate = minDateMap.get(collPrefix);
                    if (groupMinDate == null || groupMinDate.after(endOfDay)) {
                        //最早的时间在要删除的时间段后，该集合不执行删除
                        continue;
                    }
                    Long groupDeleteCount = tenantLimitableMongoDao.deleteBetween(tenantId, minDate, endOfDay);
                    deleteCount += groupDeleteCount;
                }
                //调整最小时间到后一天
                minDate = DateUtil.beginOfDay(minDate.offset(DateField.DAY_OF_YEAR, 1));
            }
            record.setExceptDeleteCount(needDelete)
                    .setDeleteCount(deleteCount);
            //清理结束再查询上报一次
            for (TenantLimitableMongoDao tenantLimitableMongoDao : tenantLimitableMongoDaoList) {
                CollStat collStat = tenantLimitableMongoDao.getCollStat(tenantId);
                String collPrefix = tenantLimitableMongoDao.getCollPrefix();
                sendBizLog(tenantId, collStat, collPrefix);
            }
        }
        return record;
    }

    private static void sendBizLog(String tenantId, CollStat collStat, String collPrefix) {
        ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder = MonitorBizLogUtil.builder(MonitorLogModule.clean_log, "beforeStat", tenantId);
        builder.num1(collStat.getCount())
                .num2(collStat.getSize())
                .num3(collStat.getStorageSize())
                .num4(collStat.getTotalIndexSize())
                .num5(collStat.getAvgObjSize())
                .label1(collPrefix);
        MonitorBizLogUtil.send(builder.build());
    }

    @Override
    public void destroy() throws Exception {
        executor.shutdown();
    }
}
