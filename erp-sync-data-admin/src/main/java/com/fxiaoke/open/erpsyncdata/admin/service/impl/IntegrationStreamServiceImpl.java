package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.data.ApiMessageData;
import com.fxiaoke.open.erpsyncdata.admin.data.DataCenterData;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.fxiaoke.open.erpsyncdata.admin.manager.AplConfigManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.IntegrationStreamManager;
import com.fxiaoke.open.erpsyncdata.admin.model.GetByIdInterfaceStatus;
import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate.K3UltimateTokenModel;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.ErRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.*;
import com.fxiaoke.open.erpsyncdata.admin.service.*;
import com.fxiaoke.open.erpsyncdata.admin.utils.ChannelUrlUtils;
import com.fxiaoke.open.erpsyncdata.admin.utils.ResultConversionUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant.K3DocumentStatusEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateApiTemplateManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ReverseWrite2CrmSourceFieldKey;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DataCenterIntegration;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ObjectMappingVo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.open.erpsyncdata.writer.manager.DoWrite2CrmManager;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 16:43 2022/2/16
 * @Desc:
 */
@Service
@Slf4j
public class IntegrationStreamServiceImpl implements IntegrationStreamService {
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpConnectInfoDao connectInfoDao;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ErRemoteManager erRemoteManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private IntegrationStreamManager integrationStreamManager;
    @Autowired
    private FsCrmObjectService fsCrmObjectService;
    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private DoWrite2CrmManager doWrite2CrmManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    public static final String RECORD_FAIL_COUNT_STREAM_TENANT = "RECORD_FAIL_COUNT_STREAM_TENANT_%s_%s";
    public static final Long EXPIRE_RECORD = 1000 * 60 * 30L;
    @Autowired
    private ErpSyncTimeManager erpSyncTimeManager;
    @Autowired
    private FsObjectDataService fsObjectDataService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjCustomFunctionService erpObjCustomFunctionService;

    @Autowired
    private CustomFunctionService customFunctionService;

    @Autowired
    private SyncQuotaService syncQuotaService;
    @Autowired
    private ErpObjectAndFieldsService erpObjectAndFieldsService;
    @Autowired
    private ErpCustomInterfaceDao erpCustomInterfaceDao;
    @Autowired
    private ErpObjInterfaceCheckedManager erpObjInterfaceCheckedManager;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private K3UltimateApiTemplateManager k3UltimateApiTemplateManager;
    @Autowired
    private ErpK3UltimateTokenDao erpK3UltimateTokenDao;
    @Autowired
    private K3UltimateEventSubscribeService k3UltimateEventSubscribeService;
    @Autowired
    private ErpTenantConfigurationDaoAccess erpTenantConfigurationDaoAccess;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;
    @Autowired
    private PushIdentifyManager pushIdentifyManager;
    @Autowired
    private AplConfigManager aplConfigManager;
    @Autowired
    private GetByIdBreakManager getByIdBreakManager;


    public static final List<ErpObjInterfaceUrlEnum> destErpUrl = Lists.newArrayList(ErpObjInterfaceUrlEnum.create,
            ErpObjInterfaceUrlEnum.update, ErpObjInterfaceUrlEnum.invalid, ErpObjInterfaceUrlEnum.recover, ErpObjInterfaceUrlEnum.delete);
    public static final List<ErpObjInterfaceUrlEnum> sourceErpUrl = Lists.newArrayList(ErpObjInterfaceUrlEnum.queryMasterById,
            ErpObjInterfaceUrlEnum.queryMasterBatch,
            ErpObjInterfaceUrlEnum.queryInvalid);

    @Override
    public Result<String> createObjectMapping(String tenantId, CreateObjectMappingArg arg, String lang) {
        if (StringUtils.isBlank(arg.getSourceDataCenterId()) || StringUtils.isBlank(arg.getDestDataCenterId())) {
            return Result.newErrorByI18N(I18NStringEnum.s754.getI18nValue(), I18NStringEnum.s754.getI18nKey(), null);
        }
        if (StringUtils.isBlank(arg.getSourceObjectApiName())
                || StringUtils.isBlank(arg.getDestObjectApiName())) {
            return Result.newErrorByI18N(I18NStringEnum.s755.getI18nValue(), I18NStringEnum.s755.getI18nKey(), null);
        }
        //校验配额
        SyncQuota syncQuota = syncQuotaService.getQuota(tenantId, false, false).safeData();
        if (syncQuota.getStreamQuota() - syncQuota.getStreamUsed() <= 0) {
            return Result.newErrorByI18N(I18NStringEnum.s756.getI18nValue(), I18NStringEnum.s756.getI18nKey(), null);
        }

        // 检查对象有主键字段
        ErpConnectInfoEntity sourceConnectInfo = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, arg.getSourceDataCenterId());
        final List<String> noIdFieldObjApiNames = getNoIdFieldObjApiName(tenantId, arg, sourceConnectInfo);
        if (CollectionUtils.isNotEmpty(noIdFieldObjApiNames)) {
            return Result.newError(i18NStringManager.get2(I18NStringEnum.s2064.getI18nKey(), lang, tenantId, I18NStringEnum.s2064.getI18nValue(), Lists.newArrayList(String.join(",", noIdFieldObjApiNames))));
        }

        //校验是否存在重复的策略明细
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByDcIdAndObjApiName(tenantId, arg.getSourceDataCenterId(),
                        arg.getDestDataCenterId(), arg.getSourceObjectApiName(), arg.getDestObjectApiName());
        if (CollectionUtils.isNotEmpty(syncPloyDetailEntities)) {
            return Result.newError(ResultCodeEnum.PLOY_DETAIL_DUPLICATE);
        }

        SyncConditionsListData detailObjectSyncConditions = new SyncConditionsListData();
        DetailObjectMappingsData detailObjectMappingData = new DetailObjectMappingsData();
        if (CollUtil.isNotEmpty(arg.getDetailObjectMappings())) {
            for (CreateObjectMappingArg.ObjectMapping objectMapping : arg.getDetailObjectMappings()) {
                if (StringUtils.isNotEmpty(objectMapping.getSourceObjectApiName()) && StringUtils.isNotEmpty(objectMapping.getDestObjectApiName())) {
                    DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping = new DetailObjectMappingsData.DetailObjectMappingData();
                    detailObjectMapping.setSourceObjectApiName(objectMapping.getSourceObjectApiName());
                    detailObjectMapping.setDestObjectApiName(objectMapping.getDestObjectApiName());
                    detailObjectMappingData.add(detailObjectMapping);
                    SyncConditionsData detailSyncConditionsData = new SyncConditionsData();
                    detailSyncConditionsData.setApiName(detailObjectMapping.getSourceObjectApiName());
                    detailObjectSyncConditions.add(detailSyncConditionsData);
                }
            }
            //检查从对象的主从字段
            List<String> erpDetailObjApiNames;
            String erpDcId;
            if (ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {
                erpDetailObjApiNames = detailObjectMappingData.stream().map(v -> v.getDestObjectApiName()).collect(Collectors.toList());
                erpDcId = arg.getDestDataCenterId();
            } else {
                if (arg.getSyncRules().getEvents() != null
                        && arg.getSyncRules().getEvents().contains(EventTypeEnum.DELETE_DIRECT.getType())
                        && arg.getSyncRules().getSyncTypeList() != null
                        && arg.getSyncRules().getSyncTypeList().contains(SyncTypeEnum.get.name())) {
                    return Result.newError(I18NStringEnum.s5112);
                }
                erpDetailObjApiNames = detailObjectMappingData.stream().map(v -> v.getSourceObjectApiName()).collect(Collectors.toList());
                erpDcId = arg.getSourceDataCenterId();
            }
            List<ErpObjectFieldEntity> masterDetailFields = erpObjectFieldDao.findByObjsAndType(tenantId, erpDcId, erpDetailObjApiNames, ErpFieldTypeEnum.master_detail);
            Set<String> validObjApiNames = masterDetailFields.stream().map(v -> v.getErpObjectApiName()).collect(Collectors.toSet());
            Collection<String> subtract = CollUtil.subtract(erpDetailObjApiNames, validObjApiNames);
            if (!subtract.isEmpty()) {
                return Result.newError(i18NStringManager.get(I18NStringEnum.s757, lang, tenantId) + subtract);
            }
        }
        SyncPloyDetailEntity entity = BeanUtil2.deepCopy(arg, SyncPloyDetailEntity.class);
        if (ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {//源是crm
            entity.setSourceTenantType(1);
            entity.setDestTenantType(2);
        } else {//源是erp
            entity.setSourceTenantType(2);
            entity.setDestTenantType(1);
        }
        entity.setId(IdUtil.generateId());
        entity.setTenantId(tenantId);
        entity.setSyncPloyId(tenantId);//使用企业id代替
        entity.setStatus(SyncPloyDetailStatusEnum.DISABLE.getStatus());
        entity.setDetailObjectMappings(detailObjectMappingData);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setSyncConditions(new SyncConditionsData());
        entity.setDetailObjectSyncConditions(detailObjectSyncConditions);

        //默认设置同步规则为轮询方式
        SyncRulesData syncRulesData = new SyncRulesData();
        if (arg.getSyncRules() != null) {
            if (CollectionUtils.isNotEmpty(arg.getSyncRules().getSyncTypeList())) {
                syncRulesData.setSyncTypeList(arg.getSyncRules().getSyncTypeList());
            }
            if (CollectionUtils.isNotEmpty(arg.getSyncRules().getEvents())) {
                //默认选中修改映射字段才能触发数据新增 7
                arg.getSyncRules().getEvents().add(7);
                syncRulesData.setEvents(Lists.newArrayList(arg.getSyncRules().getEvents()));
            }
            if (arg.getSyncRules().getPollingInterval() != null) {
                PollingIntervalApiDto pollingInterval = BeanUtil2.deepCopy(arg.getSyncRules().getPollingInterval(), PollingIntervalApiDto.class);
                syncRulesData.setPollingInterval(pollingInterval);
            }
        }
        entity.setSyncRules(syncRulesData);
        //创建集成流不需要初始化时间
//        if (TenantType.ERP == entity.getSourceTenantType()) {
//            adminSyncPloyDetailService.doLastSyncTime(tenantId, entity.getSourceDataCenterId(), entity.getSourceObjectApiName(), entity.getSyncRules());
//        }
        //默认关闭getById接口
        if (!ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {//源不是crm
            closeGetByIdStatus(tenantId, sourceConnectInfo, arg);
        }
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insertIgnore(entity);
        if (sourceConnectInfo.getChannel() == ErpChannelEnum.ERP_K3CLOUD_ULTIMATE && syncRulesData.getSyncTypeList().contains(SyncTypeEnum.subscribe.name())) {
            ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findBySplit(tenantId, arg.getSourceObjectApiName());
            String realObjApiName = relationshipEntity.getErpRealObjectApiname();

            ErpK3UltimateTokenEntity k3UltimateTokenEntity = erpK3UltimateTokenDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findData(tenantId, arg.getSourceDataCenterId(), realObjApiName, "1.0");
            if (k3UltimateTokenEntity == null) {
                K3UltimateTokenModel model = new K3UltimateTokenModel();
                model.setTenantId(tenantId);
                model.setDataCenterId(arg.getSourceDataCenterId());
                model.setErpObjApiName(realObjApiName);
                model.setVersion("1.0");
                model.setTimestamps(System.currentTimeMillis() + "");
                Result<String> genToken = k3UltimateEventSubscribeService.genToken(model);
                log.info("IntegrationStreamServiceImpl.createObjectMapping,genToken={}", genToken);
            }
        }
        return Result.newSuccess(entity.getId());
    }

    @NotNull
    private List<String> getNoIdFieldObjApiName(String tenantId, CreateObjectMappingArg arg, ErpConnectInfoEntity sourceConnectInfo) {
        final boolean crmSource = ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel());
        return Stream.concat(
                        arg.getDetailObjectMappings().stream()
                                .map(mapping -> crmSource ? mapping.getDestObjectApiName() : mapping.getSourceObjectApiName()),
                        Stream.of(crmSource ? arg.getDestObjectApiName() : arg.getSourceObjectApiName())
                ).distinct()
                .filter(name -> Objects.isNull(erpFieldManager.findIdField(tenantId, name)))
                .collect(Collectors.toList());
    }

    private void closeGetByIdStatus(String tenantId, ErpConnectInfoEntity sourceConnectInfo, CreateObjectMappingArg arg) {//源是erp
        String erpObjApiName = arg.getSourceObjectApiName();
        if (arg.getSyncRules() != null && arg.getSyncRules().getSyncTypeList() != null && arg.getSyncRules().getSyncTypeList().contains(SyncTypeEnum.push.name())) {
            //推送不默认关闭
            return;
        }
        if (!ErpChannelEnum.ERP_K3CLOUD.equals(sourceConnectInfo.getChannel())
                && !ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.equals(sourceConnectInfo.getChannel())
                && !ErpChannelEnum.ERP_U8.equals(sourceConnectInfo.getChannel())
                && !ErpChannelEnum.ERP_U8_EAI.equals(sourceConnectInfo.getChannel())) {
            List<SyncPloyDetailEntity> ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listBySourceTenantTypeAndObjApiName(tenantId, TenantType.ERP, erpObjApiName);
            if (CollectionUtils.isEmpty(ployDetailEntities)) {//不存在策略,如果存在以第一次为准不默认
                GetByIdApiStatusArg getByIdApiStatusArg = new GetByIdApiStatusArg();
                getByIdApiStatusArg.setSplitObjectApiName(erpObjApiName);
                getByIdApiStatusArg.setStatus(false);
                this.updateGetByIdStatus(tenantId, sourceConnectInfo.getId(), getByIdApiStatusArg);
            }
        }
    }

    /**
     * 目前集成流列表与数据维护列表共用一个接口。queryFailDataCount识别是数据维护列表查询，true则查询待处理条数
     */
    @Override
    public Result<QueryResult<List<IntegrationStreamResult>>> queryIntegrationStreamList(String tenantId,
                                                                                         ListIntegrationStreamArg arg,
                                                                                         boolean queryFailDataCount,
                                                                                         String lang) {
        QueryResult<List<IntegrationStreamResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        int total = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .countBySourceOrDestDcIdAndObjApiName(tenantId, arg.getDcId(), arg.getCrmObjApiName(), arg.getStatus(), arg.getQueryStr());
        queryResult.setTotal(total);
        if (total == 0) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceOrDestDcIdAndObjApiName(tenantId, arg.getDcId(), arg.getCrmObjApiName(),
                        arg.getStatus(), arg.getQueryStr(), (arg.getPageNum() - 1) * arg.getPageSize(), arg.getPageSize());
        List<IntegrationStreamResult> integrationStreamResultList = Lists.newArrayList();
        Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(tenantId, Lists.newArrayList(tenantId));
        //主要用于转换对象名字
        Map<String, Set<String>> erpDc2ObjApiNames = Maps.newHashMap();//erp,dcId->对象
        Map<String, Map<String, String>> dc2objApi2Name = Maps.newHashMap();
        Map<String, String> crmObjectApiNameToNameMap = convertObjName(tenantId, erpDc2ObjApiNames, dc2objApi2Name, syncPloyDetailEntities);

        if (queryFailDataCount) {
            //数据维护页查询
            queryDataMaintenanceList(syncPloyDetailEntities, integrationStreamResultList, crmObjectApiNameToNameMap, dc2objApi2Name, tenantId, tenantIdToNameMap, lang);
        } else {
            //集成流列表页查询
            queryIntegrationListResult(syncPloyDetailEntities, integrationStreamResultList, crmObjectApiNameToNameMap, dc2objApi2Name, tenantId, tenantIdToNameMap, lang);
        }
        queryResult.setDataList(integrationStreamResultList);
        return Result.newSuccess(queryResult);
    }

    //集成流列表转换
    private List<IntegrationStreamResult> queryIntegrationListResult(List<SyncPloyDetailEntity> syncPloyDetailEntities,
                                                                     List<IntegrationStreamResult> integrationStreamResultList,
                                                                     Map<String, String> crmObjectApiNameToNameMap,
                                                                     Map<String, Map<String, String>> dc2objApi2Name,
                                                                     String tenantId,
                                                                     Map<String, String> tenantIdToNameMap,
                                                                     String lang) {

        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
            IntegrationStreamResult integrationStreamResult = changePloyDetail2IntegrationStream(tenantId, tenantIdToNameMap, syncPloyDetailEntity,
                    crmObjectApiNameToNameMap, dc2objApi2Name, lang);
            integrationStreamResultList.add(integrationStreamResult);
        }
        return integrationStreamResultList;
    }

    // 数据维护页转换
    private List<IntegrationStreamResult> queryDataMaintenanceList(List<SyncPloyDetailEntity> syncPloyDetailEntities,
                                                                   List<IntegrationStreamResult> integrationStreamResultList,
                                                                   Map<String, String> crmObjectApiNameToNameMap,
                                                                   Map<String, Map<String, String>> dc2objApi2Name,
                                                                   String tenantId,
                                                                   Map<String, String> tenantIdToNameMap,
                                                                   String lang) {
        Map<String, String> objectAndTenantsToPloyDetailIdMap = new HashMap<>();
        List<SyncObjectAndTenantMappingData> objectApiNameMappings = new ArrayList<>();
        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
            IntegrationStreamResult integrationStreamResult = changePloyDetail2IntegrationStream(tenantId, tenantIdToNameMap, syncPloyDetailEntity,
                    crmObjectApiNameToNameMap, dc2objApi2Name, lang);
            for (TenantData sourceTenantData : integrationStreamResult.getSourceTenantDatas()) {
                for (TenantData destTenantData : integrationStreamResult.getDestTenantDatas()) {
                    SyncObjectAndTenantMappingData syncObjectAndTenantMappingData = new SyncObjectAndTenantMappingData();
                    syncObjectAndTenantMappingData.setSourceTenantId(sourceTenantData.getTenantId());
                    syncObjectAndTenantMappingData.setSourceObjectApiName(integrationStreamResult.getSourceObjectApiName());
                    syncObjectAndTenantMappingData.setDestTenantId(destTenantData.getTenantId());
                    syncObjectAndTenantMappingData.setDestObjectApiName(integrationStreamResult.getDestObjectApiName());
                    objectApiNameMappings.add(syncObjectAndTenantMappingData);
                    objectAndTenantsToPloyDetailIdMap.put(syncObjectAndTenantMappingData.toString(), integrationStreamResult.getId());
                }
            }
            integrationStreamResultList.add(integrationStreamResult);
            if (integrationStreamResult.getDetailObjectMappings() == null || integrationStreamResult.getDetailObjectMappings().size() == 0) {
                continue;
            }
            for (IntegrationStreamResult.ObjectMappingInfo detailObjectMapping : integrationStreamResult.getDetailObjectMappings()) {
                for (TenantData sourceTenantData : integrationStreamResult.getSourceTenantDatas()) {
                    for (TenantData destTenantData : integrationStreamResult.getDestTenantDatas()) {
                        SyncObjectAndTenantMappingData detailObjectAndTenantMappingData = new SyncObjectAndTenantMappingData();
                        detailObjectAndTenantMappingData.setSourceObjectApiName(detailObjectMapping.getSourceObjectApiName());
                        detailObjectAndTenantMappingData.setSourceTenantId(sourceTenantData.getTenantId());
                        detailObjectAndTenantMappingData.setDestObjectApiName(detailObjectMapping.getDestObjectApiName());
                        detailObjectAndTenantMappingData.setDestTenantId(destTenantData.getTenantId());
                        objectApiNameMappings.add(detailObjectAndTenantMappingData);
                        objectAndTenantsToPloyDetailIdMap.put(detailObjectAndTenantMappingData.toString(), integrationStreamResult.getId());
                    }
                }
            }
        }

        List<SyncDataMappingsFailedCountData> failedSyncDataMappings = syncDataMappingsDao.setTenantId(tenantId).countSyncFailed(tenantId, objectApiNameMappings);

        for (SyncDataMappingsFailedCountData countData : failedSyncDataMappings) {
            String key = countData.getSourceObjectApiName() + "-" + countData.getDestObjectApiName();
            String ployDetailId = objectAndTenantsToPloyDetailIdMap.get(key);
            for (IntegrationStreamResult syncPloyDetailInfoResult : integrationStreamResultList) {
                if (syncPloyDetailInfoResult.getId().equals(ployDetailId)) {
                    Integer newSyncFailedDataCount = syncPloyDetailInfoResult.getSyncFailedDataCount() + countData.getFailedCount();
                    syncPloyDetailInfoResult.setSyncFailedDataCount(newSyncFailedDataCount);
                }
            }
        }
        return integrationStreamResultList;
    }

    private List<IntegrationViewResult> queryNewDataMaintenanceList(List<SyncPloyDetailEntity> syncPloyDetailEntities,
                                                                    List<IntegrationViewResult> integrationStreamResultList,
                                                                    Map<String, String> crmObjectApiNameToNameMap,
                                                                    Map<String, Map<String, String>> dc2objApi2Name,
                                                                    String tenantId,
                                                                    Map<String, String> tenantIdToNameMap,
                                                                    String lang) {

        if (syncPloyDetailEntities.isEmpty()) {
            return new ArrayList<>();
        }
        MergeJedisCmd mergeJedisCmd = redisDataSource.get(this.getClass().getSimpleName());
        //熔断的集成流集合
        String breakSetTopic = String.format(CommonConstant.REDIS_KEY_PLOY_BREAK_SET, tenantId);
        Set<String> brokenPloyDetail = mergeJedisCmd.smembers(breakSetTopic);
        //停用的集成流查询一下停用时间
        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
            IntegrationViewResult integrationStreamResult = changeNewPloyDetail2IntegrationStream(tenantId, tenantIdToNameMap, syncPloyDetailEntity,
                    crmObjectApiNameToNameMap, dc2objApi2Name, lang);
            //检查是否熔断的集成流
            final Long lastSyncTime = configCenterConfig.getPloyDetailLastSyncTime(tenantId, syncPloyDetailEntity.getId());
            integrationStreamResult.setBroken(brokenPloyDetail.contains(integrationStreamResult.getId()));
            integrationStreamResult.setStopTime(lastSyncTime);
            integrationStreamResultList.add(integrationStreamResult);

            // 检查是否有反向集成流
            final SyncPloyDetailEntity reversePloy = syncPloyDetailManager.getEntityByTenantIdAndObjApiName(tenantId, syncPloyDetailEntity.getDestObjectApiName(), syncPloyDetailEntity.getSourceObjectApiName());
            if (Objects.nonNull(reversePloy)) {
                integrationStreamResult.setReverseStreamName(reversePloy.getIntegrationStreamName());
                integrationStreamResult.setReverseStreamId(reversePloy.getId());
            }
        }
        return integrationStreamResultList;
    }

    /**
     * 为了根据CRM/ERP APINAME换取对应的对象名字
     *
     * @param erpDc2ObjApiNames erp对象名字，以dcid维度
     */
    public Map<String, String> convertObjName(String tenantId, Map<String, Set<String>> erpDc2ObjApiNames, Map<String, Map<String, String>> dc2objApi2Name, List<SyncPloyDetailEntity> syncPloyDetailEntities) {
        Set<String> crmObjApiNames = Sets.newHashSet();//crm对象
        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
            getObjApiNameByPloyDetail(crmObjApiNames, erpDc2ObjApiNames, syncPloyDetailEntity);
        }
        for (String dcId : erpDc2ObjApiNames.keySet()) {
            Map<String, String> objectApiNameToNameMap = erpObjectService.queryErpObjectNameByApiName(tenantId, dcId, -10000, Lists.newArrayList(erpDc2ObjApiNames.get(dcId)));
            dc2objApi2Name.put(dcId, objectApiNameToNameMap);
        }
        //主要用于转换对象名字
        return crmRemoteManager.listObjectNamesByApiNames(tenantId, Lists.newArrayList(crmObjApiNames));
    }

    public void getObjApiNameByPloyDetail(Set<String> crmObjApiNames, Map<String, Set<String>> erpDc2ObjApiNames, SyncPloyDetailEntity syncPloyDetailEntity) {
        if (TenantType.CRM == syncPloyDetailEntity.getSourceTenantType()) {
            crmObjApiNames.add(syncPloyDetailEntity.getSourceObjectApiName());
            if (erpDc2ObjApiNames.containsKey(syncPloyDetailEntity.getDestDataCenterId())) {
                erpDc2ObjApiNames.get(syncPloyDetailEntity.getDestDataCenterId()).add(syncPloyDetailEntity.getDestObjectApiName());
            } else {
                erpDc2ObjApiNames.put(syncPloyDetailEntity.getDestDataCenterId(), Sets.newHashSet(syncPloyDetailEntity.getDestObjectApiName()));
            }
            if (syncPloyDetailEntity.getDetailObjectMappings() != null) {
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : syncPloyDetailEntity.getDetailObjectMappings()) {
                    crmObjApiNames.add(detailObjectMappingData.getSourceObjectApiName());
                    erpDc2ObjApiNames.get(syncPloyDetailEntity.getDestDataCenterId()).add(detailObjectMappingData.getDestObjectApiName());
                }
            }
        } else {
            crmObjApiNames.add(syncPloyDetailEntity.getDestObjectApiName());
            if (erpDc2ObjApiNames.containsKey(syncPloyDetailEntity.getSourceDataCenterId())) {
                erpDc2ObjApiNames.get(syncPloyDetailEntity.getSourceDataCenterId()).add(syncPloyDetailEntity.getSourceObjectApiName());
            } else {
                erpDc2ObjApiNames.put(syncPloyDetailEntity.getSourceDataCenterId(), Sets.newHashSet(syncPloyDetailEntity.getSourceObjectApiName()));
            }
            if (syncPloyDetailEntity.getDetailObjectMappings() != null) {
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : syncPloyDetailEntity.getDetailObjectMappings()) {
                    crmObjApiNames.add(detailObjectMappingData.getDestObjectApiName());
                    erpDc2ObjApiNames.get(syncPloyDetailEntity.getSourceDataCenterId()).add(detailObjectMappingData.getSourceObjectApiName());
                }
            }
        }
        if (syncPloyDetailEntity.getIntegrationStreamNodes() != null) {
            if (syncPloyDetailEntity.getIntegrationStreamNodes().getSyncConditionsQueryDataNode() != null) {
                IntegrationStreamNodesData.SyncConditionsQueryDataNode syncConditionsQueryDataNode = syncPloyDetailEntity.getIntegrationStreamNodes().getSyncConditionsQueryDataNode();
                if (syncConditionsQueryDataNode.getQueryObjectMappingData() != null) {
                    for (QueryObjectMappingData queryObjectMappingData : syncConditionsQueryDataNode.getQueryObjectMappingData()) {
                        crmObjApiNames.add(queryObjectMappingData.getSourceObjectApiName());
                    }
                }
            }
            if (syncPloyDetailEntity.getIntegrationStreamNodes().getCheckSyncDataMappingNode() != null) {
                IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode = syncPloyDetailEntity.getIntegrationStreamNodes().getCheckSyncDataMappingNode();
                crmObjApiNames.add(checkSyncDataMappingNode.getQueryObjectMappingData().getSourceObjectApiName());
                if (CollectionUtils.isNotEmpty(checkSyncDataMappingNode.getDetailCheckSyncDataMappingData())) {
                    for (DetailQueryObject2SyncDataMappingsData.DetailQueryObject2SyncDataMappingData data : checkSyncDataMappingNode.getDetailCheckSyncDataMappingData()) {
                        crmObjApiNames.add(data.getQueryObjectMappingData().getSourceObjectApiName());
                    }
                }
            }
            if (syncPloyDetailEntity.getIntegrationStreamNodes().getQueryCrmObject2DestNodeBySource() != null) {
                IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNode = syncPloyDetailEntity.getIntegrationStreamNodes().getQueryCrmObject2DestNodeBySource();
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getQueryObjectToDestObject())) {
                    for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryCrmObject2DestNode.getQueryObjectToDestObject()) {
                        if (queryObjectToDestObjectData.getQueryObjectMappingData() != null && queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName() != null) {
                            crmObjApiNames.add(queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName());
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getDetailQueryData2DestDataMapping())) {
                    for (List<QueryObjectToDestObjectData> data : queryCrmObject2DestNode.getDetailQueryData2DestDataMapping()) {
                        for (QueryObjectToDestObjectData queryObjectToDestObjectData : data) {
                            if (queryObjectToDestObjectData.getQueryObjectMappingData() != null && queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName() != null) {
                                crmObjApiNames.add(queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName());
                            }
                        }

                    }
                }
            }
            if (syncPloyDetailEntity.getIntegrationStreamNodes().getQueryCrmObject2DestNodeByDest() != null) {
                IntegrationStreamNodesData.QueryCrmObject2DestNode queryCrmObject2DestNode = syncPloyDetailEntity.getIntegrationStreamNodes().getQueryCrmObject2DestNodeByDest();
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getQueryObjectToDestObject())) {
                    for (QueryObjectToDestObjectData queryObjectToDestObjectData : queryCrmObject2DestNode.getQueryObjectToDestObject()) {
                        if (queryObjectToDestObjectData.getQueryObjectMappingData() != null && queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName() != null) {
                            crmObjApiNames.add(queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getDetailQueryData2DestDataMapping())) {
                    for (List<QueryObjectToDestObjectData> data : queryCrmObject2DestNode.getDetailQueryData2DestDataMapping()) {
                        for (QueryObjectToDestObjectData queryObjectToDestObjectData : data) {
                            if (queryObjectToDestObjectData.getQueryObjectMappingData() != null && queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName() != null) {
                                crmObjApiNames.add(queryObjectToDestObjectData.getQueryObjectMappingData().getSourceObjectApiName());
                            }
                        }
                    }
                }
            }
        }
    }

    private IntegrationStreamResult changePloyDetail2IntegrationStream(String tenantId,
                                                                       Map<String, String> tenantIdToNameMap,
                                                                       SyncPloyDetailEntity entity,
                                                                       Map<String, String> crmObjectApiNameToNameMap,
                                                                       Map<String, Map<String, String>> dc2objApi2Name,
                                                                       String lang) {
        IntegrationStreamResult integrationStreamResult = BeanUtil.deepCopy(entity, IntegrationStreamResult.class);
        integrationStreamResult.getSourceTenantDatas().add(TenantData.newDataByTenantIdAndName(entity.getTenantId(), tenantIdToNameMap.get(entity.getTenantId())));
        integrationStreamResult.setSourceDc(DataCenterData.newDataByDcId(tenantId, entity.getSourceDataCenterId(), this.connectInfoDao, i18NStringManager, lang));
        integrationStreamResult.setStatusName(SyncPloyDetailStatusEnum.getNameByStatus(i18NStringManager, lang, tenantId, entity.getStatus()));
        integrationStreamResult.getDestTenantDatas().add(TenantData.newDataByTenantIdAndName(entity.getTenantId(), tenantIdToNameMap.get(entity.getTenantId())));
        integrationStreamResult.setDestDc(DataCenterData.newDataByDcId(tenantId, entity.getDestDataCenterId(), this.connectInfoDao, i18NStringManager, lang));
        if (TenantType.CRM == integrationStreamResult.getSourceTenantType()) {
            integrationStreamResult.setSourceObjectName(crmObjectApiNameToNameMap.get(entity.getSourceObjectApiName()));
            integrationStreamResult.setDestObjectName(dc2objApi2Name.get(entity.getDestDataCenterId()).get(entity.getDestObjectApiName()));
            if (integrationStreamResult.getDetailObjectMappings() != null) {
                for (IntegrationStreamResult.ObjectMappingInfo objectMappingInfo : integrationStreamResult.getDetailObjectMappings()) {
                    objectMappingInfo.setSourceObjectName(crmObjectApiNameToNameMap.get(objectMappingInfo.getSourceObjectApiName()));
                    objectMappingInfo.setDestObjectName(dc2objApi2Name.get(entity.getDestDataCenterId()).get(objectMappingInfo.getDestObjectApiName()));
                }
            }
        } else {
            integrationStreamResult.setDestObjectName(crmObjectApiNameToNameMap.get(entity.getDestObjectApiName()));
            integrationStreamResult.setSourceObjectName(dc2objApi2Name.get(entity.getSourceDataCenterId()).get(entity.getSourceObjectApiName()));
            //ERPD的真实apiname
            ErpObjectRelationshipEntity erpObjectRelationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, integrationStreamResult.getSourceObjectApiName());
            integrationStreamResult.setSourceObjectActualApiName(erpObjectRelationshipEntity.getErpRealObjectApiname());
            if (integrationStreamResult.getDetailObjectMappings() != null) {
                for (IntegrationStreamResult.ObjectMappingInfo objectMappingInfo : integrationStreamResult.getDetailObjectMappings()) {
                    objectMappingInfo.setDestObjectName(crmObjectApiNameToNameMap.get(objectMappingInfo.getDestObjectApiName()));
                    objectMappingInfo.setSourceObjectName(dc2objApi2Name.get(entity.getSourceDataCenterId()).get(objectMappingInfo.getSourceObjectApiName()));
                }
            }
        }
        return integrationStreamResult;
    }

    @Override
    public Result<QueryIntegrationDetailResult> getIntegrationStreamDetail(String tenantId, IdArg arg, String lang) {
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getId());
        QueryIntegrationDetailResult queryIntegrationDetailResult = convertResultByStreamDetail(syncPloyDetailEntity, lang);
        //节点信息校验
        Map<String, UpdateIntegrationStreamResult.ErrorMsg> errorMap = integrationStreamManager.commonVerifyStreamDetail(tenantId, syncPloyDetailEntity, lang);
        //检验crm对象是否被禁用
        if (syncPloyDetailEntity.getSourceTenantType().equals(TenantType.CRM)) {
            queryIntegrationDetailResult.getSourceSystemNode().setErrorMsg(errorMap.get("sourceSystemNode"));
        } else {
            queryIntegrationDetailResult.getDestSystemNode().setErrorMsg(errorMap.get("destSystemNode"));
        }

        //校验数据范围的字段是否被禁用
        if (CollectionUtils.isNotEmpty(syncPloyDetailEntity.getSyncConditions().getFilters())) {
            queryIntegrationDetailResult.getSyncConditionsNode().setErrorMsg(errorMap.get("syncConditionsNode"));
        }
        //校验函数列表是否可用
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getBeforeFuncApiName())) {
            queryIntegrationDetailResult.getBeforeFunctionNode().setErrorMsg(errorMap.get("beforeFunctionNode"));

        }
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getDuringFuncApiName())) {
            queryIntegrationDetailResult.getDurationFunctionApiNode().setErrorMsg(errorMap.get("durationFunctionApiNode"));
        }
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getAfterFuncApiName())) {
            Result<Boolean> afterFunctionMessage = integrationStreamManager.verifyFunctionStatus(tenantId, syncPloyDetailEntity.getAfterFuncApiName());
            queryIntegrationDetailResult.getAfterFunctionNode().setErrorMsg(errorMap.get("afterFunctionNode"));
        }
        //字段映射
        queryIntegrationDetailResult.getFieldMappingNode().setErrorMsg(errorMap.get("fieldMappingNode"));
        //回写crm节点
        if (queryIntegrationDetailResult.getReverseWriteNode() != null) {
            queryIntegrationDetailResult.getReverseWriteNode().setErrorMsg(errorMap.get("reverseWriteNode"));
        }
        return Result.newSuccess(queryIntegrationDetailResult);
    }

    @Override
    public Result<IntegrationStreamResult> getStreamDetail(String tenantId, IdArg arg, String lang) {

        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getId());
        //主要用于转换对象名字
        Map<String, Set<String>> erpDc2ObjApiNames = Maps.newHashMap();//erp,dcId->对象
        Map<String, Map<String, String>> dc2objApi2Name = Maps.newHashMap();
        Map<String, String> crmObjectApiNameToNameMap = convertObjName(tenantId, erpDc2ObjApiNames, dc2objApi2Name, Lists.newArrayList(syncPloyDetailEntity));
        Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(tenantId, Lists.newArrayList(tenantId));
        IntegrationStreamResult integrationStreamResult = changePloyDetail2IntegrationStream(tenantId, tenantIdToNameMap, syncPloyDetailEntity,
                crmObjectApiNameToNameMap, dc2objApi2Name, lang);

        return Result.newSuccess(integrationStreamResult);
    }

    private QueryIntegrationDetailResult convertResultByStreamDetail(SyncPloyDetailEntity syncPloyDetailEntity, String lang) {
        String tenantId = syncPloyDetailEntity.getTenantId();
        Map<String, Set<String>> erpDc2ObjApiNames = Maps.newHashMap();//erp,dcId->对象
        Map<String, Map<String, String>> dc2objApi2Name = Maps.newHashMap();
        Map<String, String> crmObjectApiNameToNameMap = convertObjName(tenantId, erpDc2ObjApiNames, dc2objApi2Name, Lists.newArrayList(syncPloyDetailEntity));
        boolean isCRM2ERP = syncPloyDetailEntity.getSourceTenantType().equals(TenantType.CRM) ? true : false;
        //源系统节点
        QueryIntegrationDetailResult queryIntegrationDetailResult = new QueryIntegrationDetailResult();
        queryIntegrationDetailResult.setId(syncPloyDetailEntity.getId());
        queryIntegrationDetailResult.setIntegrationStreamName(syncPloyDetailEntity.getIntegrationStreamName());
        QueryIntegrationDetailResult.SourceSystemNode sourceSystemNode = new QueryIntegrationDetailResult.SourceSystemNode();
        //目标系统节点
        QueryIntegrationDetailResult.DestSystemNode destSystemNode = new QueryIntegrationDetailResult.DestSystemNode();
        sourceSystemNode.setSourceDc(DataCenterData.newDataByDcId(tenantId, syncPloyDetailEntity.getSourceDataCenterId(), connectInfoDao, i18NStringManager, lang));
        sourceSystemNode.setSourceObjectApiName(syncPloyDetailEntity.getSourceObjectApiName());
        Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(tenantId, Lists.newArrayList(tenantId));
        sourceSystemNode.setSourceTenantDatas(Lists.newArrayList(TenantData.newDataByTenantIdAndName(syncPloyDetailEntity.getTenantId(), tenantIdToNameMap.get(syncPloyDetailEntity.getTenantId()))));
        sourceSystemNode.setSourceTenantType(syncPloyDetailEntity.getSourceTenantType());
        destSystemNode.setDestObjectApiName(syncPloyDetailEntity.getDestObjectApiName());
        destSystemNode.setDestTenantType(syncPloyDetailEntity.getDestTenantType());
        destSystemNode.setDestTenantDatas(Lists.newArrayList(TenantData.newDataByTenantIdAndName(syncPloyDetailEntity.getTenantId(), tenantIdToNameMap.get(syncPloyDetailEntity.getTenantId()))));
        destSystemNode.setDestDc(DataCenterData.newDataByDcId(tenantId, syncPloyDetailEntity.getDestDataCenterId(), connectInfoDao, i18NStringManager, lang));
        if (isCRM2ERP) {
            sourceSystemNode.setSourceObjectName(crmObjectApiNameToNameMap.get(syncPloyDetailEntity.getSourceObjectApiName()));
            destSystemNode.setDestObjectName(dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(syncPloyDetailEntity.getDestObjectApiName()));
        } else {
            destSystemNode.setDestObjectName(crmObjectApiNameToNameMap.get(syncPloyDetailEntity.getDestObjectApiName()));
            sourceSystemNode.setSourceObjectName(dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(syncPloyDetailEntity.getSourceObjectApiName()));
        }
        if(TenantType.CRM.equals(syncPloyDetailEntity.getSourceTenantType())){
            //补充配置信息
            PaasDataSourceConfigStatus paasDataSourceConfigStatus=new PaasDataSourceConfigStatus();
            paasDataSourceConfigStatus.setCrmObjectApiName(syncPloyDetailEntity.getSourceObjectApiName());
            Result<PaasDataSourceConfigStatus> statusResult=this.queryTenantNeedPassDataSourceConfig(tenantId,paasDataSourceConfigStatus);
            if(statusResult.isSuccess()&&statusResult.getData()!=null){
                PaasDataSourceConfigStatus sourceConfigStatus=statusResult.getData();
                QueryIntegrationDetailResult.NotPassDataSourceStatus notPassDataSourceStatus=new QueryIntegrationDetailResult.NotPassDataSourceStatus();
                notPassDataSourceStatus.setStatus(sourceConfigStatus.getStatus());
                notPassDataSourceStatus.setDataSourceList(sourceConfigStatus.getDataSourceList());
                sourceSystemNode.setNotPassDataSourceStatus(notPassDataSourceStatus);
            }
        }
        queryIntegrationDetailResult.setSourceSystemNode(sourceSystemNode);
        queryIntegrationDetailResult.setDestSystemNode(destSystemNode);
        //数据范围
        Boolean condition = false, detailCondition = false, queryCrmCondition = false;
        QueryIntegrationDetailResult.SyncConditionsNode syncConditionsNode = new QueryIntegrationDetailResult.SyncConditionsNode();
        String erpDcId = isCRM2ERP ? syncPloyDetailEntity.getDestDataCenterId() : syncPloyDetailEntity.getSourceDataCenterId();
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getSyncConditions()) && CollectionUtils.isNotEmpty(syncPloyDetailEntity.getSyncConditions().getFilters())) {
            //替换字段label的值
            List<String> apiNames = Lists.newArrayList();
            apiNames.add(syncPloyDetailEntity.getSyncConditions().getApiName());
            Map<String, Map<String, ObjectFieldResult>> apiNameMap = queryByApiName(tenantId, erpDcId, apiNames, isCRM2ERP, lang);
            com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData syncConditionsData = BeanUtil2.deepCopy(syncPloyDetailEntity.getSyncConditions(), com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData.class);
            if (CollectionUtils.isNotEmpty(syncConditionsData.getFilters())) {
                condition = true;
                for (List<FilterData> filter : syncConditionsData.getFilters()) {
                    for (FilterData filterData : filter) {
                        ObjectFieldResult objectFieldResult = Optional.ofNullable(apiNameMap.get(syncConditionsData.getApiName()).get(filterData.getFieldApiName())).orElse(new ObjectFieldResult());
                        filterData.setLabel(objectFieldResult.getLabel());
                    }
                }
            }
            syncConditionsNode.setSyncConditions(syncConditionsData);
        }
        //校验从对象范围
        if (CollectionUtils.isNotEmpty(syncPloyDetailEntity.getDetailObjectSyncConditions())) {
            List<String> apiNames = Lists.newArrayList();
            apiNames.addAll(syncPloyDetailEntity.getDetailObjectSyncConditions().stream().map(SyncConditionsData::getApiName).collect(Collectors.toList()));
            Map<String, Map<String, ObjectFieldResult>> apiNameMap = queryByApiName(tenantId, erpDcId, apiNames, isCRM2ERP, lang);
            List<com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData> details = Lists.newArrayList();
            for (SyncConditionsData detailObjectSyncCondition : syncPloyDetailEntity.getDetailObjectSyncConditions()) {
                com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData syncConditionsData = new com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData();
                syncConditionsData.setApiName(detailObjectSyncCondition.getApiName());
                syncConditionsData.setIsSyncForce(detailObjectSyncCondition.getIsSyncForce());
                if (CollectionUtils.isEmpty(detailObjectSyncCondition.getFilters()))
                    continue;
                for (List<FilterData> filter : detailObjectSyncCondition.getFilters()) {
                    for (FilterData filterData : filter) {
                        ObjectFieldResult objectFieldResult = Optional.ofNullable(apiNameMap.get(detailObjectSyncCondition.getApiName()).get(filterData.getFieldApiName())).orElse(new ObjectFieldResult());
                        filterData.setLabel(objectFieldResult.getLabel());
                    }
                }
                detailCondition = true;
                syncConditionsData.setFilters(detailObjectSyncCondition.getFilters());
                details.add(syncConditionsData);
            }
            syncConditionsNode.setDetailObjectSyncConditions(details);
        }

        //字段映射
        QueryIntegrationDetailResult.FieldMappingNode fieldMappingNode = new QueryIntegrationDetailResult.FieldMappingNode();
        ObjectMappingResult objectMappingResult = new ObjectMappingResult();
        objectMappingResult.setSourceObjectApiName(syncPloyDetailEntity.getSourceObjectApiName());
        objectMappingResult.setDestObjectApiName(syncPloyDetailEntity.getDestObjectApiName());
        String masterSourceName = isCRM2ERP ? crmObjectApiNameToNameMap.get(syncPloyDetailEntity.getSourceObjectApiName()) : dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(syncPloyDetailEntity.getSourceObjectApiName());
        String masterDestObjectName = isCRM2ERP ? dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(syncPloyDetailEntity.getDestObjectApiName()) : crmObjectApiNameToNameMap.get(syncPloyDetailEntity.getDestObjectApiName());
        List<FieldMappingResult> fieldMappingResults = BeanUtil2.deepCopyList(syncPloyDetailEntity.getFieldMappings(), FieldMappingResult.class);
        objectMappingResult.setFieldMappings(fieldMappingResults);
        objectMappingResult.setSourceObjectName(masterSourceName);
        objectMappingResult.setDestObjectName(masterDestObjectName);
        boolean hasNotUpdateFieldApiNameList = syncPloyDetailEntity.getIntegrationStreamNodes() != null && syncPloyDetailEntity.getIntegrationStreamNodes().getObjApiName2NotUpdateFieldApiName() != null;
        if (hasNotUpdateFieldApiNameList) {
            List<String> fieldApiNames = syncPloyDetailEntity.getIntegrationStreamNodes().getObjApiName2NotUpdateFieldApiName().get(syncPloyDetailEntity.getDestObjectApiName());
            objectMappingResult.setNotUpdateFieldApiNameList(fieldApiNames);
        }
        objectMappingResult.fillNotCheckMappingFieldApiNameList();
        fieldMappingNode.setFieldMappings(objectMappingResult);

        List<ObjectMappingResult> detailMappings = BeanUtil2.copyList(syncPloyDetailEntity.getDetailObjectMappings(), ObjectMappingResult.class);
        for (ObjectMappingResult detailMapping : detailMappings) {
            String sourceObjectName = isCRM2ERP ? crmObjectApiNameToNameMap.get(detailMapping.getSourceObjectApiName()) : dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(detailMapping.getSourceObjectApiName());
            String destObjectName = isCRM2ERP ? dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(detailMapping.getDestObjectApiName()) : crmObjectApiNameToNameMap.get(detailMapping.getDestObjectApiName());
            // BeanUtil2.copyList不是深拷贝,FieldMappings类型不对,需要再转一次
            detailMapping.setFieldMappings(BeanUtil2.deepCopyList(detailMapping.getFieldMappings(), FieldMappingResult.class));
            detailMapping.setSourceObjectName(sourceObjectName);
            detailMapping.setDestObjectName(destObjectName);
            if (hasNotUpdateFieldApiNameList) {
                List<String> fieldApiNames = syncPloyDetailEntity.getIntegrationStreamNodes().getObjApiName2NotUpdateFieldApiName().get(detailMapping.getDestObjectApiName());
                detailMapping.setNotUpdateFieldApiNameList(fieldApiNames);
            }
            detailMapping.fillNotCheckMappingFieldApiNameList();
        }
        fieldMappingNode.setDetailObjectMappings(detailMappings);
        queryIntegrationDetailResult.setFieldMappingNode(fieldMappingNode);

        //节点函数
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getBeforeFuncApiName())) {
            QueryIntegrationDetailResult.BeforeFunctionNode beforeFunctionNode = new QueryIntegrationDetailResult.BeforeFunctionNode(syncPloyDetailEntity.getBeforeFuncApiName());
            fillFunctionNode(tenantId, syncPloyDetailEntity.getBeforeFuncApiName(), beforeFunctionNode);
            queryIntegrationDetailResult.setBeforeFunctionNode(beforeFunctionNode);
        }
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getDuringFuncApiName())) {
            QueryIntegrationDetailResult.DurationFunctionApiNode durationFunctionApiNode = new QueryIntegrationDetailResult.DurationFunctionApiNode(syncPloyDetailEntity.getDuringFuncApiName());
            fillFunctionNode(tenantId, syncPloyDetailEntity.getDuringFuncApiName(), durationFunctionApiNode);
            queryIntegrationDetailResult.setDurationFunctionApiNode(durationFunctionApiNode);

        }
        if (ObjectUtils.isNotEmpty(syncPloyDetailEntity.getAfterFuncApiName())) {
            QueryIntegrationDetailResult.AfterFunctionNode afterFunctionNode = new QueryIntegrationDetailResult.AfterFunctionNode(syncPloyDetailEntity.getAfterFuncApiName());
            fillFunctionNode(tenantId, syncPloyDetailEntity.getAfterFuncApiName(), afterFunctionNode);
            queryIntegrationDetailResult.setAfterFunctionNode(afterFunctionNode);
        }
        if (syncPloyDetailEntity.getIntegrationStreamNodes() != null) {
            IntegrationStreamNodesData integrationStreamNodes = syncPloyDetailEntity.getIntegrationStreamNodes();
            //数据范围-查询crm节点
            if (integrationStreamNodes.getSyncConditionsQueryDataNode() != null) {
                IntegrationStreamNodesData.SyncConditionsQueryDataNode nodeData = integrationStreamNodes.getSyncConditionsQueryDataNode();
                QueryIntegrationDetailResult.SyncConditionsQueryDataNode syncConditionsQueryDataNode = BeanUtil.deepCopy(nodeData, QueryIntegrationDetailResult.SyncConditionsQueryDataNode.class);
                if (CollectionUtils.isNotEmpty(syncConditionsQueryDataNode.getQueryObjectMappingData())) {
                    queryCrmCondition = true;
                    for (QueryObjectMappingResult queryObjectMappingResult : syncConditionsQueryDataNode.getQueryObjectMappingData()) {
                        String destObjectName;
                        if (isCRM2ERP) {
                            destObjectName = crmObjectApiNameToNameMap.get(queryObjectMappingResult.getDestObjectApiName());
                        } else {
                            destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(queryObjectMappingResult.getDestObjectApiName());
                        }
                        queryObjectMappingResult.setDestObjectName(destObjectName);
                        String sourceObjectName = crmObjectApiNameToNameMap.get(queryObjectMappingResult.getSourceObjectApiName());
                        queryObjectMappingResult.setSourceObjectName(sourceObjectName);
                    }
                }
                if (syncConditionsNode.getSyncConditions() == null) {
                    syncConditionsNode.setSyncConditions(new com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData());
                }
                syncConditionsNode.getSyncConditions().setApiName(syncPloyDetailEntity.getSourceObjectApiName());
                syncConditionsNode.getSyncConditions().setSyncConditionsQueryDataNode(syncConditionsQueryDataNode);
            }
            //回写crm节点
            if (integrationStreamNodes.getReverseWriteNode() != null) {
                IntegrationStreamNodesData.ReverseWriteNode nodeData = integrationStreamNodes.getReverseWriteNode();
                QueryIntegrationDetailResult.ReverseWriteNode reverseWriteNode = new QueryIntegrationDetailResult.ReverseWriteNode();
                queryIntegrationDetailResult.setReverseWriteNode(reverseWriteNode);
                //主对象
                reverseWriteNode.setFieldMappings(new ObjectMappingResult());
                reverseWriteNode.getFieldMappings().setSourceObjectApiName(nodeData.getSourceObjectApiName());
                reverseWriteNode.getFieldMappings().setSourceObjectName(masterDestObjectName);
                reverseWriteNode.getFieldMappings().setDestObjectApiName(nodeData.getDestObjectApiName());
                reverseWriteNode.getFieldMappings().setDestObjectName(masterSourceName);
                List<FieldMappingResult> masterFieldMapping = BeanUtil2.deepCopyList(nodeData.getFieldMappings(), FieldMappingResult.class);
                reverseWriteNode.getFieldMappings().setFieldMappings(masterFieldMapping);
                if (CollectionUtils.isNotEmpty(nodeData.getDetailObjectMappings())) {
                    List<ObjectMappingResult> detailReverseWriteNode = BeanUtil2.copyList(nodeData.getDetailObjectMappings(), ObjectMappingResult.class);
                    reverseWriteNode.setDetailObjectMappings(detailReverseWriteNode);
                    for (ObjectMappingResult detailMapping : detailReverseWriteNode) {//回写节点源是erp,目标是crm
                        String sourceObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(detailMapping.getSourceObjectApiName());
                        String destObjectName = crmObjectApiNameToNameMap.get(detailMapping.getDestObjectApiName());
                        detailMapping.setSourceObjectName(sourceObjectName);
                        detailMapping.setDestObjectName(destObjectName);
                    }
                }
            }

            //检查中间表节点
            if (integrationStreamNodes.getCheckSyncDataMappingNode() != null) {
                IntegrationStreamNodesData.CheckSyncDataMappingNode nodeData = integrationStreamNodes.getCheckSyncDataMappingNode();
                QueryIntegrationDetailResult.CheckSyncDataMappingNode checkSyncDataMappingNode = BeanUtil.deepCopy(nodeData, QueryIntegrationDetailResult.CheckSyncDataMappingNode.class);
                if (checkSyncDataMappingNode.getQueryObjectMappingData() != null) {
                    String destObjectName;
                    if (isCRM2ERP) {
                        destObjectName = crmObjectApiNameToNameMap.get(checkSyncDataMappingNode.getQueryObjectMappingData().getDestObjectApiName());
                    } else {
                        destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(checkSyncDataMappingNode.getQueryObjectMappingData().getDestObjectApiName());
                    }
                    checkSyncDataMappingNode.getQueryObjectMappingData().setDestObjectName(destObjectName);
                    String sourceObjectName = crmObjectApiNameToNameMap.get(checkSyncDataMappingNode.getQueryObjectMappingData().getSourceObjectApiName());
                    checkSyncDataMappingNode.getQueryObjectMappingData().setSourceObjectName(sourceObjectName);
                    for (IdFieldMappingResult objectMapping : checkSyncDataMappingNode.getSource2SyncDataMapping()) {
                        if (checkSyncDataMappingNode.getQueryObjectMappingData().getSourceObjectApiName().equals(objectMapping.getSourceObjectApiName())) {
                            objectMapping.setSourceObjectName(sourceObjectName);
                        }
                        if (checkSyncDataMappingNode.getQueryObjectMappingData().getDestObjectApiName().equals(objectMapping.getSourceObjectApiName())) {
                            objectMapping.setSourceObjectName(destObjectName);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(checkSyncDataMappingNode.getDetailCheckSyncDataMappingData())) {
                        for (DetailQueryData2SyncDataMappingResult detailData : checkSyncDataMappingNode.getDetailCheckSyncDataMappingData()) {
                            String detailDestObjectName;
                            if (isCRM2ERP) {
                                detailDestObjectName = crmObjectApiNameToNameMap.get(detailData.getQueryObjectMappingData().getDestObjectApiName());
                            } else {
                                detailDestObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(detailData.getQueryObjectMappingData().getDestObjectApiName());
                            }
                            detailData.getQueryObjectMappingData().setDestObjectName(detailDestObjectName);
                            String detailSourceObjectName = crmObjectApiNameToNameMap.get(detailData.getQueryObjectMappingData().getSourceObjectApiName());
                            detailData.getQueryObjectMappingData().setSourceObjectName(detailSourceObjectName);
                            for (IdFieldMappingResult detailObjectMapping : detailData.getSource2SyncDataMapping()) {
                                if (detailData.getQueryObjectMappingData().getSourceObjectApiName().equals(detailObjectMapping.getSourceObjectApiName())) {
                                    detailObjectMapping.setSourceObjectName(detailSourceObjectName);
                                }
                                if (detailData.getQueryObjectMappingData().getDestObjectApiName().equals(detailObjectMapping.getSourceObjectApiName())) {
                                    detailObjectMapping.setSourceObjectName(detailDestObjectName);
                                }
                            }

                        }
                    }
                }
                queryIntegrationDetailResult.setCheckSyncDataMappingNode(checkSyncDataMappingNode);
            }

            //通过源数据查询crm节点
            if (integrationStreamNodes.getQueryCrmObject2DestNodeBySource() != null) {
                IntegrationStreamNodesData.QueryCrmObject2DestNode nodeData = integrationStreamNodes.getQueryCrmObject2DestNodeBySource();
                QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNode = BeanUtil.deepCopy(nodeData, QueryIntegrationDetailResult.QueryCrmObject2DestNode.class);
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getQueryObjectToDestObject())) {
                    for (QueryObjectToDestObjectResult queryObjectToDestObjectResult : queryCrmObject2DestNode.getQueryObjectToDestObject()) {
                        if (queryObjectToDestObjectResult.getQueryObjectMappingData() != null) {
                            String destObjectName;
                            if (isCRM2ERP) {
                                destObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                            } else {
                                destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                            }
                            queryObjectToDestObjectResult.getQueryObjectMappingData().setDestObjectName(destObjectName);
                            String sourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getSourceObjectApiName());
                            queryObjectToDestObjectResult.getQueryObjectMappingData().setSourceObjectName(sourceObjectName);
                        }
                        if (queryObjectToDestObjectResult.getQueryData2DestDataMapping() != null) {
                            String destObjectName;
                            if (isCRM2ERP) {
                                destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                            } else {
                                destObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                            }
                            queryObjectToDestObjectResult.getQueryData2DestDataMapping().setDestObjectName(destObjectName);
                            String sourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getSourceObjectApiName());
                            queryObjectToDestObjectResult.getQueryData2DestDataMapping().setSourceObjectName(sourceObjectName);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getDetailQueryData2DestDataMapping())) {
                    for (List<QueryObjectToDestObjectResult> detailData : queryCrmObject2DestNode.getDetailQueryData2DestDataMapping()) {
                        for (QueryObjectToDestObjectResult queryObjectToDestObjectResult : detailData) {
                            if (queryObjectToDestObjectResult.getQueryObjectMappingData() != null) {
                                String detailDestObjectName;
                                if (isCRM2ERP) {
                                    detailDestObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                                } else {
                                    detailDestObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getSourceDataCenterId()).get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                                }
                                queryObjectToDestObjectResult.getQueryObjectMappingData().setDestObjectName(detailDestObjectName);
                                String detailSourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getSourceObjectApiName());
                                queryObjectToDestObjectResult.getQueryObjectMappingData().setSourceObjectName(detailSourceObjectName);
                            }
                            if (queryObjectToDestObjectResult.getQueryData2DestDataMapping() != null) {
                                String destObjectName;
                                if (isCRM2ERP) {
                                    destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                                } else {
                                    destObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                                }
                                queryObjectToDestObjectResult.getQueryData2DestDataMapping().setDestObjectName(destObjectName);
                                String sourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getSourceObjectApiName());
                                queryObjectToDestObjectResult.getQueryData2DestDataMapping().setSourceObjectName(sourceObjectName);
                            }
                        }
                    }
                }
                queryIntegrationDetailResult.setQueryCrmObject2DestNodeBySource(queryCrmObject2DestNode);
            }

            //通过转换后数据查询crm节点
            if (integrationStreamNodes.getQueryCrmObject2DestNodeByDest() != null) {
                IntegrationStreamNodesData.QueryCrmObject2DestNode nodeData = integrationStreamNodes.getQueryCrmObject2DestNodeByDest();
                QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNode = BeanUtil.deepCopy(nodeData, QueryIntegrationDetailResult.QueryCrmObject2DestNode.class);
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getQueryObjectToDestObject())) {
                    for (QueryObjectToDestObjectResult queryObjectToDestObjectResult : queryCrmObject2DestNode.getQueryObjectToDestObject()) {
                        if (queryObjectToDestObjectResult.getQueryObjectMappingData() != null) {
                            String destObjectName;
                            if (isCRM2ERP) {
                                destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                            } else {
                                destObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                            }
                            queryObjectToDestObjectResult.getQueryObjectMappingData().setDestObjectName(destObjectName);
                            String sourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getSourceObjectApiName());
                            queryObjectToDestObjectResult.getQueryObjectMappingData().setSourceObjectName(sourceObjectName);
                        }
                        if (queryObjectToDestObjectResult.getQueryData2DestDataMapping() != null) {
                            String destObjectName;
                            if (isCRM2ERP) {
                                destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                            } else {
                                destObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                            }
                            queryObjectToDestObjectResult.getQueryData2DestDataMapping().setDestObjectName(destObjectName);
                            String sourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getSourceObjectApiName());
                            queryObjectToDestObjectResult.getQueryData2DestDataMapping().setSourceObjectName(sourceObjectName);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(queryCrmObject2DestNode.getDetailQueryData2DestDataMapping())) {
                    for (List<QueryObjectToDestObjectResult> detailData : queryCrmObject2DestNode.getDetailQueryData2DestDataMapping()) {
                        for (QueryObjectToDestObjectResult queryObjectToDestObjectResult : detailData) {
                            if (queryObjectToDestObjectResult.getQueryObjectMappingData() != null) {
                                String detailDestObjectName;
                                if (isCRM2ERP) {
                                    detailDestObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                                } else {
                                    detailDestObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getDestObjectApiName());
                                }
                                queryObjectToDestObjectResult.getQueryObjectMappingData().setDestObjectName(detailDestObjectName);
                                String detailSourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryObjectMappingData().getSourceObjectApiName());
                                queryObjectToDestObjectResult.getQueryObjectMappingData().setSourceObjectName(detailSourceObjectName);
                            }
                            if (queryObjectToDestObjectResult.getQueryData2DestDataMapping() != null) {
                                String destObjectName;
                                if (isCRM2ERP) {
                                    destObjectName = dc2objApi2Name.get(syncPloyDetailEntity.getDestDataCenterId()).get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                                } else {
                                    destObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getDestObjectApiName());
                                }
                                queryObjectToDestObjectResult.getQueryData2DestDataMapping().setDestObjectName(destObjectName);
                                String sourceObjectName = crmObjectApiNameToNameMap.get(queryObjectToDestObjectResult.getQueryData2DestDataMapping().getSourceObjectApiName());
                                queryObjectToDestObjectResult.getQueryData2DestDataMapping().setSourceObjectName(sourceObjectName);
                            }
                        }
                    }
                }
                queryIntegrationDetailResult.setQueryCrmObject2DestNodeByDest(queryCrmObject2DestNode);
            }

            //通知节点
            if (integrationStreamNodes.getNotifyComplementNode() != null) {
                IntegrationStreamNodesData.NotifyComplementNode nodeData = integrationStreamNodes.getNotifyComplementNode();
                QueryIntegrationDetailResult.NotifyComplementNode notifyComplementNodeResult = BeanUtil.copy(nodeData, QueryIntegrationDetailResult.NotifyComplementNode.class);
                queryIntegrationDetailResult.setNotifyComplementNode(notifyComplementNodeResult);
            }
            //错误数据重试节点
            if (integrationStreamNodes.getReSyncErrorDataNode() != null) {
                IntegrationStreamNodesData.ReSyncErrorDataNode nodeData = integrationStreamNodes.getReSyncErrorDataNode();
                QueryIntegrationDetailResult.ReSyncErrorDataNode reSyncErrorDataNode = BeanUtil.deepCopy(nodeData, QueryIntegrationDetailResult.ReSyncErrorDataNode.class);
                queryIntegrationDetailResult.setReSyncErrorDataNode(reSyncErrorDataNode);
            }
            //需要返回字段ApiName
            if (integrationStreamNodes.getObjApiName2NeedReturnFieldApiName() != null) {
                queryIntegrationDetailResult.getSourceSystemNode().setObjApiName2NeedReturnFieldApiName(integrationStreamNodes.getObjApiName2NeedReturnFieldApiName());
            }

        }
        //数据范围节点
        queryIntegrationDetailResult.setSyncConditionsNode(condition || detailCondition || queryCrmCondition ? syncConditionsNode : null);

        //同步规则 目前先不节点化
        SyncRulesWebData syncRules = BeanUtil2.deepCopy(syncPloyDetailEntity.getSyncRules(), SyncRulesWebData.class);
        //特殊处理，前端不兼容以前的debug方式
        String syncType = syncRules.getSyncType().equals("debug") ? SyncTypeEnum.get.name() : syncRules.getSyncType();
        syncRules.setSyncType(syncType);
        if (CollectionUtils.isEmpty(syncPloyDetailEntity.getSyncRules().getSyncTypeList())) {//兼容历史数据
            syncRules.setSyncTypeList(Lists.newArrayList(syncType));
        }
        setApiUrl2SyncRules(syncPloyDetailEntity, syncRules, isCRM2ERP, lang);
        setCrmApiUrl2SyncRules(syncPloyDetailEntity, syncRules, isCRM2ERP, lang);
        if (!isCRM2ERP) {
            //ERP往CRM方向
            String erpObjApiName = syncPloyDetailEntity.getSourceObjectApiName();
            setSyncFollowMain(tenantId, erpObjApiName, syncRules);
            //统一从erp_sync_time取出轮询频率。
            PollingIntervalApiDto apiDto = erpSyncTimeManager.getApiDto(tenantId, erpObjApiName);
            syncRules.setPollingInterval(tenantId, apiDto, configCenterConfig.getCronBeginMinute(tenantId, erpObjApiName));
        }
        queryIntegrationDetailResult.setSyncRules(syncRules);
        queryIntegrationDetailResult.setStatus(syncPloyDetailEntity.getStatus());
        queryIntegrationDetailResult.setStatusName(SyncPloyDetailStatusEnum.getNameByStatus(i18NStringManager, lang, tenantId, syncPloyDetailEntity.getStatus()));
        queryIntegrationDetailResult.setIsValid(syncPloyDetailEntity.getIsValid());
        return queryIntegrationDetailResult;
    }

    private void fillFunctionNode(final String tenantId, final String functionApiName, final QueryIntegrationDetailResult.FunctionNode beforeFunctionNode) {
        final Result<String> functionNameSpace = customFunctionService.getFunctionNameSpace(tenantId, functionApiName);
        if (functionNameSpace.isSuccess() && StringUtils.isNotEmpty(functionNameSpace.getData())) {
            beforeFunctionNode.setNameSpace(functionNameSpace.getData());
        }
    }

    /**
     * 检查填充是否跟随主对象同步的集成流
     *
     * @param tenantId
     * @param objApiName
     */
    private void setSyncFollowMain(String tenantId, String objApiName, SyncRulesWebData syncRulesWebData) {
        ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, objApiName);
        if (relation.getSplitType().equals(ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL)) {
            String erpObjName = erpObjManager.getErpObjName(tenantId, relation.getDataCenterId(), relation.getErpRealObjectApiname());
            syncRulesWebData.syncFollowMain(erpObjName);
        }
    }

    private void setApiUrl2SyncRules(SyncPloyDetailEntity syncPloyDetailEntity, SyncRulesWebData syncRules, Boolean isCRM2ERP, String lang) {
        syncRules.setApiMsgList(Lists.newArrayList());
        String tenantId = syncPloyDetailEntity.getTenantId();
        String dataCenterId;
        String erpSplitObjApiName;
        List<ErpObjInterfaceUrlEnum> erpUrl;

        if (isCRM2ERP) {
            dataCenterId = syncPloyDetailEntity.getDestDataCenterId();
            erpSplitObjApiName = syncPloyDetailEntity.getDestObjectApiName();
            erpUrl = destErpUrl;
        } else {
            dataCenterId = syncPloyDetailEntity.getSourceDataCenterId();
            erpSplitObjApiName = syncPloyDetailEntity.getSourceObjectApiName();
            erpUrl = sourceErpUrl;
        }

        ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, erpSplitObjApiName);
        String realObjApiName = relationshipEntity.getErpRealObjectApiname();
        Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy = aplConfigManager.queryUrl2Apl(tenantId, dataCenterId, realObjApiName);
        ConnectInfoResult connectInfo = connectInfoService.getConnectInfoByDataCenterId(tenantId, -10000, dataCenterId).safeData();

        for (ErpObjInterfaceUrlEnum url : erpUrl) {
            ApiMessageData apiMessageData = new ApiMessageData();
            apiMessageData.setInterfaceSimpleUrl(url);
            apiMessageData.setInterfaceName(i18NStringManager.get(url.getI18nKey(), lang, tenantId, url.getNameDesc()));
            ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(tenantId,
                    dataCenterId,
                    realObjApiName,
                    url);
            if (checkedEntity != null) {
                if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_FUNC) {
                    if (url2Groovy.containsKey(url)) {//自定义函数
                        apiMessageData.setFuncApiName(url2Groovy.get(url).getFuncApiName());
                    }
                } else if (checkedEntity.getCheckedInterfaceType() == ErpObjInterfaceTypeEnum.CUSTOM_API) {
                    ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.findData(tenantId, dataCenterId, realObjApiName, url);
                    if (customInterfaceEntity != null) {
                        apiMessageData.setInterfaceDetailUrl(customInterfaceEntity.getUrl());
                    }
                } else {
                    //详细路径
                    apiMessageData.setInterfaceDetailUrl(ChannelUrlUtils.getApiUrl(tenantId, dataCenterId, url, connectInfo, realObjApiName, k3UltimateApiTemplateManager));
                    // 部分接口一次会调用多个url,比如K3新增(新增,提交,审核)等
                    apiMessageData.setInterfaceDetailUrls(getDetailUrls(tenantId, dataCenterId, url, connectInfo, realObjApiName));
                }

            } else {
                if (url2Groovy.containsKey(url)) {//自定义函数
                    apiMessageData.setFuncApiName(url2Groovy.get(url).getFuncApiName());
                } else {
                    //详细路径
                    apiMessageData.setInterfaceDetailUrl(ChannelUrlUtils.getApiUrl(tenantId, dataCenterId, url, connectInfo, realObjApiName, k3UltimateApiTemplateManager));
                    // 部分接口一次会调用多个url,比如K3新增(新增,提交,审核)等
                    apiMessageData.setInterfaceDetailUrls(getDetailUrls(tenantId, dataCenterId, url, connectInfo, realObjApiName));
                }
            }
            syncRules.getApiMsgList().add(apiMessageData);
        }

        if (!isCRM2ERP) {
            //推送
            ErpObjInterfaceUrlEnum url = ErpObjInterfaceUrlEnum.push;
            ApiMessageData apiMessageData = new ApiMessageData();
            apiMessageData.setInterfaceSimpleUrl(url);
            apiMessageData.setInterfaceName(i18NStringManager.get(url.getI18nKey(), lang, tenantId, url.getNameDesc()));
            if (url2Groovy.containsKey(url)) {//自定义函数
                apiMessageData.setFuncApiName(url2Groovy.get(url).getFuncApiName());
            }
            apiMessageData.setInterfaceDetailUrl(String.format("%s/erp/syncdata/open/objdata/push", ConfigCenter.ERP_OPEN_DOMAIN_URL));
            //事件订阅配置，直接增加webhook的参数在下面
            apiMessageData.setWebhookUrl(pushIdentifyManager.buildWebhookUrl(tenantId, dataCenterId));
            apiMessageData.setDatacenterFuncApiName(Opt.ofNullable(url2Groovy.get(ErpObjInterfaceUrlEnum.webhook))
                    .map(u -> u.getFuncApiName()).get());
            syncRules.getApiMsgList().add(apiMessageData);

//            if (connectInfo.getChannel() == ErpChannelEnum.ERP_K3CLOUD_ULTIMATE) {
//                //事件订阅
//                ErpObjInterfaceUrlEnum interfaceUrlEnum = ErpObjInterfaceUrlEnum.subscribeEvent;
//                ApiMessageData apiMessageData2 = new ApiMessageData();
//                apiMessageData2.setInterfaceSimpleUrl(interfaceUrlEnum);
//                apiMessageData2.setInterfaceName(i18NStringManager.get(interfaceUrlEnum.getI18nKey(), lang, tenantId, interfaceUrlEnum.getNameDesc()));
//                if (url2Groovy.containsKey(url)) {//自定义函数
//                    apiMessageData2.setFuncApiName(url2Groovy.get(url).getFuncApiName());
//                }
//                ErpK3UltimateTokenEntity entity = erpK3UltimateTokenDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
//                        .findData(tenantId, dataCenterId, realObjApiName, "1.0");
//                if (entity != null) {
//                    apiMessageData2.setToken(entity.getToken());
//                }
//
//                apiMessageData2.setInterfaceDetailUrl(ChannelUrlUtils.getApiUrl(tenantId,
//                        dataCenterId,
//                        ErpObjInterfaceUrlEnum.subscribeEvent,
//                        connectInfo,
//                        realObjApiName,
//                        k3UltimateApiTemplateManager));
//
//                List<K3UltimateEventConfigModel> supportSubscribeEventList = Lists.newArrayList(
//                        new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.SAVE, "",
//                                K3UltimateEventTypeEnum.SAVE.getName(i18NStringManager, lang, tenantId)),
//                        new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.SUBMIT, "",
//                                K3UltimateEventTypeEnum.SUBMIT.getName(i18NStringManager, lang, tenantId)),
//                        new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.UN_SUBMIT, "",
//                                K3UltimateEventTypeEnum.UN_SUBMIT.getName(i18NStringManager, lang, tenantId)),
//                        new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.AUDIT, "",
//                                K3UltimateEventTypeEnum.AUDIT.getName(i18NStringManager, lang, tenantId)),
//                        new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.UN_AUDIT, "",
//                                K3UltimateEventTypeEnum.UN_AUDIT.getName(i18NStringManager, lang, tenantId))
//                );
//                List<K3UltimateEventConfigModel> eventConfigModelList = null;
//                if (entity == null || StringUtils.isEmpty(entity.getEventConfig())) {
//                    eventConfigModelList = new ArrayList<>();
//                } else {
//                    eventConfigModelList = JSONObject.parseArray(entity.getEventConfig(), K3UltimateEventConfigModel.class);
//                }
//
//                if (CollectionUtils.isNotEmpty(eventConfigModelList)) {
//                    for (K3UltimateEventConfigModel model : supportSubscribeEventList) {
//                        for (K3UltimateEventConfigModel item : eventConfigModelList) {
//                            if (item.getEventType() == model.getEventType()) {
//                                model.setEventCode(item.getEventCode());
//                                break;
//                            }
//                        }
//                    }
//                }
//                apiMessageData2.setEventConfigList(supportSubscribeEventList);
//
//                syncRules.getApiMsgList().add(apiMessageData2);
//            }
        }
    }

    private void setCrmApiUrl2SyncRules(SyncPloyDetailEntity syncPloyDetailEntity, SyncRulesWebData syncRules, Boolean isCRM2ERP, String lang) {
        syncRules.setCrmApiMsgList(Lists.newArrayList());
        String tenantId = syncPloyDetailEntity.getTenantId();
        String objApiName = "";
        List<ErpObjInterfaceUrlEnum> crmUrl;

        if (!isCRM2ERP) {//目标是crm的
            objApiName = syncPloyDetailEntity.getDestObjectApiName();
            crmUrl = destErpUrl;
            for (ErpObjInterfaceUrlEnum url : crmUrl) {
                if (ErpObjInterfaceUrlEnum.recover.equals(url)) {//暂不支持
                    continue;
                }
                ApiMessageData apiMessageData = new ApiMessageData();
                apiMessageData.setInterfaceSimpleUrl(url);
                apiMessageData.setInterfaceName(ErpObjInterfaceUrlEnum.getNameDesc(url, i18NStringManager, lang, tenantId));
                String interfaceUrl = doWrite2CrmManager.getCrmObjectUrl(tenantId, objApiName, objApiName, url);
                apiMessageData.setInterfaceDetailUrl(interfaceUrl);
                syncRules.getCrmApiMsgList().add(apiMessageData);
            }
        }
    }

    private List<String> getDetailUrls(final String tenantId, final String dataCenterId, final ErpObjInterfaceUrlEnum url, final ConnectInfoResult connectInfo, final String realObjApiName) {
        final ArrayList<String> result = Lists.newArrayList(ChannelUrlUtils.getApiUrl(tenantId, dataCenterId, url, connectInfo, realObjApiName, k3UltimateApiTemplateManager));
        if (!ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel()) ||
                !(ErpObjInterfaceUrlEnum.create.equals(url) || ErpObjInterfaceUrlEnum.update.equals(url))) {
            // 暂时只有k3c的新增/修改接口会调用多个url
            return result;
        }

        // 查看新增状态和编辑状态
        final IdSaveExtend k3ObjectSaveExtend = erpObjectFieldsService.getK3ObjectSaveExtend(tenantId, connectInfo.getId(), realObjApiName);
        if (Objects.isNull(k3ObjectSaveExtend)) {
            return result;
        }

        // 获取新增状态和编辑状态的所有接口
        final String addStatus = k3ObjectSaveExtend.getAddStatus();
        final List<ErpObjInterfaceUrlEnum> erpObjInterfaceUrls = ErpObjInterfaceUrlEnum.create.equals(url) ?
                K3DocumentStatusEnum.getErpObjInterfaceUrls(K3DocumentStatusEnum.ADD.getStatus(), addStatus) :
                K3DocumentStatusEnum.getErpObjInterfaceUrls(addStatus, k3ObjectSaveExtend.getModifyStatus());

        erpObjInterfaceUrls.stream()
                .map(addUrl -> ChannelUrlUtils.getApiUrl(tenantId, dataCenterId, addUrl, connectInfo, realObjApiName, k3UltimateApiTemplateManager))
                .forEach(result::add);

        return result;
    }

    private Map<String, Map<String, ObjectFieldResult>> queryByApiName(String tenantId,
                                                                       String dcId,
                                                                       List<String> apiNames,
                                                                       boolean isCrm2erp,
                                                                       String lang) {
        Map<String, Map<String, ObjectFieldResult>> maps = Maps.newHashMap();
        for (String apiName : apiNames) {
            if (isCrm2erp) {//crm
                Result<ListObjectFieldsResult> crmResult = fsCrmObjectService.listObjectFieldsWithFilterBlackList(tenantId, apiName, lang);
                Map<String, ObjectFieldResult> crmResultMap = crmResult.getData().getFields().stream().collect(Collectors.toMap(ObjectFieldResult::getApiName, Function.identity(), (key1, key2) -> key2));
                maps.put(apiName, crmResultMap);
            } else {//erp
                Result<ErpObjectDescResult> erpObjectDescResult = erpObjectService.queryErpObjectByObjApiName(tenantId, dcId, -10000, apiName);
                if (!erpObjectDescResult.isSuccess() || Objects.isNull(erpObjectDescResult.getData())) {
                    continue;
                }
                ErpObjectApiNameArg queryArg = new ErpObjectApiNameArg();
                queryArg.setErpObjectApiName(apiName);
                Result<List<ErpObjectFieldResult>> listResult = erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(tenantId, -10000, queryArg, dcId);
                ListObjectFieldsResult listObjectFieldsResult = new ListObjectFieldsResult();
                if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    listObjectFieldsResult = ResultConversionUtil.converseErpObjFelds(tenantId, listResult.getData());
                    listObjectFieldsResult.setObjectName(erpObjectDescResult.getData().getErpObjectName());
                    listObjectFieldsResult.setObjectApiName(erpObjectDescResult.getData().getErpObjectApiName());
                    Map<String, ObjectFieldResult> crmResultMap = listObjectFieldsResult.getFields().stream().collect(Collectors.toMap(ObjectFieldResult::getApiName, Function.identity(), (key1, key2) -> key2));
                    maps.put(apiName, crmResultMap);
                }
            }

        }
        return maps;
    }

    @Override
    public Result<String> brush(String tenantId, boolean brushAll, String lang) {
        if (StringUtils.isNotBlank(tenantId)) {
            return brushTenant(tenantId, lang);
        }
        if (brushAll) {
            StringBuffer errorMsg = new StringBuffer();
            List<String> tenantIds = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId();
            for (String ei : tenantIds) {
                try {
                    Result<String> result = brushTenant(ei, lang);
                    if (!result.isSuccess()) {
                        errorMsg.append(i18NStringManager.get(I18NStringEnum.s622, lang, tenantId)).append(ei).append(result.getErrMsg());
                    }
                } catch (Exception e) {
                    errorMsg.append(i18NStringManager.get(I18NStringEnum.s622, lang, tenantId)).append(ei).append(i18NStringManager.get(I18NStringEnum.s764, lang, tenantId)).append(e.getMessage());
                }
            }
            if (StringUtils.isNotBlank(errorMsg.toString())) {
                return Result.newError(errorMsg.toString());
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<SyncObjectResult>> getIntegrationStreamCrmObjList(String tenantId, String lang) {
        Result<List<SyncObjectResult>> listResult = adminSyncPloyDetailService.listMasterSyncObjects(tenantId, lang);
        if (!listResult.isSuccess()) {
            return listResult;
        }
        Set<String> hasIntegrationStreamCrmObj = Sets.newHashSet();
        List<SyncObjectResult> crmObj = listResult.getData();
        Map<String, SyncObjectResult> apiName2Obj = crmObj.stream().collect(Collectors.toMap(SyncObjectResult::getApiName, v -> v));
        List<SyncPloyDetailEntity> ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByTenantId(tenantId);
        for (SyncPloyDetailEntity entity : ployDetailEntities) {
            if (TenantType.CRM == entity.getSourceTenantType()) {
                if (apiName2Obj.containsKey(entity.getSourceObjectApiName())) {
                    hasIntegrationStreamCrmObj.add(entity.getSourceObjectApiName());
                }
            } else {
                if (apiName2Obj.containsKey(entity.getDestObjectApiName())) {
                    hasIntegrationStreamCrmObj.add(entity.getDestObjectApiName());
                }
            }
        }
        List<SyncObjectResult> crmObjList = Lists.newArrayList();
        for (String objApiName : hasIntegrationStreamCrmObj) {
            crmObjList.add(apiName2Obj.get(objApiName));
        }
        return Result.newSuccess(crmObjList);
    }

    @Override
    public Result<List<SyncObjectResult>> getIntegrationStreamErpObjList(String tenantId, final String dcId) {

        List<SyncPloyDetailEntity> ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByDcId(tenantId, dcId, null);
        final List<SyncObjectResult> results = ployDetailEntities.stream()
                .map(entity -> Objects.equals(TenantType.CRM, entity.getSourceTenantType()) ?
                        entity.getDestObjectApiName() : entity.getSourceObjectApiName())
                .distinct()
                .map(apiName -> SyncObjectResult.newInstance(apiName, erpObjManager.getErpObjName(tenantId, apiName)))
                .filter(syncObjectResult -> StringUtils.isNotBlank(syncObjectResult.getName()))
                .collect(Collectors.toList());

        return Result.newSuccess(results);
    }

    @Override
    public Result<Integer> updateIntegrationStreamName(String tenantId, UpdateIntegrationStreamArg.UpdateStreamNameArg arg) {
        if (ObjectUtils.isEmpty(arg) || ObjectUtils.isEmpty(arg.getIntegrationStreamName())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        Integer count = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .updateNameById(arg.getId(), tenantId, arg.getIntegrationStreamName());

        return Result.newSuccess(count);
    }

    @Override
    public Result<UpdateIntegrationStreamResult> allUpdateIntegrationStream(String tenantId,
                                                                            UpdateIntegrationStreamArg updateIntegrationStreamArg,
                                                                            String lang) {
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, updateIntegrationStreamArg.getId());
        if (ObjectUtils.isEmpty(syncPloyDetailEntity)) {
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }
        String erpDcId = syncPloyDetailEntity.getSourceTenantType().equals(TenantType.CRM) ? syncPloyDetailEntity.getDestDataCenterId() : syncPloyDetailEntity.getSourceDataCenterId();

        UpdateIntegrationStreamArg.UpdateStreamNameArg updateStreamNameArg =
                new UpdateIntegrationStreamArg.UpdateStreamNameArg(updateIntegrationStreamArg.getId(), updateIntegrationStreamArg.getIntegrationStreamName());
        this.updateIntegrationStreamName(tenantId, updateStreamNameArg);
        QueryIntegrationDetailResult.NotPassDataSourceStatus notPassDataSourceStatus = updateIntegrationStreamArg.getSourceSystemNode().getNotPassDataSourceStatus();
        if(TenantType.CRM.equals(syncPloyDetailEntity.getSourceTenantType())&&notPassDataSourceStatus!=null){//更新配置
            PaasDataSourceConfigStatus arg=new PaasDataSourceConfigStatus();
            arg.setCrmObjectApiName(syncPloyDetailEntity.getSourceObjectApiName());
            arg.setStatus(notPassDataSourceStatus.getStatus());
            arg.setDataSourceList(notPassDataSourceStatus.getDataSourceList());
            this.updateTenantNeedPassDataSourceConfig(tenantId,arg);
        }
        Result<UpdateIntegrationStreamResult> updateIntegrationStreamResultResult = integrationStreamManager.allUpdateIntegrationStream(tenantId, updateIntegrationStreamArg, erpDcId, lang);
        return updateIntegrationStreamResultResult;
    }

    @Override
    public Result<Map<String, UpdateIntegrationStreamResult.ErrorMsg>> commonVerifyMessage(String tenantId, String dataId, String lang) {
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, dataId);
        if (ObjectUtils.isEmpty(syncPloyDetailEntity)) {
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }
        Map<String, UpdateIntegrationStreamResult.ErrorMsg> errorMsgMap = integrationStreamManager.commonVerifyStreamDetail(tenantId, syncPloyDetailEntity, lang);
        return Result.newSuccess(errorMsgMap);
    }

    @Override
    public Result<List<IntegrationViewResult.InvalidInfoResult>> queryStreamStatusAndLastSyncTime(String tenantId, List<String> streamIds, String lang) {
        List<IntegrationViewResult.InvalidInfoResult> invalidInfoResults = Lists.newArrayList();
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantIdAndId(tenantId, streamIds);

        //获取最后更新时间
        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
            IntegrationViewResult.InvalidInfoResult invalidInfoResult = new IntegrationViewResult.InvalidInfoResult();
            List<SyncDataMappingsEntity> lastSyncData = syncDataMappingsDao.getLastSyncData(tenantId, syncPloyDetailEntity.getSourceObjectApiName(), syncPloyDetailEntity.getDestObjectApiName(), 0, 1);
            Long updateTime = CollectionUtils.isNotEmpty(lastSyncData) ? lastSyncData.get(0).getUpdateTime() : null;
            //invalid现在在页面没有真正起到作用，先去掉。后面单独考虑这类校验。
            //Map<String, UpdateIntegrationStreamResult.ErrorMsg> errorMsgMap = integrationStreamManager.commonVerifyStreamDetail(tenantId, syncPloyDetailEntity, lang);
            //boolean invalid = hasFailMessage(errorMsgMap);
            invalidInfoResult.setInvalid(false);
            invalidInfoResult.setLastSyncTime(updateTime);
            invalidInfoResult.setId(syncPloyDetailEntity.getId());
            invalidInfoResults.add(invalidInfoResult);
        }
        return Result.newSuccess(invalidInfoResults);
    }

    @Override
    public Result<List<IntegrationViewResult.SyncFailResult>> querySyncFailCount(String tenantId, List<String> streamIds) {
        List<IntegrationViewResult.SyncFailResult> invalidInfoResults = Lists.newArrayList();
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantIdAndId(tenantId, streamIds);

        Map<String, String> objectAndTenantsToPloyDetailIdMap = new HashMap<>();
        List<SyncObjectAndTenantMappingData> objectApiNameMappings = new ArrayList<>();
        //获取失败数量与最后更新时间
        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
            SyncObjectAndTenantMappingData syncObjectAndTenantMappingData = new SyncObjectAndTenantMappingData();
            syncObjectAndTenantMappingData.setSourceTenantId(syncPloyDetailEntity.getTenantId());
            syncObjectAndTenantMappingData.setSourceObjectApiName(syncPloyDetailEntity.getSourceObjectApiName());
            syncObjectAndTenantMappingData.setDestTenantId(syncPloyDetailEntity.getTenantId());
            syncObjectAndTenantMappingData.setDestObjectApiName(syncPloyDetailEntity.getDestObjectApiName());
            objectApiNameMappings.add(syncObjectAndTenantMappingData);
            objectAndTenantsToPloyDetailIdMap.put(syncObjectAndTenantMappingData.toString(), syncPloyDetailEntity.getId());
        }
        List<SyncDataMappingsFailedCountData> failedSyncDataMappings = syncDataMappingsDao.setTenantId(tenantId).countSyncFailed(tenantId, objectApiNameMappings);
        Function<SyncDataMappingsFailedCountData, String> keyMapper = SyncDataMappingsFailedCountData -> SyncDataMappingsFailedCountData.getSourceObjectApiName() + "-" + SyncDataMappingsFailedCountData.getDestObjectApiName();

        Map<String, SyncDataMappingsFailedCountData> failedSyncDataMappingsMap = failedSyncDataMappings.stream().collect(Collectors.toMap(keyMapper, v -> v, (u, v) -> u));

        for (String key : objectAndTenantsToPloyDetailIdMap.keySet()) {
            IntegrationViewResult.SyncFailResult invalidInfoResult = new IntegrationViewResult.SyncFailResult();
            String streamId = objectAndTenantsToPloyDetailIdMap.get(key);
            invalidInfoResult.setId(streamId);
            invalidInfoResult.setHasTimeOutResult(false);
            SyncDataMappingsFailedCountData syncDataMappingsFailedCountData = failedSyncDataMappingsMap.get(key);
            Integer failCount = 0;
            if (ObjectUtils.isNotEmpty(syncDataMappingsFailedCountData)) {
                failCount = syncDataMappingsFailedCountData.getFailedCount();
                invalidInfoResult.setSyncFailedDataCount(syncDataMappingsFailedCountData.getFailedCount());
            }
            invalidInfoResults.add(invalidInfoResult);
            String recordKey = String.format(RECORD_FAIL_COUNT_STREAM_TENANT, tenantId, streamId);
            //缓存异常的数据量
            redisDataSource.get(this.getClass().getSimpleName()).setex(recordKey, EXPIRE_RECORD, String.valueOf(failCount));
        }
        return Result.newSuccess(invalidInfoResults);
    }

    @Override
    public Result<List<IntegrationViewResult.SyncFailResult>> querySyncFailCountByCache(String tenantId, List<String> streamIds) {
        List<IntegrationViewResult.SyncFailResult> syncFailResults = Lists.newArrayList();
        for (String streamId : streamIds) {
            String recordKey = String.format(RECORD_FAIL_COUNT_STREAM_TENANT, tenantId, streamId);
            Integer recordValue = null;
            if (ObjectUtils.isNotEmpty(redisDataSource.get(this.getClass().getSimpleName()).get(recordKey))) {
                recordValue = Integer.valueOf(redisDataSource.get(this.getClass().getSimpleName()).get(recordKey));
            }
            IntegrationViewResult.SyncFailResult countFailResult = new IntegrationViewResult.SyncFailResult(streamId, recordValue, true);
            syncFailResults.add(countFailResult);
        }
        return Result.newSuccess(syncFailResults);
    }

    @Override
    public Result<List<UserOperatorLogResult>> queryStreamOpeartorLog(String tenantId, String dcId, String id, int offset, int limit, String lang) {

        List<UserOperatorLog> userOperatorLogs =
                UserOperatorLogManager.queryUserOperatorLogs(tenantId, dcId, UserOperatorModuleEnum.INTEGRATION_STREAM.name(), id, offset, limit);
        List<Integer> userIds = userOperatorLogs.stream().filter(t -> !CrmConstants.SYSTEM_USER.equals(t.getUserId())).map(UserOperatorLog::getUserId).distinct().collect(Collectors.toList());
        Result<List<EmployeeDto>> userResult = fsObjectDataService.queryEmployeeByFsUserId(Integer.valueOf(tenantId), userIds);
        if (!userResult.isSuccess()) {
            return Result.newError(userResult.getErrMsg());
        }
        Map<Integer, List<EmployeeDto>> userIdMap =
                userResult.getData().stream().collect(Collectors.groupingBy(v -> v.getEmployeeId()));
        List<UserOperatorLogResult> datas = new ArrayList<>();
        for (UserOperatorLog userOperatorLog : userOperatorLogs) {
            UserOperatorLogResult userOperatorLogResult = new UserOperatorLogResult();
            userOperatorLogResult.setMessage(userOperatorLog.getMessage());
            userOperatorLogResult.setAction(userOperatorLog.getAction());
            userOperatorLogResult.setModuleId(userOperatorLog.getModuleId());
            userOperatorLogResult.setModuleName(UserOperatorModuleEnum.valueOf(userOperatorLog.getModule()).getDescription(i18NStringManager, lang, tenantId));
            userOperatorLogResult.setUserName(userOperatorLog.getUserId().toString());
            if (CrmConstants.SYSTEM_USER.equals(userOperatorLog.getUserId())) {
                userOperatorLogResult.setUserName(i18NStringManager.get(I18NStringEnum.s765, lang, tenantId));
            } else if (userIdMap.containsKey(userOperatorLog.getUserId())) {
                userOperatorLogResult.setUserName(userIdMap.get(userOperatorLog.getUserId()).get(0).getName());
            }
            userOperatorLogResult.setSnapshotData(userOperatorLog.getSnapshotData());
            userOperatorLogResult.setOperatorTime(userOperatorLog.getOperatorTime());
            datas.add(userOperatorLogResult);
        }
        return Result.newSuccess(datas);
    }

    @Override
    public Result<Boolean> updateGetByIdStatus(String tenantId, String dcId, GetByIdApiStatusArg arg) {
        ErpTenantConfigurationEntity result = tenantConfigurationManager.findOne(tenantId, dcId, null, TenantConfigurationTypeEnum.NOT_OFFER_GETBYID_INTERFACE_TENANTS.name());
        ErpObjectRelationshipEntity split = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, arg.getSplitObjectApiName());
        String realObjApiName = split.getErpRealObjectApiname();
        if (null == result) {//插入
            ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
            insertConfig(tenantId, dcId, realObjApiName, entity);
            result = entity;
        }
        Set<String> tenantSet = Sets.newHashSet();
        if (StringUtils.isNotBlank(result.getConfiguration())) {
            tenantSet = Sets.newHashSet(Splitter.on(";").split(result.getConfiguration()));
        }
        if (arg.getStatus()) {//开启
            if (tenantSet.contains(realObjApiName)) {//去掉
                tenantSet.remove(realObjApiName);
                result.setConfiguration(Joiner.on(";").join(tenantSet));
                tenantConfigurationManager.updateById(tenantId, result);
            }
        } else {//关闭
            if (!tenantSet.contains(realObjApiName)) {//加上
                tenantSet.add(realObjApiName);
                result.setConfiguration(Joiner.on(";").join(tenantSet));
                tenantConfigurationManager.updateById(tenantId, result);
            }
        }
        return Result.newSuccess(arg.getStatus());
    }

    @Override
    public Result<Boolean> updateMappingStatus(String tenantId, String dcId, IdArg arg) {
        syncDataMappingsDao.setTenantId(tenantId).updateLastStatusById(tenantId, arg.getId(), SyncDataStatusEnum.BE_PROCESS.getStatus());
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> updateSyncDataStatus(String tenantId, String dcId, IdArg arg, String lang) {
        syncDataFixDao.updateWaitingStatus(tenantId, arg.getId(), SyncDataStatusEnum.BE_PROCESS.getStatus(),
                i18NStringManager.get(I18NStringEnum.s689, lang, tenantId));
        return Result.newSuccess();
    }

    @Override
    public Result<GetByIdInterfaceStatus> queryGetByIdStatus(String tenantId, String dcId, GetByIdApiStatusArg arg) {
        ErpTenantConfigurationEntity result = tenantConfigurationManager.findOne(tenantId, dcId, null, TenantConfigurationTypeEnum.NOT_OFFER_GETBYID_INTERFACE_TENANTS.name());
        ErpObjectRelationshipEntity split = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, arg.getSplitObjectApiName());
        String realObjApiName = split.getErpRealObjectApiname();
        if (null == result) {//插入
            ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
            insertConfig(tenantId, dcId, null, entity);
            result = entity;
        }
        Set<String> tenantSet = Sets.newHashSet(Splitter.on(";").split(result.getConfiguration()));
        GetByIdInterfaceStatus config = new GetByIdInterfaceStatus();
        config.setGetByIdStatus(!tenantSet.contains(realObjApiName));//不包含就是开启的
        boolean isBreak = getByIdBreakManager.isBreak(tenantId,dcId,split.getChannel(),realObjApiName);
        config.setIsBreak(isBreak);
        return Result.newSuccess(config);
    }

    private void insertConfig(String tenantId, String dcId, String realObjApiName, ErpTenantConfigurationEntity entity) {
        ErpConnectInfoEntity byIdAndTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dcId);
        entity.setId(idGenerator.get());
        entity.setTenantId(tenantId);
        entity.setDataCenterId(dcId);
        entity.setChannel(byIdAndTenantId.getChannel().name());
        entity.setType(TenantConfigurationTypeEnum.NOT_OFFER_GETBYID_INTERFACE_TENANTS.name());
        Set<String> tenantSet = Sets.newHashSet();
        if (StringUtils.isNotBlank(realObjApiName)) {
            tenantSet.add(realObjApiName);
        }
        entity.setConfiguration(Joiner.on(";").join(tenantSet));
        tenantConfigurationManager.insert(tenantId, entity);
    }

    private boolean hasFailMessage(Map<String, UpdateIntegrationStreamResult.ErrorMsg> errorMsgMap) {
        boolean hasFail = false;
        if (ObjectUtils.isNotEmpty(errorMsgMap.get("sourceSystemNode"))) {
            if (errorMsgMap.get("sourceSystemNode").getInvalid()) {
                return hasFail = true;
            }
        }
        if (ObjectUtils.isNotEmpty(errorMsgMap.get("destSystemNode"))) {
            if (errorMsgMap.get("destSystemNode").getInvalid()) {
                return hasFail = true;
            }
        }
        if (ObjectUtils.isNotEmpty(errorMsgMap.get("syncConditionsNode"))) {
            if (errorMsgMap.get("syncConditionsNode").getInvalid()) {
                return hasFail = true;
            }
        }
        if (ObjectUtils.isNotEmpty(errorMsgMap.get("beforeFunctionNode"))) {
            if (errorMsgMap.get("beforeFunctionNode").getInvalid()) {
                return hasFail = true;
            }
        }
        if (ObjectUtils.isNotEmpty(errorMsgMap.get("durationFunctionApiNode"))) {
            if (errorMsgMap.get("durationFunctionApiNode").getInvalid()) {
                return hasFail = true;
            }
        }
        if (ObjectUtils.isNotEmpty(errorMsgMap.get("afterFunctionNode"))) {
            if (errorMsgMap.get("afterFunctionNode").getInvalid()) {
                return hasFail = true;
            }
        }
        if (ObjectUtils.isNotEmpty(errorMsgMap.get("fieldMappingNode"))) {
            if (errorMsgMap.get("fieldMappingNode").getInvalid()) {
                return hasFail = true;
            }
        }
        return hasFail;

    }

    private Result<String> brushTenant(String tenantId, String lang) {
        if (StringUtils.isNotBlank(tenantId)) {
            ErpConnectInfoEntity crmDc = getOrCreateCrmDc(tenantId, lang);
            if (crmDc == null) {
                return Result.newErrorByI18N(I18NStringEnum.s766.getI18nValue(), I18NStringEnum.s766.getI18nKey(), null);
            }
            ErpObjectEntity query = new ErpObjectEntity();
            query.setTenantId(tenantId);
            query.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
            List<ErpObjectEntity> allErpObj = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            Map<String, String> api2DcId = allErpObj.stream().collect(Collectors.toMap(ErpObjectEntity::getErpObjectApiName, ErpObjectEntity::getDataCenterId));
            List<SyncPloyDetailEntity> allStream = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listByTenantId(tenantId);
            StringBuffer errorMsg = new StringBuffer();
            for (SyncPloyDetailEntity entity : allStream) {
                String name = null;
                if (StringUtils.isBlank(entity.getIntegrationStreamName())) {
                    name = i18NStringManager.get(I18NStringEnum.s767, lang, tenantId) + entity.getSourceObjectApiName() + "->" + entity.getDestObjectApiName();
                }
                String sourceDcId, destDcId;
                if (TenantType.CRM == entity.getSourceTenantType()) {//crm->erp
                    sourceDcId = crmDc.getId();
                    destDcId = api2DcId.get(entity.getDestObjectApiName());
                } else {
                    sourceDcId = api2DcId.get(entity.getSourceObjectApiName());
                    destDcId = crmDc.getId();
                }
                if (StringUtils.isBlank(sourceDcId) || StringUtils.isBlank(destDcId)) {
                    errorMsg.append(name).append(i18NStringManager.get(I18NStringEnum.s768, lang, tenantId)).append(System.lineSeparator());
                    continue;
                }
                adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .updateNameAndDcIdById(entity.getId(), tenantId, name, sourceDcId, destDcId);
            }
            return Result.newError(errorMsg.toString());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<QueryResult<List<IntegrationViewResult>>> newQueryIntegrationStreamList(String tenantId,
                                                                                          ListIntegrationStreamArg arg,
                                                                                          boolean queryFailDataCount,
                                                                                          String lang) {
        QueryResult<List<IntegrationViewResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        int total = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .countBySourceOrDestDcIdAndObjApiName(tenantId, arg.getDcId(), arg.getCrmObjApiName(), arg.getStatus(), arg.getQueryStr());
        queryResult.setTotal(total);
        if (total == 0) {
            queryResult.setDataList(Lists.newArrayList());
            return Result.newSuccess(queryResult);
        }
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceOrDestDcIdAndObjApiName(tenantId, arg.getDcId(), arg.getCrmObjApiName(),
                        arg.getStatus(), arg.getQueryStr(), (arg.getPageNum() - 1) * arg.getPageSize(), arg.getPageSize());
        List<IntegrationViewResult> integrationStreamResultList = Lists.newArrayList();
        Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(tenantId, Lists.newArrayList(tenantId));
        //主要用于转换对象名字
        Map<String, Set<String>> erpDc2ObjApiNames = Maps.newHashMap();//erp,dcId->对象
        Map<String, Map<String, String>> dc2objApi2Name = Maps.newHashMap();
        Map<String, String> crmObjectApiNameToNameMap = convertObjName(tenantId, erpDc2ObjApiNames, dc2objApi2Name, syncPloyDetailEntities);
        //数据维护页查询
        queryNewDataMaintenanceList(syncPloyDetailEntities, integrationStreamResultList, crmObjectApiNameToNameMap, dc2objApi2Name, tenantId, tenantIdToNameMap, lang);
        queryResult.setDataList(integrationStreamResultList);
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<QueryResult<List<IntegrationViewResult>>> singleQueryIntegrationStreamList(String tenantId, IdArg arg, String lang) {
        QueryResult<List<IntegrationViewResult>> queryResult = new QueryResult<>();
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantIdAndId(tenantId, Lists.newArrayList(arg.getId()));
        if (CollectionUtils.isEmpty(syncPloyDetailEntities)) {
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }
        Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(tenantId, Lists.newArrayList(tenantId));
        //主要用于转换对象名字
        List<IntegrationViewResult> integrationStreamResultList = Lists.newArrayList();
        Map<String, Set<String>> erpDc2ObjApiNames = Maps.newHashMap();//erp,dcId->对象
        Map<String, Map<String, String>> dc2objApi2Name = Maps.newHashMap();
        Map<String, String> crmObjectApiNameToNameMap = convertObjName(tenantId, erpDc2ObjApiNames, dc2objApi2Name, syncPloyDetailEntities);
        //数据维护页查询
        queryNewDataMaintenanceList(syncPloyDetailEntities, integrationStreamResultList, crmObjectApiNameToNameMap, dc2objApi2Name, tenantId, tenantIdToNameMap, lang);
        queryResult.setDataList(integrationStreamResultList);
        return Result.newSuccess(queryResult);
    }

    private IntegrationViewResult changeNewPloyDetail2IntegrationStream(String tenantId,
                                                                        Map<String, String> tenantIdToNameMap,
                                                                        SyncPloyDetailEntity entity,
                                                                        Map<String, String> crmObjectApiNameToNameMap,
                                                                        Map<String, Map<String, String>> dc2objApi2Name,
                                                                        String lang) {
        if (entity.getSyncRules() != null && StringUtils.isNotBlank(entity.getSyncRules().getSyncType())
                && CollectionUtils.isEmpty(entity.getSyncRules().getSyncTypeList())) {//兼容历史数据
            entity.getSyncRules().setSyncTypeList(Lists.newArrayList(entity.getSyncRules().getSyncType()));
        }

        IntegrationViewResult integrationStreamResult = BeanUtil2.deepCopy(entity, IntegrationViewResult.class);
        integrationStreamResult.getSourceTenantDatas().add(TenantData.newDataByTenantIdAndName(entity.getTenantId(), tenantIdToNameMap.get(entity.getTenantId())));
        integrationStreamResult.setSourceDc(DataCenterData.newDataByDcId(tenantId, entity.getSourceDataCenterId(), this.connectInfoDao, i18NStringManager, lang));
        integrationStreamResult.setStatusName(SyncPloyDetailStatusEnum.getNameByStatus(i18NStringManager, lang, tenantId, entity.getStatus()));
        integrationStreamResult.getDestTenantDatas().add(TenantData.newDataByTenantIdAndName(entity.getTenantId(), tenantIdToNameMap.get(entity.getTenantId())));
        integrationStreamResult.setDestDc(DataCenterData.newDataByDcId(tenantId, entity.getDestDataCenterId(), this.connectInfoDao, i18NStringManager, lang));
        if (TenantType.CRM.equals(integrationStreamResult.getSourceTenantType())) {
            integrationStreamResult.setSourceObjectName(crmObjectApiNameToNameMap.get(entity.getSourceObjectApiName()));
            integrationStreamResult.setDestObjectName(dc2objApi2Name.get(entity.getDestDataCenterId()).get(entity.getDestObjectApiName()));
            if (integrationStreamResult.getDetailObjectMappings() != null) {
                for (IntegrationStreamResult.ObjectMappingInfo objectMappingInfo : integrationStreamResult.getDetailObjectMappings()) {
                    objectMappingInfo.setSourceObjectName(crmObjectApiNameToNameMap.get(objectMappingInfo.getSourceObjectApiName()));
                    objectMappingInfo.setDestObjectName(dc2objApi2Name.get(entity.getDestDataCenterId()).get(objectMappingInfo.getDestObjectApiName()));
                }
            }
        } else {
            setSyncFollowMain(tenantId, entity.getSourceObjectApiName(), integrationStreamResult.getSyncRules());
            integrationStreamResult.setDestObjectName(crmObjectApiNameToNameMap.get(entity.getDestObjectApiName()));
            integrationStreamResult.setSourceObjectName(dc2objApi2Name.get(entity.getSourceDataCenterId()).get(entity.getSourceObjectApiName()));
            //ERPD的真实apiname
            ErpObjectRelationshipEntity erpObjectRelationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, integrationStreamResult.getSourceObjectApiName());
            integrationStreamResult.setSourceObjectActualApiName(erpObjectRelationshipEntity.getErpRealObjectApiname());
            if (integrationStreamResult.getDetailObjectMappings() != null) {
                for (IntegrationStreamResult.ObjectMappingInfo objectMappingInfo : integrationStreamResult.getDetailObjectMappings()) {
                    objectMappingInfo.setDestObjectName(crmObjectApiNameToNameMap.get(objectMappingInfo.getDestObjectApiName()));
                    objectMappingInfo.setSourceObjectName(dc2objApi2Name.get(entity.getSourceDataCenterId()).get(objectMappingInfo.getSourceObjectApiName()));
                }
            }
        }
        //        //查询最近一次数据维护更新的时间
        //        SyncDataMappingsEntity latestMappingData = adminSyncDataMappingsDao.setTenantId(tenantId).getLatestMappingData(tenantId, integrationStreamResult.getSourceObjectApiName(), tenantId, integrationStreamResult.getDestObjectApiName());
        //        if(ObjectUtils.isNotEmpty(latestMappingData)){
        //            integrationStreamResult.setLastSyncTime(latestMappingData.getUpdateTime());
        //        }
        return integrationStreamResult;
    }

    private ErpConnectInfoEntity getOrCreateCrmDc(String tenantId, String lang) {
        Result<ErpConnectInfoEntity> crmDc = connectInfoService.getOrCreateCrmDc(tenantId, lang);
        if (crmDc.isSuccess()) {
            return crmDc.getData();
        }
        return null;
    }

    @Override
    public Result<Void> checkAndUpdateDetailObjMapping(UpdateIntegrationStreamArg.CheckAndUpdateDetailObjMappingArg arg, String lang) {
        if (arg == null
                || arg.getTenantId() == null
                || arg.getDcId() == null
                || arg.getId() == null
                || arg.getMasterObjMapping() == null
                || CollectionUtils.isEmpty(arg.getDetailObjMappingList())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        SyncPloyDetailEntity ployDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                .getById(arg.getTenantId(), arg.getId());
        //检查主对象apiName是否匹配
        if (!StringUtils.equals(ployDetailEntity.getSourceObjectApiName(), arg.getMasterObjMapping().getSourceObjectApiName())
                || !StringUtils.equals(ployDetailEntity.getDestObjectApiName(), arg.getMasterObjMapping().getDestObjectApiName())) {
            return Result.newErrorByI18N(ResultCodeEnum.PARAM_ERROR, I18NStringEnum.s769.getI18nValue(), I18NStringEnum.s769.getI18nKey(), null);
        }
        for (UpdateIntegrationStreamArg.ObjectMappingModel mappingModel : arg.getDetailObjMappingList()) {
            //如果明细对象映射被删除，检查对应的中间表数据是否被手动删除
            if (mappingModel.isDelete()) {
                log.info("IntegrationStreamServiceImpl.checkAndUpdateDetailObjMapping,mappingModel={}", mappingModel);
                //检查已同步的数据是否被手动删除
                int count = syncDataMappingsDao.setTenantId(arg.getTenantId())
                        .countIsCreatedMapping(arg.getTenantId(), mappingModel.getSourceObjectApiName(),
                                mappingModel.getDestObjectApiName(), true);
                log.info("IntegrationStreamServiceImpl.checkAndUpdateDetailObjMapping,count={}", count);
                if (count > 0) {
                    return Result.newError(ResultCodeEnum.SYNC_DATA_MAPPING_NOT_EMPTY);
                }

                //检查同步中的数据是否被手动删除
                count = syncDataMappingsDao.setTenantId(arg.getTenantId())
                        .countIsCreatedMapping(arg.getTenantId(), mappingModel.getSourceObjectApiName(),
                                mappingModel.getDestObjectApiName(), false);
                log.info("IntegrationStreamServiceImpl.checkAndUpdateDetailObjMapping,count2={}", count);
                if (count > 0) {
                    return Result.newError(ResultCodeEnum.SYNC_DATA_MAPPING_NOT_EMPTY);
                }
            }
        }

        for (UpdateIntegrationStreamArg.ObjectMappingModel mappingModel : arg.getDetailObjMappingList()) {
            //删除明细对象
            if (mappingModel.isDelete()) {
                if (ployDetailEntity.getIntegrationStreamNodes() != null) {
                    IntegrationStreamNodesData integrationStreamNodes = ployDetailEntity.getIntegrationStreamNodes();
                    if (integrationStreamNodes.getReverseWriteNode() != null
                            && integrationStreamNodes.getReverseWriteNode().getDetailObjectMappings() != null) {
                        //删除回写组件里面的明细对象映射关系
                        ployDetailEntity.getIntegrationStreamNodes().getReverseWriteNode().getDetailObjectMappings()
                                .removeIf((detailObjectMappingData -> StringUtils.equals(mappingModel.getSourceObjectApiName(), detailObjectMappingData.getDestObjectApiName())
                                        && StringUtils.equals(mappingModel.getDestObjectApiName(), detailObjectMappingData.getSourceObjectApiName())));
                    }
                    if (integrationStreamNodes.getCheckSyncDataMappingNode() != null
                            && integrationStreamNodes.getCheckSyncDataMappingNode().getDetailCheckSyncDataMappingData() != null) {
                        //删除检查中间表组件里面的明细对象映射关系
                        integrationStreamNodes.getCheckSyncDataMappingNode().getDetailCheckSyncDataMappingData()
                                .removeIf((data -> data.getQueryObjectMappingData() != null
                                        && StringUtils.equals(mappingModel.getSourceObjectApiName(), data.getQueryObjectMappingData().getDestObjectApiName())));
                    }
                    if (integrationStreamNodes.getQueryCrmObject2DestNodeBySource() != null
                            && integrationStreamNodes.getQueryCrmObject2DestNodeBySource().getDetailQueryData2DestDataMapping() != null) {
                        //删除根据源查询查询crm组件里面的明细对象映射关系
                        integrationStreamNodes.getQueryCrmObject2DestNodeBySource().getDetailQueryData2DestDataMapping()
                                .removeIf((data -> CollectionUtils.isNotEmpty(data)
                                        && StringUtils.equals(mappingModel.getSourceObjectApiName(), data.get(0).getQueryObjectMappingData().getDestObjectApiName())));
                    }
                    if (integrationStreamNodes.getQueryCrmObject2DestNodeByDest() != null
                            && integrationStreamNodes.getQueryCrmObject2DestNodeByDest().getDetailQueryData2DestDataMapping() != null) {
                        //删除根据目标查询查询crm组件里面的明细对象映射关系
                        integrationStreamNodes.getQueryCrmObject2DestNodeByDest().getDetailQueryData2DestDataMapping()
                                .removeIf((data -> CollectionUtils.isNotEmpty(data)
                                        && StringUtils.equals(mappingModel.getDestObjectApiName(), data.get(0).getQueryObjectMappingData().getDestObjectApiName())));
                    }
                    if (integrationStreamNodes.getObjApiName2NotUpdateFieldApiName() != null) {//删除更新不传值字段
                        integrationStreamNodes.getObjApiName2NotUpdateFieldApiName().remove(mappingModel.getDestObjectApiName());
                    }
                    if (integrationStreamNodes.getObjApiName2NeedReturnFieldApiName() != null) {//删除需要返回字段
                        integrationStreamNodes.getObjApiName2NeedReturnFieldApiName().remove(mappingModel.getDestObjectApiName());
                    }
                }
                //删除明细对象映射关系
                ployDetailEntity.getDetailObjectMappings()
                        .removeIf((detailObjectMappingData -> StringUtils.equals(mappingModel.getSourceObjectApiName(), detailObjectMappingData.getSourceObjectApiName())
                                && StringUtils.equals(mappingModel.getDestObjectApiName(), detailObjectMappingData.getDestObjectApiName())));
                //删除明细对象数据范围
                ployDetailEntity.getDetailObjectSyncConditions()
                        .removeIf((syncConditionsData -> StringUtils.equals(mappingModel.getSourceObjectApiName(),
                                syncConditionsData.getApiName())));
            } else {
                //新增明细对象
                if (StringUtils.isNotEmpty(mappingModel.getNewSourceObjectApiName()) && StringUtils.isNotEmpty(mappingModel.getNewDestObjectApiName())) {
                    DetailObjectMappingsData.DetailObjectMappingData newDetailObj = new DetailObjectMappingsData.DetailObjectMappingData();
                    newDetailObj.setSourceObjectApiName(mappingModel.getNewSourceObjectApiName());
                    newDetailObj.setDestObjectApiName(mappingModel.getNewDestObjectApiName());
                    //新增明细对象映射
                    ployDetailEntity.getDetailObjectMappings().add(newDetailObj);
                }
            }
        }

        int count = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(arg.getTenantId()))
                .updateByIdSelective(ployDetailEntity);
        if (count > 0) {
            return Result.newSuccess();
        }
        return Result.newErrorByI18N(I18NStringEnum.s770.getI18nValue(), I18NStringEnum.s770.getI18nKey(), null);
    }

    public void fillFieldMappingsResult(final String tenantId, final String dataCenterId, final Integer sourceTenantType, final Integer destTenantType, final ObjectMappingResult masterObjectMapping, final List<ObjectMappingResult> detailObjectMappings, final String sourceTenantId, final String destTenantId) {
        //获取源企业对象apiNames，包括从对象
        List<String> sourceObjectApiNames = new ArrayList<>();
        sourceObjectApiNames.add(masterObjectMapping.getSourceObjectApiName());
        if (detailObjectMappings != null) {
            List<String> sourceDetailObjectApiNames = detailObjectMappings.stream().map(ObjectMappingResult::getSourceObjectApiName).collect(Collectors.toList());
            sourceObjectApiNames.addAll(sourceDetailObjectApiNames);
        }
        //获取源企业对象名称
        List<ObjectDescribe> sourceObjectDescribes = null;
        if (TenantType.CRM == sourceTenantType) {//如果源企业类型是crm
            sourceObjectDescribes = crmRemoteManager.listObjectAndFieldByApiNames(sourceTenantId, sourceObjectApiNames);
        } else {//源企业类型是erp
            sourceObjectDescribes = erpObjectAndFieldsService.getErpObjAndFields(tenantId, dataCenterId, sourceObjectApiNames);
        }
        Map<String, String> sourceObjectApiNameToNameMap = sourceObjectDescribes.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, ObjectDescribe::getDisplayName));
        //获取源企业对象的字段描述
        Map<String, Map<String, FieldDescribe>> sourceObjectApiNameToFieldMap = sourceObjectDescribes.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, ObjectDescribe::getFields));

        List<String> destObjectApiNames = new ArrayList<>();
        //获取目标企业对象apiNames，包括从对象 List<String> destObjectApiNames = new ArrayList<>();
        destObjectApiNames.add(masterObjectMapping.getDestObjectApiName());
        if (detailObjectMappings != null) {
            List<String> destDetailObjectApiNames = detailObjectMappings.stream().map(ObjectMappingResult::getDestObjectApiName).collect(Collectors.toList());
            destObjectApiNames.addAll(destDetailObjectApiNames);
        }
        //获取目标企业对象名称
        List<ObjectDescribe> destObjectDescribes = null;
        if (TenantType.CRM == destTenantType) {
            destObjectDescribes = crmRemoteManager.listObjectAndFieldByApiNames(destTenantId, destObjectApiNames);
        } else {
            destObjectDescribes = erpObjectAndFieldsService.getErpObjAndFields(tenantId, dataCenterId, destObjectApiNames);
        }
        Map<String, String> destObjectApiNameToNameMap = destObjectDescribes.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, ObjectDescribe::getDisplayName));
        //获取目标企业对象的字段描述
        Map<String, Map<String, FieldDescribe>> destObjectApiNameToFieldMap = destObjectDescribes.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, ObjectDescribe::getFields));

        Map<String, FieldDescribe> sourceMasterObjectFieldDescribeMap = sourceObjectApiNameToFieldMap.get(masterObjectMapping.getSourceObjectApiName());
        Map<String, FieldDescribe> destMasterObjectFieldDescribeMap = destObjectApiNameToFieldMap.get(masterObjectMapping.getDestObjectApiName());

        masterObjectMapping.setSourceObjectName(sourceObjectApiNameToNameMap.get(masterObjectMapping.getSourceObjectApiName()));
        masterObjectMapping.setDestObjectName(destObjectApiNameToNameMap.get(masterObjectMapping.getDestObjectApiName()));
        for (FieldMappingResult fieldMapping : masterObjectMapping.getFieldMappings()) {
            FieldDescribe sourceFieldDescribe = sourceMasterObjectFieldDescribeMap.get(fieldMapping.getSourceApiName());
            FieldDescribe destFieldDescribe = destMasterObjectFieldDescribeMap.get(fieldMapping.getDestApiName());
            if (sourceFieldDescribe != null) {
                fieldMapping.setSourceName(sourceFieldDescribe.getLabel());
            }
            if (destFieldDescribe != null) {
                fieldMapping.setDestName(destFieldDescribe.getLabel());
            }
        }

        if (detailObjectMappings != null) {
            for (ObjectMappingResult detailObjectMapping : detailObjectMappings) {
                detailObjectMapping.setSourceObjectName(sourceObjectApiNameToNameMap.get(detailObjectMapping.getSourceObjectApiName()));
                detailObjectMapping.setDestObjectName(destObjectApiNameToNameMap.get(detailObjectMapping.getDestObjectApiName()));
                Map<String, FieldDescribe> sourceDetailObjectFieldDescribeMap = sourceObjectApiNameToFieldMap.get(detailObjectMapping.getSourceObjectApiName());
                Map<String, FieldDescribe> destDetailObjectFieldDescribeMap = destObjectApiNameToFieldMap.get(detailObjectMapping.getDestObjectApiName());
                if (detailObjectMapping.getFieldMappings() != null) {
                    for (FieldMappingResult fieldMapping : detailObjectMapping.getFieldMappings()) {
                        FieldDescribe sourceDetailField = sourceDetailObjectFieldDescribeMap.get(fieldMapping.getSourceApiName());
                        if (sourceDetailField != null) {
                            fieldMapping.setSourceName(sourceDetailObjectFieldDescribeMap.get(fieldMapping.getSourceApiName()).getLabel());
                        }
                        FieldDescribe destDetailField = destDetailObjectFieldDescribeMap.get(fieldMapping.getDestApiName());
                        if (destDetailField != null) {
                            fieldMapping.setDestName(destDetailField.getLabel());
                        }
                    }
                }
            }
        }
    }

    /**
     *
     *
     * @param tenantId
     * @param arg
     * @return
     */
    @Override
    public Result<PaasDataSourceConfigStatus> updateTenantNeedPassDataSourceConfig(String tenantId, PaasDataSourceConfigStatus arg) {
        ErpTenantConfigurationEntity entity = erpTenantConfigurationDaoAccess.findOneNoCache(tenantId, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name());
        if (entity == null) {
            ErpTenantConfigurationEntity global = erpTenantConfigurationDaoAccess.findOneNoCache(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name());
            Map<String, List<String>> configuration = Maps.newHashMap();
            if(global!=null){
                Map<String, List<String>> globalConfig = JacksonUtil.fromJson(global.getConfiguration(), Map.class);
                Set<String> needRemoveKey=Sets.newHashSet();
                for(String key:globalConfig.keySet()){
                    if(key.equals("*")){
                        configuration.put(key, globalConfig.get(key));
                    }
                    if(key.equals(tenantId)||key.startsWith(tenantId+"_")){
                        configuration.put(key, globalConfig.get(key));
                        needRemoveKey.add(key);
                    }
                }
                if(CollectionUtils.isNotEmpty(needRemoveKey)){
                    for (String key : needRemoveKey) {
                        globalConfig.remove(key);
                    }
                }
                global.setConfiguration(JacksonUtil.toJson(globalConfig));
                tenantConfigurationManager.updateById(DataBaseBatchIndexUtil.notTenantId, global);
            }
            entity = ErpTenantConfigurationEntity.builder()
                    .id(com.fxiaoke.api.IdGenerator.get())
                    .tenantId(tenantId)
                    .dataCenterId(CommonConstant.configUniformIdentifier)
                    .channel(CommonConstant.configUniformIdentifier)
                    .type(TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name())
                    .configuration(JacksonUtil.toJson(configuration))
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .build();
            tenantConfigurationManager.insert(tenantId, entity);
        }
        Map<String, List<String>> configuration = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
        String tenantObj = tenantId + "_" + arg.getCrmObjectApiName();
        if (configuration.containsKey(tenantId)) {//如果包含企业级的配置（兼容历史数据），获取现在所有crm->erp集成流，去掉全局配置
            List<SyncPloyDetailEntity> entities = adminSyncPloyDetailDao.listByTenantId(tenantId);
            Set<String> allCrmObjApiNames = entities.stream().filter(v -> TenantType.CRM == v.getSourceTenantType())
                    .map(SyncPloyDetailEntity::getSourceObjectApiName).collect(Collectors.toSet());
            for (String crmObjApiName : allCrmObjApiNames) {
                String other_tenant_obj = tenantId + "_" + crmObjApiName;
                configuration.put(other_tenant_obj, configuration.get(tenantId));
            }
            configuration.remove(tenantId);
        }
        if (arg.getStatus()) {//开启
            List<String> objPassDataSource = Lists.newArrayList();
            objPassDataSource.addAll(configuration.get("*")==null?CommonConstant.default_paas_dataSource:configuration.get("*"));//默认过滤的
            objPassDataSource.removeAll(arg.getDataSourceList());//去掉开启的触发的
            configuration.put(tenantObj, objPassDataSource);
        } else {//关闭
            configuration.remove(tenantObj);
        }
        entity.setConfiguration(JacksonUtil.toJson(configuration));
        tenantConfigurationManager.updateById(tenantId, entity);
        return Result.newSuccess(arg);
    }

    @Override
    public Result<PaasDataSourceConfigStatus> queryTenantNeedPassDataSourceConfig(String tenantId, PaasDataSourceConfigStatus arg) {
        ErpTenantConfigurationEntity entity = erpTenantConfigurationDaoAccess.findOneNoCache(tenantId, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name());
        if(entity==null){
            entity = erpTenantConfigurationDaoAccess.findOneNoCache(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier,
                    TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE.name());
        }
        PaasDataSourceConfigStatus result = new PaasDataSourceConfigStatus();
        result.setCrmObjectApiName(arg.getCrmObjectApiName());
        if (entity == null || StringUtils.isBlank(entity.getConfiguration())) {//如果没有配置，那就是不过滤
            result.setStatus(true);
            result.getDataSourceList().add(CommonConstant.dataSource_import);
            result.getDataSourceList().add(CommonConstant.dataSource_optool_batch);
            return Result.newSuccess(result);
        }
        Map<String, List<String>> config = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
        Boolean need = tenantConfigurationManager.ifPassDataSource(tenantId, arg.getCrmObjectApiName(), null, CommonConstant.dataSource_import, config);
        if (!need) {
            result.setStatus(true);
            result.getDataSourceList().add(CommonConstant.dataSource_import);
        }
        need = tenantConfigurationManager.ifPassDataSource(tenantId, arg.getCrmObjectApiName(), null, CommonConstant.dataSource_optool_batch, config);
        if (!need) {
            result.setStatus(true);
            result.getDataSourceList().add(CommonConstant.dataSource_optool_batch);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> createIntegrationStream(String tenantId, CreateObjectMappingArg arg, String lang) {
        //校验配额
//        SyncQuota syncQuota = syncQuotaService.getQuota(tenantId, false,false).safeData();
//        if (syncQuota.getStreamQuota() - syncQuota.getStreamUsed() <= 0) {
//            return Result.newError(i18NStringManager.get(I18NStringEnum.s756,lang,tenantId));
//        }

        // 检查对象有主键字段
        ErpConnectInfoEntity sourceConnectInfo = connectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, arg.getSourceDataCenterId());
//        final List<String> noIdFieldObjApiNames = getNoIdFieldObjApiName(tenantId, arg, sourceConnectInfo);
//        if (CollectionUtils.isNotEmpty(noIdFieldObjApiNames)) {
//            return Result.newError(i18NStringManager.get2(I18NStringEnum.s2064.getI18nKey(), lang, tenantId, I18NStringEnum.s2064.getI18nValue(), Lists.newArrayList(String.join(",", noIdFieldObjApiNames))));
//        }

        //校验是否存在重复的策略明细
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByDcIdAndObjApiName(tenantId, arg.getSourceDataCenterId(),
                        arg.getDestDataCenterId(), arg.getSourceObjectApiName(), arg.getDestObjectApiName());

        if (CollectionUtils.isNotEmpty(syncPloyDetailEntities)) {
            return Result.newError(String.format(i18NStringManager.get(I18NStringEnum.s4034,
                    lang,
                    tenantId),
                    syncPloyDetailEntities.get(0).getIntegrationStreamName()));
        }

        //获取erp的 dataCenterId
        String erpDataCenterId;
        if (ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {//源是crm
            erpDataCenterId = arg.getSourceDataCenterId();
        } else {//源是erp
            erpDataCenterId = arg.getDestDataCenterId();
        }

        //名称重复需要加上后缀
        List<SyncPloyDetailEntity> repeatSyncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findSyncPloyDetailByStartWithName(tenantId, erpDataCenterId, arg.getIntegrationStreamName());

        if(CollectionUtils.isNotEmpty(repeatSyncPloyDetailEntities)) {
            //处理重名  集成流 + (n)
            //转成set
            Set<String> repeatNameSet = repeatSyncPloyDetailEntities.stream().map(SyncPloyDetailEntity::getIntegrationStreamName).collect(Collectors.toSet());
            arg.setIntegrationStreamName(convertName(arg.getIntegrationStreamName(), repeatNameSet, 1));
            log.info("IntegrationStreamServiceImpl.createIntegrationStream,integrationStreamName={}",arg.getIntegrationStreamName());
        }

        SyncConditionsListData detailObjectSyncConditions = new SyncConditionsListData();
        DetailObjectMappingsData detailObjectMappingData = new DetailObjectMappingsData();
        if (CollUtil.isNotEmpty(arg.getDetailObjectMappings())) {
            for (CreateObjectMappingArg.ObjectMapping objectMapping : arg.getDetailObjectMappings()) {
                if (StringUtils.isNotEmpty(objectMapping.getSourceObjectApiName()) && StringUtils.isNotEmpty(objectMapping.getDestObjectApiName())) {
                    DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping = new DetailObjectMappingsData.DetailObjectMappingData();
                    detailObjectMapping.setSourceObjectApiName(objectMapping.getSourceObjectApiName());
                    detailObjectMapping.setDestObjectApiName(objectMapping.getDestObjectApiName());
                    detailObjectMappingData.add(detailObjectMapping);
                    SyncConditionsData detailSyncConditionsData = new SyncConditionsData();
                    detailSyncConditionsData.setApiName(detailObjectMapping.getSourceObjectApiName());
                    detailObjectSyncConditions.add(detailSyncConditionsData);
                }
            }
            //检查从对象的主从字段
//            List<String> erpDetailObjApiNames;
//            String erpDcId;
//            if (ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {
//                erpDetailObjApiNames = detailObjectMappingData.stream().map(v -> v.getDestObjectApiName()).collect(Collectors.toList());
//                erpDcId = arg.getDestDataCenterId();
//            } else {
//                erpDetailObjApiNames = detailObjectMappingData.stream().map(v -> v.getSourceObjectApiName()).collect(Collectors.toList());
//                erpDcId = arg.getSourceDataCenterId();
//            }
//            List<ErpObjectFieldEntity> masterDetailFields = erpObjectFieldDao.findByObjsAndType(tenantId, erpDcId, erpDetailObjApiNames, ErpFieldTypeEnum.master_detail);
//            Set<String> validObjApiNames = masterDetailFields.stream().map(v -> v.getErpObjectApiName()).collect(Collectors.toSet());
//            Collection<String> subtract = CollUtil.subtract(erpDetailObjApiNames, validObjApiNames);
//            if (!subtract.isEmpty()) {
//                return Result.newError(i18NStringManager.get(I18NStringEnum.s757,lang,tenantId) + subtract);
//            }
        }

        SyncPloyDetailEntity entity = BeanUtil2.deepCopy(arg, SyncPloyDetailEntity.class);
        if (ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {//源是crm
            entity.setSourceTenantType(1);
            entity.setDestTenantType(2);
        } else {//源是erp
            entity.setSourceTenantType(2);
            entity.setDestTenantType(1);
        }
        entity.setId(IdUtil.generateId());
        entity.setTenantId(tenantId);
        entity.setSyncPloyId(tenantId);//使用企业id代替
        entity.setStatus(SyncPloyDetailStatusEnum.DISABLE.getStatus());
        entity.setDetailObjectMappings(detailObjectMappingData);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setSyncConditions(new SyncConditionsData());
        entity.setDetailObjectSyncConditions(detailObjectSyncConditions);

        //默认设置同步规则为轮询方式
        SyncRulesData syncRulesData = new SyncRulesData();
        if (arg.getSyncRules() != null) {
            if (CollectionUtils.isNotEmpty(arg.getSyncRules().getSyncTypeList())) {
                syncRulesData.setSyncTypeList(arg.getSyncRules().getSyncTypeList());
            }
            if (CollectionUtils.isNotEmpty(arg.getSyncRules().getEvents())) {
                //默认选中修改映射字段才能触发数据新增 7
                arg.getSyncRules().getEvents().add(7);
                syncRulesData.setEvents(Lists.newArrayList(arg.getSyncRules().getEvents()));
            }
            if (arg.getSyncRules().getPollingInterval() != null) {
                PollingIntervalApiDto pollingInterval = BeanUtil2.deepCopy(arg.getSyncRules().getPollingInterval(), PollingIntervalApiDto.class);
                syncRulesData.setPollingInterval(pollingInterval);
            }
        }
        entity.setSyncRules(syncRulesData);
        //创建集成流不需要初始化时间
//        if (TenantType.ERP == entity.getSourceTenantType()) {
//            adminSyncPloyDetailService.doLastSyncTime(tenantId, entity.getSourceDataCenterId(), entity.getSourceObjectApiName(), entity.getSyncRules());
//        }
        //默认关闭getById接口
        if (!ErpChannelEnum.CRM.equals(sourceConnectInfo.getChannel())) {//源不是crm
            closeGetByIdStatus(tenantId, sourceConnectInfo, arg);
        }
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insertIgnore(entity);
        if(sourceConnectInfo.getChannel()==ErpChannelEnum.ERP_K3CLOUD_ULTIMATE && syncRulesData.getSyncTypeList().contains(SyncTypeEnum.subscribe.name())) {
            ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findBySplit(tenantId, arg.getSourceObjectApiName());
            String realObjApiName = relationshipEntity.getErpRealObjectApiname();

            ErpK3UltimateTokenEntity k3UltimateTokenEntity = erpK3UltimateTokenDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findData(tenantId,arg.getSourceDataCenterId(),realObjApiName, "1.0");
            if(k3UltimateTokenEntity==null) {
                K3UltimateTokenModel model = new K3UltimateTokenModel();
                model.setTenantId(tenantId);
                model.setDataCenterId(arg.getSourceDataCenterId());
                model.setErpObjApiName(realObjApiName);
                model.setVersion("1.0");
                model.setTimestamps(System.currentTimeMillis()+"");
                Result<String> genToken = k3UltimateEventSubscribeService.genToken(model);
                log.info("IntegrationStreamServiceImpl.createIntegrationStream,genToken={}",genToken);
            }
        }
        return Result.newSuccess(entity.getId());
    }

    @Override
    public Result<ListObjectFieldsResult> getReverseWriteObjFieldApiNames(String tenantId, String dcId,Integer userId, String objectApiName, String lang) {
        ErpConnectInfoEntity tenantEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        //目前只有固定的这几个
        ListObjectFieldsResult result=new ListObjectFieldsResult();
        result.setObjectApiName(objectApiName);
        result.setFields(Lists.newArrayList());
        ObjectFieldResult syncTime=new ObjectFieldResult();
        syncTime.setApiName(ReverseWrite2CrmSourceFieldKey.SYNC_TIME);
        syncTime.setLabel(i18NStringManager.get(I18NStringEnum.s64,lang,tenantId));
        syncTime.setDefineType(ErpFieldTypeEnum.date_time.name());
        syncTime.setType(ErpFieldTypeEnum.date_time.name());
        result.getFields().add(syncTime);
        ObjectFieldResult errorKey=new ObjectFieldResult();
        errorKey.setApiName(ReverseWrite2CrmSourceFieldKey.ERROR_KEY);
        errorKey.setLabel(i18NStringManager.get(I18NStringEnum.s4505,lang,tenantId));
        errorKey.setDefineType(ErpFieldTypeEnum.long_text.name());
        errorKey.setType(ErpFieldTypeEnum.long_text.name());
        result.getFields().add(errorKey);
        ObjectFieldResult errorCode=new ObjectFieldResult();
        errorCode.setApiName(ReverseWrite2CrmSourceFieldKey.ERROR_CODE);
        errorCode.setLabel(i18NStringManager.get(I18NStringEnum.s65,lang,tenantId));
        errorCode.setDefineType(ErpFieldTypeEnum.long_text.name());
        errorCode.setType(ErpFieldTypeEnum.long_text.name());
        result.getFields().add(errorCode);
        ObjectFieldResult errorMsg=new ObjectFieldResult();
        errorMsg.setApiName(ReverseWrite2CrmSourceFieldKey.ERROR_MSG);
        errorMsg.setLabel(i18NStringManager.get(I18NStringEnum.s66,lang,tenantId));
        errorMsg.setDefineType(ErpFieldTypeEnum.long_text.name());
        errorMsg.setType(ErpFieldTypeEnum.long_text.name());
        result.getFields().add(errorMsg);
        ObjectFieldResult id=new ObjectFieldResult();
        id.setApiName(ReverseWrite2CrmSourceFieldKey.DEST_ID);
        id.setLabel(i18NStringManager.get(I18NStringEnum.s67,lang,tenantId));
        id.setDefineType(ErpFieldTypeEnum.long_text.name());
        id.setType(ErpFieldTypeEnum.long_text.name());
        result.getFields().add(id);
        if(tenantEntity!=null&& ErpChannelEnum.ERP_K3CLOUD.equals(tenantEntity.getChannel())){
            ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, objectApiName);
            if(relation!=null&& ErpObjSplitTypeEnum.NOT_SPLIT.equals(relation.getSplitType())){
                ObjectFieldResult num=new ObjectFieldResult();
                num.setApiName(ReverseWrite2CrmSourceFieldKey.DEST_NAME);
                num.setLabel(i18NStringManager.get(I18NStringEnum.s68,lang,tenantId));
                num.setDefineType(ErpFieldTypeEnum.long_text.name());
                num.setType(ErpFieldTypeEnum.long_text.name());
                result.getFields().add(num);
            }
        }
        Result<ErpObjectDescResult> erpObjectDescResult = erpObjectService.queryErpObjectByObjApiName(tenantId, dcId, userId, objectApiName);
        if (!erpObjectDescResult.isSuccess() || Objects.isNull(erpObjectDescResult.getData())) {
            return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
        }
        Boolean supportOtherFields=ErpChannelEnum.ERP_K3CLOUD.equals(tenantEntity.getChannel())
                ||ErpChannelEnum.STANDARD_CHANNEL.equals(tenantEntity.getChannel())
                ||ErpChannelEnum.ERP_SAP.equals(tenantEntity.getChannel());
        if(supportOtherFields){
            ErpObjectApiNameArg queryArg = new ErpObjectApiNameArg();
            queryArg.setErpObjectApiName(objectApiName);
            Result<List<ErpObjectFieldResult>> listResult = erpObjectFieldsService.queryErpObjectFieldsByObjApiNameAndDcId(tenantId, userId, queryArg, dcId);
            ListObjectFieldsResult listObjectFieldsResult;
            if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                listObjectFieldsResult = ResultConversionUtil.converseErpObjFelds(tenantId, listResult.getData());
                result.getFields().addAll(listObjectFieldsResult.getFields());
            }
        }
        return Result.newSuccess(result);
    }

    private String convertName(String name, Set<String> repeatNameSet, Integer count) {
        if (repeatNameSet.contains(name)) {
            String newName = name + "(" + count + ")";
            if (repeatNameSet.contains(newName)) {
                return convertName(name, repeatNameSet, count + 1);
            } else {
                return newName;
            }
        } else {
            return name;
        }
    }


    @Override
    public Result<List<DataCenterIntegration>> groupIntegrationStreamsBySourceApiName(String tenantId, String sourceApiName, String lang) {
        List<SyncPloyDetailEntity> entityList=syncPloyDetailManager.listBySourceApiName(tenantId,sourceApiName);
        if(CollectionUtils.isEmpty(entityList)){
            return Result.newSuccess(Lists.newArrayList());
        }
        List<DataCenterIntegration> result=Lists.newArrayList();
        Map<String, List<SyncPloyDetailEntity>> groupedMap = entityList.stream()
                .collect(Collectors.groupingBy(SyncPloyDetailEntity::getDestDataCenterId));
        String defaultIntegrationStreamName=i18NStringManager.getByEi(I18NStringEnum.s3749, tenantId);
        groupedMap.forEach((destDataCenterId, entities) -> {
            DataCenterIntegration integration = new DataCenterIntegration();
            integration.setValue(destDataCenterId);
            ErpConnectInfoEntity erpConnectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, destDataCenterId);
            if(erpConnectInfo!=null){//有些数据中心删除了，还存在集成流
                integration.setLabel(erpConnectInfo.getDataCenterName());
                integration.setChildren(Lists.newArrayList());
                for(SyncPloyDetailEntity entity:entities){
                    DataCenterIntegration.IntegrationStreamChild child=new DataCenterIntegration.IntegrationStreamChild();
                    child.setLabel(StringUtils.isBlank(entity.getIntegrationStreamName())?defaultIntegrationStreamName:entity.getIntegrationStreamName());
                    child.setValue(entity.getDestObjectApiName());
                    integration.getChildren().add(child);
                }
                result.add(integration);
            }
        });

        return Result.newSuccess(result);
    }

    public static void main(String[] args) {
        List<ObjectMappingVo> detailObjMappingList = new ArrayList<>();
        ObjectMappingVo objectMappingVo = new ObjectMappingVo("s1", "s2");
        ObjectMappingVo objectMappingVo2 = new ObjectMappingVo("s3", "s4");
        detailObjMappingList.add(objectMappingVo);
        detailObjMappingList.add(objectMappingVo2);
        Map<String, ObjectMappingVo> detailObjMapping = detailObjMappingList.stream()
                .collect(Collectors.toMap(ObjectMappingVo::getSourceObjectApiName, u -> u));
        System.out.println(detailObjMapping);
    }
}
