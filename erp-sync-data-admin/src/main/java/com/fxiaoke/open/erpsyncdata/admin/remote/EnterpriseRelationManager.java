package com.fxiaoke.open.erpsyncdata.admin.remote;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.enterpriserelation2.arg.IsDownstreamArg;
import com.fxiaoke.enterpriserelation2.arg.TenantIdCascadeArg;
import com.fxiaoke.enterpriserelation2.arg.UpstreamTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.listAllTemplateEnterprisesResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.TemplateEnterpriseService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/23 16:35:13
 */
@Component
@Slf4j
public class EnterpriseRelationManager {
    @Autowired
    private EnterpriseRelationService enterpriseRelationService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private TemplateEnterpriseService templateEnterpriseService;

    public Result2<List<String>> getAllDownstreamTenantIds(String tenantId) {
        final HeaderObj headerObj = getHeaderObj(tenantId);
        final TenantIdCascadeArg tenantIdCascadeArg = new TenantIdCascadeArg();
        tenantIdCascadeArg.setTenantId(Integer.valueOf(tenantId));
        tenantIdCascadeArg.setIsCascade(true);

        final RestResult<Set<Integer>> setRestResult = enterpriseRelationService.listAllDownstreamTenantIds(headerObj, tenantIdCascadeArg);
        if (setRestResult.isSuccess()) {
            return Result2.newSuccess(setRestResult.getData().stream().map(String::valueOf).collect(Collectors.toList()));
        }

        log.warn("listAllDownstreamTenantIds error arg:{} result:{}", JSON.toJSONString(tenantIdCascadeArg), JSON.toJSONString(setRestResult));
        return Result2.newError(setRestResult.getErrCode(), setRestResult.getErrMsg());
    }

    @Cached(timeUnit = TimeUnit.MINUTES, expire = 1, cacheType = CacheType.LOCAL, postCondition = "#result.isSuccess() && #result.getData()")
    public Result2<Boolean> isDownstream(String upstreamTenantId, String downstreamTenantId) {
        final HeaderObj headerObj = getHeaderObj(upstreamTenantId);

        final String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(downstreamTenantId));
        final IsDownstreamArg arg = IsDownstreamArg.builder()
                .ea(ea)
                .upstreamTenantId(Integer.valueOf(upstreamTenantId))
                .build();
        final RestResult<Boolean> downstream = enterpriseRelationService.isDownstream(headerObj, arg);

        return downstream.isSuccess() ? Result2.newSuccess(downstream.getData()) : Result2.newError(downstream.getErrCode(), downstream.getErrMsg());
    }

    @Cached(expire = 60, cacheType = CacheType.LOCAL, postCondition = "#result.isSuccess() && #result.getData()")
    public Result2<Boolean> isTemplateEnterprise(String tenantId, String templateId) {
        final HeaderObj headerObj = getHeaderObj(tenantId);

        final UpstreamTenantIdOutArg arg = new UpstreamTenantIdOutArg();
        arg.setUpstreamTenantId(Integer.parseInt(tenantId));
        arg.setFsUserIds(-10000);

        final RestResult<listAllTemplateEnterprisesResult> result = templateEnterpriseService.listAllTemplateEnterprises(headerObj, arg);

        if (!result.isSuccess()) {
            log.warn("templateEnterpriseService.listAllTemplateEnterprises error tenantId:{} errorCode:{} errMsg:{}", tenantId, result.getErrCode(), result.getErrMsg());
            return Result2.newError(result.getErrCode(), result.getErrMsg());
        }

        final boolean match = result.getData().getTemplateEnterprises().stream()
                .map(listAllTemplateEnterprisesResult.TemplateEnterprisesData::getEnterpriseId)
                .map(String::valueOf)
                .filter(StringUtils::isNotBlank)
                .anyMatch(templateId::equals);

        return Result2.newSuccess(match);
    }

    @NotNull
    private static HeaderObj getHeaderObj(String tenantId) {
        return HeaderObj.newInstance(Integer.parseInt(tenantId));
    }

    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public Result2<List<String>> getAllTemplateIds(String upstreamTenantId) {
        final HeaderObj headerObj = getHeaderObj(upstreamTenantId);

        final UpstreamTenantIdOutArg arg = new UpstreamTenantIdOutArg();
        arg.setUpstreamTenantId(Integer.parseInt(upstreamTenantId));
        arg.setFsUserIds(-10000);

        final RestResult<listAllTemplateEnterprisesResult> result = templateEnterpriseService.listAllTemplateEnterprises(headerObj, arg);

        if (!result.isSuccess()) {
            return Result2.newError(result.getErrCode(), result.getErrMsg());
        }

        final List<String> templateIds = result.getData().getTemplateEnterprises().stream()
                .map(listAllTemplateEnterprisesResult.TemplateEnterprisesData::getEnterpriseId)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());

        return Result2.newSuccess(templateIds);
    }
}
