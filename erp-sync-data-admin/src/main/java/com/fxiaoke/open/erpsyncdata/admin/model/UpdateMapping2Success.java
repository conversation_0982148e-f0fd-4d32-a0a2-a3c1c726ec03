package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (^_−)☆
 */
public class UpdateMapping2Success {

    @Data
    @Accessors(chain = true)
    public static class Arg {
        private String tenantId;

        private String sourceObjApiName;

        private String destObjApiName;

        /**
         * 默认仅修复 isCreated=true的数据
         */
        private boolean onlyCreated = true;

        /**
         * 最多多少数量
         */
        private long limit = 10000L;
    }

    @Data
    @Accessors(chain = true)
    public static class Record {
        private Long totalQuery;
        private Long totalUpdate;
    }
}
