package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CategoryFieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.utils.ExcelUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ProductCategory;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataMonitoredException;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/9
 */
@Slf4j
@Service
public class ImportProductCategoryManager {

    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private ErpConnectInfoManager connectInfoManager;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private UserCenterService userCenterService;

    public boolean importProductCateGory2Crm(String tenantId, Integer userId,
                                             String dataCenterId, List<CategoryFieldDataMappingExcelVo> importList,
                                             String lang) {
        importList.removeIf(v -> Objects.equals(v.getErpDataId(), v.getErpParentDataId()));
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        List<ErpFieldDataMappingEntity> oldMappings = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listNoSearch(tenantId, dataCenterId, ErpFieldTypeEnum.category, null, null);
        //读当前crm产品分类
        List<ProductCategory.Vo> crmCategories = listExistFsCategories(tenantId);
        //key:code,value:categoryCode
        Map<String, String> map1 = crmCategories.stream().collect(
                Collectors.toMap(ProductCategory.Vo::getCode, ProductCategory.Vo::getCategoryCode));
        BiMap<String, String> codeMap = HashBiMap.create(map1);
        //清理旧的映射表
        cleanOldFieldDataMapping(tenantId, oldMappings, crmCategories);
        boolean valid = true;
        Map<String, CategoryFieldDataMappingExcelVo> erpExcelVoMap = new HashMap<>(importList.size() * 2);
        Map<String, CategoryFieldDataMappingExcelVo> crmExcelVoMap = new HashMap<>(importList.size() * 2);
        Map<String, ErpFieldDataMappingEntity> erpMapping = oldMappings.stream()
                .collect(Collectors.toMap(ErpFieldDataMappingEntity::getErpDataId, u -> u));
        for (CategoryFieldDataMappingExcelVo vo : importList) {
            boolean curValid = firstFormatCheck(vo, erpMapping, codeMap, erpExcelVoMap, crmExcelVoMap,lang,tenantId);
            valid = valid && curValid;
        }
        for (CategoryFieldDataMappingExcelVo vo : importList) {
            boolean curValid = fillFsCategoryCode(vo, erpMapping, codeMap, erpExcelVoMap,lang,tenantId);
            valid = valid && curValid;
        }
        if (!valid) {
            return sendErrorExcelResult(tenantId, dataCenterId, userId, importList,lang);
        }

        Map<String, ProductCategory.Vo> cateCodeMap = crmCategories.stream().collect(
                Collectors.toMap(ProductCategory.Vo::getCategoryCode, u -> u));
        //构建树，并导入到CRM
        valid = updateCrmCategories(tenantId, dataCenterId, cateCodeMap, crmCategories, importList, crmExcelVoMap,lang);
        if (!valid) {
            return sendErrorExcelResult(tenantId, dataCenterId, userId, importList,lang);
        }
        //导入中间表
        List<ErpFieldDataMappingEntity> newMappings = new ArrayList<>();
        for (CategoryFieldDataMappingExcelVo excelVo : importList) {
            String erpDataId = excelVo.getErpDataId();
            String fsCategoryCode = excelVo.getFsCategoryCode();
            ProductCategory.Vo vo = cateCodeMap.get(fsCategoryCode);
            ErpFieldDataMappingEntity mapping = erpMapping.get(erpDataId);
            if (mapping != null) {
                if (!mapping.getFsDataName().equals(excelVo.getFsDataName())
                        || !mapping.getErpDataName().equals(excelVo.getErpDataName())) {
                    mapping.setFsDataName(excelVo.getFsDataName());
                    mapping.setErpDataName(excelVo.getErpDataName());
                    mapping.setFieldDataExtendValue(JacksonUtil.toJson(vo));
                    //更新名称
                    int i = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(mapping);
                    log.info("update by id cate mapping,result:{}", i);
                }
            } else {
                long now = System.currentTimeMillis();
                ErpFieldDataMappingEntity newMapping = new ErpFieldDataMappingEntity();
                //映射表使用生成的id，避免沙盒环境重复
                newMapping.setId(idGenerator.get());
                newMapping.setTenantId(tenantId);
                newMapping.setDataCenterId(dataCenterId);
                newMapping.setChannel(connectInfo.getChannel());
                newMapping.setDataType(ErpFieldTypeEnum.category);
                //使用code作为中间表的fsdataId
                newMapping.setFsDataId(vo.getCode());
                newMapping.setFsDataName(excelVo.getFsDataName());
                newMapping.setErpDataId(excelVo.getErpDataId());
                newMapping.setErpDataName(excelVo.getErpDataName());
                newMapping.setFieldDataExtendValue(JacksonUtil.toJson(vo));
                newMapping.setCreateTime(now);
                newMapping.setUpdateTime(now);
                newMappings.add(newMapping);
            }
        }
        if (!newMappings.isEmpty()) {
            List<List<ErpFieldDataMappingEntity>> split = ListUtil.split(newMappings, 100);
            //分批插入
            for (List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities : split) {
                int i = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(erpFieldDataMappingEntities);
                log.info("batch insert cate mapping,result:{}", i);
            }
        }
        return true;
    }

    private boolean sendErrorExcelResult(String tenantId,
                                         String dataCenterId,
                                         Integer userId,
                                         List<CategoryFieldDataMappingExcelVo> importList,
                                         String lang) {
        if (userId == null) {
            return false;
        }
        //写excel
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HorizontalCellStyleStrategy styleStrategy = ExcelUtils.getDefaultStyle();
        ExcelWriter excelWriter = EasyExcel.write(outputStream, importList.get(0).getClass())
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.write(importList, writeSheet);
        excelWriter.finish();
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        //上传文件系统
        String tnPath = fileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, outputStream.toByteArray(),lang);
        log.info("upload excel, result:{}", tnPath);
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(dataCenterId);
        arg.setReceivers(Collections.singletonList(userId));
        arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s730,lang,tenantId));
        String previewUrl = String.format(userCenterService.getPreviewFilePathFormat(ea), tnPath + ".xlsx");
        arg.setMsg(i18NStringManager.get(I18NStringEnum.s731,lang,tenantId) + previewUrl);
        notificationService.sendErpSyncDataAppNotice(arg,
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,lang,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
        return false;
    }

    private void cleanOldFieldDataMapping(String tenantId, List<ErpFieldDataMappingEntity> oldMapping, List<ProductCategory.Vo> crmCategories) {
        Map<String, ProductCategory.Vo> crmCodeMap = crmCategories.stream().collect(
                Collectors.toMap(ProductCategory.Vo::getCode, u -> u));
        Set<String> deleteIds = oldMapping.stream().filter(v -> !crmCodeMap.containsKey(v.getFsDataId()))
                .map(ErpFieldDataMappingEntity::getId).collect(Collectors.toSet());
        if (!deleteIds.isEmpty()) {
            oldMapping.removeIf(v -> deleteIds.contains(v.getId()));
            int i = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchDeleteByIds(tenantId, deleteIds);
            log.info("clean delete category mapping rows:{}", i);
        }
    }

    /**
     * @param tenantId
     * @param dcId
     * @param cateCodeMap   已经存在的crm分类map，key：categoryCode
     * @param crmCategories
     * @param importList
     * @param crmExcelVoMap
     * @return
     */
    private boolean updateCrmCategories(String tenantId,
                                        String dcId,
                                        Map<String, ProductCategory.Vo> cateCodeMap,
                                        List<ProductCategory.Vo> crmCategories,
                                        List<CategoryFieldDataMappingExcelVo> importList,
                                        Map<String, CategoryFieldDataMappingExcelVo> crmExcelVoMap,
                                        String lang) {
        boolean onlyOneDc = dataCenterManager.onlyOneErpDataCenter(tenantId);
        List<ProductCategory.Vo> rootCategory = new ArrayList<>();
        //检查需要新增还是更新
        for (CategoryFieldDataMappingExcelVo excelVo : importList) {
            ProductCategory.Vo vo = cateCodeMap.get(excelVo.getFsCategoryCode());
            if (vo != null) {
                if (!vo.getName().equals(excelVo.getFsDataName())) {
                    //更新名称
                    vo.setName(excelVo.getFsDataName());
                    vo.setUpdateTag(2);
                }
            } else {
                ProductCategory.Vo newVo = new ProductCategory.Vo();
                newVo.setCategoryCode(excelVo.getFsCategoryCode());
                newVo.setName(excelVo.getFsDataName());
                newVo.setUpdateTag(1);
                newVo.setParentCateGoryCode(excelVo.getFsParentCategoryCode());
                crmCategories.add(newVo);
                cateCodeMap.put(newVo.getCategoryCode(), newVo);
            }
        }
        //按父分类+名称分组
        Map<String, List<ProductCategory.Vo>> parentCodeNameMap = crmCategories.stream().collect(Collectors.groupingBy(v -> v.getParentCateGoryCode() + v.getName()));
        for (List<ProductCategory.Vo> list : parentCodeNameMap.values()) {
            if (list.size() > 1) {
                for (int i = 0; i < list.size(); i++) {
                    ProductCategory.Vo vo = list.get(i);
                    vo.setName(String.format("%s(%s)", vo.getName(), i + 1));
                    if (vo.getUpdateTag() == 0) {
                        vo.setUpdateTag(2);
                    }
                }
            }
        }
        //生成树状结构
        for (ProductCategory.Vo crmCategory : crmCategories) {
            String parentCateGoryCode = crmCategory.getParentCateGoryCode();
            if (parentCateGoryCode != null) {
                //增加在父分类下
                cateCodeMap.get(parentCateGoryCode).getChildren().add(crmCategory);
            } else {
                //root分类
                rootCategory.add(crmCategory);
            }
        }
        if (onlyOneDc) {
            //只有一个数据中心时，直接使用一颗树
            boolean add2Db = recursiveCreateCategories(tenantId, rootCategory, crmExcelVoMap,lang);
            sfaApiManager.syncDbProductCategory2Describe(tenantId);
            return add2Db;
        } else {
            //多数据中心，新增的树会放到数据中心子树下，但是原来存在的不在数据中心根树下的，继续使用，不修改。
            rootCategory = checkDataCenterTree(tenantId, dcId, rootCategory);
            boolean add2Db = recursiveCreateCategories(tenantId, rootCategory, crmExcelVoMap,lang);
            sfaApiManager.syncDbProductCategory2Describe(tenantId);
            return add2Db;
        }
    }

    private List<ProductCategory.Vo> checkDataCenterTree(String tenantId, String dcId, List<ProductCategory.Vo> rootCategories) {
        //新的子树
        ProductCategory.Vo dcCate = null;
        List<ProductCategory.Vo> newRoot = new ArrayList<>();
        //旧的子树
        List<ProductCategory.Vo> allRoot = new ArrayList<>();
        for (ProductCategory.Vo rootCategory : rootCategories) {
            if (dcId.equals(rootCategory.getCategoryCode())) {
                dcCate = rootCategory;
            } else if (rootCategory.getId() == null) {
                //新子树
                rootCategory.setParentCateGoryCode(dcId);
                newRoot.add(rootCategory);
            } else {
                allRoot.add(rootCategory);
            }
        }
        if (dcCate != null) {
            //已存在dc子树
            dcCate.getChildren().addAll(newRoot);
        } else {
            //新增子树
            ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
            String dataCenterName = connectInfo.getDataCenterName();
            dcCate = new ProductCategory.Vo();
            dcCate.setCategoryCode(dcId);
            dcCate.setName(dataCenterName);
            dcCate.setOrderField(rootCategories.size() - newRoot.size() + 1);
            ProductCategory.SaveResult saveResult = sfaApiManager.createProductCategoryAdd2Db(tenantId, dcCate);
            if (saveResult.isSuccess()) {
                ProductCategory.Vo newCategory = ProductCategory.Vo.fromObjData(saveResult.getData().getResult());
                dcCate.setCode(newCategory.getCode());
                dcCate.setId(newCategory.getId());
                dcCate.setChildren(newRoot);
            } else {
                log.error("create category failed,arg:{},result:{}", dcCate, saveResult);
                throw new ErpSyncDataException(I18NStringEnum.s112, tenantId);
            }
        }
        allRoot.add(dcCate);
        return allRoot;
    }


    private boolean recursiveCreateCategories(String tenantId,
                                              List<ProductCategory.Vo> curCategories,
                                              Map<String, CategoryFieldDataMappingExcelVo> crmExcelVoMap,
                                              String lang) {
        boolean valid = true;
        for (int i = 0; i < curCategories.size(); i++) {
            ProductCategory.Vo curVo = curCategories.get(i);
            if (curVo.getUpdateTag() == 1) {
                //新增
                curVo.setOrderField(i + 1);
                if (!createProductCategory(tenantId, crmExcelVoMap, curVo,lang)) {
                    return false;
                }
            } else if (curVo.getUpdateTag() == 2) {
                //更新
                ProductCategory.UpdateResult updateResult = sfaApiManager.updateProductCategoryPack(tenantId, curVo);
                ThreadUtil.safeSleep(500);
                if (updateResult == null || !updateResult.isSuccess() || !updateResult.getData().getResult()) {
                    log.error("create category failed,arg:{},result:{}", curVo, updateResult);
                    crmExcelVoMap.get(curVo.getCategoryCode())
                            .appendRemark(i18NStringManager.get(I18NStringEnum.s733,lang,tenantId) + (updateResult == null ? i18NStringManager.get(I18NStringEnum.s734,lang,tenantId) : updateResult.getMessage()));
                }
            }
            curVo.getChildren().forEach(v -> v.setPid(curVo.getId()));
            valid = valid && recursiveCreateCategories(tenantId, curVo.getChildren(), crmExcelVoMap,lang);
        }
        return valid;
    }

    private boolean createProductCategory(String tenantId,
                                          Map<String, CategoryFieldDataMappingExcelVo> crmExcelVoMap,
                                          ProductCategory.Vo curVo,
                                          String lang) {
        try {
            for (int i = 0; i < 5; i++) {
                ProductCategory.SaveResult saveResult = sfaApiManager.createProductCategoryAdd2Db(tenantId, curVo);
                if (saveResult.isSuccess()) {
                    ProductCategory.Vo newCategory = ProductCategory.Vo.fromObjData(saveResult.getData().getResult());
                    curVo.setCode(newCategory.getCode());
                    curVo.setId(newCategory.getId());
                    //新增使用add2db不再sleep
//                    Thread.sleep(ConfigCenter.CREATE_CATEGORY_SLEEP_MILLI);
                    return true;
                } else if (!saveResult.getCode().equals(201111024)) {
                    //描述过期，则重试，其他情况直接返回失败
                    log.error("create category failed,arg:{},result:{}", curVo, saveResult);
                    crmExcelVoMap.get(curVo.getCategoryCode())
                            .appendRemark(i18NStringManager.get(I18NStringEnum.s732,lang,tenantId) + saveResult.getMessage());
                    return false;
                }
                Thread.sleep(5000);
            }
        } catch (Exception e) {
            crmExcelVoMap.get(curVo.getCategoryCode())
                    .appendRemark(i18NStringManager.get(I18NStringEnum.s732,lang,tenantId) + e.getMessage());
            log.error("create product category error,", e);
            return false;
        }
        crmExcelVoMap.get(curVo.getCategoryCode())
                .appendRemark(i18NStringManager.get(I18NStringEnum.s735,lang,tenantId));
        return false;
    }

    /**
     * 第一次校验，检查中间表重复和名称规范，名称不能带"/",检查导入的编码是否重复
     *
     * @param vo
     * @param erpExcelVoMap
     * @param crmExcelVoMap
     * @return
     */
    private boolean firstFormatCheck(CategoryFieldDataMappingExcelVo vo,
                                     Map<String, ErpFieldDataMappingEntity> erpMapping,
                                     BiMap<String, String> codeMap,
                                     Map<String, CategoryFieldDataMappingExcelVo> erpExcelVoMap,
                                     Map<String, CategoryFieldDataMappingExcelVo> crmExcelVoMap,
                                     String lang,
                                     String tenantId) {
        boolean valid = true;
        String fsDataId = vo.getFsCategoryCode();
        String erpDataId = vo.getErpDataId();
        String fsDataName = validName(vo.getFsDataName());
        vo.setFsDataName(fsDataName);
        String remark = vo.getRemark();
        boolean ignoreCheck = "ignore check".equals(remark);
        if (fsDataName.contains("/")) {
            vo.appendRemark(i18NStringManager.get(I18NStringEnum.s736,lang,tenantId));
            valid = false;
        }
        if (StringUtils.isBlank(fsDataId) || StringUtils.isBlank(erpDataId)) {
            vo.appendRemark(i18NStringManager.get(I18NStringEnum.s737,lang,tenantId));
            valid = false;
        }
        if (fsDataId.trim().length() != fsDataId.length()) {
            vo.appendRemark(i18NStringManager.get(I18NStringEnum.s738,lang,tenantId));
            valid = false;
        }
        if (!ignoreCheck && erpDataId.trim().length() != erpDataId.length()) {
            vo.appendRemark(i18NStringManager.get(I18NStringEnum.s739,lang,tenantId));
            valid = false;
        }
        if (erpMapping.containsKey(erpDataId)) {
            //原来已存在该ERPid的映射
            ErpFieldDataMappingEntity entity = erpMapping.get(erpDataId);
            if (!vo.getFsCategoryCode().equals(codeMap.get(entity.getFsDataId()))) {
                vo.appendRemark(i18NStringManager.get(I18NStringEnum.s740,lang,tenantId));
                return false;
            }
        }
        CategoryFieldDataMappingExcelVo erpPut = erpExcelVoMap.putIfAbsent(erpDataId, vo);
        if (erpPut != null) {
            erpPut.appendRemark(i18NStringManager.get(I18NStringEnum.s741,lang,tenantId));
            vo.appendRemark(i18NStringManager.get(I18NStringEnum.s741,lang,tenantId));
            valid = false;
        }
        CategoryFieldDataMappingExcelVo fsPut = crmExcelVoMap.putIfAbsent(fsDataId, vo);
        if (fsPut != null) {
            fsPut.appendRemark(i18NStringManager.get(I18NStringEnum.s742,lang,tenantId));
            vo.appendRemark(i18NStringManager.get(I18NStringEnum.s742,lang,tenantId));
            valid = false;
        }
        return valid;
    }


    private String validName(String name) {
        String match = "[/]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name.trim());
        String result = m.replaceAll("_");
        return result;
    }

    private boolean fillFsCategoryCode(CategoryFieldDataMappingExcelVo vo,
                                       Map<String, ErpFieldDataMappingEntity> erpMapping,
                                       BiMap<String, String> codeMap,
                                       Map<String, CategoryFieldDataMappingExcelVo> erpExcelVoMap,
                                       String lang,
                                       String tenantId) {
        if (vo.getErpParentDataId() == null) {
            return true;
        }
        if (erpExcelVoMap.containsKey(vo.getErpParentDataId())) {
            //上级编码在excel里
            CategoryFieldDataMappingExcelVo parentExcelVo = erpExcelVoMap.get(vo.getErpParentDataId());
            vo.setFsParentCategoryCode(parentExcelVo.getFsCategoryCode());
        } else if (erpMapping.containsKey(vo.getErpParentDataId())) {
            //上级编码在旧映射表里
            ErpFieldDataMappingEntity entity = erpMapping.get(vo.getErpParentDataId());
            //中间表存的是code，转成categoryCode
            vo.setFsParentCategoryCode(codeMap.get(entity.getFsDataId()));
        } else {
            vo.appendRemark(i18NStringManager.get(I18NStringEnum.s743,lang,tenantId));
            return false;
        }
        return true;
    }

    /**
     * 查找已存在的产品分类
     *
     * @param tenantId
     * @return
     */
    private List<ProductCategory.Vo> listExistFsCategories(String tenantId) {
        ProductCategory.ListResult listResult = sfaApiManager.listProductCategory(tenantId);
        if (listResult.getCode() != 0) {
            throw new ErpSyncDataMonitoredException(I18NStringEnum.s111,tenantId);
        }
        List<ObjectData> objectDataList = listResult.getData().getResult();
        List<ProductCategory.Vo> productCategories = new ArrayList<>();
        Map<String, String> idCodeMap = new HashMap<>();
        for (ObjectData objectData : objectDataList) {
            ProductCategory.Vo category = ProductCategory.Vo.fromObjData(objectData);
            idCodeMap.put(category.getId(), category.getCategoryCode());
            productCategories.add(category);
        }
        //设置父分类编码
        for (ProductCategory.Vo vo : productCategories) {
            if (vo.getPid() != null) {
                vo.setParentCateGoryCode(idCodeMap.get(vo.getPid()));
            }
        }
        return productCategories;
    }

}
