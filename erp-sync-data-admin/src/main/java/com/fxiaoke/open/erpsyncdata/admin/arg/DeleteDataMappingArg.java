package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/7/12
 * @Desc:
 */
@Data
@ApiModel
public class DeleteDataMappingArg implements Serializable {
    @ApiModelProperty("源对象APIName")
    private String sourceObjectApiName;

    @ApiModelProperty("目标对象APIname")
    private String destObjectApiName;
    /**
     * crm->erp : 1
     * erp->crm : 2
     */
    @ApiModelProperty("同步方向")
    private Integer syncDirection;

//    @ApiModelProperty("fs数据ids")
//    private List<String> fsDataId;

    @ApiModelProperty("源数据Id")
    private List<String> sourceDataIds;
    @ApiModelProperty("目标数据Id")
    private List<String> destDataIds;
}
