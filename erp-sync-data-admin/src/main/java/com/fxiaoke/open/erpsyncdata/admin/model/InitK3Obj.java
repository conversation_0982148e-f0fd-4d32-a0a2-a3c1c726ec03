package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import lombok.Data;

import java.util.LinkedHashSet;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/23
 */
public class InitK3Obj {

    @Data
    public static class Arg {
        /**
         * 企业id
         */
        private String tenantId;
        /**
         * 连接参数
         */
        private K3CloudConnectParam k3CloudConnectParam;
        /**
         * K3对象formId
         */
        private String formId;
        /**
         * 是否增量预置对象和字段
         */
        private Boolean inCrementObjectField = true;
        /**
         * 当前数据中心id（连接信息id）
         */
        private String dataCenterId;
    }


    @Data
    public static class AnalyzeObjArg {
        /**
         * K3对象formId
         */
        private String formId;

        /**
         * 是否校验已存在对象，为true时，并且已经存在，会报错
         */
        private boolean checkExist;
    }


    @Data
    public static class PresetObjArg {
        /**
         * K3对象formId
         */
        private String erpObjectApiName;

        /**
         * 主对象是否已经存在，用于判断是否需要预置主对象。
         */
        private boolean exist = false;
        /**
         * 需要预置的对象
         */
        private LinkedHashSet<String> erpObjectApiNames;
    }


    @Data
    public static class AnalysisFieldArg {

        /**
         * 真实对象apiName 可以从中间对象自动获取
         */
        private String actualObjApiName;
        /**
         * 中间对象apiName
         */
        private String splitObjApiName;

        /**
         * 字段apiName，不指定查所有
         */
        private String fieldApiName;

    }
}
