package com.fxiaoke.open.erpsyncdata.admin.data;

import com.fxiaoke.open.erpsyncdata.admin.result.QueryIntegrationDetailResult;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncForceConstant;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncConditionsData implements Serializable {
    private String apiName;
    private List<List<FilterData>> filters;

    /**
     * 强制同步
     */
    private Boolean isSyncForce = SyncForceConstant.CLOSE;
    @ApiModelProperty("数据范围-查询crm")
    private QueryIntegrationDetailResult.SyncConditionsQueryDataNode syncConditionsQueryDataNode;
}
