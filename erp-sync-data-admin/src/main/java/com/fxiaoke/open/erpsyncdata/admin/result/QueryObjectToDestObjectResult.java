package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class QueryObjectToDestObjectResult  implements Serializable {
    @ApiModelProperty("查询crm")
    private QueryObjectMappingResult queryObjectMappingData; //查询crm
    @ApiModelProperty("查询对象字段映射到目标对象")
    private ObjectMappingResult queryData2DestDataMapping;//查出来的对象字段映射到目标对象
}
