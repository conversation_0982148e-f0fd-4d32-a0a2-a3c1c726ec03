package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectFieldResult;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/21
 */
@Data
@Accessors(chain = true)
public class ErpAnalysisField {
    List<ErpObjectFieldResult> erpObjectFields = new ArrayList<>();
}
