package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.admin.service.FsObjectDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpHistoryTaskLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpHistoryTaskLog;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.impl.task.AbstractErpHistoryTaskServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 20:27 2021/8/10
 * @Desc:
 */
@Service
@Slf4j
public class ErpHistoryDataTaskServiceImpl implements ErpHistoryDataTaskService {

    @Autowired
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private RedissonClient redissonClient;
    private Map<Integer, AbstractErpHistoryTaskServiceImpl> erpHistoryTaskServiceMap;
    @Autowired
    private FsObjectDataService fsObjectDataService;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpHistoryTaskLogDao erpHistoryTaskLogDao;
    @Autowired
    private ErpHistoryTaskLogDao historyTaskLogDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;

    @Autowired
    public void setErpHistoryTaskServiceMap(List<AbstractErpHistoryTaskServiceImpl> erpHistoryTaskServices) {
        this.erpHistoryTaskServiceMap = erpHistoryTaskServices.stream().collect(Collectors.toMap(v -> v.taskType().getStatus(), v -> v));
    }

    @Override
    public Result<String> createErpHistoryDataTask(String tenantId,String dcId, Integer userId, CreateErpHistoryDataTaskArg arg, String lang) {
        ErpHistoryDataTaskResult task=arg.getTask();
        return erpHistoryTaskServiceMap.get(task.getTaskType()).createErpHistoryDataTask(tenantId, dcId, userId, arg,lang);
    }

    @Override
    public Result<String> editErpHistoryDataTask(String tenantId, String dcId, Integer userId, CreateErpHistoryDataTaskArg arg, String lang) {
        //只允许还没执行的任务，编辑
        ErpHistoryDataTaskResult task=arg.getTask();
        return erpHistoryTaskServiceMap.get(task.getTaskType()).editErpHistoryDataTask(tenantId, dcId, userId, arg,lang);
    }

    @Override
    public Result<QueryResult<List<ErpHistoryDataTaskResult>>> queryErpHistoryDataTask(String tenantId,
                                                                                       Integer userId,
                                                                                       QueryErpHistoryDataTasksArg arg,
                                                                                       String lang) {
//        //为什么不通过erp的真实apiname拿历史任务：因为之前对象拆分批次，是一个真实apiname对到两个虚拟apiname。兼容这种情况，只能通过这种方式
         List<String> dataCenterIds=Lists.newArrayList();
         List<String> actualObjApiNames=Lists.newArrayList();
        fillQueryTaskDcIdAndActualApiName(tenantId, arg, dataCenterIds, actualObjApiNames);

        String taskName=null;
        if (StringUtils.isNotEmpty(arg.getTaskName())) {
            taskName = "%" + arg.getTaskName() + "%";
        }
        QueryResult<List<ErpHistoryDataTaskResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        int count = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).viewAllCountByDcIdAndActualObjStatus(tenantId, dataCenterIds,actualObjApiNames,taskName,arg.getStatus());
        queryResult.setTotal(count);
        if(count==0){
            return Result.newSuccess(queryResult);
        }

        List<ErpHistoryDataTaskEntity> erpHistoryDataTaskEntities = erpHistoryDataTaskDao.viewAllListByDcIdAndActualObjStatus(tenantId,dataCenterIds, actualObjApiNames,taskName,
                arg.getStatus(),queryResult.getPageSize(), (queryResult.getPageNum() - 1) * queryResult.getPageSize(),ErpHistoryDataTaskStatusEnum.getSequence(arg.getStatus()));
//        //根据erpHistoryEntityes返回数据
        final List<ErpHistoryDataTaskResult> resultList = erpHistoryDataTaskEntities.stream()
                .map(entity -> convert2ErpHistoryDataTaskResult(tenantId, entity, lang))
                .collect(Collectors.toList());
        queryResult.setDataList(resultList);
        return Result.newSuccess(queryResult);
    }

    @NotNull
    private ErpHistoryDataTaskResult convert2ErpHistoryDataTaskResult(String tenantId, ErpHistoryDataTaskEntity entity, String lang) {
        //前端页面展示的历史任务，是最新的lastTask
        ErpHistoryDataTaskResult copy = BeanUtil.copy(entity, ErpHistoryDataTaskResult.class);

        // CRM为条件的需要特殊处理
        erpHistoryTaskServiceMap.get(copy.getTaskType()).afterConvert2Vo(copy, entity);

        if(StringUtils.isNotBlank(entity.getDataIds())){
            copy.setDataIds(JSONObject.parseArray(entity.getDataIds(),String.class));
        }
        if(entity.getTaskStatus()!=null){
            copy.setTaskStatusEnum(ErpHistoryDataTaskStatusEnum.getErpHistoryDataTaskStatus(entity.getTaskStatus()));
            copy.setTaskStatusDesc(i18NStringManager.get(copy.getTaskStatusEnum().getI18nKey(), lang, tenantId,copy.getTaskStatusEnum().getDesc()));
        }
        if(entity.getTotalCostTime()!=null){
            String formatTotalTime = (entity.getTotalCostTime() / 1000) + "s";
            copy.setTotalCostTime(formatTotalTime);

        }

        if(entity.getCreator()!=null){
            List<Integer> userIds=Lists.newArrayList(entity.getCreator());
            Result<List<EmployeeDto>> userResult = fsObjectDataService.queryEmployeeByFsUserId(Integer.valueOf(tenantId), userIds);
            if(userResult.isSuccess()&& CollectionUtils.isNotEmpty(userResult.getData())){
                copy.setCreatorName(userResult.getData().get(0).getName());
            }
        }
        if(entity.getDataCenterId()!=null){
            ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, entity.getDataCenterId());
            copy.setDataCenterName(connectInfoEntity.getDataCenterName());
            copy.setChannel(connectInfoEntity.getChannel());
            Connector connector = AllConnectorUtil.getByChannelAndConnectParam(connectInfoEntity.getChannel(), connectInfoEntity.getConnectParams());
            copy.setConnectorKey(connector.getKey());
            copy.setIconUrl(connector.getIconUrl());
            final String objName = getObjName(tenantId, Objects.equals(connectInfoEntity.getChannel(), ErpChannelEnum.CRM), entity.getObjApiName());
            copy.setObjName(objName);
        } else {
            copy.setObjName(getObjName(tenantId, false, entity.getObjApiName()));
        }

        List<IntegrationSimpleViewInfoResult> integrationResults=Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(entity.getRelatedPloyDetailId())){
            List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantIdAndId(tenantId, entity.getRelatedPloyDetailId());
            syncPloyDetailEntities.stream().forEach(item ->{
                IntegrationSimpleViewInfoResult integrationSimpleViewInfoResult=
                        IntegrationSimpleViewInfoResult.builder().integrationStreamName(item.getIntegrationStreamName()).id(item.getId()).build();
                integrationResults.add(integrationSimpleViewInfoResult);
            });
            copy.setIntegrationResults(integrationResults);
        }
        return copy;
    }

    private void fillQueryTaskDcIdAndActualApiName(String tenantId, QueryErpHistoryDataTasksArg arg, List<String> dataCenterIds, List<String> actualObjApiNames) {
        if(arg.getDataCenterObjApiNames()!=null){
            for (QueryErpHistoryDataTasksArg.DataCenterObjApiNames dataCenterObjApiName : arg.getDataCenterObjApiNames()) {
                if (StringUtils.isBlank(dataCenterObjApiName.getDataCenterId())) {
                    continue;
                }
                dataCenterIds.add(dataCenterObjApiName.getDataCenterId());

                if (CollectionUtils.isEmpty(dataCenterObjApiName.getErpSplitObjApiNames())) {
                    continue;
                }

                final ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterObjApiName.getDataCenterId());
                if (Objects.equals(connectInfoEntity.getChannel(), ErpChannelEnum.CRM)) {
                    // crm的中间对象就是真实对象
                    actualObjApiNames.addAll(dataCenterObjApiName.getErpSplitObjApiNames());
                } else {
                    List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).ListBySplit(tenantId, dataCenterObjApiName.getDataCenterId(), dataCenterObjApiName.getErpSplitObjApiNames());
                    actualObjApiNames.addAll(erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpRealObjectApiname).collect(Collectors.toList()));
                }
            }
       }
    }

    @Override
    public Result<QueryResult<List<ErpHistorySnapshotResult>>> queryErpTaskSnapshotList(String tenantId, String dcId, Integer userId, QueryErpHistoryDataTasksArg arg, String lang) {
        if(StringUtils.isAnyEmpty(arg.getId())){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        QueryResult<List<ErpHistorySnapshotResult>> queryResult = new QueryResult<>();
        queryResult.setPageNum(arg.getPageNum());
        queryResult.setPageSize(arg.getPageSize());
        Integer count = erpHistoryTaskLogDao.countTasklogById(tenantId, dcId, arg.getId());
        queryResult.setTotal(count);
        if(count==0){
            return Result.newSuccess(queryResult);
        }
        List<ErpHistoryTaskLog> erpHistoryTaskLogs = erpHistoryTaskLogDao.pageByFiltersByFilterInStatus(tenantId, dcId, arg.getId(), queryResult.getPageNum(), queryResult.getPageSize());

        List<ErpHistorySnapshotResult> resultList=Lists.newArrayList();
        for(ErpHistoryTaskLog erpHistoryTaskLog:erpHistoryTaskLogs){
            ErpHistorySnapshotResult erpHistorySnapshotResult = BeanUtil.copy(erpHistoryTaskLog, ErpHistorySnapshotResult.class);
            if(erpHistoryTaskLog.getTaskStatus()!=null){
                erpHistorySnapshotResult.setTaskStatusEnum(ErpHistoryDataTaskStatusEnum.getErpHistoryDataTaskStatus(erpHistoryTaskLog.getTaskStatus()));
                erpHistorySnapshotResult.setTaskStatusDesc(i18NStringManager.get(erpHistorySnapshotResult.getTaskStatusEnum().getI18nKey(),lang,tenantId,erpHistorySnapshotResult.getTaskStatusEnum().getDesc()));
            }
            if(erpHistoryTaskLog.getExecuteTime()!=null){
                erpHistorySnapshotResult.setExecuteTime(erpHistoryTaskLog.getExecuteTime().getTime());
                erpHistorySnapshotResult.setTaskStatusDesc(i18NStringManager.get(erpHistorySnapshotResult.getTaskStatusEnum().getI18nKey(),lang,tenantId,erpHistorySnapshotResult.getTaskStatusEnum().getDesc()));
            }
            resultList.add(erpHistorySnapshotResult);
        }
        queryResult.setDataList(resultList);
        return Result.newSuccess(queryResult);
    }

    @Override
    public Result<ErpHistoryDataTaskResult> queryErpHistoryDetail(String tenantId, Integer userId, BaseTaskArg arg, String lang) {
        if(StringUtils.isAllEmpty(arg.getId())){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        ErpHistoryDataTaskEntity erpHistoryDataTaskEntity = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(arg.getId());
        if(ObjectUtils.isEmpty(erpHistoryDataTaskEntity)){
            return Result.newSuccess();
        }
        ErpHistoryDataTaskResult copy = convert2ErpHistoryDataTaskResult(tenantId, erpHistoryDataTaskEntity, lang);
        return Result.newSuccess(copy);
    }

    private String getObjName(String tenantId, boolean crmObj, String objApiName) {
        if (StringUtils.isBlank(objApiName)) {
            return null;
        }
        if (crmObj) {
            final Map<String, String> stringStringMap = crmRemoteManager.listObjectNamesByApiNames(tenantId, Lists.newArrayList(objApiName));
            return stringStringMap.get(objApiName);
        }
        ErpObjectEntity byObjApiName = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByObjApiName(tenantId, null, objApiName);
        return Optional.ofNullable(byObjApiName).map(ErpObjectEntity::getErpObjectName).orElse(null);
    }


    @Override
    public Result<List<ErpHistoryDataTaskResult>> queryErpHistoryDataTaskByFakeObj(String tenantId,
                                                                                   String dcId,
                                                                                   Integer userId,
                                                                                   String erpFakeObjApiName,
                                                                                   String lang) {
        if(StringUtils.isBlank(erpFakeObjApiName)){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpObjectRelationshipEntity relationshipEntity = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, erpFakeObjApiName);
        if(relationshipEntity==null){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dcId);
        List<ErpHistoryDataTaskEntity> erpHistoryDataTaskEntities = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByRealObj(tenantId, dcId,relationshipEntity.getErpRealObjectApiname());
        if(CollectionUtils.isNotEmpty(erpHistoryDataTaskEntities)){
            List<ErpHistoryDataTaskResult> resultList=Lists.newArrayList();
            //有些历史任务没有在log表的，先查询pg，再查询mongo。后续可以去掉这个逻辑，查询mongo就行
            List<String> historyTask = erpHistoryDataTaskEntities.stream().map(ErpHistoryDataTaskEntity::getId).collect(Collectors.toList());
            List<ErpHistoryTaskLog> erpHistoryTaskLogs = historyTaskLogDao.limitCountLog(tenantId, dcId, historyTask);
            List<ErpHistoryTaskLog> entityLogs = erpHistoryDataTaskEntities.stream().map(item -> {
                ErpHistoryTaskLog historyTaskLog = BeanUtil.copy(item, ErpHistoryTaskLog.class);
                return historyTaskLog.convertTimeEntity(item);
            }).collect(Collectors.toList());
            entityLogs.addAll(erpHistoryTaskLogs);
            //再做一次排序
             Collections.sort(entityLogs, new Comparator<ErpHistoryTaskLog>() {
                @Override
                public int compare(ErpHistoryTaskLog log1, ErpHistoryTaskLog log2) {
                    return log2.getUpdateTime().compareTo(log1.getUpdateTime()); // 降序排序
                }
            });
            List<ErpHistoryDataTaskResult> erpHistoryDataTaskResults=BeanUtil.copyList(entityLogs,ErpHistoryDataTaskResult.class);
            return Result.newSuccess(erpHistoryDataTaskResults);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> deleteErpHistoryDataTask(String tenantId, Integer userId, BaseTaskArg deleteArg) {
        if(StringUtils.isBlank(deleteArg.getId())){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        int delete = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(deleteArg.getId());
        if(delete!=1){
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        erpHistoryTaskLogDao.deleteByTaskId(tenantId,deleteArg.getDataCenterId(),deleteArg.getId());
        return Result.newSuccess();
    }

    @Override
    public Result<ErpHistoryDataTaskResult> getErpHistoryDataTask(String tenantId, Integer userId, BaseArg idArg,String lang) {
        ErpHistoryDataTaskEntity byId = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(idArg.getId());
        if(byId==null){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpHistoryDataTaskResult copy = BeanUtil.copy(byId, ErpHistoryDataTaskResult.class);
        if(byId.getTaskStatus()!=null){
            copy.setTaskStatusEnum(ErpHistoryDataTaskStatusEnum.getErpHistoryDataTaskStatus(byId.getTaskStatus()));
            copy.setTaskStatusDesc(i18NStringManager.get(copy.getTaskStatusEnum().getI18nKey(),lang,tenantId,copy.getTaskStatusEnum().getDesc()));
        }
        if(StringUtils.isNotBlank(byId.getDataIds())){
            copy.setDataIds(JSONObject.parseArray(byId.getDataIds(),String.class));
        }
        if(byId.getTotalCostTime()!=null){
            String formatTotalTime = (byId.getTotalCostTime() / 1000) + "s";
            copy.setTotalCostTime(formatTotalTime);
        }
        return Result.newSuccess(copy);
    }

    @Override
    public Result<String> stopErpHistoryDataTask(String tenantId, Integer userId, BaseTaskArg baseTaskArg) {
        String remark=i18NStringManager.getByEi(I18NStringEnum.s3906,tenantId);
        int stopTask = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateRemarkAndStopById( baseTaskArg.getId(),remark, true,ErpHistoryDataTaskStatusEnum.STATUS_STOP.getStatus());
        if(stopTask!=1){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        erpHistoryTaskLogDao.stopLastTaskByTaskId(tenantId,baseTaskArg.getDataCenterId(),baseTaskArg.getId(),remark,true);
        return Result.newSuccess();
    }

    @Override
    public Result<String> reStartErpHistoryDataTask(String tenantId, Integer userId, ErpHistoryDataRestartTasksArg erpHistoryDataRestartTasksArg) {
        ErpHistoryDataTaskEntity taskEntity = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findById(erpHistoryDataRestartTasksArg.getId());
        List<Integer> taskStatusEnum = ErpTaskStatusArgEnum.existsExecuteTaskEnum().stream().map(ErpHistoryDataTaskStatusEnum::getStatus).collect(Collectors.toList());
        if(taskStatusEnum.contains(taskEntity.getTaskStatus())){
            return Result.newSystemError(I18NStringEnum.s3904);
        }

//        List<ErpHistoryDataTaskEntity> erpHistoryDataTaskEntities = erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByStatus(tenantId,
//                Lists.newArrayList(ErpHistoryDataTaskStatusEnum.STATUS_START.getStatus(),ErpHistoryDataTaskStatusEnum.STATUS_EXECUTING.getStatus()));
//        if(erpHistoryDataTaskEntities.size()>0){
//            return Result.newErrorByI18N(ResultCodeEnum.DATA_REPEAT_ERROR,
//                    "该企业已存在开启的同步历史数据任务，对象："+erpHistoryDataTaskEntities.get(0).getRealObjApiName(),
//                    I18NStringEnum.s361.getI18nKey(),
//                    Lists.newArrayList(erpHistoryDataTaskEntities.get(0).getRealObjApiName()));
//        }
        //重试任务数据的只能支持修改操作时间
        Long executeTime=ObjectUtils.isEmpty(erpHistoryDataRestartTasksArg.getExecuteTime())?System.currentTimeMillis():erpHistoryDataRestartTasksArg.getExecuteTime();
        String lockName="RESTART_TASK_ID"+erpHistoryDataRestartTasksArg.getId();
        boolean lockAccept=false;
        try {
            lockAccept=  redissonClient.getLock(lockName).tryLock();
            if(lockAccept){
                ErpHistoryDataTaskEntity erpHistoryDataTaskEntity= BeanUtil.copy(taskEntity,ErpHistoryDataTaskEntity.class);
                erpHistoryDataTaskEntity.setUpdateTime(System.currentTimeMillis());
                erpHistoryDataTaskEntity.setExecuteTime(executeTime);
                erpHistoryDataTaskEntity.setRemark(i18NStringManager.getByEi(I18NStringEnum.s3744, tenantId));
                erpHistoryDataTaskEntity.setLimit(null);
                erpHistoryDataTaskEntity.setOffset(null);
                erpHistoryDataTaskEntity.setTotalDataSize(null);
                erpHistoryDataTaskEntity.setTotalCostTime(null);
                erpHistoryDataTaskEntity.setLastQueryStartTime(null);
                erpHistoryDataTaskEntity.setNeedStop(false);
                erpHistoryDataTaskEntity.setTaskNum(CommonConstants.TASK_KEY + System.currentTimeMillis());
                erpHistoryDataTaskEntity.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());
                //创建一个任务快照
                ErpHistoryTaskLog erpHistoryTaskLog=BeanUtil.copy(erpHistoryDataTaskEntity,ErpHistoryTaskLog.class);
                erpHistoryTaskLog.setTaskId(erpHistoryDataTaskEntity.getId());
                erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).restartEntity(erpHistoryDataTaskEntity);
                //mongo创建
                //日期转换
                erpHistoryTaskLog=erpHistoryTaskLog.convertTimeEntity(erpHistoryDataTaskEntity);
                erpHistoryTaskLogDao.batchInsert(tenantId,Lists.newArrayList(erpHistoryTaskLog));

            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            redissonClient.getLock(lockName).unlock();
        }

//        erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateRemarkAndStopById(erpHistoryDataRestartTasksArg.getTaskId(),"重新开启任务，时间："+System.currentTimeMillis(),false);
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> brushHistoryTask(String tenantId) {
        //获取相关的datacenter
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listErpDcByTenantId(tenantId);
        List<String> erpDataCenterIds = erpConnectInfoEntities.stream().filter(item -> !item.getChannel().equals(ErpChannelEnum.CRM)).map(ErpConnectInfoEntity::getId).collect(Collectors.toList());
        //获取所有的erp对象
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listBySourceType(tenantId, TenantType.ERP);
        Map<String, List<SyncPloyDetailEntity>> syncPloyDetail = syncPloyDetailEntities.stream().collect(Collectors.groupingBy(u -> u.getSourceDataCenterId()));

        try {
            for (String erpDataCenterId : erpDataCenterIds) {
                if(erpDataCenterId.contains("643f7322b54ea80001767d86")){
                    log.info("");
                }
                List<SyncPloyDetailEntity> syncPloyDetailEntities1 = syncPloyDetail.get(erpDataCenterId);
                if(CollectionUtils.isEmpty(syncPloyDetailEntities1)){
                    continue;
                }
                Map<String, List<SyncPloyDetailEntity>> sourceObjAPINames = syncPloyDetailEntities1.stream()
                        .collect(Collectors.groupingBy(SyncPloyDetailEntity::getSourceObjectApiName));
                for (Map.Entry<String, List<SyncPloyDetailEntity>> erpObj : sourceObjAPINames.entrySet()) {
                    Set<String> collect1 = erpObj.getValue().stream().map(SyncPloyDetailEntity::getId).collect(Collectors.toSet());

                    ListStringData listStringData=new ListStringData();
                    listStringData.addAll(collect1);
                    String objKey=erpObj.getKey();
                    erpHistoryDataTaskDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateTaskDataCenterId(tenantId,erpDataCenterId,listStringData,erpObj.getKey());
                    log.info("update tenantid:{},erpobj:{}",tenantId,erpObj.getKey());
                }
            }
        } catch (Exception e) {
            log.info("error");
        }

        return Result.newSuccess();
    }
}
