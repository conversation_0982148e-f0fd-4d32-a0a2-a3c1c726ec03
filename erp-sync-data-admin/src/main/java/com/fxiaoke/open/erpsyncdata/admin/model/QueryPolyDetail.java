package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/3 14:56:13
 */
public interface QueryPolyDetail {

    @Data
    class Arg {
        /**
         * 来源中间对象apiName
         */
        private String sourceObjApiName;
        /**
         * 目标中间对象apiName
         */
        private String destObjApiName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        /**
         * 策略对象所含详情id
         */
        private String id;
        /**
         * 策企业id
         */
        private String tenantId;
        /**
         * 集成流名称
         */
        private String integrationStreamName;
        /**
         * 源数据中心
         */
        private String sourceDataCenterId;
        /**
         * 目标数据中心
         */
        private String destDataCenterId;
        /**
         * 策略状态 1.启用 2.停用 {@link  SyncPloyDetailStatusEnum}
         */
        private Integer status;
        /**
         * 企业类型 1.纷享 2.外部对接
         */
        private Integer sourceTenantType;
        /**
         * 源企业策略对象名称
         */
        private String sourceObjectApiName;
        /**
         * 企业类型 1.纷享 2.外部对接
         */
        private Integer destTenantType;
        /**
         * 源企业策略对象名称
         */
        private String destObjectApiName;
        /**
         * 执行策略是否有效 1.有效 2.无效
         */
        private Boolean isValid;
        /**
         * 备注
         */
        private String remark;
    }


}
