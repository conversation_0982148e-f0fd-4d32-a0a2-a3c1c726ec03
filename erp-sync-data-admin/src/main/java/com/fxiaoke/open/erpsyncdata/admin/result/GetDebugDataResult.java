package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
public class GetDebugDataResult implements Serializable {
    private StandardData standardData;
    //ERP对象编码
    private String number;
    //数据是从缓存中来还是从ERP系统中来
    private boolean dataFromErp;
    //对象id字段apiName
    private String idFieldApiName;
    //ERP接口连通结果
    private ErpInterfaceTestResult connectResult = new ErpInterfaceTestResult();
    //ERP接口获取数据结果
    private ErpInterfaceTestResult getDataResult = new ErpInterfaceTestResult();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErpInterfaceTestResult {
        private int errCode;
        private String errMsg = "";
    }
}
