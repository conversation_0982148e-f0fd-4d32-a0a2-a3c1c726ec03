package com.fxiaoke.open.erpsyncdata.admin.model.lexiang;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LeXiangDoc implements Serializable {
    private LeXiangData data;

    @Data
    public static class LeXiangData implements Serializable {
        private String type;
        private Attributes attributes;
        private Relationships relationships;

        @Data
        public static class Attributes implements Serializable {
            public static final String DefaultSignature = "吴贝贝";   // ignoreI18n  K吧乐享

            private String title;
            private String content;
            private int is_markdown;
            private int privilege_type;
            private String source;
            private String reship_url;
            private String signature;
            private int allow_comment;
            private String picture_url;
            private int only_team;
            private int enable_image_watermark;
            private int enable_watermark;
            private int enable_copy_limit;
        }

        @Data
        public static class DataItem implements Serializable {
            private String type;
            private String id;
        }

        @Data
        public static class Relationships implements Serializable {
            private Category category;
            private Team team;
            private Directory directory;
            private Privilege privilege;
            private Attachments attachments;
        }

        @Data
        public static class Team implements Serializable {
            private DataItem data;
        }

        @Data
        public static class Category implements Serializable {
            private DataItem data;
        }

        @Data
        public static class Directory implements Serializable {
            private DataItem data;
        }

        @Data
        public static class Privilege implements Serializable {
            private List<DataItem> data;
        }

        @Data
        public static class Attachments implements Serializable {
            private List<DataItem> data;
        }
    }

    public static void main(String[] args) {
        String json = "{\n" +
                "    \"data\":{\n" +
                "        \"type\":\"doc\",\n" +
                "        \"attributes\":{\n" +
                "            \"title\":\"通过开放接口创建的文档\",\n" +
                "            \"content\":\"<h1>富文本内容</h1>\",\n" +
                "            \"is_markdown\":0,\n" +
                "            \"privilege_type\":1,\n" +
                "            \"source\":\"reship\",\n" +
                "            \"reship_url\":\"https://lexiangla.com\",\n" +
                "            \"signature\":\"Peter Chan\",\n" +
                "            \"allow_comment\":1,\n" +
                "            \"picture_url\":\"https://lexiangla.com/assets/123\",\n" +
                "            \"only_team\":0,\n" +
                "            \"enable_image_watermark\":1,\n" +
                "            \"enable_watermark\":1,\n" +
                "            \"enable_copy_limit\":1\n" +
                "        },\n" +
                "        \"relationships\":{\n" +
                "            \"category\":{\n" +
                "                \"data\":{\n" +
                "                    \"type\":\"category\",\n" +
                "                    \"id\":\"uuid123\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"team\":{\n" +
                "                \"data\":{\n" +
                "                    \"type\":\"team\",\n" +
                "                    \"id\":\"uuid123\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"directory\":{\n" +
                "                \"data\":{\n" +
                "                    \"type\":\"directory\",\n" +
                "                    \"id\":\"uuid123\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"privilege\": {\n" +
                "                \"data\": [\n" +
                "                    {\n" +
                "                        \"type\": \"staff\",\n" +
                "                        \"id\": \"ThreeZhang\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"type\": \"department\",\n" +
                "                        \"id\": 1\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            \"attachments\": {\n" +
                "                \"data\": [\n" +
                "                    {\n" +
                "                        \"type\": \"attachment\",\n" +
                "                        \"id\": \"uuid123\"\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"type\": \"attachment\",\n" +
                "                        \"id\": \"uuid123\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}";
        LeXiangDoc doc = JSONObject.parseObject(json, LeXiangDoc.class);
        System.out.println(doc);
    }
}
