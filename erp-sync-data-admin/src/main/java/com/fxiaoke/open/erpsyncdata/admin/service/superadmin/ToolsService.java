package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

public interface ToolsService {
    /**
     *  迁移部门集成流数据到部门字段表
     * @param tenantId
     * @return
     */
    Result<Void> migrateDepartmentData(String tenantId, String dcId,boolean fsDataIdIsNumber);

    /**
     * 迁移员工集成流数据到员工字段表
     * @param tenantId
     * @return
     */
    Result<Void> migrateEmployeeData(String tenantId, String dcId,boolean fsDataIdIsNumber);
}
