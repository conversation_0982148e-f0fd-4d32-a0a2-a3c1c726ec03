package com.fxiaoke.open.erpsyncdata.admin.aop;

import com.fxiaoke.open.erpsyncdata.admin.service.RelationErpShardService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.AbstractReplaceEnterpriseAspect;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationManageGroupDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplatePloyDetailChangeEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/8 11:26:08
 * <p>
 * 监听模板企业的集成流和sync_time变更,同步下游
 */
@Slf4j
@Aspect
@Order
@Component
public class TemplateSyncTimeChangeAspect extends AbstractReplaceEnterpriseAspect {

    @Autowired
    @Lazy
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;

    @Autowired
    private RelationManageGroupDao relationManageGroupDao;

    @Autowired
    @Lazy
    private RelationErpShardService relationErpShardService;

    /**
     * 监听sync_time变更,发送mq,异步处理下游企业的sync_time
     */
    @After("execution(* com.fxiaoke.open.erpsyncdata.admin.service.InitLastSyncTimeService.initLastSyncTime(..))")
    public void notifyTemplateSyncTimeChange(JoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        String tenantId = (String) args[0];
        String objectApiName = (String) args[1];

        if (Objects.isNull(tenantId)) {
            return;
        }

        if (!isTemplateId(tenantId)) {
            return;
        }

        relationErpShardService.notifyTemplateSyncTimeChange(tenantId, objectApiName);
    }


    /**
     * 监听集成流的启用/停用,发送mq,异步处理下游企业的sync_time
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao.updateStatusById(String, String, Integer, Integer)) && args(tenantId, id, status, oldStatus)")
    public Object notifyTemplatePloyStatusChange(ProceedingJoinPoint jp, String tenantId, String id, Integer status, Integer oldStatus) throws Throwable {
        final int proceed = (int) jp.proceed();
        if (Objects.isNull(tenantId) || proceed < 1) {
            return proceed;
        }

        if (!isTemplateId(tenantId)) {
            return proceed;
        }

        final TemplatePloyDetailChangeEntity.Reason change1 = Objects.equals(status, SyncPloyDetailStatusEnum.ENABLE.getStatus()) ? TemplatePloyDetailChangeEntity.Reason.ENABLED : TemplatePloyDetailChangeEntity.Reason.DISABLED;
        relationErpShardService.notifyTemplatePloyStatusChange(tenantId, id, change1);

        return proceed;
    }

    /**
     * 监听集成流的删除,发送mq,异步处理下游企业的sync_time
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao.deleteByTenantAndId(String, String)) && args(tenantId, id))")
    public Object notifyTemplatePloyDelete(ProceedingJoinPoint jp, String tenantId, String id) throws Throwable {
        final int proceed = (int) jp.proceed();
        if (Objects.isNull(tenantId) || proceed < 1) {
            return proceed;
        }

        if (!isTemplateId(tenantId)) {
            return proceed;
        }

        relationErpShardService.notifyTemplatePloyStatusChange(tenantId, id, TemplatePloyDetailChangeEntity.Reason.DELETED_PLOY_DETAIL);

        return proceed;
    }


    /**
     * 检查是否为模板企业
     */
    protected boolean isTemplateId(String tenantId) {
        return StringUtils.isNotBlank(tenantId) && relationManageGroupDao.isTemplateId(tenantId);
    }
}
