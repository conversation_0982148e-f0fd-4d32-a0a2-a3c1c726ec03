package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.model.ObjectFieldAnalysisResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ObjectFieldAnalysisService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class ObjectFieldAnalysisServiceImpl implements ObjectFieldAnalysisService {
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Override
    public Result<ObjectFieldAnalysisResult> analysisObjectFields(String tenantId,String sourceObjectApiName,Integer sourceTenantType) {
        sourceObjectApiName = StringUtils.isEmpty(sourceObjectApiName)?null:sourceObjectApiName;
        sourceTenantType = sourceTenantType == null ? 2 : sourceTenantType;
        List<SyncPloyDetailEntity> entityList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBy(sourceObjectApiName,sourceTenantType,1);
        ObjectFieldAnalysisResult result = new ObjectFieldAnalysisResult();
        for(SyncPloyDetailEntity entity : entityList) {
            ObjectFieldAnalysisResult.ObjectAnalysisModel objectAnalysisModel = getObjectAnalysisModel(entity.getSourceObjectApiName(),
                    result.getObjectList());
            for(FieldMappingData fieldMappingData : entity.getFieldMappings()) {
                updateFieldAnalysisModel(fieldMappingData,objectAnalysisModel.getFieldList());
            }
            updateObjectAnalysisModel(entity.getDetailObjectMappings(),objectAnalysisModel.getDetails());
            Collections.sort(objectAnalysisModel.getFieldList(),(o1, o2) -> o2.getCount() - o1.getCount());
        }
        Collections.sort(result.getObjectList(),(o1, o2) -> o2.getCount() - o1.getCount());
        return Result.newSuccess(result);
    }

    private void updateObjectAnalysisModel(DetailObjectMappingsData detailObjectMappingsData,
                                           List<ObjectFieldAnalysisResult.ObjectAnalysisModel> details) {
        for(DetailObjectMappingsData.DetailObjectMappingData mappingData : detailObjectMappingsData) {
            ObjectFieldAnalysisResult.ObjectAnalysisModel objectAnalysisModel = getObjectAnalysisModel(mappingData.getSourceObjectApiName(),
                    details);
            for(FieldMappingData fieldMappingData : mappingData.getFieldMappings()) {
                updateFieldAnalysisModel(fieldMappingData,objectAnalysisModel.getFieldList());
            }
            Collections.sort(objectAnalysisModel.getFieldList(),(o1, o2) -> o2.getCount() - o1.getCount());
        }
    }

    private void updateDestFieldAnalysisModel(FieldMappingData fieldMappingData,
                                              List<ObjectFieldAnalysisResult.DestFieldAnalysisModel> destFieldList) {
        ObjectFieldAnalysisResult.DestFieldAnalysisModel destFieldAnalysisModel = null;
        for(ObjectFieldAnalysisResult.DestFieldAnalysisModel model : destFieldList) {
            if(model.getFieldApiName().equalsIgnoreCase(fieldMappingData.getDestApiName())) {
                destFieldAnalysisModel = model;
                break;
            }
        }

        if(destFieldAnalysisModel==null) {
            destFieldAnalysisModel = new ObjectFieldAnalysisResult.DestFieldAnalysisModel();
            destFieldAnalysisModel.setFieldApiName(fieldMappingData.getDestApiName());
            destFieldAnalysisModel.setFieldMappingData(fieldMappingData);
            destFieldList.add(destFieldAnalysisModel);
        }
        destFieldAnalysisModel.setCount(destFieldAnalysisModel.getCount()+1);
    }

    private void updateFieldAnalysisModel(FieldMappingData fieldMappingData,
                                          List<ObjectFieldAnalysisResult.FieldAnalysisModel> fieldList) {
        ObjectFieldAnalysisResult.FieldAnalysisModel fieldAnalysisModel = null;
        for(ObjectFieldAnalysisResult.FieldAnalysisModel model : fieldList) {
            if(StringUtils.isEmpty(model.getFieldApiName())) {
                if(StringUtils.isEmpty(model.getFieldValue())) continue;
                if(model.getFieldValue().equalsIgnoreCase(fieldMappingData.getValue())) {
                    fieldAnalysisModel = model;
                    break;
                } else continue;
            }
            if(model.getFieldApiName().equalsIgnoreCase(fieldMappingData.getSourceApiName())) {
                fieldAnalysisModel = model;
                break;
            }
        }

        if(fieldAnalysisModel==null) {
            fieldAnalysisModel = new ObjectFieldAnalysisResult.FieldAnalysisModel();
            if(StringUtils.isEmpty(fieldMappingData.getSourceApiName())) {
                fieldAnalysisModel.setFieldValue(fieldMappingData.getValue());
            } else {
                fieldAnalysisModel.setFieldApiName(fieldMappingData.getSourceApiName());
            }

            updateDestFieldAnalysisModel(fieldMappingData,fieldAnalysisModel.getDestFieldList());
            fieldList.add(fieldAnalysisModel);
        }
        fieldAnalysisModel.setCount(fieldAnalysisModel.getCount()+1);
    }

    private ObjectFieldAnalysisResult.ObjectAnalysisModel getObjectAnalysisModel(String objectApiName,
                                                                                 List<ObjectFieldAnalysisResult.ObjectAnalysisModel> objectList) {
        ObjectFieldAnalysisResult.ObjectAnalysisModel objectAnalysisModel=null;
        for(ObjectFieldAnalysisResult.ObjectAnalysisModel model : objectList) {
            if(model.getObjectApiName().equalsIgnoreCase(objectApiName)) {
                objectAnalysisModel = model;
                break;
            }
        }
        if(objectAnalysisModel==null) {
            objectAnalysisModel = new ObjectFieldAnalysisResult.ObjectAnalysisModel();
            objectAnalysisModel.setObjectApiName(objectApiName);
            objectList.add(objectAnalysisModel);
        }
        objectAnalysisModel.setCount(objectAnalysisModel.getCount()+1);

        return objectAnalysisModel;
    }
}
