package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import cn.hutool.core.thread.ThreadUtil;
import com.fxiaoke.open.erpsyncdata.admin.model.SendMsgHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.PlusTenantConfigManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.DispatcherMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SendEventMqRecord;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.github.mybatis.local.TenantThreadLocal;
import com.google.common.collect.Lists;
import com.mongodb.client.FindIterable;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/24
 */
@Slf4j
@Service
public class OpsToolServiceImpl implements OpsToolService {
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private ProbeErpDataService probeErpDataServiceRest;
    @Autowired
    private DispatcherMongoStore dispatcherMongoStore;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static final ExecutorService executorService = new ThreadPoolExecutor(10, 100,
            60L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(5000),
            new NamedThreadFactory("opstool", false));

    @Override
    public Result<Void> syncAllNoTriggerErpTemp(String tenantId, String dcId, String realObjApiName, String ltId, String gtId, SendMsgHelper sendMsgHelper) {
        TenantThreadLocal.set(tenantId);
        sendMsgHelper.getSendTextNoticeArg().setDataCenterId(dcId);
        String splitObjApiName = erpObjManager.getMasterSplitObjApiName(tenantId, dcId, realObjApiName);
        List<SyncPloyDetailEntity> ployDetails = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listBySourceTenantTypeAndObjApiName(tenantId, TenantType.ERP, splitObjApiName);
        AssertUtil.notEmpty(ployDetails, I18NStringEnum.s227,tenantId);
        //获取最新的id
        if (ltId == null) {
            List<Document> firstData = erpTempDataDao.listErpTempByIdFilter(tenantId, dcId, realObjApiName, gtId, null, 1);
            AssertUtil.notEmpty(firstData, I18NStringEnum.s228,tenantId);
            Document firstDatum = firstData.get(0);
            String latestMongoId = firstDatum.getObjectId("_id").toString();
            ltId = latestMongoId;
        }
        sendMsgHelper.newMsg(String.format("objApiName:%s,ltId:%s", realObjApiName, ltId))
                .appendWithTime(i18NStringManager.getByEi(I18NStringEnum.s3756, tenantId))
                .append("traceId:" + TraceUtil.get());
        notificationService.sendErpSyncDataAppNotice(sendMsgHelper.getSendTextNoticeArg(),
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
        Set<String> failedIds = new HashSet<>();
        AtomicLong countSend = new AtomicLong();
        sendMsgHelper.newMsg();
        for (int i = 0; i < 10000; i++) {
            //一次1000万最多
            List<Document> erpTempList = erpTempDataDao.listErpTempByIdFilter(tenantId, dcId, realObjApiName, gtId, ltId, 1000);
            if (erpTempList.isEmpty()) {
                sendMsgHelper.append(i18NStringManager.getByEi2(I18NStringEnum.s3692, tenantId, ltId, gtId));
                notificationService.sendErpSyncDataAppNotice(sendMsgHelper.getSendTextNoticeArg(),
                        AlarmRuleType.OTHER,
                        AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                        AlarmType.OTHER,
                        AlarmLevel.GENERAL);
                return Result.newSuccess();
            }
            Set<String> erpIds = erpTempList.stream().map(v -> v.getString("data_id")).collect(Collectors.toSet());
            try {
                List<String> processFailed = process(tenantId, ployDetails, erpIds, countSend);
                failedIds.addAll(processFailed);
            } catch (InterruptedException e) {
                log.info("time out,ids:{}", erpIds);
                failedIds.addAll(erpIds);
            }
            Document minIdResult = CollUtil.getLast(erpTempList);
            String minId = minIdResult.getObjectId("_id").toString();
            ltId = minId;
            if (failedIds.size() >= 5000) {
                log.info("failed more than 5000,minId:{}", minId);
                sendMsgHelper.appendWithTime(i18NStringManager.getByEi2(I18NStringEnum.s3693, tenantId, failedIds.toArray(new String[0])));
                notificationService.sendErpSyncDataAppNotice(sendMsgHelper.getSendTextNoticeArg(),
                        AlarmRuleType.OTHER,
                        AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                        AlarmType.OTHER,
                        AlarmLevel.GENERAL);
               return Result.newSystemError(I18NStringEnum.s3671);
            }
            if (countSend.get() > 100000) {
                //已发送10万数据，停一下
                log.info("send more than 100000,minId:{}", minId);
                sendMsgHelper.appendWithTime(i18NStringManager.getByEi2(I18NStringEnum.s3694, tenantId, minId));
                notificationService.sendErpSyncDataAppNotice(sendMsgHelper.getSendTextNoticeArg(),
                        AlarmRuleType.OTHER,
                        AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                        AlarmType.OTHER,
                        AlarmLevel.GENERAL);
                for (int j = 0; j < 11; j++) {
                    if (j == 10) {
                        log.info("waited 10 times,minId:{}", minId);
                        sendMsgHelper.appendWithTime(i18NStringManager.getByEi2(I18NStringEnum.s3695, tenantId, minId));
                        return Result.newSystemError(I18NStringEnum.s3672);
                    }
                    //睡眠10分钟，最多十次
                    ThreadUtil.safeSleep(600000);
                    Long count = dispatcherMongoStore.getStoreCollectionCountLimit(tenantId);
                    if (count < 1000) {
                        sendMsgHelper.appendWithTime(i18NStringManager.getByEi2(I18NStringEnum.s3696, tenantId, minId, String.valueOf(count)));
                        countSend.set(0);
                        break;
                    } else {
                        sendMsgHelper.appendWithTime(i18NStringManager.getByEi2(I18NStringEnum.s3697, tenantId, String.valueOf(count)));
                        notificationService.sendErpSyncDataAppNotice(sendMsgHelper.getSendTextNoticeArg(),
                                AlarmRuleType.OTHER,
                                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                                AlarmType.OTHER,
                                AlarmLevel.GENERAL);
                    }
                }
            }
            log.info("opstool process ,minId:{}", minId);
            if (i % 100 == 0) {
                //每十万记录一次,百万发送一次消息
                sendMsgHelper.appendWithTime(i18NStringManager.getByEi2(I18NStringEnum.s3698, tenantId, minId));
                if (i % 1000 == 0) {
                    notificationService.sendErpSyncDataAppNotice(sendMsgHelper.getSendTextNoticeArg(),
                            AlarmRuleType.OTHER,
                            AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                            AlarmType.OTHER,
                            AlarmLevel.GENERAL);
                    sendMsgHelper.newMsg();
                }
            }
        }
        sendMsgHelper.appendWithTime(i18NStringManager.getByEi2(I18NStringEnum.s3699, tenantId, ltId));
        notificationService.sendErpSyncDataAppNotice(sendMsgHelper.getSendTextNoticeArg(),
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
        return Result.newSystemError(I18NStringEnum.s3673);
    }

    @Override
    public Result<Integer> scanErpTempBetweenId(String tenantId, Long startTime, Long endTime) {
        Integer notifyCount = plusTenantConfigManager.getGlobalTypeConfig(TenantConfigurationTypeEnum.SCAN_NO_TRIGGER_TEMP_NOTIFY_COUNT, Integer.class, 1);
        Integer limitCount = plusTenantConfigManager.getGlobalTypeConfig(TenantConfigurationTypeEnum.SCAN_NO_TRIGGER_TEMP_LIMIT_COUNT, Integer.class, 10000);
        FindIterable<Document> findIterable = erpTempDataDao.scanNotTrigger(tenantId,
                new ObjectId(new Date(startTime)), new ObjectId(new Date(endTime)));
        Integer counter = 0;
        List<ObjectId> updateTemp = new ArrayList<>();
        Integer batchLimit = 1000;
        for (Document document : findIterable) {
            counter++;
            ObjectId id = document.getObjectId("_id");
            updateTemp.add(id);
            if (updateTemp.size() >= batchLimit) {
                updateLastSyncTime(tenantId, updateTemp);
                updateTemp.clear();
            }
            if (counter.equals(limitCount)) {
                log.warn("scan limit");
                break;
            }
        }
        if (!updateTemp.isEmpty()){
            //循环后更新一次
            updateLastSyncTime(tenantId, updateTemp);
        }
        if (counter >= notifyCount) {
            SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder()
                    .tenantId(tenantId).msgTitle(i18NStringManager.getByEi(I18NStringEnum.s3757, tenantId))
                    .msg(i18NStringManager.getByEi(I18NStringEnum.s3758, tenantId) + counter).build();
            notificationService.sendSuperAdminNotice(sendAdminNoticeArg);
        }
        return Result.newSuccess(counter);
    }

    private void updateLastSyncTime(String tenantId, List<ObjectId> updateTemp) {
        //推迟到30分钟后执行
        Long lastSyncTime = System.currentTimeMillis() + 30 * 60 * 1000;
        UpdateResult updateResult = erpTempDataDao.batchUpdateLastSyncTime(tenantId, updateTemp, lastSyncTime);
        log.info("update lastSyncTime,ids:{},lastSyncTime:{}，result:{}", updateTemp, lastSyncTime,updateResult);
    }

    private List<String> process(String tenantId, List<SyncPloyDetailEntity> ployDetails, Set<String> erpIds, AtomicLong countSend) throws InterruptedException {
        String sourceObjectApiName = ployDetails.get(0).getSourceObjectApiName();
        //检查数据映射是否已经成功
        Set<String> needResyncIds = new HashSet<>();
        for (SyncPloyDetailEntity ployDetail : ployDetails) {
            Set<String> successfulIds = syncDataMappingsDao.setTenantId(tenantId).listSuccessBySrcIds(tenantId, ployDetail.getSourceObjectApiName(), ployDetail.getDestObjectApiName(), erpIds);
            erpIds.stream().filter(v -> !successfulIds.contains(v)).forEach(v -> needResyncIds.add(v));
        }
        if (needResyncIds.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> failedIds = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(needResyncIds.size());
        countSend.addAndGet(needResyncIds.size());
        for (String needResyncId : needResyncIds) {
            executorService.submit(() -> {
                try {
                    boolean resync = resync(tenantId, sourceObjectApiName, needResyncId);
                    if (!resync) {
                        failedIds.add(needResyncId);
                    }
                } catch (Exception e) {
                    log.info("opstool error,", e);
                    failedIds.add(needResyncId);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await(5000, TimeUnit.SECONDS);
        return failedIds;
    }

    private boolean resync(String tenantId, String objApiName, String srcId) {
        //如果源企业是erp，从新拉取erp最新数据发送mq,eventTriggerService会找到最新策略id
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setObjAPIName(objApiName);
        erpIdArg.setIncludeDetail(true);
        erpIdArg.setDataId(srcId);
        Result<SyncDataContextEvent> erpObjDataRes = erpDataPreprocessService.getErpObjDataFromMongoIfExist(erpIdArg);
        if (erpObjDataRes.isSuccess() && erpObjDataRes.getData() != null) {
            Result<SendEventMqRecord> record = probeErpDataServiceRest.batchSendErpDataMqByContext(Collections.singletonList(erpObjDataRes.getData()), true);
            //成功
            return record.isSuccess() && record.getData().getFailedNum() < 1;
        }
        return false;
    }
}