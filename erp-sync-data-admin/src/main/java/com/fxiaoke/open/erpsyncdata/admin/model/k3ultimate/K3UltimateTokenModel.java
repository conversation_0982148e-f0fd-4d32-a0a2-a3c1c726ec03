package com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 云星空旗舰版token模型
 */
@Data
public class K3UltimateTokenModel implements Serializable {
    private String tenantId;
    private String dataCenterId;
    private String erpObjApiName;
    private String version;
    private String timestamps;

    /**
     * 验证参数是否合法
     *
     * @param model
     * @return
     */
    public static boolean verify(K3UltimateTokenModel model) {
        if (model == null
                || StringUtils.isEmpty(model.getTenantId())
                || StringUtils.isEmpty(model.getDataCenterId())
                || StringUtils.isEmpty(model.getErpObjApiName())
                || StringUtils.isEmpty(model.getVersion())
                || StringUtils.isEmpty(model.getTimestamps())) {
            return false;
        }
        return true;
    }

    /**
     * 拼接原始字符串
     *
     * @param model
     * @return
     */
    public static String concat(K3UltimateTokenModel model) {
        return model.getTenantId() + "-" + model.getDataCenterId() + "-" + model.getErpObjApiName() + "-" + model.getVersion() + "-" + model.getTimestamps();
    }
}
