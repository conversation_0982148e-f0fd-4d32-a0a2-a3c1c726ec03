package com.fxiaoke.open.erpsyncdata.admin.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;

public enum UserOperatorModuleEnum {

    INTEGRATION_STREAM("集成流", I18NStringEnum.s767.getI18nKey()),

    TENANT_CONFIG_OPERATION("企业环境操作",I18NStringEnum.s1024.getI18nKey());

    private String description;
    private String i18nKey;

    public String getDescription(I18NStringManager i18NStringManager,String lang,String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,description);
    }

    UserOperatorModuleEnum(String description,String i18nKey) {
        this.description = description;
        this.i18nKey = i18nKey;
    }

}
