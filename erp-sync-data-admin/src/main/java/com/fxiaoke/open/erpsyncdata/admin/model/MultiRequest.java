package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.Data;

import java.util.LinkedHashSet;
import java.util.Map;

/**
 * 多云请求
 * <AUTHOR> (^_−)☆
 */
@Data
public class MultiRequest {
    /**
     * 请求，只支持get和post
     */
    private String method;
    /**
     * 路径，包括query param
     */
    private String path;
    /**
     * 请求体
     */
    private Object requestBody;

    /**
     * 请求头，不支持重复key
     */
    private Map<String, String> headerMap;

    /**
     * 限制执行云环境，多选，为空查所有
     */
    private LinkedHashSet<String> domains;
}
