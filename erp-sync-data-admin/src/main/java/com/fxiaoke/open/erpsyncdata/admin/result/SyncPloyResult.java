package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class SyncPloyResult implements Serializable {
    @ApiModelProperty("同步策略id")
    private String id;
    @ApiModelProperty("1 erp->crm 2 crm->rep")
    private Integer type;
    @ApiModelProperty("策略所属的数据中心ids")
    private ListStringData dataCenterIds;
    @ApiModelProperty("数据来源哪个应用")
    private String appId;
    @ApiModelProperty("策略名称")
    private String name;
    @ApiModelProperty("主对象")
    private SyncObjectResult syncObject;
    @ApiModelProperty("从对象列表")
    private List<SyncObjectResult> detailSyncObjects;
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 给当前同步策略添加一个从对象
     * @param syncObject
     */
    public void addDetailObjectApiName(SyncObjectResult syncObject){
        if(detailSyncObjects == null){
            detailSyncObjects = new ArrayList<>();
        }
        detailSyncObjects.add(syncObject);
    }
}
