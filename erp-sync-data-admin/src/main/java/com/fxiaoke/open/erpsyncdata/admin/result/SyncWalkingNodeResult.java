package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/6 14:30 链路节点
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class SyncWalkingNodeResult implements Serializable {

    @ApiModelProperty("同步节点名称")
    private String nodeName;
    @Builder.Default
    @ApiModelProperty("同步节点次序")
    private Integer nodeOrder=1;
    @ApiModelProperty("同步节点类型")
    /**
     * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum
     */
    private String nodeType;
    @ApiModelProperty("是否可以查询日志")
    private Boolean canQueryLog;
    @ApiModelProperty("图标")
    private String icon;
    @ApiModelProperty("子节点")
    private List<SyncWalkingNodeResult> childNodes;
}
