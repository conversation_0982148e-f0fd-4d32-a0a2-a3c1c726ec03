package com.fxiaoke.open.erpsyncdata.admin.model;

import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/21
 */
@Data
public class MappingCountVo {
    private Long countFromStatTable;
    private Long countFromPgClass;


    private Long countDirect;
    private boolean match = false;
    /**
     * 是否需要执行vacuum
     */
    private boolean needVacuum = false;

    public boolean isMatch() {
        Long statCount = getStatCount();
        if (statCount == null || countDirect == null) {
            return false;
        }
        return Math.abs(countDirect - statCount) < 1000;
    }

    public Long getStatCount() {
        return countFromStatTable != null ? countFromStatTable : countFromPgClass;
    }


    public void setCountFromPgClass(Long countFromPgClass) {
        this.countFromPgClass = countFromPgClass;
        if (countFromStatTable == null && countFromPgClass > 100000) {
            this.needVacuum = true;
        }
    }

    public void setCountDirect(Long countDirect) {
        this.countDirect = countDirect;
        this.match = isMatch();
    }
}
