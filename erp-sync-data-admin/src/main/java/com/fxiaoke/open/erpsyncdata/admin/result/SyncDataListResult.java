package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class SyncDataListResult implements Serializable {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("id")
    private String syncDataId;
    @ApiModelProperty("日志id")
    private String syncLogId;
    @ApiModelProperty("源数据id")
    private String sourceDataId;
    @ApiModelProperty("源数据主属性")
    private String sourceDataName;
    @ApiModelProperty("目标数据id")
    private String destDataId;
    @ApiModelProperty("操作人员工id")
    private String operatorId;
    @ApiModelProperty("操作人名称")
    private String operatorName;
    @ApiModelProperty("源行为的类型")
    private Integer sourceEventType;
    @ApiModelProperty("源行为的名称")
    private String sourceEventTypeName;
    @ApiModelProperty("目标行为的类型")
    private Integer destEventType;
    @ApiModelProperty("目标行为的名称")
    private String destEventTypeName;
    /**
     * {@link SyncStatusEnum}
     */
    @ApiModelProperty("同步状态")
    private Integer status;
    @ApiModelProperty("同步状态名称")
    private String statusName;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("操作时间")
    private Long updateTime;

}
