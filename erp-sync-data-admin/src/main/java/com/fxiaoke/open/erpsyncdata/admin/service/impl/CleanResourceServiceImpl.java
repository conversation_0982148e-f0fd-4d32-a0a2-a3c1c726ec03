package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.GroupTimeInterval;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.admin.service.CleanResourceService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigRouteManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static jodd.util.ThreadUtil.sleep;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/5/30
 */
@Service
@Slf4j
public class CleanResourceServiceImpl implements CleanResourceService {
    @Autowired
    private ErpTableDao tableDao;
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    @Override
    public Result<Dict> cleanDeletedTables(int needDeleteBeforeDay) {
        //查找所有数据源
        Map<String, List<String>> db2Tenant = configRouteManager.getDb2Tenants();
        //因为这里只是直接展示，用了map做返回值。
        Dict result = new Dict();
        db2Tenant.forEach((db, eis) -> {
            log.info("begin clean {}", db);
            //取一个表路由的tenantId来获取表
            String dbTenantId = eis.get(0);
            //_需要转义
            List<String> deletedTables = tableDao.setTenantId(dbTenantId).listAllTableLeftMatching("deleted\\_\\_");
            List<String> dropTables = new ArrayList<>();
            List<String> waitingTables = new ArrayList<>();
            List<String> exceptionTables = new ArrayList<>();
            //允许删除的日期,10天前
            DateTime canDropDate = DateTime.now().offset(DateField.DAY_OF_YEAR, needDeleteBeforeDay * -1);
            for (String deletedTable : deletedTables) {
                try {
                    String date = StrUtil.subBefore(StrUtil.removePrefix(deletedTable, "deleted__"), "_", false);
                    DateTime deleteDate = DateUtil.parse(date, "yyyyMMdd");
                    if (deleteDate.isBefore(canDropDate)) {
                        String sql = String.format("drop table if exists %s;", deletedTable);
                        tableDao.setTenantId(dbTenantId).superUpdateSql(sql);
                        dropTables.add(deletedTable);
                    } else {
                        waitingTables.add(deletedTable);
                    }
                } catch (Exception e) {
                    log.error("clean exception", e);
                    exceptionTables.add(deletedTable);
                }
            }
            Dict dbResult = Dict.of();
            dbResult.put("dropTables", dropTables);
            dbResult.put("waitingTables", waitingTables);
            dbResult.put("exceptionTables", exceptionTables);
            result.put(db, dbResult);
        });
        return Result.newSuccess(result);
    }

    @Override
    public Result<Dict> cleanDeletedData(List<String> tenantIds) {
        if(CollectionUtils.isEmpty(tenantIds)){
            tenantIds =erpConnectInfoDao.listTenantId();//查找所有企业
        }
        List<String> createdIndexFailed= Lists.newArrayList();
        List<String> deletedIndexFailed= Lists.newArrayList();
        //先创建索引
        for(String ei:tenantIds){
            try {
                String sql="CREATE index concurrently if not exists \"mappings_is_deleted_{tenantId}\" ON sync_data_mappings_{tenantId} (id) where is_deleted = true;".replaceAll("\\{tenantId}", ei);
                tableDao.setTenantId(ei).superQuerySql(sql,ei);
            }catch (Exception e){
                createdIndexFailed.add(ei);
                log.error("CREATE index e={}",e);
            }
        }
        //因为这里只是直接展示，用了map做返回值。
        Dict result = new Dict();
        long allCount = 0L;
        long eiCount;
        GroupTimeInterval gti = new GroupTimeInterval(false);
        gti.start("all");
        for (String ei : tenantIds) {
                log.info("begin clean ei {}", ei);
                try {
                    if (!NumberUtil.isInteger(ei)) {
                        log.info("invalid ei {}", ei);
                        break;
                    }
                    gti.start("ei");
                    eiCount = 0L;
                    String minId = "0";
                    for (; ; ) {
                        gti.start("once");
                        List<String> deletedIds = syncDataMappingsDao.queryDeletedData(ei, minId);
                        if (deletedIds.isEmpty()) {
                            log.info("clean ei end {},ei count:{},cost:{},all count:{},cost:{}",
                                    ei, eiCount, gti.intervalPretty("ei"), allCount, gti.intervalPretty("all"));
                            break;
                        }
                        minId = deletedIds.stream().max(Comparator.comparing(v -> v)).get();
                        //实际删除未使用ei作为条件
                        int i = syncDataMappingsDao.deleteByIds(ei, deletedIds);
                        eiCount += i;
                        allCount += i;
                        log.info("clean ei {} {} {},ei count:{},cost:{},all count:{},cost:{}",
                                ei, i, gti.intervalPretty("once"), eiCount, gti.intervalPretty("ei"),
                                allCount, gti.intervalPretty("all"));
                    }
                    result.put(ei, eiCount);
                } catch (Exception e) {
                    log.info("clean ei {} exception", ei, e);
                    //noinspection unchecked
                    ((Set<String>) result.computeIfAbsent("exceptionEi", k -> new LinkedHashSet<>())).add(ei);
                }

            }
        result.put("all", allCount);
        //删除索引
        for(String ei:tenantIds){
            try {
                String sql = String.format("drop index if exists %s", "mappings_is_deleted_"+ei);
                tableDao.setTenantId(ei).superQuerySql(sql,ei);
            }catch (Exception e){
                deletedIndexFailed.add(ei);
                log.error("drop index e={}",e);
            }
        }
        result.put("createdIndexFailed",createdIndexFailed);
        result.put("deletedIndexFailed",deletedIndexFailed);
        return Result.newSuccess(result);
    }
}
