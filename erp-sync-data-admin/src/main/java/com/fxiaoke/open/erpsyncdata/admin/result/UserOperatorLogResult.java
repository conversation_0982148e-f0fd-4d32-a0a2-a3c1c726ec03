package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

@ApiModel("操作日志信息")
@Data
public class UserOperatorLogResult implements Serializable {

    private static final long serialVersionUID = -8139602437071240813L;
    @ApiModelProperty("操作时间,单位：毫秒")
    private Long operatorTime;
    @ApiModelProperty("操作人")
    private String userName;
    @ApiModelProperty("操作动作")
    private String action;
    @ApiModelProperty("操作模块")
    private String moduleName;
    @ApiModelProperty("操作模块id")
    private String moduleId;
    @ApiModelProperty("操作信息")
    private String message;

    @ApiModelProperty("快照数据")
    private String snapshotData;

}
