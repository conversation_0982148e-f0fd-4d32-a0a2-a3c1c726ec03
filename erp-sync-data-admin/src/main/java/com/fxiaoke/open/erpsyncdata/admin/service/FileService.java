package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum;
import com.fxiaoke.open.erpsyncdata.admin.model.ImportIntegrationStreamMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CrmSpecialFieldExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ExportSyncDataMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Validated
public interface FileService {

    /**
     * 构建excel表格，并上传到临时文件，返回路径
     * 仅支持一次传输数据，单个sheet
     *
     * @param <R>
     * @param buildExcelArg
     * @return
     */
    <R> Result<BuildExcelFile.Result> buildExcelFile(BuildExcelFile.Arg<R> buildExcelArg,String lang);


    /**
     * 构建excel表格，并上传到临时文件，返回路径
     * 仅支持一次传输数据，单个sheet
     *
     * @param ea
     * @param arg
     * @return
     */
    Result<BuildExcelFile.Result> buildExcelTemplate(@NotNull String ea, @NotNull ImportExcelFile.FieldDataMappingArg arg);

    /**
     * 获取crm国家省市区
     * @param tenantId
     * @param userId
     * @return
     */
    Result<List<CrmSpecialFieldExcelVo>> getCrmDistrictExcel(@NotNull Integer tenantId, @NotNull Integer userId,String lang);

    Result<List<CrmSpecialFieldExcelVo>> queryCountryAreas(@NotNull String tenantId, @NotNull Integer userId, List<String> districtCodes, String lang);

    Result<List<CrmSpecialFieldExcelVo>> getCrmDeptExcel(@NotNull Integer tenantId, @NotNull Integer userId,String lang);

    /**
     * 构建对象映射表格模板，并上传到临时文件，返回路径
     *
     * @param tenantId
     * @return
     */
    Result<BuildExcelFile.Result> buildObjectDataMappingTemplate(String tenantId,String ployDetailId,String lang);

    /**
     * 生成对象字段模板
     * @return
     */
    Result<BuildExcelFile.Result> buildObjectFieldTemplate(String tenantId,String dataCenterId,String realObjectApiName, String lang);

    /**
     * 导入excel文件
     * @param arg
     * @return
     * @throws IOException
     */
    Result<ImportExcelFile.Result> importExcelFile(@Valid ImportExcelFile.FieldDataMappingArg arg,String dataCenterId,String lang) throws IOException;

    /**
     * 导入对象数据映射
     * 对大文件可能导致oom,建议使用下面的方法
     * @see FileService#importObjectDataMapping(String, Integer, String, ExcelTypeEnum, InputStream, String, String)
     * @param arg
     * @return
     * @throws IOException
     */
    @Deprecated
    Result<ImportExcelFile.Result> importObjectDataMapping(@Valid ImportExcelFile.ObjectDataMappingArg arg,String lang) throws IOException;

    Result<ImportExcelFile.Result> importObjectDataMapping(String tenantId, Integer userId, String ployDetailId, ExcelTypeEnum excelType, InputStream inputStream, String fileType, String lang);

    /**
     * 导出数据维护
     * @param arg
     * @param dataCenterId
     * @return
     */
    Result<ExportSyncDataMapping.Result> asyncExportSyncDataMappingData(ExportSyncDataMapping.ExportSyncDataMappingArg arg, String dataCenterId,String lang);

    Result<BuildExcelFile.Result> buildDataVerificationTemplate(String tenantId, String dataCenterId,String lang);

    /**
     * 生成集成流导入模板
     * @return
     */
    Result<BuildExcelFile.Result> buildIntegrationStreamTemplate(String ea, String lang);

    /**
     * 批量导入集成流
     * @param arg
     * @param lang
     * @return
     */
    Result<ImportIntegrationStreamMapping.Result> batchImportErpIntegrationStreams(ImportIntegrationStreamMapping.Arg arg, String lang);
}
