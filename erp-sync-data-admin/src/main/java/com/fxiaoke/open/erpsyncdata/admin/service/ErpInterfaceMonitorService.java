package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.result.QueryIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpObjInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryInterfaceMonitorLogArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceSimpleMsgResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjNameDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:22 2021/8/10
 * @Desc:
 */
public interface ErpInterfaceMonitorService {

    Result<QueryResult<List<ErpInterfaceMonitorResult>>> queryObjInterfaceList(String tenantId,
                                                                               String dcId,
                                                                               Integer userId,
                                                                               QueryErpObjInterfaceMonitorArg arg,
                                                                               String lang);

    Result<Page<ErpInterfaceMonitorResult>> queryObjInterfaceListNotActualAccount(String tenantId,
                                                                                  String dcId,
                                                                                  Integer userId,
                                                                                  QueryErpObjInterfaceMonitorArg arg,
                                                                                  String lang);

    Result<List<ErpInterfaceMonitorResult>> queryObjInterfaceListByIds(String tenantId, List<ObjectId> objectIds, final Integer size,String lang);

    Result<List<ErpInterfaceSimpleMsgResult>> queryAllObjInterfaceList(String tenantId,
                                                                       String dcId,
                                                                       Integer userId,
                                                                       QueryErpInterfaceMonitorArg arg,
                                                                       String lang);

    Result<ErpInterfaceMonitorResult> getObjInterfaceMonitor(String tenantId, Integer userId, BaseArg IdArg,String lang);

    Result<List<ErpInterfaceMonitorResult>> getObjInterfaceMonitorByObjIds(String tenantId, List<ObjectId> objectIds,String lang);

    Result<List<ErpObjNameDescResult>> queryAllErpRealObj(String tenantId,String dcId, Integer userId);

    Result<Page<ErpInterfaceMonitorResult>> queryObjInterfaceListByResult(String tenantId,
                                                                          String queryId,
                                                                          String dcId,
                                                                          int i,
                                                                          QueryErpObjInterfaceMonitorArg queryErpObjInterfaceMonitorArg,
                                                                          String lang);

    Result<QueryIdResult<Page<ErpInterfaceMonitorResult>>> queryListReadLogByQueryId(String tenantId, String dcId, String queryId);

    Result<Page<ErpInterfaceMonitorResult>> queryInterfaceMonitorLogList(String tenantId, QueryInterfaceMonitorLogArg arg, String lang);
}
