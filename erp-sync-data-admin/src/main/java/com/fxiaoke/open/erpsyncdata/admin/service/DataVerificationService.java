package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.arg.DataVerificationIdArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.MultipartFileArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryDataVerificationArg;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.result.DataVerificationResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationIdStatus;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:16 2022/11/3
 * @Desc:
 */
public interface DataVerificationService {
    Result<DataVerificationResult> verifyDataId(String tenantId,
                                                Integer userId,
                                                String dataCenterId,
                                                DataVerificationIdArg arg,
                                                String lang);

    Result<DataVerificationResult> verifyDataIdByTaskNum(String dataVerificationTaskId, String tenantId, String taskNum, Long totalDataSize, String sourceObjectApiName, String destObjectApiName, String sourceDataCenterId, String erpRealObjectApiname);

    Result<DataVerificationResult> verifyDataIdByFile(String dataVerificationTaskId,
                                                      String tenantId,
                                                      String sourceObjectApiName,
                                                      String destObjectApiName,
                                                      String sourceDcId,
                                                      String erpRealObjectApiname,
                                                      String fileUrl,
                                                      String lang);

    Result<DataVerificationResult> verifyDataIdByIdList(String dataVerificationTaskId, String tenantId, String historyTaskNum, String sourceObjectApiName, String destObjectApiName, String sourceDcId, String erpRealObjectApiname, List<String> dataIds);

    Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> findNotMappingIdList(String tenantId, String sourceObjectApiName, String destObjectApiName, Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> idSyncStatusMap);

    Map<IdSyncStatus, Map<String, DataVerificationIdStatus>> findNotTempIdList(String tenantId, String sourceDcId, String historyTaskNum, String erpRealObjectApiname, List<String> idList);

    Result<DataVerificationResult> queryDataVerificationTask(String tenantId, Integer userId, String dataCenterId, QueryDataVerificationArg arg);

    Result<String> uploadDataVerificationIdField(String tenantId, Integer loginUserId, MultipartFileArg arg,String lang) throws IOException;

    Result<BuildExcelFile.DownUrlResult> downloadIdDetail(String tenantId, String dataCenterId, Integer userId, QueryDataVerificationArg arg,String lang);

    Result<DataVerificationResult> verifyTimeHistoryDataId(String tenantId,
                                                           String dataCenterId,
                                                           Integer userId,
                                                           DataVerificationIdArg arg,
                                                           String lang);

    Result<DataVerificationResult> verifyHistoryDataId(String dataVerificationTaskId, String tenantId, String historyTaskNum, String sourceDcId, String erpRealObjectApiname, List<String> dataIds);

    Result<DataVerificationResult> stop(String tenantId, String dataCenterId, Integer userId, DataVerificationIdArg arg);

    Result<String> batchReSync(String tenantId, String dataCenterId, Integer userId, QueryDataVerificationArg arg,String lang);
}
