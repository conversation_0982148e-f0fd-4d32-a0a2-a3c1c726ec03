package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.IntegrationViewResult;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface ExportIntegrationStreamMapping {

    @Data
    class Arg implements Serializable {

    }

    @Data
    @ApiModel("导出excel文件结果")
    class Result implements Serializable {

        private static final long serialVersionUID = 9181946463285972887L;
        private Boolean success = false;

        private String printMsg;

        private String downloadUrl;

    }

    @Data
    @ApiModel("第一个sheet页")
    class DataFlowDiagram {
        @ExcelProperty("erpdss.global.global.s1095")
        private String k3cObjName;
        @ExcelProperty("erpdss.global.global.s1096")
        private String direction;
        @ExcelProperty("erpdss.global.global.s1097")
        private String crmObjName;
        @ExcelProperty("erpdss.global.global.s1098")
        private String name;

        public static List<List<String>> getHeads(I18NStringManager i18NStringManager,
                                            String lang,
                                            String tenantId) {
            List<List<String>> headerList = new ArrayList<>();
            headerList.add(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s1095,lang,tenantId)));
            headerList.add(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s1096,lang,tenantId)));
            headerList.add(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s1097,lang,tenantId)));
            headerList.add(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s1098,lang,tenantId)));
            return headerList;
        }
    }

    static List<DataFlowDiagram> convert2DataFlowDiagram(I18NStringManager i18NStringManager,
                                                         String lang,
                                                         String tenantId,
                                                         List<IntegrationViewResult> integrationViewResultList) {
        return integrationViewResultList.stream()
                .map(view -> {
                    final DataFlowDiagram dataFlowDiagram = new DataFlowDiagram();
                    dataFlowDiagram.setName(view.getIntegrationStreamName());
                    final String source = Stream.of(
                            view.getSourceObjectName() + "(" + view.getSourceObjectApiName() + ")",
                            view.getDetailObjectMappings().stream()
                                    .map(detailObjectMapping -> detailObjectMapping.getSourceObjectName() + "(" + detailObjectMapping.getSourceObjectApiName() + ")")
                                    .collect(Collectors.joining("、"))
                    ).collect(Collectors.joining(i18NStringManager.get(I18NStringEnum.s1100,lang,tenantId)+"、"));
                    final String dest = Stream.of(
                            view.getDestObjectName() + "(" + view.getDestObjectApiName() + ")",
                            view.getDetailObjectMappings().stream()
                                    .map(detailObjectMapping -> detailObjectMapping.getDestObjectName() + "(" + detailObjectMapping.getDestObjectApiName() + ")")
                                    .collect(Collectors.joining("、"))
                    ).collect(Collectors.joining(i18NStringManager.get(I18NStringEnum.s1100,lang,tenantId)+"、"));
                    if (Objects.equals(view.getSourceTenantType(), TenantTypeEnum.CRM.getType())) {
                        dataFlowDiagram.setK3cObjName(dest);
                        dataFlowDiagram.setDirection("CRM -> " + i18NStringManager.get(I18NStringEnum.s1099,lang,tenantId));
                        dataFlowDiagram.setCrmObjName(source);
                    } else {
                        dataFlowDiagram.setK3cObjName(source);
                        dataFlowDiagram.setDirection(i18NStringManager.get(I18NStringEnum.s1099,lang,tenantId)+" -> CRM");
                        dataFlowDiagram.setCrmObjName(dest);
                    }
                    return dataFlowDiagram;
                }).collect(Collectors.toList());
    }

    @Data
    @ApiModel("其他sheet页")
    class IntegrationStreamMapping {
//        public static final List<String> SOURCE_HEADS = Lists.newArrayList("字段名称", "字段编码", "类型", "固定值", "默认值");
//        public static final List<String> DEST_HEADS = Lists.newArrayList("字段名称", "字段编码", "类型");

        @ExcelProperty("erpdss.global.global.s1090")
        private String sourceName;
        @ExcelProperty("erpdss.global.global.s1091")
        private String sourceApiName;
        @ExcelProperty("erpdss.global.global.s1092")
        private String sourceType;
        @ExcelProperty("erpdss.global.global.s1093")
        private String value;
        @ExcelProperty("erpdss.global.global.s1094")
        private String defaultValue;
        @ExcelProperty("erpdss.global.global.s1090")
        private String destName;
        @ExcelProperty("erpdss.global.global.s1091")
        private String destApiName;
        @ExcelProperty("erpdss.global.global.s1092")
        private String destType;

        public static List<String> getSourceHeads(I18NStringManager i18NStringManager,
                                           String lang,
                                           String tenantId) {
            return Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s1090,lang,tenantId),
                    i18NStringManager.get(I18NStringEnum.s1091,lang,tenantId),
                    i18NStringManager.get(I18NStringEnum.s1092,lang,tenantId),
                    i18NStringManager.get(I18NStringEnum.s1093,lang,tenantId),
                    i18NStringManager.get(I18NStringEnum.s1094,lang,tenantId));
        }

        public static List<String> getDestHeads(I18NStringManager i18NStringManager,
                                         String lang,
                                         String tenantId) {
            return Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s1090,lang,tenantId),
                    i18NStringManager.get(I18NStringEnum.s1091,lang,tenantId),
                    i18NStringManager.get(I18NStringEnum.s1092,lang,tenantId));
        }
    }

    static List<IntegrationStreamMapping> convert2IntegrationStreamMapping(I18NStringManager i18NStringManager,
                                                                           String lang,
                                                                           String tenantId,
                                                                           List<FieldMappingResult> fieldMappings) {
        return fieldMappings.stream()
                .map(fieldMapping -> {
                    final IntegrationStreamMapping integrationStreamMapping = new IntegrationStreamMapping();
                    integrationStreamMapping.setSourceName(fieldMapping.getSourceName());
                    integrationStreamMapping.setSourceApiName(fieldMapping.getSourceApiName());
                    integrationStreamMapping.setSourceType(fieldMapping.getSourceType());
                    integrationStreamMapping.setValue(fieldMapping.getValue());
                    integrationStreamMapping.setDefaultValue(fieldMapping.getDefaultValue());
                    integrationStreamMapping.setDestName(fieldMapping.getDestName());
                    integrationStreamMapping.setDestApiName(fieldMapping.getDestApiName());
                    integrationStreamMapping.setDestType(fieldMapping.getDestType());
                    return integrationStreamMapping;
                }).collect(Collectors.toList());
    }
}
