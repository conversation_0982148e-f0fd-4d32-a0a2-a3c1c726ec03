package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ObjectFieldAnalysisResult implements Serializable {
    private List<ObjectAnalysisModel> objectList = new ArrayList<>();

    @Data
    public static class ObjectAnalysisModel {
        private String objectApiName;
        private int count;
        private List<FieldAnalysisModel> fieldList = new ArrayList<>();
        private List<ObjectAnalysisModel> details = new ArrayList<>();
    }

    @Data
    public static class FieldAnalysisModel {
        private String fieldApiName;
        private String fieldValue;
        private int count;
        private List<DestFieldAnalysisModel> destFieldList = new ArrayList<>();
    }

    @Data
    public static class DestFieldAnalysisModel {
        private String fieldApiName;
        private int count;
        private FieldMappingData fieldMappingData;
    }
}
