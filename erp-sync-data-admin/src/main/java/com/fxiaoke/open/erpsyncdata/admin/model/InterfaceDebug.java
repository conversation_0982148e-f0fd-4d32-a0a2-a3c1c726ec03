package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/14
 */
public class InterfaceDebug {

    @Data
    @ApiModel
    @Builder
    public static class GetInfoArg {
        private String tenantId;
        private String dcId;
        /**
         * 对象apiName
         */
        @ApiModelProperty("对象apiName")
        @NotBlank
        private String objApiName;
        /**
         * 接口类型
         * {@link com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum}
         */
        @ApiModelProperty("接口类型")
        @NotBlank
        private String type;
    }

    @Data
    @ApiModel
    public static class InfoData {
        /**
         * 接口名称
         */
        @ApiModelProperty("接口名称")
        private String interfaceName;
        /**
         * 接口类型
         */
        @ApiModelProperty("接口类型")
        private String dataCenterName;
        /**
         * 表单id及名称
         */
        @ApiModelProperty("表单id及名称")
        private String formFullName;
        /**
         * 接口地址
         */
        @ApiModelProperty("接口地址")
        private String interfaceUrl;
    }

    @Data
    @ApiModel
    public static class GetDataArg {
        private String tenantId;
        private String dcId;
        @ApiModelProperty("对象apiName")
        private String objApiName;
        /**
         * 数据编码
         */
        @ApiModelProperty("数据编码")
        private String dataNumber;
        /**
         * 组织编码，K3C选填
         */
        @ApiModelProperty("组织编码，K3C选填")
        private String orgNumber;
    }

    @Data
    @ApiModel
    public static class ListDataArg {
        private String tenantId;
        private String dcId;
        /**
         * 对象apiName
         */
        @ApiModelProperty("对象apiName")
        private String objApiName;
        /**
         * 开始时间
         */
        @ApiModelProperty("开始时间")
        private Long startTime;
        /**
         * 结束时间
         */
        @ApiModelProperty("结束时间")
        private Long endTime;
        @ApiModelProperty()
        @NotNull
        private Integer offset = 0;
        @ApiModelProperty()
        @NotNull
        @Max(1000)
        private Integer limit;

        /**
         * operate可参考
         *
         * @see com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum
         */
        @ApiModelProperty("配置的筛选条件")
        private List<List<FilterDataVo>> filters;

        /**
         *
         */
        private String lastMaxId;


        /**
         * 查询类型 1-正常列表 2-作废列表
         */
        private Integer type;
    }
}
