package com.fxiaoke.open.erpsyncdata.admin.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.multi.RowKeyTable;
import cn.hutool.core.map.multi.Table;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.model.Template;
import com.fxiaoke.open.erpsyncdata.admin.model.Template.TemplateErpObjInfo;
import com.fxiaoke.open.erpsyncdata.admin.model.Template.TemplateErpSplitObjInfo;
import com.fxiaoke.open.erpsyncdata.admin.model.Template.TemplateFieldInfo;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateOptionDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.InputStream;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 模板工具
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/1
 */
public class TemplateUtil {
    public static Template.Detail analyse(List<SyncPloyDetailEntity> syncPloyDetailEntities,
                                          List<ErpObjExtendDto> erpObjs,
                                          List<ErpObjectFieldEntity> erpFields,
                                          List<ObjectDescribe> crmObjDesc) {
        List<TemplateErpObjInfo> erpObjInfos = new ArrayList<>();
        List<Template.StreamInfo> streamInfos = new ArrayList<>();
        //key:erp中间对象 字段列表
        Map<String, Set<String>> streamSplitErpObjFields = new HashMap<>();
        Map<String, Set<String>> streamSplitErpObjDetails = new HashMap<>();
        Map<String, ErpObjExtendDto> erpSplitObjMap = new HashMap<>();
        Map<String, String> split2RealMap = new HashMap<>();
        Map<String, ObjectDescribe> crmObjDescMap = crmObjDesc.stream().collect(Collectors.toMap(v -> v.getApiName(), v -> v));
        for (ErpObjExtendDto erpObj : erpObjs) {
            split2RealMap.put(erpObj.getSplitObjApiName(), erpObj.parseRealApiCode());
            erpSplitObjMap.put(erpObj.getSplitObjApiName(), erpObj);
        }
        Table<String, String, ErpObjectFieldEntity> splitFieldTable = new RowKeyTable<>();
        for (ErpObjectFieldEntity erpField : erpFields) {
            splitFieldTable.put(erpField.getErpObjectApiName(), erpField.getFieldApiName(), erpField);
        }
        //解析集成流
        for (SyncPloyDetailEntity stream : syncPloyDetailEntities) {
            Integer sourceTenantType = stream.getSourceTenantType();
            boolean isErp2Crm = sourceTenantType.equals(TenantType.ERP);
            String sourceObjectApiName = stream.getSourceObjectApiName();
            String destObjectApiName = stream.getDestObjectApiName();
            FieldMappingsData fieldMappings = stream.getFieldMappings();
            ObjectMappingResult mainMapping = buildObjMapping(streamSplitErpObjFields, erpSplitObjMap, split2RealMap, crmObjDescMap, fieldMappings, isErp2Crm, sourceObjectApiName, destObjectApiName, splitFieldTable);
            Template.StreamInfo streamInfo = new Template.StreamInfo();
            streamInfo.setStreamName(stream.getIntegrationStreamName());
            streamInfo.setSourceTenantType(stream.getSourceTenantType());
            streamInfo.setMainMapping(mainMapping);
            SyncRulesWebData syncRulesWebData = BeanUtil.deepCopy(stream.getSyncRules(), SyncRulesWebData.class);
            streamInfo.setSyncRules(syncRulesWebData);
            if (isErp2Crm) {
                streamSplitErpObjDetails.computeIfAbsent(sourceObjectApiName, v -> new HashSet<>());
            } else {
                streamSplitErpObjDetails.computeIfAbsent(destObjectApiName, v -> new HashSet<>());
            }
            //处理明细
            if (stream.getDetailObjectMappings() != null) {
                streamInfo.setDetailMappings(new ArrayList<>());
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : stream.getDetailObjectMappings()) {
                    if (isErp2Crm) {
                        streamSplitErpObjDetails.computeIfAbsent(sourceObjectApiName, v -> new HashSet<>()).add(detailObjectMapping.getSourceObjectApiName());
                    } else {
                        streamSplitErpObjDetails.computeIfAbsent(destObjectApiName, v -> new HashSet<>()).add(detailObjectMapping.getDestObjectApiName());
                    }
                    ObjectMappingResult detailObjMapping = buildObjMapping(streamSplitErpObjFields, erpSplitObjMap, split2RealMap, crmObjDescMap, detailObjectMapping.getFieldMappings(), isErp2Crm, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName(), splitFieldTable);
                    streamInfo.getDetailMappings().add(detailObjMapping);
                }
            }
            streamInfos.add(streamInfo);
        }
        streamSplitErpObjDetails.forEach((main, details) -> {
            TemplateErpObjInfo erpObjInfo = new TemplateErpObjInfo()
                    .setMainRealObjApiName(split2RealMap.get(main));
            TemplateErpSplitObjInfo mainInfo = buildObjInfo(streamSplitErpObjFields, erpSplitObjMap, split2RealMap, splitFieldTable, main);
            erpObjInfo.setMain(mainInfo).setDetails(new ArrayList<>());
            for (String detail : details) {
                TemplateErpSplitObjInfo detailInfo = buildObjInfo(streamSplitErpObjFields, erpSplitObjMap, split2RealMap, splitFieldTable, detail);
                erpObjInfo.getDetails().add(detailInfo);
            }
            erpObjInfos.add(erpObjInfo);
        });
        Template.Detail detail = new Template.Detail().setErpObjInfos(erpObjInfos).setStreamInfos(streamInfos);
        return detail;
    }

    private static TemplateErpSplitObjInfo buildObjInfo(Map<String, Set<String>> streamSplitErpObjFields, Map<String, ErpObjExtendDto> erpSplitObjMap, Map<String, String> split2RealMap, Table<String, String, ErpObjectFieldEntity> splitFieldTable, String main) {
        ErpObjExtendDto erpObjExtendDto = erpSplitObjMap.get(main);
        TemplateErpSplitObjInfo mainInfo = new TemplateErpSplitObjInfo();
        mainInfo.setObjName(erpObjExtendDto.getObjName());
        mainInfo.setRealObjApiName(erpObjExtendDto.parseRealApiCode());
        mainInfo.setFields(new ArrayList<>());
        Set<String> fieldApis = streamSplitErpObjFields.get(main);
        //主键，主从字段保留
        for (ErpObjectFieldEntity value : splitFieldTable.values()) {
            if (value.getFieldDefineType().equals(ErpFieldTypeEnum.id)
                    || value.getFieldDefineType().equals(ErpFieldTypeEnum.detail)
                    || value.getFieldDefineType().equals(ErpFieldTypeEnum.master_detail)) {
                fieldApis.add(value.getFieldApiName());
            }
        }
        for (String fieldApi : fieldApis) {
            ErpObjectFieldEntity erpField = splitFieldTable.get(main, fieldApi);
            if (erpField != null) {
                TemplateFieldInfo templateFieldInfo = new TemplateFieldInfo();
                templateFieldInfo.setFieldApiName(erpField.getFieldApiName());
                templateFieldInfo.setFieldLabel(erpField.getFieldLabel());
                templateFieldInfo.setFieldDefineType(erpField.getFieldDefineType());
                if (erpField.getFieldDefineType().equals(ErpFieldTypeEnum.object_reference)
                        || erpField.getFieldDefineType().equals(ErpFieldTypeEnum.object_reference_many)
                        || erpField.getFieldDefineType().equals(ErpFieldTypeEnum.master_detail))
                    templateFieldInfo.setFieldExtendValue(split2RealMap.get(erpField.getFieldExtendValue()));
                mainInfo.getFields().add(templateFieldInfo);
            }
        }
        return mainInfo;
    }

    private static ObjectMappingResult buildObjMapping(Map<String, Set<String>> streamErpObjFields, Map<String, ErpObjExtendDto> erpSplitObjMap, Map<String, String> split2RealMap, Map<String, ObjectDescribe> crmObjDescMap, List<FieldMappingData> fieldMappings, boolean isErp2Crm, String sourceObjectApiName, String destObjectApiName, Table<String, String, ErpObjectFieldEntity> splitFieldTable) {
        String sourceObjApiName2, destObjApiName2, sourceObjName, destObjName;
        ErpObjExtendDto erpObj;
        ObjectDescribe crmObj;
        if (isErp2Crm) {
            erpObj = erpSplitObjMap.get(sourceObjectApiName);
            sourceObjApiName2 = erpObj.parseRealApiCode();
            sourceObjName = erpObj.getObjName();
            crmObj = crmObjDescMap.get(destObjectApiName);
            destObjApiName2 = crmObj.getApiName();
            destObjName = crmObj.getDisplayName();
        } else {
            erpObj = erpSplitObjMap.get(destObjectApiName);
            destObjApiName2 = erpObj.parseRealApiCode();
            destObjName = erpObj.getObjName();
            crmObj = crmObjDescMap.get(sourceObjectApiName);
            sourceObjApiName2 = crmObj.getApiName();
            sourceObjName = crmObj.getDisplayName();
        }
        Set<String> erpStreamFields = streamErpObjFields.computeIfAbsent(erpObj.getSplitObjApiName(), v -> new LinkedHashSet<>());
        ObjectMappingResult mainMapping = new ObjectMappingResult();
        mainMapping.setSourceObjectApiName(sourceObjApiName2);
        mainMapping.setDestObjectApiName(destObjApiName2);
        mainMapping.setSourceObjectName(sourceObjName);
        mainMapping.setDestObjectName(destObjName);
        mainMapping.setFieldMappings(new ArrayList<>());
        for (FieldMappingData fieldMapping : fieldMappings) {
            FieldMappingResult mappingResult = new FieldMappingResult();
            BeanUtils.copyProperties(fieldMapping, mappingResult);
            //替换erpObj,填充字段名称
            String sourceApiName = mappingResult.getSourceApiName();
            String destApiName = mappingResult.getDestApiName();
            mappingResult.setDestName(destApiName);
            if (isErp2Crm) {
                if (StrUtil.isNotBlank(sourceApiName)) {
                    erpStreamFields.add(sourceApiName);
                    mappingResult.setSourceName(getErpFieldName(splitFieldTable, erpObj, sourceApiName));
                }
                if (StrUtil.isNotBlank(destApiName)) {
                    mappingResult.setDestName(getCrmFieldName(crmObj, destApiName));
                }
                mappingResult.setSourceTargetApiName(split2RealMap.get(mappingResult.getSourceTargetApiName()));
                mappingResult.setSourceQuoteFieldTargetObjectApiName(split2RealMap.get(mappingResult.getSourceQuoteFieldTargetObjectApiName()));
            } else {
                if (StrUtil.isNotBlank(destApiName)) {
                    erpStreamFields.add(destApiName);
                    mappingResult.setDestName(getErpFieldName(splitFieldTable, erpObj, destApiName));
                }
                if (StrUtil.isNotBlank(sourceApiName)) {
                    mappingResult.setSourceName(getCrmFieldName(crmObj, sourceApiName));
                }
                mappingResult.setDestTargetApiName(split2RealMap.get(mappingResult.getDestTargetApiName()));
            }
            mainMapping.getFieldMappings().add(mappingResult);
        }
        return mainMapping;
    }

    private static String getCrmFieldName(ObjectDescribe crmObj, String destApiName) {
        String destName;
        if (destApiName.endsWith("__c")) {
            //自定义字段
            destName = "自定义字段";   // ignoreI18n  模板不支持i18n
        } else {
            FieldDescribe fieldDescribe = crmObj.getFields().get(destApiName);
            if (fieldDescribe != null) {
                destName = fieldDescribe.getLabel();
            } else {
                destName = destApiName;
            }
        }
        return destName;
    }

    private static String getErpFieldName(Table<String, String, ErpObjectFieldEntity> splitFieldTable, ErpObjExtendDto erpObj, String sourceApiName) {
        ErpObjectFieldEntity erpObjectFieldEntity = splitFieldTable.get(erpObj.getSplitObjApiName(), sourceApiName);
        return erpObjectFieldEntity == null ? sourceApiName : erpObjectFieldEntity.getFieldLabel();
    }

    private static List<TemplateDoc> getTemplatesFromFile() {
        try {
            //读取jar文件，判断是否需要更新数据库数据
            PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource resource = pathMatchingResourcePatternResolver.getResource("config/template/templateData.json");
            InputStream inputStream = resource.getInputStream();
            String resourceStr = IoUtil.readUtf8(inputStream);
            inputStream.close();
            List<TemplateDoc> templateDocs = JSON.parseObject(resourceStr, new TypeReference<List<TemplateDoc>>() {
            });
            return templateDocs;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private static List<TemplateOptionDoc> getTemplateOptionsFromFile() {
        try {
            PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
            //读取jar文件，判断是否需要更新数据库数据
            Resource resource = pathMatchingResourcePatternResolver.getResource("config/template/templateOptionData.json");
            InputStream inputStream = resource.getInputStream();
            String resourceStr = IoUtil.readUtf8(resource.getInputStream());
            inputStream.close();
            List<TemplateOptionDoc> templateOptDocs = JSON.parseObject(resourceStr, new TypeReference<List<TemplateOptionDoc>>() {
            });
            return templateOptDocs;
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public static Supplier<List<TemplateDoc>> fileTemplatesSup = () -> getTemplatesFromFile();
    public static Supplier<List<TemplateOptionDoc>> fileOptionsSup = () -> getTemplateOptionsFromFile();
}
