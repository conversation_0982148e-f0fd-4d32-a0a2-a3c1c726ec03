package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 12:01 2021/6/16
 * @Desc:
 */
@Data
@ApiModel
public class QueryFieldDescArg implements Serializable {
    @ApiModelProperty("数据中心id")
    private String dataCenterId;
    @ApiModelProperty("中间对象名字")
    private String objectApiName;
    @ApiModelProperty("crm对象 1 ERP对象2  ")
    private Integer objectType;

}
