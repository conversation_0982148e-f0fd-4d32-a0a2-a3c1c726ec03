package com.fxiaoke.open.erpsyncdata.admin.result;


import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncPloyDetailInfoResult implements Serializable {
    @ApiModelProperty("策略id")
    private String ployId;
    @ApiModelProperty("策略名称")
    private String ployName;
    @ApiModelProperty("策略明细id")
    private String id;
    @ApiModelProperty("源企业数据")
    private List<TenantData> sourceTenantDatas;
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("源企业对象名称")
    private String sourceObjectName;
    @ApiModelProperty("目标企业数据")
    private  List<TenantData> destTenantDatas;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("目标企业对象名称")
    private String destObjectName;
    @ApiModelProperty("从对象信息")
    List<DetailObjectMappingInfo> detailObjectMappings;
    @ApiModelProperty("启用状态")
    private Integer status;
    @ApiModelProperty("策略异常状态")
    private Boolean valid;
    @ApiModelProperty("状态名称，启用，停用")
    private String statusName;
    @ApiModelProperty("数据同步失败的数量")
    private Integer syncFailedDataCount = 0;

    @Data
    public static class DetailObjectMappingInfo implements Serializable{
        @ApiModelProperty("源企业对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("源企业对象名称")
        private String sourceObjectName;
        @ApiModelProperty("目标企业对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("目标企业对象名称")
        private String destObjectName;
    }
}
