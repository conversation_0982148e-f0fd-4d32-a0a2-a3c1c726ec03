package com.fxiaoke.open.erpsyncdata.admin.result;


import com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.admin.data.TenantData;
import com.fxiaoke.open.erpsyncdata.admin.utils.ResultConversionUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
public class SyncPloyDetailResult implements Serializable {
    @ApiModelProperty("策略id")
    private String ployId;
    @ApiModelProperty("策略名称")
    private String ployName;
    @ApiModelProperty("策略所属企业")
    private String ployTenantId;
    @ApiModelProperty("策略明细id")
    private String id;
    @ApiModelProperty("源企业id")
    private List<TenantData> sourceTenantDatas = Lists.newArrayList();
    @ApiModelProperty("源企业类型")
    private Integer sourceTenantType;
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("源企业对象名称")
    private String sourceObjectName;
    @ApiModelProperty("目标企业Id")
    private List<TenantData> destTenantDatas = Lists.newArrayList();
    @ApiModelProperty("目标企业类型")
    private Integer destTenantType;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("目标企业对象名称")
    private String destObjectName;
    @ApiModelProperty("从对象相关信息")
    private List<ObjectMappingInfo> detailObjectMappings = Lists.newArrayList();
    @ApiModelProperty("启用状态，1启用 2停用")
    private Integer status;
    @ApiModelProperty("状态名称，启用，停用")
    private String statusName;
    @ApiModelProperty("异常状态显示")
    private Boolean isValid;
    /**  同步方式 轮询：get,推送：push 调试：debug**/
    @Deprecated
    private String syncType;
    /**  同步方式 轮询：get,推送：push**/
    private List<String> syncTypeList;
    /** 同步前自定义函数APIName */
    private String beforeFuncApiName;
    /** 同步中自定义函数APIName */
    private String duringFuncApiName;
    /** 同步后自定义函数APIName */
    private String afterFuncApiName;
    private Boolean hadReverseWriteNode;
    private List<FieldMappingData> fieldMappings;

    /** 同步数据范围，即查找条件 */
    private SyncConditionsData syncConditions;
    /** 从对象数据范围，即查找条件 */
    private List<SyncConditionsData> detailObjectSyncConditions;
    /**同步规则*/
    private SyncRulesResult syncRulesResult;
    /** 源数据中心 */
    private String sourceDataCenterId;
    /** 目标数据中心 */
    private String destDataCenterId;

    @Data
    public static class ObjectMappingInfo implements Serializable {
        public static ObjectMappingInfo newInfo( String sourceObjectApiName,String destObjectApiName, List<FieldMappingData> fieldMappings){
            ObjectMappingInfo info = new ObjectMappingInfo();
            info.setSourceObjectApiName(sourceObjectApiName);
            info.setDestObjectApiName(destObjectApiName);
            info.setFieldMappings(fieldMappings);
            return info;
        }
        @ApiModelProperty("源企业对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("源企业对象名称")
        private String sourceObjectName;
        @ApiModelProperty("目标企业对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("目标企业对象名称")
        private String destObjectName;
        private List<FieldMappingData> fieldMappings;
    }

    public static SyncPloyDetailResult buildSyncPloyDetailResultByEntity(I18NStringManager i18NStringManager,
                                                                         String lang,
                                                                         String tenantId,
                                                                         SyncPloyDetailEntity entity) {
        SyncPloyDetailResult syncPloyDetailResult = new SyncPloyDetailResult();
        syncPloyDetailResult.setId(entity.getId());
        syncPloyDetailResult.setPloyId(entity.getSyncPloyId());
        syncPloyDetailResult.setPloyTenantId(entity.getTenantId());
        syncPloyDetailResult.setPloyName(entity.getIntegrationStreamName());
        syncPloyDetailResult.setStatus(entity.getStatus());
        syncPloyDetailResult.setStatusName(SyncPloyDetailStatusEnum.getNameByStatus(i18NStringManager,lang,tenantId,entity.getStatus()));
        //设置源企业信息
        syncPloyDetailResult.setSourceObjectApiName(entity.getSourceObjectApiName());
        syncPloyDetailResult.getSourceTenantDatas().add(TenantData.newDataByTenantId(entity.getTenantId()));
        syncPloyDetailResult.setSourceTenantType(entity.getSourceTenantType());
        //设置目标企业信息
        syncPloyDetailResult.setDestObjectApiName(entity.getDestObjectApiName());
        syncPloyDetailResult.getDestTenantDatas().add(TenantData.newDataByTenantId(entity.getTenantId()));
        syncPloyDetailResult.setDestTenantType(entity.getDestTenantType());
        if (entity.getSyncRules() != null) {
            if (entity.getSyncRules().getSyncTypeList() == null && entity.getSyncRules().getSyncType() != null) {
                //不知道为什么旧数据以前能启用，现在启用会异常，必须存在syncTypeList，这里做一下兼容
                syncPloyDetailResult.setSyncTypeList(Lists.newArrayList(entity.getSyncRules().getSyncType()));
            } else {
                syncPloyDetailResult.setSyncTypeList(entity.getSyncRules().getSyncTypeList());
            }
            SyncRulesResult syncRulesResult = BeanUtil2.deepCopy(entity.getSyncRules(), SyncRulesResult.class);
            syncPloyDetailResult.setSyncRulesResult(syncRulesResult);
        }
        syncPloyDetailResult.setBeforeFuncApiName(entity.getBeforeFuncApiName());
        syncPloyDetailResult.setDuringFuncApiName(entity.getDuringFuncApiName());
        syncPloyDetailResult.setAfterFuncApiName(entity.getAfterFuncApiName());
        syncPloyDetailResult.setIsValid(entity.getIsValid());
        syncPloyDetailResult.setFieldMappings(ResultConversionUtil.convertFieldMappingData(entity.getFieldMappings()));
        syncPloyDetailResult.setSyncConditions(BeanUtil2.deepCopy(entity.getSyncConditions(), com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData.class));
        syncPloyDetailResult.setHadReverseWriteNode(Objects.nonNull(entity.getIntegrationStreamNodes()) && Objects.nonNull(entity.getIntegrationStreamNodes().getReverseWriteNode()));
        syncPloyDetailResult.setDetailObjectSyncConditions(BeanUtil2.deepCopyList(entity.getDetailObjectSyncConditions(), com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData.class));
        List<SyncPloyDetailResult.ObjectMappingInfo> detailObjectMappingInfos = new ArrayList<>();
        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : entity.getDetailObjectMappings()) {
            SyncPloyDetailResult.ObjectMappingInfo detailObjectMappingInfo = new SyncPloyDetailResult.ObjectMappingInfo();
            detailObjectMappingInfo.setSourceObjectApiName(detailObjectMapping.getSourceObjectApiName());
            detailObjectMappingInfo.setDestObjectApiName(detailObjectMapping.getDestObjectApiName());
            detailObjectMappingInfo.setFieldMappings(ResultConversionUtil.convertFieldMappingData(detailObjectMapping.getFieldMappings()));
            detailObjectMappingInfos.add(detailObjectMappingInfo);
        }
        syncPloyDetailResult.setDetailObjectMappings(detailObjectMappingInfos);
        syncPloyDetailResult.setSourceDataCenterId(entity.getSourceDataCenterId());
        syncPloyDetailResult.setDestDataCenterId(entity.getDestDataCenterId());
        return syncPloyDetailResult;
    }

    public static List<SyncPloyDetailResult> buildSyncPloyDetailResultsByEntities(I18NStringManager i18NStringManager,
                                                                                  String lang,
                                                                                  String tenantId,
                                                                                  List<SyncPloyDetailEntity> syncPloyDetailEntities) {
        if (CollectionUtils.isEmpty(syncPloyDetailEntities)) {
            return Lists.newArrayList();
        }

        return syncPloyDetailEntities.stream()
                .map(entity->buildSyncPloyDetailResultByEntity(i18NStringManager,lang,tenantId,entity))
                .collect(Collectors.toList());
    }
}