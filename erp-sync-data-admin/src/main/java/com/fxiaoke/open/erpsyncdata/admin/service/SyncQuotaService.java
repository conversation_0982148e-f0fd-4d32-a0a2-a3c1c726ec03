package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import com.fxiaoke.open.erpsyncdata.admin.result.CheckQuotaResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.LogStorageRuleEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

public interface SyncQuotaService {
    /**
     * 获取配额
     *
     * @param tenantId
     * @param countMappings 是否计算中间表数量
     * @param countLogs 是否需要触发异常计算，只有前端请求需要设置为true，后台任务和其它场景，不需要设置为true
     * @return
     */
    Result<SyncQuota> getQuota(String tenantId,Boolean countMappings,boolean countLogs);

    /**
     * 从缓存查询日志配额
     *
     * @param tenantId
     * @return
     */
    Result<SyncQuota> getLogQuotaFromCache(String tenantId);

    /**
     * 查询日志存储规则
     * @param tenantId
     * @return
     */
    Result<LogStorageRuleEnum> queryLogStorageRules(String tenantId);

    /**
     * 更新日志存储规则
     * @param tenantId
     * @param rules
     * @return
     */
    Result<Void> updateLogStorageRules(String tenantId, LogStorageRuleEnum rules);

    /**
     * 检查中间表配额配额
     *
     * @param tenantId
     * @param alert
     * @return
     */
    Result<SyncQuota> checkMappingsQuota(String tenantId,Boolean alert,String lang);
    Result<List<CheckQuotaResult>> listCheckQuotaResult();


    /**
     * 检车配额任务
     *
     * @param tenantIds 指定企业id，为空查所有企业
     * @param alert 是否发送告警
     * @param cleanLog 是否清理日志
     * @param vacuum 是否执行pg vacuum
     */
    Result<List<CheckQuotaResult>> checkQuotaTask(List<String> tenantIds,boolean alert,boolean cleanLog,boolean vacuum,String lang);
}
