package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.service.CheckCodeService;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TimeUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.sharecrm.egress.sdk.entity.SmsSendResponse;
import com.sharecrm.egress.sdk.service.SmsApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Date: 14:08 2021/12/6
 * @Desc:
 */
@Service
@Slf4j
public class CheckCodeServiceImpl implements CheckCodeService {
    @Autowired
    private SmsApiService smsApiService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static final Joiner JOINER = Joiner.on(":").useForNull("null");
    private static final String ERPDSS_CODE = "ERPDSS:CHECK:CODE";

    @Override
    public Result<String> sendCode(String tenantId, String phone, String code) {
        if(StringUtils.isBlank(phone)){
            return Result.newError(ResultCodeEnum.THE_USER_PHONE_IS_NULL);
        }
        if(StringUtils.isBlank(code)){
            code=initCode();
        }
        String msgFormat=i18NStringManager.getByEi(I18NStringEnum.s720,tenantId);
        SmsSendResponse smsSendResponse = smsApiService.sendSms(phone, String.format(msgFormat, code), "erpdss");
        log.info("CheckCodeServiceImpl.sendCode,smsSendResponse={}",smsSendResponse);
        if(smsSendResponse.isSuccess()){
            return saveRedis(tenantId,phone,code);
        }else{
            return new Result<>(ResultCodeEnum.SEND_CODE_ERROR,smsSendResponse.getMessage());
        }
    }

    private String initCode() {
        return TimeUtil.hms();
    }

    private Result<String> saveRedis(String tenantId, String phone, String code) {
        String value = redisDataSource.get(this.getClass().getSimpleName()).set(getKey(tenantId, phone), code);
        Long expire = redisDataSource.get(this.getClass().getSimpleName()).expire(getKey(tenantId, phone), 15 * 60);//15分钟
        return Result.newSuccess(code);
    }

    private Result<String> getCode(String tenantId, String phone) {
        String value = redisDataSource.get(this.getClass().getSimpleName()).get(getKey(tenantId, phone));
        if(value!=null){
            return Result.newSuccess(value);
        }else{
            return Result.newError(ResultCodeEnum.CODE_IS_INVALID);
        }

    }

    private String getKey(String tenantId, String phone) {
        return JOINER.join(ERPDSS_CODE, tenantId,phone);
    }

    @Override
    public Result<Boolean> checkCode(String tenantId, String phone, String code) {
        if(StringUtils.isBlank(phone)){
            return Result.newError(ResultCodeEnum.THE_USER_PHONE_IS_NULL);
        }
        Result<String> oldCode = getCode(tenantId, phone);
        if(!oldCode.isSuccess()){
            return Result.copy(oldCode);
        }else{
           if(oldCode.getData().equals(code)){
               return Result.newSuccess(true);
           }else{
               return Result.newSuccess(false);
           }
        }
    }

}
