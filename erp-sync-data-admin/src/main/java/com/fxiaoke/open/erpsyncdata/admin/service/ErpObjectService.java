package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.admin.model.ImportIntegrationStreamMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.preprocess.CascaderInfoAndObjResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.DeleteErpObjectArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjectSimpleInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.UpdateErpObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 13:54 2020/8/19
 * @Desc:
 */
public interface ErpObjectService {
    Result<List<ErpObjectDescResult>> queryErpFakeObjectByTenantIdAndDcId(String tenantId, int userId,String dataCenterId);
    Result<List<ErpObjectDescResult>> queryErpFakeMasterObjectByTenantId(String tenantId, int userId,String dataCenterId);
    Result<List<CascaderInfoAndObjResult>> queryAllErpFakeMasterObject(String tenantId, int userId);
    Result<List<ErpObjectDescResult>> queryErpFakeDetailObjectByMasterApiNameAndDcId(String tenantId, int userId,String masterObjApiName,String dataCenterId);
    Result<ErpObjectDescResult> queryErpObjectByObjApiName(String tenantId,String dataCenterId, int userId,String objApiName);
    Result<String> updateErpObject(String tenantId,String dataCenterId,int userId,ErpObjectDescResult erpObjectDescResult);
    Result<UpdateErpObjectResult> updateErpObjects(String tenantId, String dataCenterId, int userId, ErpObjectRelationshipResult erpObjectRelationshipResult,String lang);
    //Result<List<ErpObjectDescResult>> updateFakeObject(int tenantId,int userId,ErpObjectDescResult actualObj,ErpObjectDescResult fakeObj);
    Result<String> deleteErpObject(String tenantId, int userId, DeleteErpObjectArg deleteArg);
    Map<String, String> queryErpObjectNameByApiName(String oneTenantId,String dataCenterId, int userId,List<String> apiNames);


    /**
     * 查询所有中间对象，无字段信息
     */
    Result<List<ErpObjectRelationshipResult>> queryRealErpObjectByTenantIdAndDcId(String tenantId, int userId, String queryStr, String dataCenterId);


    /**
     * 通过真实apiName获取ERP对象信息
     * 原来的逻辑复杂，多次调用db，故改良
     * @param tenantId
     * @param realApiName
     * @param splitSeq 批次
     * @param dataCenterId
     * @return
     */
    Result<ErpObjectRelationshipResult> getErpObjectRelationshipByRealObjApiName(String tenantId, String realApiName, final Integer splitSeq, String dataCenterId,String lang);
    List<String> getErpFakeObjApiNames(String tenantId, String dataCenterId);
    Result<ErpObjectSimpleInfo> getErpObjSimpleInfo(String tenantId,int userId,String objApiName,String lang);

    /**
     * 导入对象
     */
    Result<Map<String, String>> importObjectApiNameData(String tenantId,
                                                        int userId,
                                                        String dataCenterId,
                                                        ErpChannelEnum channel,
                                                        String lang,
                                                        List<ErpIntegrationStreamExcelVo> integrationStreamExcelVos);
}
