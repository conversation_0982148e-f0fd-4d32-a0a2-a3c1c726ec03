package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel
public class ObjectMappingResult implements Serializable {
    @ApiModelProperty("源对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("源对象名称")
    private String sourceObjectName;
    @ApiModelProperty("目标对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("目标对象名称")
    private String destObjectName;
    @ApiModelProperty("对象字段映射列表，每条数据对应一个字段的映射关系")
    private List<FieldMappingResult> fieldMappings;
    @ApiModelProperty("不更新字段apiName")
    private List<String> notUpdateFieldApiNameList;
    @ApiModelProperty("不校验映射字段apiName")
    private List<String> notCheckMappingFieldApiNameList;

    public static void fillMappingConfig(ObjectMappingResult fieldMappings) {
        final List<String> notUpdateFieldApiNameList = ListUtils.emptyIfNull(fieldMappings.getNotUpdateFieldApiNameList());
        final List<String> notCheckMappingFieldApiNameList = ListUtils.emptyIfNull(fieldMappings.getNotCheckMappingFieldApiNameList());
        for (FieldMappingResult fieldMappingResult : fieldMappings.getFieldMappings()) {
            fieldMappingResult.setNotUpdateField(notUpdateFieldApiNameList.contains(fieldMappingResult.getDestApiName()));
            fieldMappingResult.setNotCheckMappingField(notCheckMappingFieldApiNameList.contains(fieldMappingResult.getDestApiName()));
        }
    }

    public void fillNotCheckMappingFieldApiNameList() {
        notCheckMappingFieldApiNameList = ListUtils.emptyIfNull(fieldMappings).stream()
                .filter(mapping -> BooleanUtils.isTrue(mapping.getNotCheckMappingField()))
                .map(FieldMappingResult::getDestApiName)
                .collect(Collectors.toList());
    }
}