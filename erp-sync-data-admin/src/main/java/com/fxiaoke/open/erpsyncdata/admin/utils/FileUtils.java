//package com.fxiaoke.open.erpsyncdata.admin.utils;
//
//import cn.hutool.core.io.resource.ClassPathResource;
//import com.alibaba.fastjson.JSON;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
//
//import java.io.InputStream;
//import java.util.jar.JarInputStream;
//
///**
// * <AUTHOR> (^_−)☆
// * @date 2020/11/16
// */
//public class FileUtils {
//    public static String getResourceFile2String(String filePath) {
//        try (InputStream inputStream = FileUtils.class.getResourceAsStream(filePath)) {
//            return JSON.parseObject(inputStream, String.class);
//        } catch (Exception e) {
//            throw new ErpSyncDataException(e,null);
//        }
//    }
//
//
//
//    public static void main(String[] args) {
//        String resourceFile2String = getResourceFile2String("/object_describe/erporganization/describe.json");
//        System.out.println(resourceFile2String);
//    }
//
//}
