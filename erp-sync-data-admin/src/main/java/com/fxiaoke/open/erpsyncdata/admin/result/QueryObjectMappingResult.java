package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:41 2023/5/19
 * @Desc:
 */
@Data
public class QueryObjectMappingResult implements Serializable {
    @ApiModelProperty("需要查询的目标对象")
    private String sourceObjectApiName;
    @ApiModelProperty("需要查询的目标对象名称")
    private String sourceObjectName;
    @ApiModelProperty("取值的源对象")
    private String destObjectApiName;
    @ApiModelProperty("取值的源对象名称")
    private String destObjectName;
    @ApiModelProperty("查询条件")
    private List<List<FilterData>> queryFieldMappings;
}
