package com.fxiaoke.open.erpsyncdata.admin.remote;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/18 19:37:44
 */
@Component
@Slf4j
public class UserRoleManager {

    // 管理权限的crm管理员角色的code为31, 业务权限的crm管理员角色的code为AuthConstant.ROLE_CODE.CRM_MANAGER
    private static final String CRM_MANAGER_ROLE_CODE = "31";

    public static final String ERPDSS_MANAGER_ROLE_CODE = "erpdss_manager";


    private <T> T postConvert(String tenantId, String path, AuthArg authArg, com.alibaba.fastjson2.TypeReference<T> beanType) {
        return postConvert(tenantId, path, authArg, beanType.getType());
    }

    private <T> T postConvert(String tenantId, String path, AuthArg authArg, Type beanType) {
        String url = ConfigCenter.svc_fs_paas_auth + path;
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("x-fs-ei", tenantId);
        HttpResponse httpResponse = OkHttpUtils.postNonblocking(url, headerMap, JacksonUtil.toJson(authArg));
        if (!httpResponse.isOk()) {
            throw new ErpSyncDataException("call paas auth failed," + httpResponse.getCode() + ":" + httpResponse.getMessage());
        }
        String body = httpResponse.getBody();
        if (StringUtils.isEmpty(body)) {
            throw new ErpSyncDataException("call paas auth return empty");
        }
        JSONObject jsonObject = JSON.parseObject(body);
        Integer errCode = jsonObject.getInteger("errCode");
        String errMessage = jsonObject.getString("errMessage");
        if (errCode == 0) {
            //成功
            T result = jsonObject.getObject("result", beanType);
            return result;
        } else {
            throw new ErpSyncDataException("call paas auth failed," + errCode + ":" + errMessage);
        }
    }

    private Set<String> queryRoleCodeByUserId(String tenantId, String userId) {
        AuthArg authArg = getAuthArg(tenantId, userId);
        Set<String> codes = postConvert(tenantId, "/queryRoleCodeListByUserId", authArg, new TypeReference<Set<String>>() {
        });
        return codes;
    }

    private List<RolePojo> queryAllRoleInfo(String tenantId, Collection<String> roleCodes, Boolean filterDeleted) {
        AuthArg authArg = getAuthArg(tenantId);
        authArg.put("roleCodes", roleCodes);
        if (filterDeleted != null) {
            authArg.put("filterDeleted", filterDeleted);
        }
        List<RolePojo> list = postConvert(tenantId, "/queryAllRoleInfo", authArg, new TypeReference<List<RolePojo>>() {
        });
        return list;
    }

    private Map<String, Boolean> userFuncPermissionCheck(String tenantId, String employeeId, Set<String> funcCodes) {
        AuthArg authArg = getAuthArg(tenantId, employeeId, AuthConstant.AppId.SYSTEM);
        authArg.put("funcCodeList", funcCodes);
        Map<String, Boolean> result = postConvert(tenantId, "/funcPermissionCheck", authArg, new TypeReference<Map<String, Boolean>>() {
        });
        return result;
    }


    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public boolean checkAdminAuth(final String tenantId, final String employeeId) {
        // 临时校验角色信息(crm管理员/系统管理员),后续需要改为cep调用接口,使用cep鉴权
        try {
            final Set<String> roles = queryRoleCodeByUserId(tenantId, employeeId);
            return roles.contains(CRM_MANAGER_ROLE_CODE) || roles.contains(AuthConstant.ROLE_CODE.SYSTEM_ROLE);
        } catch (Exception e) {
            log.warn("checkAdminAuth error, tenantId:{}, employeeId:{}", tenantId, employeeId, e);
            return true;
        }
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public boolean hasFuncPermission(final String tenantId, final Integer userId, Set<String> funcCodes) {
        if (CollectionUtils.isEmpty(funcCodes)) {
            return true;
        }
        final String employeeId = String.valueOf(userId);
        if (checkAdminAuth(tenantId, employeeId)) {
            return true;
        }
        final Map<String, Boolean> permissionCheck = userFuncPermissionCheck(tenantId, employeeId, funcCodes);
        return permissionCheck.values().stream().anyMatch(BooleanUtils::isTrue);
    }

    /**
     * 获取该企业缺少的角色
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public List<String> getLackRoles(String tenantId, Collection<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return new ArrayList<>();
        }
        final HashSet<String> roleCodes = Sets.newHashSet(roles);
        final List<RolePojo> rolePojos = queryAllRoleInfo(tenantId, roleCodes, false);
        rolePojos.stream().map(RolePojo::getRoleCode).forEach(roleCodes::remove);

        return Lists.newArrayList(roleCodes);
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public Map<String, String> queryRoleNameByRoleCode(String tenantId, List<String> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return new HashMap<>();
        }
        final List<RolePojo> rolePojos = queryAllRoleInfo(tenantId, Sets.newHashSet(roles), true);
        return rolePojos.stream().collect(Collectors.toMap(RolePojo::getRoleCode, RolePojo::getRoleName, (o1, o2) -> o1));
    }

    /**
     * 查询有某个管理功能权限的员工
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 60)
    public List<Integer> getUserIdsBySystemRoleCode(String tenantId, String roleCode) {
        AuthArg arg = getSystemAuthArg(tenantId);
        arg.put("roleCode", roleCode);
        arg.put("pageInfo", new PageInfo(1, 1000));
        final RoleUserPageInfoResponse roleUserPageInfoResponse = postConvert(tenantId, "/roleUser", arg, RoleUserPageInfoResponse.class);
        return roleUserPageInfoResponse.getUsers().stream()
                .filter(StringUtils::isNumeric)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
    }

    @NotNull
    private static AuthArg getAuthArg(String tenantId) {
        return getAuthArg(tenantId, AuthConstant.SYSTEM_USERID);
    }

    private static AuthArg getAuthArg(String tenantId, String employeeId) {
        return getAuthArg(tenantId, employeeId, AuthConstant.AppId.ALL);
    }

    /**
     * 查询管理功能权限的authContext
     */
    @NotNull
    private static AuthArg getSystemAuthArg(String tenantId) {
        return getAuthArg(tenantId, AuthConstant.SYSTEM_USERID, AuthConstant.AppId.SYSTEM);
    }

    @NotNull
    private static AuthArg getAuthArg(String tenantId, String employeeId, String appId) {
        // 临时校验角色信息(crm管理员/系统管理员),后续需要改为cep调用接口,使用cep鉴权
        final AuthContext authContext = new AuthContext();
        authContext.setTenantId(tenantId);
        authContext.setAppId(appId);
        authContext.setUserId(employeeId);
        return new AuthArg(authContext);
    }

    @Data
    static class AuthContext {

        //当前租户的id
        private String tenantId;
        //应用id
        private String appId;
        //要查询人员的id
        private String userId;
        //外部企业id
        private String outerTenantId;

        //外部appId
        private Map<String, String> properties;

        //外部身份类型，公司、个人
        private String identityType;
    }

    static class AuthArg extends Dict {
        public AuthArg(AuthContext authContext) {
            put("authContext", authContext);
        }
    }

    @Data
    static class RolePojo {
        private String roleCode;
        private String roleName;
    }

    @Data
    static class RoleUserPageInfoResponse {
        private List<String> users;
        private PageInfo pageInfo;
    }

    @Data
    static class PageInfo {
        private int total;
        private int currentPage;
        private int pageSize;
        private int totalPage;


        public PageInfo(Integer currentPage, Integer pageSize) {
            this.currentPage = currentPage;
            this.pageSize = pageSize;
        }
    }


    public interface AuthConstant {

        String SYSTEM_USERID = "-10000";

        interface AppId {
            String CRM = "CRM";
            String SYSTEM = "facishare-system";
            /**
             * CRM+facishare-system
             */
            String ALL = "All";
        }

        interface ROLE_CODE {

            String SYSTEM_ROLE = "99";
        }
    }
}
