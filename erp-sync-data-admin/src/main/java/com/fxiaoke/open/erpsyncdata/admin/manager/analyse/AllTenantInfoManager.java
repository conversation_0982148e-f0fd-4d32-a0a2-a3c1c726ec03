package com.fxiaoke.open.erpsyncdata.admin.manager.analyse;

import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.multi.SetValueMap;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.converter.EIEAConverter;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.fxiaoke.open.erpsyncdata.admin.model.ExpiringWeakReference;
import com.fxiaoke.open.erpsyncdata.admin.model.fstool.SystemSearch;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.ErRemoteManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigRouteManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.AdminConnectorDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.BaseConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 */
@Service
public class AllTenantInfoManager {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ErRemoteManager erRemoteManager;
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    private ExpiringWeakReference<SystemSearch.InfoHolder> infoHolderRef;

    private final Lock lock = new ReentrantLock();

    @Cached(cacheType = CacheType.BOTH,
            name = "AllAdminTenantInfoList.",
            expire = 120,
            localExpire = 10,
            timeUnit = TimeUnit.MINUTES)
    public List<AdminTenantInfo> getAllAdminTenantInfoList() {
        List<String> allTenantIdList = erpConnectInfoDao.listTenantId();
        List<AdminTenantInfo> tenantInfoList = new ArrayList<>();
        Map<String, SimpleEnterpriseData> simpleEnterpriseMap = erRemoteManager.batchGetSimpleEnterprise(allTenantIdList);
        //路由
        Map<String, String> tenant2RouteMap = configRouteManager.getTenant2Db();
        Map<String, List<String>> tenantId2Level = crmRemoteManager.queryEnterpriseLevelLabel(allTenantIdList);
        //getAllAdminTenantInfoList缓存刷新时，同时获取最新的并且刷新这个信息的缓存
        SystemSearch.InfoHolder infoHolder = getInfoHolder(true);
        Map<String, SystemSearch.TenantInfo> dc2TenantInfo = infoHolder.getDc2TenantInfo();
        //仅正式企业有值
        SetValueMap<String, String> ei2ConnectorKeys = buildEi2ConnectorNames(infoHolder, dc2TenantInfo);
        SetValueMap<String, String> ei2SystemNames = buildEi2SystemNames(infoHolder, dc2TenantInfo);
        for (String tenantId : allTenantIdList) {
            AdminTenantInfo tenantInfo = new AdminTenantInfo();
            ErpSyncDataBackStageEnvironmentEnum tenantEnv = tenantEnvManager.getTenantAllModelEnv(tenantId);
            SimpleEnterpriseData simpleEnterprise = simpleEnterpriseMap.get(tenantId);
            String enterpriseName = "notFound";
            String enterpriseAccount = "notFound";
            if (simpleEnterprise != null) {
                enterpriseName = simpleEnterprise.getEnterpriseName();
                enterpriseAccount = simpleEnterprise.getEnterpriseAccount();
                tenantInfo.setRunStatus(simpleEnterprise.getRunStatus());
            }
            tenantInfo.setConnectorKeys(ei2ConnectorKeys.get(tenantId));
            tenantInfo.setSystemNames(ei2SystemNames.get(tenantId));
            tenantInfo.setTenantId(tenantId);
            tenantInfo.setTenantEnv(tenantEnv);
            tenantInfo.setEnterpriseAccount(enterpriseAccount);
            tenantInfo.setEnterpriseName(enterpriseName);
            tenantInfo.setPgRoute(tenant2RouteMap.getOrDefault(tenantId, "NotFound"));
            //填充客户级别,可能为null
            tenantInfo.setEnterpriseLevel(tenantId2Level.get(tenantId));
            tenantInfoList.add(tenantInfo);
        }
        return tenantInfoList;
    }

    private static @NotNull SetValueMap<String, String> buildEi2SystemNames(SystemSearch.InfoHolder infoHolder, Map<String, SystemSearch.TenantInfo> dc2TenantInfo) {
        SetValueMap<String,String> ei2SystemNames = new SetValueMap<>();
        infoHolder.getSystemNameKey2DcId().forEach((systemName, dcs)->{
            for (String dc : dcs) {
                String ei = dc2TenantInfo.get(dc).getEi().toString();
                ei2SystemNames.putValue(ei,systemName);
            }
        });
        return ei2SystemNames;
    }

    private static @NotNull SetValueMap<String, String> buildEi2ConnectorNames(SystemSearch.InfoHolder infoHolder, Map<String, SystemSearch.TenantInfo> dc2TenantInfo) {
        SetValueMap<String,String> ei2ConnectorKeys = new SetValueMap<>();
        infoHolder.getConnectorKey2DcId().forEach((connectorKey, dcs)->{
            for (String dc : dcs) {
                String ei = dc2TenantInfo.get(dc).getEi().toString();
                ei2ConnectorKeys.putValue(ei,connectorKey);
            }
        });
        return ei2ConnectorKeys;
    }

    @NotNull
    public SystemSearch.InfoHolder getInfoHolder(boolean refreshCache) {
        SystemSearch.InfoHolder infoHolder = null;
        if (infoHolderRef != null) {
            //获取引用值，获取成功后，就不会被gc
            infoHolder = infoHolderRef.get();
        }
        if (infoHolder == null || refreshCache) {
            if (lock.tryLock()) {
                try {
                    //先从redis缓存取
                    String cache = redisCacheManager.getCache(CommonConstant.REDIS_CACHE_SYSTEM_INFO_HOLDER);
                    if (cache == null || refreshCache) {
                        infoHolder = new SystemSearch.InfoHolder();
                        //构建
                        //初始化数据
                        loadConnectInfo(infoHolder);
                        redisCacheManager.setCache(CommonConstant.REDIS_CACHE_SYSTEM_INFO_HOLDER, JacksonUtil.toJson(infoHolder), TimeUnit.DAYS.toSeconds(1L));
                    } else {
                        infoHolder = JacksonUtil.fromJson(cache, SystemSearch.InfoHolder.class);
                    }
                    //本地10分钟过期
                    infoHolderRef = new ExpiringWeakReference<>(infoHolder, TimeUnit.MINUTES, 10L);
                } finally {
                    lock.unlock();
                }
            }else {
                throw new ErpSyncDataException("info is being initialized. Please try again later!");
            }
        }
        return infoHolder;
    }

    private void loadConnectInfo(SystemSearch.InfoHolder infoHolder) {
        String searchAfter = "";
        Map<Integer, String> ei2EaMap = new HashMap<>();
        for (int i = 0; i < 10000; i++) {
            //最多加载100,0000条数据
            List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.list100ByIdAfter(searchAfter);
            if (erpConnectInfoEntities.isEmpty()) {
                break;
            }
            Set<Integer> eis = erpConnectInfoEntities.stream()
                    .map(v -> Opt.ofTry(() -> Integer.parseInt(v.getTenantId())).get())
                    .filter(v -> v != null)
                    .collect(Collectors.toSet());
            updateEi2Ea(eis, ei2EaMap);
            searchAfter = erpConnectInfoEntities.get(erpConnectInfoEntities.size() - 1).getId();
            for (ErpConnectInfoEntity connectInfo : erpConnectInfoEntities) {
                Integer ei = Opt.ofTry(() -> Integer.parseInt(connectInfo.getTenantId())).get();
                String ea = ei2EaMap.get(ei);
                if (ei == null || ea == null) {
                    continue;
                }
                if (StrUtil.endWithIgnoreCase(ea, "_sandbox")
                        || StrUtil.startWith(ea, "fktest")) {
                    //测试或沙盒企业不统计
                    continue;
                }
                SystemSearch.TenantInfo tenantInfo = new SystemSearch.TenantInfo();
                tenantInfo.setEi(ei);
                tenantInfo.setEa(ea);
                ErpChannelEnum channel = connectInfo.getChannel();
                if (channel != ErpChannelEnum.CRM) {
                    Connector connector = AllConnectorUtil.getByChannelAndConnectParam(channel, connectInfo.getConnectParams());
                    String dcId = connectInfo.getId();
                    infoHolder.getConnectorKey2DcId().putValue(connector.getKey(), dcId);
                    //从连接信息取系统名称
                    BaseConnectParam connectParam = channel.safeGetConnectParam(connectInfo.getConnectParams());
                    String systemName = null;
                    if (connectParam != null) {
                        systemName = connectParam.getSystemName();
                    }
                    if (systemName == null) {
                        systemName = channel.getDefaultName();
                    }
                    //做一些处理,移除空格、大写转小写
                    systemName = StrUtil.cleanBlank(systemName).toLowerCase();
                    if (StrUtil.isNotBlank(systemName)) {
                        infoHolder.getSystemNameKey2DcId().putValue(systemName, dcId);
                    }
                    infoHolder.getDc2TenantInfo().put(dcId, tenantInfo);
                }
            }
        }
    }

    /**
     * ei2eaMap
     */
    private void updateEi2Ea(Set<Integer> eis, Map<Integer, String> ei2EaMap) {
        eis.removeIf(ei2EaMap::containsKey);
        if (!eis.isEmpty()) {
            //查询
            Map<Integer, String> newEi2EaMap = eieaConverter.enterpriseIdToAccount(eis);
            ei2EaMap.putAll(newEi2EaMap);
        }
    }
}
