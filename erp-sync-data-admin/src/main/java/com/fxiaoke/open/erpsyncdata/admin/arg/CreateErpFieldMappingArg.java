package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;

@Data
@ApiModel
public class CreateErpFieldMappingArg implements Serializable {
    private static final long serialVersionUID = 238714512355793735L;


    /*** 数据中心id*/
    private String dataCenterId;

    /*** 渠道 */
    private ErpChannelEnum channel;

    /*** 基础数据类型*/
    private ErpFieldTypeEnum dataType;

    /*** 纷享数据id*/
    private String fsDataId;

    /*** 纷享数据name*/
    private String fsDataName;

    /*** erp数据id*/
    private String erpDataId;

    /*** erp数据name*/
    private String erpDataName;
}
