package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotifyType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AddAlarmRuleModel implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 企业ei
     */
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 集成流ID列表
     *
     */
    private List<String> ployDetailIdList;

    /**
     * 告警规则类型
     */
    private AlarmRuleType alarmRuleType;

    /**
     * 告警规则类型
     */
    private String alarmRuleName;

    /**
     * 告警类型
     */
    private AlarmType alarmType;

    /**
     * 告警等级
     */
    private AlarmLevel alarmLevel;

    /**
     * 告警阀值
     */
    private Integer threshold;
    /**
     * 是否告警：POLLING_NOT_FIRST_ERP
     */
    private Boolean isAlarmPollingNotFirstPageError;
    /**
     * 通知类型
     *
     */
    private List<NotifyType> notifyType;

    /**
     * 通知人员ID列表
     *
     */
    private List<String> userIdList;

    /**
     * 通知角色ID列表
     *
     */
    private List<String> roleIdList;
}
