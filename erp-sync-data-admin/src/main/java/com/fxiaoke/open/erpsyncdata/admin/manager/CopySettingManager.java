package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.fxiaoke.open.erpsyncdata.admin.constant.CreateObjectEnum;
import com.fxiaoke.open.erpsyncdata.admin.model.CopyErpSettingOptions;
import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.SyncQuotaService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QuickCopySettingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SubDetailExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/17 16:15 复制相关配置
 * @Version 1.0
 */
@Component
@Slf4j
public class CopySettingManager {

    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpU8EaiConfigDao erpU8EaiConfigDao;
    @Autowired
    private ErpDbProxyConfigDao erpDbProxyConfigDao;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private SyncQuotaService syncQuotaService;
    @Autowired
    private DataCenterManager dataCenterManager;

    /**
     * 新建连接器走这里的逻辑
     *
     * @param quickCopySettingArg
     * @param lang
     * @return
     */
    public Result<String> handlerCopySetting(QuickCopySettingArg quickCopySettingArg,String lang) {
        Result<String> copyResult = Result.newSuccess();
        if (quickCopySettingArg.getHasCopyExistsConnect()) {
            //复制连接器时，检查配额
            SyncQuota syncQuota = syncQuotaService.getQuota(quickCopySettingArg.getTenantId(), false,false).safeData();
            int srcEnableStreamCount = adminSyncPloyDetailDao.setGlobalTenant(quickCopySettingArg.getTenantId()).
                    countEnableByDcId(quickCopySettingArg.getTenantId(), quickCopySettingArg.getSourceDataCenterId());//只复制启用状态的
            if (srcEnableStreamCount>syncQuota.getStreamQuota()-syncQuota.getStreamUsed()){
                return Result.newErrorByI18N(I18NStringEnum.s651.getI18nValue(),
                        I18NStringEnum.s651.getI18nKey(),
                        null);
            }
        }
        ConnectInfoResult connectInfoResult=new ConnectInfoResult();
        connectInfoResult.setChannel(quickCopySettingArg.getErpChannelEnum());
        connectInfoResult.setDataCenterName(quickCopySettingArg.getDestDataCenterName());
        connectInfoResult.setConnectorKey(quickCopySettingArg.getConnectorKey());
        Result<String> dataCenterResult = connectInfoService.initDataCenterInfo(quickCopySettingArg.getTenantId(), -1000, connectInfoResult,!quickCopySettingArg.getHasCopyExistsConnect(),lang);
        if(!dataCenterResult.isSuccess()){
            return Result.copy(dataCenterResult);
        }
        if(!quickCopySettingArg.getHasCopyExistsConnect()&&dataCenterResult.isSuccess()){
            return Result.newSuccess(dataCenterResult.getData());
        }
        quickCopySettingArg.setDestDataCenterId(dataCenterResult.getData());
        //源连接器序号
        int srcDcSeq = dataCenterManager.getDataCenterSeq(quickCopySettingArg.getTenantId(), quickCopySettingArg.getSourceDataCenterId());
        //目标连接器序号
        int destDcSeq = dataCenterManager.getDataCenterSeq(quickCopySettingArg.getTenantId(), quickCopySettingArg.getDestDataCenterId());
        switch (quickCopySettingArg.getErpChannelEnum()) {
            case ERP_K3CLOUD:
            case ERP_K3CLOUD_ULTIMATE:
            case ERP_SAP:
            case ERP_U8_EAI:
            case ERP_DB_PROXY:
            case ERP_JDY:
                return copyErpSetting(quickCopySettingArg.getTenantId(), quickCopySettingArg.getSourceDataCenterId(), quickCopySettingArg.getDestDataCenterId(), CopyErpSettingOptions.of().oldSuffix(buildSuffix(srcDcSeq)).newSuffix(buildSuffix(destDcSeq)));
            case STANDARD_CHANNEL:
                return copyErpSetting(quickCopySettingArg.getTenantId(), quickCopySettingArg.getSourceDataCenterId(), quickCopySettingArg.getDestDataCenterId(), CopyErpSettingOptions.of().oldSuffix("_" + srcDcSeq).newSuffix("_" + destDcSeq));
            default:
                break;

        }
        return copyResult;
    }

    private String buildSuffix(int dcSeq) {
        if (dcSeq <= 0) {
            return "";
        } else {
            return "_" + dcSeq;
        }
    }

    public Result<String> copyErpSetting(String tenantId, String fromDataCenterId, String destDataCenterId, CopyErpSettingOptions options) {
        String oldSuffix = options.oldSuffix();
        String newSuffix = options.newSuffix();
        if(StringUtils.isAnyEmpty(tenantId,fromDataCenterId,destDataCenterId)){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        //已存在该渠道的连接信息个数
        copyErpObj(tenantId, fromDataCenterId, destDataCenterId, oldSuffix, newSuffix);
        //TODO 记录插入成功的日志
        //复制relation-ship表
        copyErpRelationShip(tenantId, fromDataCenterId, destDataCenterId, oldSuffix, newSuffix);
        //复制erp-field表
        copyErpField(tenantId, fromDataCenterId, destDataCenterId, oldSuffix, newSuffix);
        //复制ERP字段扩展表数据
        copyErpFieldExtend(tenantId, fromDataCenterId, destDataCenterId, oldSuffix, newSuffix);
        //复制groovy 表
        copyGroovy(tenantId, fromDataCenterId, destDataCenterId);
        //复制U8-
        copyErpU8EaiConfig(tenantId, fromDataCenterId, destDataCenterId);
        //复制集成流
        copyErpIntegrationStream(tenantId, fromDataCenterId, destDataCenterId,options);
        //db连接器复制erp_db_proxy_config
        copyDBConfig(tenantId, fromDataCenterId, destDataCenterId);
        return Result.newSuccess(destDataCenterId);
    }


    private Integer getConnectSuffix(String tenantId, ErpChannelEnum erpChannelEnum) {
        ErpConnectInfoEntity connQuery = new ErpConnectInfoEntity();
        connQuery.setTenantId(tenantId);
        connQuery.setChannel(erpChannelEnum);
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(connQuery);
        return erpConnectInfoEntities.size();
    }


    public int copyGroovy(String tenantId, String fromDataCenterId, String destDataCenterId) {
        ErpObjGroovyEntity groovyQueryArg = new ErpObjGroovyEntity();
        groovyQueryArg.setTenantId(tenantId);
        groovyQueryArg.setDataCenterId(fromDataCenterId);
        List<ErpObjGroovyEntity> groovyEntities = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(groovyQueryArg);
        groovyEntities.forEach(v -> {
            v.setId(idGenerator.get());
            v.setTenantId(tenantId);
            v.setDataCenterId(destDataCenterId);
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        int groovyCount = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(groovyEntities);
        log.info("copy setting groovy count:{}", groovyCount);
        return groovyCount;
    }

    public void copyErpU8EaiConfig(String tenantId, String fromDataCenterId,String destDataCenterId) {
        ErpU8EaiConfigEntity entity = new ErpU8EaiConfigEntity();
        entity.setTenantId(tenantId);
        entity.setDataCenterId(fromDataCenterId);
        List<ErpU8EaiConfigEntity> entities = erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(entity);
        if(CollectionUtils.isEmpty(entities)){
            return;
        }
        entities.forEach(v -> {
            v.setId(idGenerator.get());
            v.setTenantId(tenantId);
            v.setDataCenterId(destDataCenterId);
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        int i2 = erpU8EaiConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(entities);
        log.info("copy erp sync config,insert ErpU8EaiConfigEntity ,{}", i2);
    }

    public void copyDBConfig(String tenantId, String fromDataCenterId,String destDataCenterId) {
        ErpDBProxyConfigEntity entity = new ErpDBProxyConfigEntity();
        entity.setTenantId(tenantId);
        entity.setDataCenterId(fromDataCenterId);

        List<ErpDBProxyConfigEntity> erpDBProxyConfigEntities = erpDbProxyConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(entity);

        if(CollectionUtils.isEmpty(erpDBProxyConfigEntities)){
            return;
        }
        erpDBProxyConfigEntities.forEach(v -> {
            v.setId(idGenerator.get());
            v.setTenantId(tenantId);
            v.setDataCenterId(destDataCenterId);
            long now = System.currentTimeMillis();
            v.setCreateTime(now);
            v.setUpdateTime(now);
        });
        int i2 = erpDbProxyConfigDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(erpDBProxyConfigEntities);
        log.info("copy erp sync config,insert ErpU8EaiConfigEntity ,{}", i2);
    }


    public int copyErpField(String tenantId, String fromDataCenterId, String destDataCenterId,String oldSuffix,String newSuffix) {
        ErpObjectFieldEntity fieldQueryArg = new ErpObjectFieldEntity();
        fieldQueryArg.setTenantId(tenantId);
        fieldQueryArg.setDataCenterId(fromDataCenterId);
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(fieldQueryArg);
        for (ErpObjectFieldEntity erpObjectFieldEntity : erpObjectFieldEntities) {
            erpObjectFieldEntity.setDataCenterId(destDataCenterId);
            erpObjectFieldEntity.setId(idGenerator.get());
            String afterObjectName = changeSuffix(erpObjectFieldEntity.getErpObjectApiName(),oldSuffix,newSuffix);
            if(ErpFieldTypeEnum.object_reference.equals(erpObjectFieldEntity.getFieldDefineType())
                    ||ErpFieldTypeEnum.master_detail.equals(erpObjectFieldEntity.getFieldDefineType())){
                //查找关联或者主从,关联对象apiName增加后缀
                erpObjectFieldEntity.setFieldExtendValue(changeSuffix(erpObjectFieldEntity.getFieldExtendValue(),oldSuffix,newSuffix));
            }
            erpObjectFieldEntity.setErpObjectApiName(afterObjectName);
        }
        List<List<ErpObjectFieldEntity>> entities = CollUtil.split(erpObjectFieldEntities, 100);
        for (List<ErpObjectFieldEntity> entity : entities) {
            int fieldCount = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(entity);
            log.info("copy setting fieldCount :{}", fieldCount);
        }
        return erpObjectFieldEntities.size();
    }

    public int copyErpFieldExtend(String tenantId, String fromDataCenterId, String destDataCenterId, String oldSuffix, String newSuffix) {
        ErpFieldExtendEntity fieldQueryArg = new ErpFieldExtendEntity();
        fieldQueryArg.setTenantId(tenantId);
        fieldQueryArg.setDataCenterId(fromDataCenterId);
        List<ErpFieldExtendEntity> erpFieldExtendEntityList = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryList(fieldQueryArg);

        for (ErpFieldExtendEntity erpFieldExtendEntity : erpFieldExtendEntityList) {
            erpFieldExtendEntity.setDataCenterId(destDataCenterId);
            erpFieldExtendEntity.setId("copy_" + idGenerator.get());
        }
        List<List<ErpFieldExtendEntity>> entities = CollUtil.split(erpFieldExtendEntityList, 100);
        for (List<ErpFieldExtendEntity> entity : entities) {
            int fieldCount = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(entity);
            log.info("copy field extend table data, fieldCount :{}", fieldCount);
        }
        return erpFieldExtendEntityList.size();
    }

    public int copyErpRelationShip(String tenantId, String fromDataCenterId, String destDataCenterId,String oldSuffix,String newSuffix) {
        ErpObjectRelationshipEntity relationQueryArg = new ErpObjectRelationshipEntity();
        relationQueryArg.setTenantId(tenantId);
        relationQueryArg.setDataCenterId(fromDataCenterId);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(relationQueryArg);
        for (ErpObjectRelationshipEntity erpObjectRelationshipEntity : erpObjectRelationshipEntities) {
            erpObjectRelationshipEntity.setId(idGenerator.get());
            erpObjectRelationshipEntity.setDataCenterId(destDataCenterId);
            String afterObjectName = changeSuffix(erpObjectRelationshipEntity.getErpSplitObjectApiname(),oldSuffix,newSuffix);
            erpObjectRelationshipEntity.setErpSplitObjectApiname(afterObjectName);
        }
        int relationCount = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(erpObjectRelationshipEntities);
        erpObjectRelationshipDao.invalidCacheErpObj(tenantId,destDataCenterId);
        log.info("copy setting relationCount :{}", relationCount);
        return relationCount;
    }

    public int copyErpObj(String tenantId, String fromDataCenterId, String destDataCenterId,String oldSuffix, String newSuffix) {
        ErpObjectEntity queryObjArg = new ErpObjectEntity();
        queryObjArg.setTenantId(tenantId);
        queryObjArg.setDataCenterId(fromDataCenterId);
        //复制erp-object表
        List<ErpObjectEntity> erpObjectEntities =
                erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryObjArg);
        for (ErpObjectEntity objectEntity : erpObjectEntities) {
            if (objectEntity.getErpObjectType().equals(ErpObjectTypeEnum.REAL_OBJECT)) {
                objectEntity.setDataCenterId(destDataCenterId);
                objectEntity.setId(idGenerator.get());

            } else {
                if (StrUtil.isNotBlank(objectEntity.getErpObjectExtendValue())) {
                    try {
                        //孙对象在这里储存了明细对象apiName用来关联。
                        SubDetailExtend parse = SubDetailExtend.parse(objectEntity.getErpObjectExtendValue());
                        if (parse != null && parse.getDetailObjectApiName() != null) {
                            parse.setDetailObjectApiName(changeSuffix(parse.getDetailObjectApiName(), oldSuffix, newSuffix));
                            objectEntity.setErpObjectExtendValue(parse.toJson());
                        }
                    } catch (Exception e) {
                        log.info("can not parse");
                    }
                }
                objectEntity.setId(idGenerator.get());
                objectEntity.setDataCenterId(destDataCenterId);
                String afterObjectName = changeSuffix(objectEntity.getErpObjectApiName(),oldSuffix,newSuffix);
                objectEntity.setErpObjectApiName(afterObjectName);
            }
        }
        int objCount = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(erpObjectEntities);
        log.info("copy setting objCount :{}", objCount);
        return objCount;
    }

    private String changeSuffix(String splitObjApiName,String oldSuffix,String newSuffix){
        return StrUtil.removeSuffix(splitObjApiName, oldSuffix) + newSuffix;
    }

    public int copyErpIntegrationStream(String tenantId, String fromDataCenterId, String destDataCenterId,CopyErpSettingOptions options) {
        String oldSuffix = options.oldSuffix();
        String newSuffix = options.newSuffix();
        List<SyncPloyDetailEntity> syncPloyDetailActives = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).
                listBySourceOrDestDcIdAndObjApiName(tenantId, fromDataCenterId, null, options.copyAllStream() ? null : 1, null, 0, 1000);//默认只复制启用状态的

        if (syncPloyDetailActives.isEmpty()) {
            log.info("copy erp sync config,insert SyncPloyDetailEntity empty");
            return 0;
        }
        for (SyncPloyDetailEntity syncPloyDetailActive : syncPloyDetailActives) {
            syncPloyDetailActive.setId(idGenerator.get());
            if (syncPloyDetailActive.getSourceTenantType().equals(TenantTypeEnum.CRM.getType())) {
                syncPloyDetailActive.setDestDataCenterId(destDataCenterId);
                String afterDestApiName = changeSuffix(syncPloyDetailActive.getDestObjectApiName(),oldSuffix,newSuffix);
                syncPloyDetailActive.setDestObjectApiName(afterDestApiName);
                for (FieldMappingData fieldMapping : syncPloyDetailActive.getFieldMappings()) {
                    if(ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getDestType())||
                            ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getDestType())){
                        String destName=changeSuffix(fieldMapping.getDestTargetApiName(),oldSuffix,newSuffix);
                        fieldMapping.setDestTargetApiName(destName);
                    }
                }
                //修改明细
                if(ObjectUtils.isNotEmpty(syncPloyDetailActive.getDetailObjectMappings())){
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailActive.getDetailObjectMappings()) {
                        detailObjectMapping.setDestObjectApiName(changeSuffix(detailObjectMapping.getDestObjectApiName(),oldSuffix,newSuffix));
                        for (FieldMappingData fieldMapping : detailObjectMapping.getFieldMappings()) {
                            if(ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getDestType())||
                                    ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getDestType())){
                                fieldMapping.setDestTargetApiName(changeSuffix(fieldMapping.getDestTargetApiName(),oldSuffix,newSuffix));
                            }
                        }
                    }
                }
                //source=CRM不修改同步范围

            } else {
                syncPloyDetailActive.setSourceDataCenterId(destDataCenterId);
                String afterDestApiName = changeSuffix(syncPloyDetailActive.getSourceObjectApiName(),oldSuffix,newSuffix);
                syncPloyDetailActive.setSourceObjectApiName(afterDestApiName);
                for (FieldMappingData fieldMapping : syncPloyDetailActive.getFieldMappings()) {
                    if(ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getSourceType())||
                            ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getSourceType())){
                        fieldMapping.setSourceTargetApiName(changeSuffix(fieldMapping.getSourceTargetApiName(),oldSuffix,newSuffix));
                    }
                }
                //修改明细
                if(ObjectUtils.isNotEmpty(syncPloyDetailActive.getDetailObjectMappings())){
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : syncPloyDetailActive.getDetailObjectMappings()) {
                        detailObjectMapping.setSourceObjectApiName(changeSuffix(detailObjectMapping.getSourceObjectApiName(),oldSuffix,newSuffix));
                        for (FieldMappingData fieldMapping : detailObjectMapping.getFieldMappings()) {
                            if(ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getSourceType())||
                                    ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getSourceType())){
                                String sourceName= changeSuffix(fieldMapping.getSourceTargetApiName(),oldSuffix,newSuffix);
                                fieldMapping.setSourceTargetApiName(sourceName);
                            }
                        }
                    }
                }
                //修改同步范围
                if(ObjectUtils.isNotEmpty(syncPloyDetailActive.getSyncConditions())){
                    syncPloyDetailActive.getSyncConditions().setApiName(afterDestApiName);
                }
                //修改明细同步范围
                if(!CollectionUtils.isEmpty(syncPloyDetailActive.getDetailObjectSyncConditions())){
                    for (SyncConditionsData detailObjectSyncCondition : syncPloyDetailActive.getDetailObjectSyncConditions()) {
                        detailObjectSyncCondition.setApiName(changeSuffix(detailObjectSyncCondition.getApiName(),oldSuffix,newSuffix));
                    }
                }

            }
            syncPloyDetailActive.setCreateTime(System.currentTimeMillis());
            syncPloyDetailActive.setUpdateTime(System.currentTimeMillis());
            syncPloyDetailActive.setStatus(2);
        }
        int ployDetailCount = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insertList(Lists.newArrayList(syncPloyDetailActives));
        log.info("copy syncPloyDetail count:{}", ployDetailCount);
        return ployDetailCount;
    }

    public void initJdySetting(){
        String erpObjectJson= CreateObjectEnum.getJDYErpOBJ();
        List<ErpObjectEntity> erpObjectEntities= JSONArray.parseArray(erpObjectJson,ErpObjectEntity.class);
        erpObjectDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).batchInsert(erpObjectEntities);
        String erpOBJField= CreateObjectEnum.getJDYErpOBJField();
        List<ErpObjectFieldEntity> erpObjectFieldEntities= JSONArray.parseArray(erpOBJField,ErpObjectFieldEntity.class);
        erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).batchInsert(erpObjectFieldEntities);
        String jdyErpRelationShip = CreateObjectEnum.getJDYErpRelationShip();
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities= JSONArray.parseArray(jdyErpRelationShip,ErpObjectRelationshipEntity.class);
        erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).batchInsert(erpObjectRelationshipEntities);

    }
    public void initSyncPloyDetail(String tenantId){
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
        String crmDataCenterId="";
        String jdyDataCenterId="";
        for (ErpConnectInfoEntity erpConnectInfoEntity : erpConnectInfoEntities) {
            if(erpConnectInfoEntity.getChannel().equals(ErpChannelEnum.CRM)){
                crmDataCenterId=erpConnectInfoEntity.getId();
            }
            if(erpConnectInfoEntity.getChannel().equals(ErpChannelEnum.ERP_JDY)){
                jdyDataCenterId=erpConnectInfoEntity.getId();
            }
        }
        String syncPloyDetail = CreateObjectEnum.getSyncPloyDetail();
        List<SyncPloyDetailEntity> syncPloyDetailEntities = JSONArray.parseArray(syncPloyDetail, SyncPloyDetailEntity.class);
        for (SyncPloyDetailEntity syncPloyDetailEntity : syncPloyDetailEntities) {
            if(syncPloyDetailEntity.getSourceTenantType().equals(TenantTypeEnum.CRM.getType())){
                syncPloyDetailEntity.setSourceDataCenterId(crmDataCenterId);
                syncPloyDetailEntity.setDestDataCenterId(jdyDataCenterId);
            }else{
                syncPloyDetailEntity.setSourceDataCenterId(jdyDataCenterId);
                syncPloyDetailEntity.setDestDataCenterId(crmDataCenterId);
            }
            syncPloyDetailEntity.setTenantId(tenantId);
            syncPloyDetailEntity.setCreateTime(System.currentTimeMillis());
            syncPloyDetailEntity.setUpdateTime(System.currentTimeMillis());
            syncPloyDetailEntity.setStatus(SyncPloyDetailStatusEnum.DISABLE.getStatus());
            syncPloyDetailEntity.setId(idGenerator.get());
        }
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insertList(syncPloyDetailEntities);
    }

}
