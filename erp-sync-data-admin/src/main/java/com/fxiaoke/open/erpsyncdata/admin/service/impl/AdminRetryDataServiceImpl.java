package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo;
import com.fxiaoke.open.erpsyncdata.admin.result.PollingTempRecordResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ReTrySendMqResult;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.AdminRetryDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.arg.RetryConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.PollingTempFailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RetrySendMqDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.PollingTempFailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReTrySendMq;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo.Fields.enterpriseLevel;
import static com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo.Fields.pgRoute;
import static com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo.Fields.runStatus;

/**
 * <AUTHOR>
 * @create 2024/5/21 19:43
 * @desc
 */
@Service
@Slf4j
public class AdminRetryDataServiceImpl implements AdminRetryDataService {

    @Autowired
    private RetrySendMqDao retrySendMqDao;
    @Autowired
    private AsyncReTryIfFailedManager asyncReTryIfFailedManager;
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private PollingTempFailDao pollingTempFailDao;
    @Autowired
    private ProxyHttpClient proxyHttpClient;


    /**
     * 重试任务，web模块触发的逻辑是通过http请求到task.保持定时任务的逻辑。
     *
     * @return
     */


    @Override
    public Result<Integer> retryDataByType(RetryConfig retryConfig) {
        String url = new StringBuilder().append(ConfigCenter.TASK_BASE_URL).append("/erp/syncdata/probedata/executeFailDataRetry").toString();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        Result triggerResponse=proxyHttpClient.postUrl(url, retryConfig, header,new TypeReference<Result>(){});
        return Result.newSuccess();
    }

    @Override
    public Result<Amis.Crud<ReTrySendMqResult>> queryDataByType(String tenantId, String dataType, List<Integer> status, Long startTime, Long endTime, Integer limit, Integer offset) {
        Long countRetryData = retrySendMqDao.countRetryData(tenantId, dataType,status ,startTime, endTime);
        List<ReTrySendMq> reTrySendMqs = retrySendMqDao.listRetrySendMqIdList(tenantId, dataType, startTime, endTime, status, limit, offset);
        List<ReTrySendMqResult> queryResult= Lists.newArrayList();
        for (ReTrySendMq reTrySendMq : reTrySendMqs) {
            ReTrySendMqResult result= BeanUtil.copy(reTrySendMq,ReTrySendMqResult.class);
            result.setId(reTrySendMq.getId().toString());
            result.setCreateTime(reTrySendMq.getCreateTime().getTime());
            result.setUpdateTime(reTrySendMq.getUpdateTime().getTime());
            queryResult.add(result);
        }
        //构建columns
        Amis.Crud<ReTrySendMqResult> dataPage = Amis.Crud.of(queryResult);
        dataPage.setTotal(Integer.valueOf(countRetryData.toString()));
        return Result.newSuccess(dataPage);
    }

    @Override
    public Result<Amis.Crud<PollingTempRecordResult>> queryDataByTempType(String tenantId, List<Integer> status, Long startTime, Long endTime, Integer limit, Integer offset) {
        Integer totalNum = pollingTempFailDao.calculateCount(tenantId, status, startTime, endTime);
        List<PollingTempFailEntity> pollingTempFailEntities = pollingTempFailDao.listByData(tenantId, limit, offset, status,startTime,endTime);
        List<PollingTempRecordResult> queryResult= Lists.newArrayList();
        for (PollingTempFailEntity pollingTempFailEntity : pollingTempFailEntities) {
            PollingTempRecordResult result= BeanUtil.copy(pollingTempFailEntity,PollingTempRecordResult.class);
            result.setId(pollingTempFailEntity.getId().toString());
            queryResult.add(result);
        }
        //构建columns
        Amis.Crud<PollingTempRecordResult> dataPage = Amis.Crud.of(queryResult);
        dataPage.setTotal(Integer.valueOf(totalNum));
        return Result.newSuccess(dataPage);
    }

    @Override
    public Result<Amis.Crud<PollingTempRecordResult>> retryDataByTempType(RetryConfig retryConfig) {
        String url = new StringBuilder().append(ConfigCenter.TASK_BASE_URL).append("/erp/syncdata/probedata/executeTempData").toString();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        Result triggerResponse=proxyHttpClient.postUrl(url, retryConfig, header,new TypeReference<Result>(){});
        return Result.newSuccess();
    }

    private Collection<Amis.Col> buildTenantCols(List<AdminTenantInfo> tenantInfoList) {
        Amis.ColHelper<AdminTenantInfo> colHelper = Amis.ColHelper.parse(AdminTenantInfo.class,tenantInfoList);
        colHelper.filterable(enterpriseLevel, v -> v.getEnterpriseLevel());
        colHelper.filterable(runStatus, v -> v.getRunStatus());
        colHelper.filterable(pgRoute, v -> v.getPgRoute());
        return colHelper.getCols();
    }
}
