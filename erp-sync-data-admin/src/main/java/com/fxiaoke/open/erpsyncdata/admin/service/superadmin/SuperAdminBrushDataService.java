package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.admin.arg.CopyBetweenDbArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.CopyLog2ClickHouseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 10:26 2021/11/30
 * @Desc:
 */
public interface SuperAdminBrushDataService {

    Result<String>  brushTableData(List<String> sourceTenantId,String destDataBaseTenantId);
    Result<String> brushDispatcherTime(List<String> sourceTenantIds, Integer time, Boolean cancel);
    Result<String>  copyBetweenDb(CopyBetweenDbArg arg);
    Result<Map<String, Set<String>>> getTenantIdRoute(Boolean onlySandBox,String destRoute);

    Result<String> copyLog2ClickHouse(CopyLog2ClickHouseArg arg);
}
