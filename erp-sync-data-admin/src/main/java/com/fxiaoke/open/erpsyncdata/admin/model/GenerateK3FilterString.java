package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 10:35:54
 */
public interface GenerateK3FilterString {
    @Data
    class Arg {
        /**
         * 中间对象apiName
         */
        private String objAPIName;

        /**
         * operate可参考
         *
         * @see com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum
         */
        @ApiModelProperty("配置的筛选条件")
        private List<List<FilterDataVo>> filters;

        @ApiModelProperty("最小时间")
        private Long startTime = System.currentTimeMillis() - 24 * 3600 * 1000L;

        @ApiModelProperty("最大时间")
        private Long endTime = System.currentTimeMillis();

        @ApiModelProperty("偏移量")
        private Integer offset = 0;

        @ApiModelProperty("限制数量")
        private Integer limit = 100;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private String filterString;
    }
}
