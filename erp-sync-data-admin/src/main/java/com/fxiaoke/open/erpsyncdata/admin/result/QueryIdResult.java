package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 17:16 2022/11/17
 * @Desc:
 */
@Getter
@Setter
@ApiModel
public class QueryIdResult<T> implements Serializable {
    @ApiModelProperty("数据")
    protected T data;
    @ApiModelProperty("查询是否成功")
    private Boolean isSuccess=false;
    @ApiModelProperty("查询id")
    private String queryId;
}
