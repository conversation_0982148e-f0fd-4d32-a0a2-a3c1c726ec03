package com.fxiaoke.open.erpsyncdata.admin.model.fstool;

import cn.hutool.core.map.multi.SetValueMap;
import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 */
public class SystemSearch {

    @Data
    public static class Arg{
        /**
         * 通过系统名称 搜索连接器
         */
        private String systemName;
        /**
         * 通过connectorKey 查询连接器
         */
        private String connectorKey;

        /**
         * CRM往ERP 的CRM对象apiName
         */
        private String fromCrmObjApiName;
        /**
         * ERP往CRM 的CRM对象apiName
         */
        private String toCrmObjApiName;
    }

    @Data
    public static class TenantInfo {
        @ApiModelProperty("企业Id")
        private Integer ei;
        @ApiModelProperty("企业账号")
        private String ea;
        @ApiModelProperty("是否沙盒或测试企业")
        private boolean isTest = false;
    }



    @Data
    @AllArgsConstructor(staticName = "of")
    public static class ConnectorInfo {
        private String key;
        private String name;
    }

    @Data
    public static class InfoHolder implements RAMEstimable {
        private SetValueMap<String, String> connectorKey2DcId = new SetValueMap<>();
        private SetValueMap<String, String> systemNameKey2DcId = new SetValueMap<>();
        private SetValueMap<String, String> toCrmApiName2DcId = new SetValueMap<>();
        private SetValueMap<String, String> fromCrmApiName2DcId = new SetValueMap<>();
        private Map<String, TenantInfo> dc2TenantInfo = new HashMap<>();

        @Override
        public long ramBytesUsed(int depth) {
            return RamUsageEstimateUtil.sizeOfObject(connectorKey2DcId, depth)
                    + RamUsageEstimateUtil.sizeOfObject(systemNameKey2DcId, depth)
                    + RamUsageEstimateUtil.sizeOfObject(toCrmApiName2DcId, depth)
                    + RamUsageEstimateUtil.sizeOfObject(fromCrmApiName2DcId, depth)
                    + RamUsageEstimateUtil.sizeOfObject(dc2TenantInfo, depth)
                    ;
        }
    }
}
