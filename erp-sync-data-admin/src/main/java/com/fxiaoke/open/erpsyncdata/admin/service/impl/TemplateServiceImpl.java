package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateObjectMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.model.Template;
import com.fxiaoke.open.erpsyncdata.admin.model.Template.StreamInfo;
import com.fxiaoke.open.erpsyncdata.admin.model.Template.TemplateErpObjInfo;
import com.fxiaoke.open.erpsyncdata.admin.model.Template.TemplateErpSplitObjInfo;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.admin.service.TemplateService;
import com.fxiaoke.open.erpsyncdata.admin.utils.TemplateUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.BaseResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.CrmConfig;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmObjAndFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjFieldsDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplateDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.TemplateOptionDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.UsageMetricDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateOptionDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/3/28
 */
@Service
@Slf4j
public class TemplateServiceImpl implements TemplateService {
    @Autowired
    private TemplateDao templateDao;
    @Autowired
    private TemplateOptionDao templateOptionDao;
    @Autowired
    private ErpConnectInfoManager connectInfoManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private CrmObjAndFieldManager crmObjAndFieldManager;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private UsageMetricDao usageMetricDao;
    @Autowired
    private I18NStringManager i18NStringManager;

    private final String cond_crmobj = "crmobj-";
    private final String cond_crmconfig = "crmconfig-";
    private final String cond_stockconfig = "stockconfig-";
    private final String cond_stream = "stream-";
    /**
     * 在集成平台中的配置
     */
    private final String cond_tenantcnfig = "tenantconfig-";

    private final String conditionTitle = "## 使用此模板的前置条件";   // ignoreI18n
    //如果上面的链接出现##就可能有问题
    private final Pattern conditionPattern = Pattern.compile(conditionTitle + ".*?##", Pattern.DOTALL);

    private final List<String> K3FormOrders = ListUtil.of(ORG_Organizations, BD_Customer, BD_CommonContact, BD_UNIT, SAL_MATERIALGROUP,
            BD_MATERIAL, SAL_SaleOrder, BD_SAL_PriceList, SAL_SC_CUSTMAT, ENG_BOM, BD_STOCK, BATCH_OBJ, BD_SerialMainFile, STK_Inventory,
            SAL_OUTSTOCK, AR_RECEIVEBILL, IV_SALESOC, IV_SALESIC, null);


    @PostConstruct
    public void loadTemplateFromFile() throws IOException {
        //读取jar文件，判断是否需要更新数据库数据
        Supplier<List<TemplateDoc>> fileTemplatesSup = TemplateUtil.fileTemplatesSup;
        Supplier<List<TemplateOptionDoc>> fileOptionsSup = TemplateUtil.fileOptionsSup;
        try {
            loadTemplateFromSup(fileTemplatesSup, fileOptionsSup);
        } catch (Exception e) {
            log.error("load template failed", e);
        }
    }

    /**
     * 方便测试
     *
     * @param fileTemplatesSup
     * @param fileOptionsSup
     */
    protected void loadTemplateFromSup(Supplier<List<TemplateDoc>> fileTemplatesSup, Supplier<List<TemplateOptionDoc>> fileOptionsSup) {
        List<TemplateDoc> templateDocs = fileTemplatesSup.get();
        if (!templateDocs.isEmpty()) {
            List<TemplateDoc> templatesOld = templateDao.listAllBaseInfo();
            //检查版本只会检查一条
            if (!templatesOld.isEmpty()) {
                String version = templatesOld.get(0).getVersion();
                if (VersionComparator.INSTANCE.compare(version, templateDocs.get(0).getVersion()) >= 0) {
                    log.info("not need load template ");
                    //当数据库版本比文件大，不处理
                    return;
                }
            }
            Map<ObjectId, TemplateDoc> oldMap = templatesOld.stream().collect(Collectors.toMap(v -> v.getId(), v -> v));
            List<ObjectId> disableIds = new ArrayList<>(oldMap.keySet());
            for (TemplateDoc newTemplate : templateDocs) {
                ObjectId id = newTemplate.getId();
                if (disableIds.remove(id)) {
                    //更新数据
                    templateDao.upsertIgnore(newTemplate, oldMap.get(id).getVersion());
                } else {
                    //插入数据，带id插入
                    templateDao.replaceById(newTemplate);
                }
            }
            //停用数据
            if (!disableIds.isEmpty()) {
                templateDao.disableTemplates(disableIds);
            }
        }
        //更新了模板才更新选项，暂不支持删除选项
        List<TemplateOptionDoc> templateOptDocs = fileOptionsSup.get();
        for (TemplateOptionDoc templateOptDoc : templateOptDocs) {
            templateOptionDao.upsertIgnore(templateOptDoc);
        }
    }

    @Override
    public Result<Template.Filters> getFilters() {
        //查找有效渠道，暂时只有K3C
        List<Template.Option> channels = queryOptions("channel");
        //查找有效场景
        List<Template.Option> scenes = queryOptions("scene");
        List<Template.Option> tags = queryOptions("tag");
        Template.Filters filters = Template.Filters.of()
                .setChannels(channels)
                .setScenes(scenes)
                .setTags(tags);
        return Result.newSuccess(filters);
    }


    @Override
    public Result<List<Template.TemplateInfo>> listBaseInfo(Template.QueryBaseInfoArg arg) {
        List<TemplateDoc> allTemplateDocs = templateDao.listAllBaseInfo();
        List<Template.TemplateInfo> infos = new ArrayList<>();
        for (TemplateDoc doc : allTemplateDocs) {
            infos.add(doc2Template(doc));
        }
        //排序
        infos.sort(Comparator.comparingInt(v -> v.getOrder()));
        if (arg.getChannel() != null) {
            //渠道筛选
            infos.removeIf(v -> !arg.getChannel().equals(v.getChannel()));
        }
        if (StrUtil.isNotBlank(arg.getSceneId())) {
            //场景筛选
            infos.removeIf(v -> !CollUtil.contains(v.getSceneIds(), arg.getSceneId()));
        }
        if (arg.getTagIds() != null) {
            //标签筛选
            infos.removeIf(v -> !CollUtil.containsAny(v.getTagIds(), arg.getTagIds()));
        }
        if (!arg.isIncludeDisable()) {
            infos.removeIf(v -> !v.isEnable());
        }
        return Result.newSuccess(infos);
    }

    @Override
    public Result<Template.TemplateInfo> getAllInfo(String id) {
        TemplateDoc doc = templateDao.get(id, true);
        Template.TemplateInfo templateInfo = doc2Template(doc);
        return Result.newSuccess(templateInfo);
    }

    @Override
    public Result<Void> upsertTemplate(Template.TemplateInfo info, String oldVersion) {
        //新增
        TemplateDoc doc = template2Doc(info);
        templateDao.upsertDoc(doc, oldVersion);
        return Result.newSuccess();
    }

    @Override
    public Result<List<Template.OptionGroup>> listStreams(String tenantId) {
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantId(tenantId);
        Map<String, List<Template.Option>> groupOptions = syncPloyDetailEntities.stream()
                .filter(v -> StrUtil.isNotBlank(v.getIntegrationStreamName()))
                .collect(Collectors.groupingBy(v -> v.getSourceTenantType().equals(TenantType.ERP) ? v.getSourceDataCenterId() : v.getDestDataCenterId(),
                        Collectors.mapping(v -> Template.Option.of(v.getId(), v.getIntegrationStreamName()), Collectors.toList())));
        List<ErpConnectInfoEntity> erpConnectInfos = connectInfoManager.listByTenantId(tenantId);
        List<Template.OptionGroup> groups = erpConnectInfos.stream().map(v -> new Template.OptionGroup().setLabel(v.getDataCenterName()).setChildren(groupOptions.getOrDefault(v.getId(), new ArrayList<>()))).collect(Collectors.toList());
        return Result.newSuccess(groups);
    }

    @Override
    public Result<Template.Detail> parseTemplateFromStreams(String tenantId, List<String> streamIds) {
        //集成流信息
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantIdAndId(tenantId, streamIds);
        //获取ERP对象列表和CRM对象列表
        Set<String> crmObjs = new HashSet<>();
        for (SyncPloyDetailEntity stream : syncPloyDetailEntities) {
            if (stream.getSourceTenantType().equals(TenantType.CRM)) {
                crmObjs.add(stream.getSourceObjectApiName());
                stream.getDetailObjectMappings().stream().map(v -> v.getSourceObjectApiName()).forEach(v -> crmObjs.add(v));
            } else {
                crmObjs.add(stream.getDestObjectApiName());
                stream.getDetailObjectMappings().stream().map(v -> v.getDestObjectApiName()).forEach(v -> crmObjs.add(v));
            }
        }
        List<ObjectDescribe> crmObjDesc = crmObjs.stream().map(v -> crmObjAndFieldManager.getObjectDescribeCache(tenantId, v)).collect(Collectors.toList());
        List<ErpObjExtendDto> allErpObjs = erpObjectDao.queryAllObjExtendDTOByDc(tenantId, null);
        //获取字段
        List<ErpObjectFieldEntity> allErpFields = erpObjectFieldDao.findByTenantId(tenantId);
        //解析
        Template.Detail detail = TemplateUtil.analyse(syncPloyDetailEntities, allErpObjs, allErpFields, crmObjDesc);
        return Result.newSuccess(detail);
    }

    private TemplateDoc template2Doc(Template.TemplateInfo info) {
        TemplateDoc doc = new TemplateDoc();
        BeanUtils.copyProperties(info, doc);
        if (info.getId() != null) {
            doc.setId(new ObjectId(info.getId()));
        }
        if (info.getDetail() != null) {
            doc.setDetailStr(JSON.toJSONString(info.getDetail()));
        }
        return doc;
    }


    private Template.TemplateInfo doc2Template(TemplateDoc doc) {
        Template.TemplateInfo info = BeanUtil.copy(doc, Template.TemplateInfo.class);
        if (StrUtil.isNotBlank(doc.getDetailStr())) {
            Template.Detail detail = JSON.parseObject(doc.getDetailStr(), Template.Detail.class);
            String detailMd = detail.getDetailMd();
            //替换条件
            if (StrUtil.isBlank(detailMd)) {
                //赋值默认介绍
                detailMd = "## 模板介绍\r\n" +
                        "待补充\r\n" +
                        "## 使用此模板的前置条件\r\n" +
                        "## 对接对象和数据流向图\r\n" +
                        "![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/dataFlow1.png)";
            }
            if (CollUtil.isNotEmpty(info.getPreconditionIds())) {
                //替换前置条件
                Map<String, Template.Option> preconditionMap = getOptionsMap("precondition");
                String conditionsStr = info.getPreconditionIds().stream().filter(v -> preconditionMap.get(v) != null)
                        .map(v -> "- ✅ " + preconditionMap.get(v).getLabel() + "\r\n").collect(Collectors.joining());
                //一直到下一个标题前
                detailMd = ReUtil.replaceAll(detailMd, conditionPattern, conditionTitle + "\r\n" + conditionsStr + "##");
            }
            detail.setDetailMd(detailMd);
            info.setDetail(detail);
            if (detail.getErpObjInfos() != null) {
                //排序
                detail.getErpObjInfos().sort(Comparator.comparingInt(v -> getOrderIdByErpObjApiName(v.getMainRealObjApiName())));
            }
            if (detail.getStreamInfos() != null) {
                detail.getStreamInfos().sort(Comparator.comparingInt(v -> {
                    ObjectMappingResult mainMapping = v.getMainMapping();
                    String erpObjApiName = Objects.equals(v.getSourceTenantType(), TenantType.ERP) ? mainMapping.getSourceObjectApiName() : mainMapping.getDestObjectApiName();
                    return getOrderIdByErpObjApiName(erpObjApiName);
                }));
            }
        }
        if (doc.getId() != null) {
            info.setId(doc.getId().toString());
        }
        return info;
    }

    private int getOrderIdByErpObjApiName(String erpObjApiName) {
        int index = K3FormOrders.indexOf(erpObjApiName);
        if (index < 0) {
            index = Integer.MAX_VALUE;
        }
        return index;
    }

    @Override
    public Result<List<Template.Precondition>> preCheckCondition(String tenantId, String dcId, Template.PreCheckArg arg) {
        //获取
        Template.TemplateInfo templateInfo = getAllInfo(arg.getTemplateId()).safeData();
        if (CollUtil.isEmpty(templateInfo.getPreconditionIds())) {
            return Result.newSuccess(new ArrayList<>());
        }
        Map<String, Template.Option> optionMap = getOptionsMap("precondition");
        List<Template.Precondition> results = new ArrayList<>();
        for (String linkPrecondition : templateInfo.getPreconditionIds()) {
            Template.Option option = optionMap.get(linkPrecondition);
            if (option == null) {
                continue;
            }
            Template.Precondition precondition = BeanUtil.copy(option, Template.Precondition.class);
            boolean valid;
            if (linkPrecondition.startsWith(cond_crmobj)) {
                valid = checkCrmObj(tenantId, linkPrecondition);
            } else if (linkPrecondition.startsWith(cond_crmconfig)) {
                String configName = linkPrecondition.substring(cond_crmconfig.length());
                String validValue = "1";
                if (configName.startsWith("!")) {
                    //不开启
                    validValue = "0";
                    configName = configName.substring(1);
                }
                BaseResult<CrmConfig.Result> crmConfig = sfaApiManager.getCrmConfig(tenantId, new CrmConfig.Arg().setKey(configName));
                valid = crmConfig.isSuccess() && crmConfig.getData() != null && crmConfig.getData().getValue().equals(validValue);
            } else if (linkPrecondition.startsWith(cond_stockconfig)) {
                //进销存配置
                String[] split = linkPrecondition.split("-");
                if (split.length != 3) {
                    continue;
                }
                String configName = split[1];
                String validValue = split[2];
                BaseResult<CrmConfig.Result> crmConfig = sfaApiManager.getStockConfig(tenantId, new CrmConfig.Arg().setKey(configName));
                valid = crmConfig.isSuccess() && crmConfig.getData() != null && crmConfig.getData().getValue().equals(validValue);
            } else if (linkPrecondition.startsWith(cond_tenantcnfig)) {
                //这类会直接插入配置
                String[] split = linkPrecondition.split("-");
                if (split.length != 3) {
                    continue;
                }
                String configName = split[1];
                String validValue = split[2];
                ErpTenantConfigurationEntity config = tenantConfigurationManager.findOne(tenantId, dcId, templateInfo.getChannel().name(), configName);
                if (config == null) {
                    //插入配置
                    try {
                        config = new ErpTenantConfigurationEntity();
                        config.setTenantId(tenantId);
                        config.setType(configName);
                        config.setId(IdGenerator.get());
                        config.setChannel(templateInfo.getChannel().name());
                        config.setDataCenterId(dcId);
                        config.setConfiguration(validValue);
                        int insert = tenantConfigurationManager.insert(tenantId, config);
                    } catch (Exception e) {
                        log.error("add config error", e);
                        //异常的配置，不显示了直接
                        continue;
                    }
                    valid = true;
                } else {
                    //检查已有配置
                    valid = Objects.equals(validValue, config.getConfiguration());
                }
            } else if (linkPrecondition.startsWith(cond_stream)) {
                //检查集成流,只检查CRM对象。
                valid = checkCrmObjStream(tenantId, dcId, linkPrecondition);
            } else {
                //特殊的
                switch (linkPrecondition) {
                    case "k3connect":
                        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
                        valid = connectInfo != null && connectInfo.getConnectParams() != null;
                        break;
                    case "jskcidcheck":
                        //检查id字段
                        //找到库存的中间对象apiName
                        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectFieldDao.queryObjExtendDTO(tenantId, dcId, K3CloudForm.STK_Inventory);
                        Optional<String> idField = erpObjExtendDtos.stream().filter(v -> v.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT)).findFirst()
                                .map(v -> v.getIdFieldName());
                        valid = idField.isPresent() && idField.get().equals("FID");
                        break;
                    default:
                        //不支持的条件，不展示
                        continue;
                }
            }
            precondition.setValid(valid);
            results.add(precondition);
        }
        //排序
        results.sort(Comparator.comparingInt(v -> v.getOrder()));
        return Result.newSuccess(results);
    }

    private boolean checkCrmObj(String tenantId, String linkPrecondition) {
        boolean valid = true;
        String objApiNames = linkPrecondition.substring(cond_crmobj.length());
        for (String objApiName : objApiNames.split("&")) {
            boolean need = true;
            if (objApiName.startsWith("!")) {
                need = false;
                objApiName = objApiName.substring(1);
            }
            boolean exist = true;
            try {
                ObjectDescribe objectDescribeCache = crmObjAndFieldManager.getObjectDescribeCache(tenantId, objApiName);
            } catch (Exception e) {
                //无法获取描述
                exist = false;
            }
            if (exist != need) {
                //不合法
                valid = false;
                break;
            }
        }
        return valid;
    }


    private boolean checkCrmObjStream(String tenantId, String dcId, String linkPrecondition) {
        boolean valid = true;
        String objApiNames = linkPrecondition.substring(cond_stream.length());
        for (String objApiName : objApiNames.split("&")) {
            boolean need = true;
            if (objApiName.startsWith("!")) {
                need = false;
                objApiName = objApiName.substring(1);
            }
            Integer count = adminSyncPloyDetailDao.countByErpDcIdAndCrmApiName(tenantId, dcId, objApiName);
            if (!(count > 0 && need)) {
                //不合法
                valid = false;
                break;
            }
        }
        return valid;
    }


    @Override
    public Result<List<TemplateErpObjInfo>> preCheckErpObj(String tenantId, String dcId, Template.PreCheckArg arg,String lang) {
        Template.TemplateInfo templateInfo = getAllInfo(arg.getTemplateId()).safeData();
        Map<String, ErpObjExtendDto> existObjMap = queryErpObjExtendDtoMap(tenantId, dcId);
        //存在的对象，后面用于检查字段
        List<TemplateErpSplitObjInfo> needCheckFieldObjInfos = new ArrayList<>();
        for (TemplateErpObjInfo templateErpObjInfo : templateInfo.getDetail().getErpObjInfos()) {
            TemplateErpSplitObjInfo main = templateErpObjInfo.getMain();
            String mainRealObjApiName = templateErpObjInfo.getMainRealObjApiName();
            ErpObjExtendDto mainExistObj = existObjMap.get(mainRealObjApiName);
            if (mainExistObj == null) {
                //不存在该对象
                main.setExist(false).invalid(i18NStringManager.get(I18NStringEnum.s334,lang,tenantId));
                if (templateErpObjInfo.getDetails() != null) {
                    for (TemplateErpSplitObjInfo detail : templateErpObjInfo.getDetails()) {
                        detail.setExist(false).invalid(i18NStringManager.get(I18NStringEnum.s334,lang,tenantId));
                    }
                }
            } else {
                //存在主
                //填充主对象splitApiName
                templateErpObjInfo.setMainSplitObjApiName(mainExistObj.getSplitObjApiName())
                        .setSplitSeq(mainExistObj.getSplitSeq());
                //填充主对象信息
                main.setObjName(mainExistObj.getObjName())
                        .setSplitObjApiName(mainExistObj.getSplitObjApiName());
                needCheckFieldObjInfos.add(main);
                //检查明细对象
                if (templateErpObjInfo.getDetails() != null) {
                    for (TemplateErpSplitObjInfo detail : templateErpObjInfo.getDetails()) {
                        ErpObjExtendDto detailObjDto = existObjMap.get(detail.getRealObjApiName());
                        if (detailObjDto == null) {
                            detail.setExist(false).invalid(i18NStringManager.get(I18NStringEnum.s334,lang,tenantId));
                        } else {
                            detail.setObjName(detailObjDto.getObjName()).setSplitObjApiName(detailObjDto.getSplitObjApiName());
                            needCheckFieldObjInfos.add(detail);
                        }
                    }
                }
            }
        }
        //避免无字段的数据影响
        needCheckFieldObjInfos.removeIf(v -> v.getFields() == null);
        //暂时不检查fieldExtend
        List<ObjFieldsDto> fieldsArg = needCheckFieldObjInfos.stream().map(v -> {
            ObjFieldsDto objFieldsDto = new ObjFieldsDto();
            objFieldsDto.setObjApiName(v.getSplitObjApiName());
            List<String> fieldList = v.getFields().stream().map(f -> f.getFieldApiName()).collect(Collectors.toList());
            objFieldsDto.setFieldApiNames(fieldList);
            return objFieldsDto;
        }).collect(Collectors.toList());
        if (!fieldsArg.isEmpty()) {
            List<ErpObjectFieldEntity> existFields = erpObjectFieldDao.bulkQuery(tenantId, dcId, fieldsArg);
            Map<String, Map<String, ErpObjectFieldEntity>> objFieldMap = existFields.stream().collect(Collectors.groupingBy(v -> v.getErpObjectApiName(),
                    Collectors.toMap(f -> f.getFieldApiName(), f -> f)));
            for (TemplateErpSplitObjInfo curObjInfo : needCheckFieldObjInfos) {
                List<String> missingFields = new ArrayList<>();
                for (Template.TemplateFieldInfo fieldInfo : curObjInfo.getFields()) {
                    Map<String, ErpObjectFieldEntity> fieldMap = objFieldMap.computeIfAbsent(curObjInfo.getSplitObjApiName(), v -> new HashMap<>());
                    ErpObjectFieldEntity existField = fieldMap.get(fieldInfo.getFieldApiName());
                    if (existField == null) {
                        missingFields.add(fieldInfo.getFieldApiName());
                        //缺少字段
                    } else {
                        if (fieldInfo.isNeedBeIdType() && !ErpFieldTypeEnum.id.equals(existField.getFieldDefineType())) {
                            curObjInfo.invalid(i18NStringManager.get2(I18NStringEnum.s336.getI18nKey(),
                                    lang,
                                    tenantId,
                                    String.format(I18NStringEnum.s336.getI18nValue(), fieldInfo.getFieldApiName()),
                                    Lists.newArrayList(fieldInfo.getFieldApiName())));
                        }

                        if (fieldInfo.isNeedCheckType()) {
                            if (!existField.getFieldDefineType().equals(fieldInfo.getFieldDefineType())) {
                                curObjInfo.invalid(i18NStringManager.get2(I18NStringEnum.s337.getI18nKey(),
                                        lang,
                                        tenantId,
                                        String.format(I18NStringEnum.s337.getI18nValue(), fieldInfo.getFieldApiName(), fieldInfo.getFieldDefineType()),
                                        Lists.newArrayList(fieldInfo.getFieldApiName(), fieldInfo.getFieldDefineType().name())));
                                continue;
                            }
                            if (ErpFieldTypeEnum.object_reference.equals(fieldInfo.getFieldDefineType())) {
                                if (!ObjectUtil.equal(existField.getFieldExtendValue(), fieldInfo.getFieldExtendValue())) {
                                    curObjInfo.invalid(i18NStringManager.get2(I18NStringEnum.s338.getI18nKey(),
                                            lang,
                                            tenantId,
                                            String.format(I18NStringEnum.s338.getI18nValue(), fieldInfo.getFieldApiName(), fieldInfo.getFieldDefineType()),
                                            Lists.newArrayList(fieldInfo.getFieldApiName(), fieldInfo.getFieldDefineType().name())));
                                }
                                continue;
                            }
                        }
                    }
                }
                if (!missingFields.isEmpty()) {
                    curObjInfo.setMissingFields(missingFields).invalid(i18NStringManager.get(I18NStringEnum.s335,lang,tenantId));
                }
            }
        }
        return Result.newSuccess(templateInfo.getDetail().getErpObjInfos());
    }

    /**
     * key 真实对象apiName，
     *
     * @param tenantId
     * @param dcId
     * @return
     */
    private Map<String, ErpObjExtendDto> queryErpObjExtendDtoMap(String tenantId, String dcId) {
        List<ErpObjExtendDto> erpObjExtendDtos = erpObjectDao.queryAllObjExtendDTOByDc(tenantId, dcId);
        //都通过真实对象apiName来标识.key：真实编码，value：对象名称
        Map<String, ErpObjExtendDto> existObjMap = new HashMap<>();
        for (ErpObjExtendDto erpObjExtendDto : erpObjExtendDtos) {
            String realApiCode = erpObjExtendDto.parseRealApiCode();
            existObjMap.put(realApiCode, erpObjExtendDto);
        }
        return existObjMap;
    }


    @Override
    public Result<List<StreamInfo>> createTemplateIntegrationStreams(String tenantId, String dcId, Template.ListStreamsArg arg, String lang) {
        Template.TemplateInfo templateTemplateInfo = getAllInfo(arg.getTemplateId()).safeData();
        //以真实对象apiName标识的集成流模板信息
        List<StreamInfo> streamInfos = templateTemplateInfo.getDetail().getStreamInfos();
        Map<String, ErpObjExtendDto> erpObjExtendDtoMap = queryErpObjExtendDtoMap(tenantId, dcId);
        //检查集成流
        ListIntegrationStreamArg listIntegrationStreamArg = new ListIntegrationStreamArg();
        listIntegrationStreamArg.setDcId(dcId);
        listIntegrationStreamArg.setPageSize(1000);
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantId(tenantId);
        ErpConnectInfoEntity crmConnect = connectInfoManager.getOrCreateCrmDc(tenantId);
        List<StreamInfo> needReturnStreamInfos = new ArrayList<>();
        for (StreamInfo streamInfo : streamInfos) {
            try {
                createStream(tenantId, dcId, arg, erpObjExtendDtoMap, syncPloyDetailEntities, crmConnect, needReturnStreamInfos, streamInfo,lang);
            } catch (ErpSyncDataException e) {
                log.warn("create stream error,{},{}", streamInfo.getStreamName(), e.getMessage(), e);
                return Result.newErrorByI18N(String.format("创建集成流%s异常，异常信息：%s", streamInfo.getStreamName(), e.getMessage()),   // ignoreI18n  模板不支持i18n
                        I18NStringEnum.s339.getI18nKey(),
                        Lists.newArrayList(streamInfo.getStreamName(), e.getMessage()));
            } catch (Exception e) {
                log.error("create stream error,{},{}", streamInfo.getStreamName(), e.getMessage(), e);
                return Result.newErrorByI18N(String.format("创建集成流%s异常，异常信息：%s", streamInfo.getStreamName(), e.getMessage()),   // ignoreI18n  模板不支持i18n
                        I18NStringEnum.s339.getI18nKey(),
                        Lists.newArrayList(streamInfo.getStreamName(), e.getMessage()));
            }
        }
        //创建成功后计数
        try {
            List<String> streamIds = streamInfos.stream().map(v -> v.getId()).collect(Collectors.toList());
            usageMetricDao.incGlobal("createTemplateIntegrationStreams-" + arg.getTemplateId(), "count", 1, CollUtil.newArrayList(tenantId), streamIds);
        } catch (Exception e) {
            log.error("inc count error", e);
        }
        return Result.newSuccess(needReturnStreamInfos);
    }

    private void createStream(String tenantId,
                              String dcId,
                              Template.ListStreamsArg arg,
                              Map<String, ErpObjExtendDto> erpObjExtendDtoMap,
                              List<SyncPloyDetailEntity> syncPloyDetailEntities,
                              ErpConnectInfoEntity crmConnect,
                              List<StreamInfo> needReturnStreamInfos,
                              StreamInfo streamInfo,
                              String lang) {
        boolean erp2Crm = TenantType.ERP.equals(streamInfo.getSourceTenantType());
        String erpMainRealApiName;
        ObjectMappingResult mainMapping = streamInfo.getMainMapping();
        //替换apiName为中间对象apiName
        List<ObjectMappingResult> detailMappings = streamInfo.getDetailMappings();
        if (erp2Crm) {
            erpMainRealApiName = mainMapping.getSourceObjectApiName();
            if (arg.getNotSyncRealObjApiNames().contains(erpMainRealApiName)) {
                //不同步的对象
                return;
            }
            ErpObjExtendDto erpObjExtendDto = erpObjExtendDtoMap.get(erpMainRealApiName);
            if (erpObjExtendDto == null) {
                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s262.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s262.getI18nValue(), erpMainRealApiName),
                        Lists.newArrayList(erpMainRealApiName)),
                        null,
                        null);
            }
            mainMapping.setSourceObjectApiName(erpObjExtendDto.getSplitObjApiName());
            for (Iterator<ObjectMappingResult> iterator = detailMappings.iterator(); iterator.hasNext(); ) {
                ObjectMappingResult detailObjectMapping = iterator.next();
                String erpDetailRealApiName = detailObjectMapping.getSourceObjectApiName();
                if (arg.getNotSyncRealObjApiNames().contains(erpDetailRealApiName)) {
                    //不同步的对象
                    iterator.remove();
                    continue;
                }
                erpObjExtendDto = erpObjExtendDtoMap.get(erpDetailRealApiName);
                if (erpObjExtendDto == null) {
                    throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s262.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s262.getI18nValue(), erpDetailRealApiName),
                            Lists.newArrayList(erpDetailRealApiName)),
                            null,
                            null);
                }
                detailObjectMapping.setSourceObjectApiName(erpObjExtendDto.getSplitObjApiName());
            }
        } else {
            erpMainRealApiName = mainMapping.getDestObjectApiName();
            if (arg.getNotSyncRealObjApiNames().contains(erpMainRealApiName)) {
                //不同步的对象
                return;
            }
            ErpObjExtendDto erpObjExtendDto = erpObjExtendDtoMap.get(erpMainRealApiName);
            if (erpObjExtendDto == null) {
                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s262.getI18nKey(),
                        tenantId,
                        String.format(I18NStringEnum.s262.getI18nValue(), erpMainRealApiName),
                        Lists.newArrayList(erpMainRealApiName)),
                        null,
                        null);
            }
            mainMapping.setDestObjectApiName(erpObjExtendDto.getSplitObjApiName());
            for (Iterator<ObjectMappingResult> iterator = detailMappings.iterator(); iterator.hasNext(); ) {
                ObjectMappingResult detailObjectMapping = iterator.next();
                String erpDetailRealApiName = detailObjectMapping.getDestObjectApiName();
                if (arg.getNotSyncRealObjApiNames().contains(erpDetailRealApiName)) {
                    //不同步的对象
                    iterator.remove();
                    continue;
                }
                erpObjExtendDto = erpObjExtendDtoMap.get(erpDetailRealApiName);
                if (erpObjExtendDto == null) {
                    throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s262.getI18nKey(),
                            tenantId,
                            String.format(I18NStringEnum.s262.getI18nValue(), erpDetailRealApiName),
                            Lists.newArrayList(erpDetailRealApiName)),
                            null,
                            null);
                }
                detailObjectMapping.setDestObjectApiName(erpObjExtendDto.getSplitObjApiName());
            }
        }

        //检查是否存在集成流
        Optional<SyncPloyDetailEntity> firstOp = syncPloyDetailEntities.stream().filter(v ->
                ObjectUtil.equal(v.getSourceObjectApiName(), mainMapping.getSourceObjectApiName())
                        && ObjectUtil.equal(v.getDestObjectApiName(), mainMapping.getDestObjectApiName())
        ).findFirst();
        SyncPloyDetailEntity syncPloyDetailEntity;
        if (firstOp.isPresent()) {
            streamInfo.setExists(true);
            syncPloyDetailEntity = firstOp.get();
            streamInfo.setId(syncPloyDetailEntity.getId());
        } else {
            //创建映射
            String sourceDcId = erp2Crm ? dcId : crmConnect.getId();
            String destDcId = erp2Crm ? crmConnect.getId() : dcId;
            CreateObjectMappingArg createObjectMappingArg = new CreateObjectMappingArg();
            createObjectMappingArg.setSourceObjectApiName(mainMapping.getSourceObjectApiName());
            createObjectMappingArg.setDestObjectApiName(mainMapping.getDestObjectApiName());
            createObjectMappingArg.setSourceDataCenterId(sourceDcId);
            createObjectMappingArg.setDestDataCenterId(destDcId);
            createObjectMappingArg.setIntegrationStreamName(streamInfo.getStreamName());
            createObjectMappingArg.setSyncRules(streamInfo.getSyncRules());
            for (ObjectMappingResult detailObjectMapping : detailMappings) {
                CreateObjectMappingArg.ObjectMapping detailMappingArg = new CreateObjectMappingArg.ObjectMapping();
                detailMappingArg.setSourceObjectApiName(detailObjectMapping.getSourceObjectApiName());
                detailMappingArg.setDestObjectApiName(detailObjectMapping.getDestObjectApiName());
                createObjectMappingArg.getDetailObjectMappings().add(detailMappingArg);
            }
            //未存在，创建映射
            String id = integrationStreamService.createObjectMapping(tenantId, createObjectMappingArg,lang).safeData();
            //查询集成流
            syncPloyDetailEntity = adminSyncPloyDetailDao.getById(tenantId, id);
            syncPloyDetailEntities.add(syncPloyDetailEntity);
            streamInfo.setId(id);
        }
        needReturnStreamInfos.add(streamInfo);
        //检查字段
        //主
        FieldMappingsData existFieldMappingData = syncPloyDetailEntity.getFieldMappings();
        List<FieldMappingResult> templateFieldMappings = mainMapping.getFieldMappings();
        boolean needUpdateMain = checkFieldMappings(tenantId,streamInfo, existFieldMappingData, templateFieldMappings, erp2Crm, erpObjExtendDtoMap);
        //明细
        DetailObjectMappingsData detailObjectMappings = syncPloyDetailEntity.getDetailObjectMappings();
        boolean needUpdateDetail = false;
        for (ObjectMappingResult templateDetailMapping : detailMappings) {
            //检查明细映射
            DetailObjectMappingsData.DetailObjectMappingData currentDetailMapping;
            Optional<DetailObjectMappingsData.DetailObjectMappingData> first = detailObjectMappings.stream().filter(v -> v.getSourceObjectApiName().equals(templateDetailMapping.getSourceObjectApiName()) && v.getDestObjectApiName().equals(templateDetailMapping.getDestObjectApiName())).findFirst();
            if (!first.isPresent()) {
                //增加从对象映射
                currentDetailMapping = new DetailObjectMappingsData.DetailObjectMappingData();
                currentDetailMapping.setSourceObjectApiName(templateDetailMapping.getSourceObjectApiName());
                currentDetailMapping.setDestObjectApiName(templateDetailMapping.getDestObjectApiName());
                currentDetailMapping.setFieldMappings(new ArrayList<>());
                detailObjectMappings.add(currentDetailMapping);
                needUpdateDetail = true;
            } else {
                currentDetailMapping = first.get();
            }
            boolean needUpdateDetailCur = checkFieldMappings(tenantId,streamInfo, currentDetailMapping.getFieldMappings(), templateDetailMapping.getFieldMappings(), erp2Crm, erpObjExtendDtoMap);
            needUpdateDetail = needUpdateDetail || needUpdateDetailCur;
        }
        if (!needUpdateMain && !needUpdateDetail) {
            log.info("not need update stream {}", streamInfo.getStreamName());
        } else {
            SyncPloyDetailEntity update = new SyncPloyDetailEntity();
            update.setId(streamInfo.getId());
            if (needUpdateMain) {
                update.setFieldMappings(existFieldMappingData);
            }
            if (needUpdateDetail) {
                update.setDetailObjectMappings(detailObjectMappings);
            }
            adminSyncPloyDetailDao.updateByIdSelective(update);
        }
    }

    private boolean checkFieldMappings(String tenantId,StreamInfo streamInfo, List<FieldMappingData> checkFieldMappings, List<FieldMappingResult> templateFieldMappings, boolean erp2Crm, Map<String, ErpObjExtendDto> erpObjExtendDtoMap) {
        //根据source+dest apiName判断.兼容原来数据存在重复的异常
        Map<Pair<String, String>, FieldMappingData> existFieldMap = checkFieldMappings.stream().collect(Collectors.toMap(v -> Pair.of(v.getSourceApiName(), v.getDestApiName()), v -> v, (u, v) -> u));
        boolean needUpdate = false;
        for (FieldMappingResult templateFieldMapping : templateFieldMappings) {
            if (existFieldMap.containsKey(Pair.of(templateFieldMapping.getSourceApiName(), templateFieldMapping.getDestApiName()))) {
                continue;
            }
            if (StrUtil.endWith(templateFieldMapping.getSourceApiName(), "__c")
                    || StrUtil.endWith(templateFieldMapping.getDestApiName(), "__c")) {
                //自定义字段
                continue;
            }
            needUpdate = true;
            FieldMappingData newFieldMapping = BeanUtil.copy(templateFieldMapping, FieldMappingData.class);
            //需要替换一些apiName
            if (erp2Crm) {
                if (ErpFieldTypeEnum.object_reference.name().equals(newFieldMapping.getSourceType()) ||
                        ErpFieldTypeEnum.master_detail.name().equals(newFieldMapping.getSourceType())) {
                    //替换apiName
                    ErpObjExtendDto erpObjExtendDto = erpObjExtendDtoMap.get(newFieldMapping.getSourceTargetApiName());
                    if (erpObjExtendDto == null) {
                        throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s263.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s263.getI18nValue(), streamInfo.getStreamName(), newFieldMapping.getSourceApiName(), newFieldMapping.getSourceTargetApiName()),
                                Lists.newArrayList(streamInfo.getStreamName(), newFieldMapping.getSourceApiName(), newFieldMapping.getSourceTargetApiName())),
                                null,
                                null);
                    }
                    newFieldMapping.setSourceTargetApiName(erpObjExtendDto.getSplitObjApiName());
                }
            } else {
                if (ErpFieldTypeEnum.object_reference.name().equals(newFieldMapping.getDestType()) ||
                        ErpFieldTypeEnum.master_detail.name().equals(newFieldMapping.getDestType())) {
                    //替换apiName
                    ErpObjExtendDto erpObjExtendDto = erpObjExtendDtoMap.get(newFieldMapping.getDestTargetApiName());
                    if (erpObjExtendDto == null) {
                        throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s263.getI18nKey(),
                                tenantId,
                                String.format(I18NStringEnum.s263.getI18nValue(), streamInfo.getStreamName(), newFieldMapping.getDestApiName(), newFieldMapping.getDestTargetApiName()),
                                Lists.newArrayList(streamInfo.getStreamName(), newFieldMapping.getDestApiName(), newFieldMapping.getDestTargetApiName())),
                                null,
                                null);
                    }
                    newFieldMapping.setDestTargetApiName(erpObjExtendDto.getSplitObjApiName());
                }
            }
            checkFieldMappings.add(newFieldMapping);
        }
        return needUpdate;
    }

    @Override
    public Result<List<Template.Option>> listOptions(String optionsType) {
        return Result.newSuccess(queryOptions(optionsType));
    }

    private Map<String, Template.Option> getOptionsMap(String optionsName) {
        return queryOptions(optionsName).stream().collect(Collectors.toMap(v -> v.getValue(), v -> v));
    }

    private List<Template.Option> queryOptions(String optionsType) {
        List<TemplateOptionDoc> byType = templateOptionDao.findByType(optionsType);
        List<Template.Option> options = BeanUtil.copyList(byType, Template.Option.class);
        //排序
        options.sort(Comparator.comparingInt(v -> v.getOrder()));
        //替换固定值
        for (Template.Option option : options) {
            if (StrUtil.isNotBlank(option.getExtend())) {
                option.setExtend(StrUtil.replace(option.getExtend(), "{domain}", ConfigCenter.ERP_DOMAIN_URL));
            }
        }
        return options;
    }


    @Override
    public Result<Void> deleteOptions(String optionType, Template.Option option) {
        templateOptionDao.delete(optionType, option.getValue());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> saveOrders(List<String> ids, String type) {
        switch (type) {
            case "template":
                //模板排序
                templateDao.updateOrder(ids);
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + type);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Dict> downloadALlData() {
        //模板
        List<TemplateDoc> allTemplate = templateDao.findAll();
        List<TemplateOptionDoc> allOptions = templateOptionDao.findAll();
        Dict dict = Dict.of("templates", JSON.toJSONString(allTemplate), "options", JSON.toJSONString(allOptions));
        return Result.newSuccess(dict);
    }


    @Override
    public Result<Void> addOptions(String type, Template.Option option) {
        TemplateOptionDoc doc = BeanUtil.copy(option, TemplateOptionDoc.class);
        doc.setType(type);
        //替换
        doc.setExtend(StrUtil.replace(doc.getExtend(), ConfigCenter.ERP_DOMAIN_URL, "{domain}"));
        templateOptionDao.upsert(doc);
        return Result.newSuccess();
    }
}
