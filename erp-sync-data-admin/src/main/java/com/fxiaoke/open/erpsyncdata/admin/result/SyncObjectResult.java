package com.fxiaoke.open.erpsyncdata.admin.result;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class SyncObjectResult implements Serializable {
    private String apiName;
    private String name;

    public SyncObjectResult() {
    }

    public static SyncObjectResult newInstance(String apiName, String name) {
        SyncObjectResult result = new SyncObjectResult();
        result.apiName = apiName;
        result.name = name;
        return result;
    }
}
