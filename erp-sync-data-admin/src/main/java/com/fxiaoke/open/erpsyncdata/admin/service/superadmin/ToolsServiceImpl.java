package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ToolsServiceImpl implements ToolsService {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private IdGenerator idGenerator;

    @Override
    public Result<Void> migrateDepartmentData(String tenantId, String dcId,boolean fsDataIdIsNumber) {
        return migrateDepOrEmpData(tenantId,
                dcId,
                ErpFieldTypeEnum.department,
                ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName(),
                fsDataIdIsNumber);
    }

    @Override
    public Result<Void> migrateEmployeeData(String tenantId, String dcId,boolean fsDataIdIsNumber) {
        return migrateDepOrEmpData(tenantId,
                dcId,
                ErpFieldTypeEnum.employee,
                ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName(),fsDataIdIsNumber);
    }

    private Result<Void> migrateDepOrEmpData(String tenantId,
                                             String dcId,
                                             ErpFieldTypeEnum fieldType,
                                             String destObjApiName,
                                             boolean fsDataIdIsNumber) {
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dcId);
        log.info("ToolsServiceImpl.migrateDepOrEmpData,connectInfoEntity={}",connectInfoEntity);

        List<SyncPloyDetailEntity> ployDetailEntityList = adminSyncPloyDetailDao.queryByDcId(tenantId,
                dcId,
                SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if(CollectionUtils.isEmpty(ployDetailEntityList)) {
            log.info("ToolsServiceImpl.migrateDepOrEmpData,ployDetailEntityList is empty");
            return Result.newSuccess();
        }

        Optional<SyncPloyDetailEntity> ployDetailEntity = ployDetailEntityList.stream()
                .filter((item) -> StringUtils.equalsIgnoreCase(item.getDestObjectApiName(), destObjApiName))
                .findFirst();
        log.info("ToolsServiceImpl.migrateDepOrEmpData,ployDetailEntity={}",ployDetailEntity);

        if(ployDetailEntity.isPresent()==false) {
            log.info("ToolsServiceImpl.migrateDepOrEmpData,destObjApiName={} ployDetailEntity is empty",destObjApiName);
            return Result.newSuccess();
        }
        long startTime = 0L;
        long endTime = System.currentTimeMillis();
        int offset = 0;
        int limit = 100;

        while (true) {
            SyncObjectAndTenantMappingData mappingData = new SyncObjectAndTenantMappingData();
            mappingData.setSourceTenantId(tenantId);
            mappingData.setSourceObjectApiName(ployDetailEntity.get().getSourceObjectApiName());
            mappingData.setDestTenantId(tenantId);
            mappingData.setDestObjectApiName(ployDetailEntity.get().getDestObjectApiName());
            List<SyncDataMappingsEntity> mappingsEntityList = syncDataMappingsDao.setTenantId(tenantId).listByObjectApiNames(tenantId,
                    Lists.newArrayList(mappingData),
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    offset,
                    limit,
                    startTime,
                    endTime);
            if(CollectionUtils.isEmpty(mappingsEntityList)) {
                break;
            } else {
                offset += limit;
                mappingsEntityList.stream().forEach((entity -> {
                    if(fsDataIdIsNumber) {
                        try {
                            Integer.valueOf(entity.getDestDataId());
                        } catch (Exception e) {
                            return;
                        }
                    }
                    ErpFieldDataMappingEntity fieldDataMappingEntity = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .findByDataId(tenantId,
                                    dcId,
                                    fieldType,
                                    entity.getDestDataId(),
                                    entity.getSourceDataId());
                    if(fieldDataMappingEntity==null) {
                        fieldDataMappingEntity = new ErpFieldDataMappingEntity();
                        fieldDataMappingEntity.setId(idGenerator.get());
                        fieldDataMappingEntity.setTenantId(tenantId);
                        fieldDataMappingEntity.setDataCenterId(dcId);
                        fieldDataMappingEntity.setChannel(connectInfoEntity.getChannel());
                        fieldDataMappingEntity.setDataType(fieldType);

                        fieldDataMappingEntity.setFsDataId(entity.getDestDataId());
                        fieldDataMappingEntity.setFsDataName(entity.getDestDataName());
                        fieldDataMappingEntity.setErpDataId(entity.getSourceDataId());
                        fieldDataMappingEntity.setErpDataName(entity.getSourceDataName());

                        fieldDataMappingEntity.setCreateTime(System.currentTimeMillis());
                        fieldDataMappingEntity.setUpdateTime(System.currentTimeMillis());

                        int count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                                .insert(fieldDataMappingEntity);
                        if(count!=1) {
                            log.info("ToolsServiceImpl.migrateDepOrEmpData,insert data faield,fieldDataMappingEntity={}",fieldDataMappingEntity);
                        }
                    } else {
                        log.info("ToolsServiceImpl.migrateDepOrEmpData,data exist,fieldDataMappingEntity={}",fieldDataMappingEntity);
                    }
                }));
            }
        }
        log.info("ToolsServiceImpl.migrateDepOrEmpData,all data migrate completed,destObjApiName={}",destObjApiName);
        return Result.newSuccess();
    }
}
