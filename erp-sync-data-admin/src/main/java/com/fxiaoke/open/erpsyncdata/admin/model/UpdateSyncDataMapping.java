package com.fxiaoke.open.erpsyncdata.admin.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/27 10:28:40
 */
public interface UpdateSyncDataMapping {

    @Data
    class Arg implements Serializable {
        @ApiModelProperty("集成流id")
        private String ployDetailId;
        @ApiModelProperty("中间表映射id")
        private String dataId;
        @ApiModelProperty("源数据id")
        private String sourceDataId;
        @ApiModelProperty("源数据主属性")
        private String sourceDataName;
        @ApiModelProperty("目标数据id")
        private String destDataId;
        @ApiModelProperty("目标数据主属性")
        private String destDataName;
        @ApiModelProperty("主对象数据id")
        private String masterDataId;
    }
}
