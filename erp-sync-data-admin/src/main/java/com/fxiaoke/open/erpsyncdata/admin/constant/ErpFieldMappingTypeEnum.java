//package com.fxiaoke.open.erpsyncdata.admin.constant;
//
//import lombok.Getter;
///**
// * <AUTHOR>
// * @Date: 9:58 2020/12/19
// * @Desc:
// */
//public enum ErpFieldMappingTypeEnum {
//    //erp使用号段2000-3000
//    DEPARTMENT_EMPLOYEE_FROM_MAPPING_TABLE(2001, "部门人员对象从erp_field_data_mapping表取值", "syncdata_common_erpfieldmappingtypeenum_departmentemployeefrommappingtable"),
//
//    ATTACHMENT_MAPPING_TABLE(2002, "附件双向同步", "syncdata_common_erpfieldmappingtypeenum_departmentemployeefrommappingtable"),
//    ;
//    @Getter
//    private int type;
//    @Getter
//    private String name;
//    @Getter
//    private String nameI18nKey;
//
//    ErpFieldMappingTypeEnum(int type, String name, String nameI18nKey) {
//        this.type = type;
//        this.name = name;
//        this.nameI18nKey = nameI18nKey;
//    }
//}
