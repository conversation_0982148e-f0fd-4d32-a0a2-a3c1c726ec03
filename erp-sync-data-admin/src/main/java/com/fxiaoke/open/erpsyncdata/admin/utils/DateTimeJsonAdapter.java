package com.fxiaoke.open.erpsyncdata.admin.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.gson.*;

import java.lang.reflect.Type;

/**
 * <AUTHOR> (^_−)☆
 */
public class DateTimeJsonAdapter implements JsonSerializer<DateTime>,JsonDeserializer<DateTime>{

    @Override
    public JsonElement serialize(DateTime src, Type typeOfSrc, JsonSerializationContext context) {
        if (src == null) {
            return JsonNull.INSTANCE;
        } else {
            return new JsonPrimitive(src.toString());
        }
    }

    @Override
    public DateTime deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        return DateUtil.parse(json.getAsString());
    }
}
