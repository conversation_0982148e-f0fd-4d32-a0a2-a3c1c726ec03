package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate.K3UltimateGetApiTemplateModel;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateBaseApiTemplate;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateTokenEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * 云星空旗舰版web接口集合
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
public interface K3UltimateWebApiService {

    /**
     * 获取云星空旗舰版对象api template
     *
     * @param tenantId
     * @param dataCenterId
     * @param erpObjApiName
     * @return
     */
    Result<K3UltimateGetApiTemplateModel> getApiTemplate(String tenantId,
                                                         String dataCenterId,
                                                         String erpObjApiName,
                                                         String lang);

    /**
     * 获取云星空旗舰版对象api template
     *
     * @param tenantId
     * @param dataCenterId
     * @param erpObjApiName
     * @return
     */
    Result<Void> updateApiTemplate(String tenantId,
                                   String dataCenterId,
                                   String erpObjApiName,
                                   K3UltimateBaseApiTemplate apiTemplate);
}
