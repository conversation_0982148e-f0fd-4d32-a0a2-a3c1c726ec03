package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
public class BuildExcelFile {

    @ApiModel(value = "构建excel文件结果",
            description = "文件下载地址：https://www.ceshi112.com/FSC/EM/File/DownloadByPath?Path=[Path]&name=[fileName]")
    @Data
    public static class Result implements Serializable {
        private static final long serialVersionUID = 8785941275320897965L;
        @ApiModelProperty("文件名称")
        private String fileName;
        @ApiModelProperty("临时文件地址")
        private String tnFilePath;
    }

    @Data
    @ApiModel("导出excel文件结果")
    public static class DownUrlResult implements Serializable {
        private static final long serialVersionUID = 9181946463285972887L;

        private Boolean success=false;

        private String printMsg;

        private String downloadUrl;


    }

    @Data
    public static class Arg<T> implements Serializable {
        private static final long serialVersionUID = -968360108756417347L;

        /**
         * 企业id
         */
        private String tenantId;

        /**
         * tenantId和ea必填一个
         */
        private String ea;

        /**
         * 文件名称
         */
        private String fileName;

        /**
         * sheet名称，可不传
         */
        private List<String> sheetNames;

        /**
         * 数据，class需使用easyexcel的注解
         */
        private List<T> dataList;

        /**
         * 表格样式，可不传，默认使用内容居中
         */
        private HorizontalCellStyleStrategy style;

        /**
         * excel文档类型
         */
        private ExcelTypeEnum excelType;
    }

}
