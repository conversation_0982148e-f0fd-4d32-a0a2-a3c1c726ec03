package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class CheckQuotaResult extends SyncQuota {
    private String tenantId;
    private boolean success = true;
    private String exceptionMsg;
    private Long checkTime = System.currentTimeMillis();
    private Long usedPercent = 0L;
    private String cleanLogMsg;
}
