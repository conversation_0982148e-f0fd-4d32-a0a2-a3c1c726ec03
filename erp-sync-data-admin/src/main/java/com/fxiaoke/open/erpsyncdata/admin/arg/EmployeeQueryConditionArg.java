package com.fxiaoke.open.erpsyncdata.admin.arg;


import com.fxiaoke.open.erpsyncdata.common.data.PageData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EmployeeQueryConditionArg extends PageData implements Serializable {
    @ApiModelProperty("對接企業TenantId")
    private Integer destTenantId;
    @ApiModelProperty("0:我方对接人    1：对方对接人")
    private Integer queryType;
    @ApiModelProperty("是否返回需要fs账号信息")
    private Boolean needFsAccountInfo = Boolean.FALSE;
    @ApiModelProperty("搜索关键字，当前仅支持对接人姓名或拼音模糊匹配，为null或空则不作匹配")
    private String searchText;
    @ApiModelProperty("是否查询对接范围，0：不查询，1：查询。默认为1")
    private Integer needDestRange = 1;
    @ApiModelProperty("关联状态 0:所有  1:关联有效  2:关联失效 3:无关联")
    private int mapperStatus;
}
