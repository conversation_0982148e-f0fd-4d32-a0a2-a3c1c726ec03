package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class QueryObjectFilterFieldMappingResult implements Serializable {
    @ApiModelProperty("源企业对象字段的apiName")
    private String sourceApiName;
    @ApiModelProperty("源企业对象字段的名称")
    private String sourceName;
    @ApiModelProperty("目标对象字段apiName")
    private String destApiName;
    @ApiModelProperty("目标对象字段名称")
    private String destName;
    @ApiModelProperty("筛选操作符")
    private String filterOperator;

}
