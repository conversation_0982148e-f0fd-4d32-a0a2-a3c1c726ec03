package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetByIdApiStatusArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListSyncDataHistoryArg;
import com.fxiaoke.open.erpsyncdata.admin.model.GetByIdInterfaceStatus;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataHistoryListResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataHistoryResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectAndFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.PloyDetailNodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdminSyncDataServiceImpl implements AdminSyncDataService {
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    public ObjectDataService objectDataService;
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private TenantInfoManager tenantInfoManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private CHSyncLogManager chSyncLogManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErpObjectAndFieldsService erpObjectAndFieldsService;

    @Override
    public Result<List<SyncDataHistoryListResult>> listSyncDataHistory(String tenantId, ListSyncDataHistoryArg arg, String lang) {
        if (!tenantId.equals(arg.getSourceTenantId()) && !tenantId.equals(arg.getDestTenantId())) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        SyncDataMappingsEntity syncDataMappingsEntity = syncDataMappingsDao.setTenantId(tenantId)
                .getBySourceData(tenantId, arg.getSourceObjectApiName(), arg.getDestObjectApiName(), arg.getSourceDataId());
        if (syncDataMappingsEntity == null) {
            return Result.newError(ResultCodeEnum.DATA_NOT_FOUND);
        }
        SyncPloyDetailEntity entity = null;
        if (StringUtils.isNotBlank(arg.getStreamId())) {
            entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getStreamId());
        }
        if (entity == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        Map<String, SyncDataMappingsEntity> sourceObj2Mapping = Maps.newHashMap();
        sourceObj2Mapping.put(syncDataMappingsEntity.getSourceObjectApiName(), syncDataMappingsEntity);
        Map<String, SyncDataHistoryListResult> sourceObj2Result = Maps.newHashMap();
        Boolean canReSync = true;
        if (TenantType.ERP == entity.getSourceTenantType()) {
            GetByIdApiStatusArg getByIdArg = new GetByIdApiStatusArg();
            getByIdArg.setSplitObjectApiName(entity.getSourceObjectApiName());
            Result<GetByIdInterfaceStatus> result = integrationStreamService.queryGetByIdStatus(entity.getTenantId(), entity.getSourceDataCenterId(), getByIdArg);
            if (result != null && result.getData() != null&&result.getData().getGetByIdStatus()!=null) {
                canReSync = result.getData().getGetByIdStatus();
            }
        }
        setSyncDataHistoryListResult(entity, syncDataMappingsEntity, sourceObj2Result, canReSync);
        setEndLogTime(tenantId, arg, syncDataMappingsEntity, entity);
        if (arg.getStartLogTime() == null) {
            arg.setStartLogTime(arg.getEndLogTime() - 1000 * 60 * 60 * 24 * 7);
        }
        List<String> sourceObjectApiNames = Lists.newArrayList(), destObjectApiNames = Lists.newArrayList();
        if (StringUtils.isNotBlank(arg.getSourceObjectApiName())) {
            sourceObjectApiNames.add(arg.getSourceObjectApiName());
        }
        if (StringUtils.isNotBlank(arg.getDestObjectApiName())) {
            destObjectApiNames.add(arg.getDestObjectApiName());
        }
        Boolean needFillLog = arg.getNeedFillLog() == null ? false : arg.getNeedFillLog();
        String sourceDataId = arg.getSourceDataId(), sourceDataName = null;
        Long startLogTime = arg.getStartLogTime(), endLogTime = arg.getEndLogTime();
        return getSyncDataHistoryListResult(tenantId, sourceObjectApiNames, sourceDataId, sourceDataName, destObjectApiNames, startLogTime, endLogTime,
                lang, entity, sourceObj2Mapping, sourceObj2Result, canReSync,needFillLog);
    }

    private void setSyncDataHistoryListResult(SyncPloyDetailEntity entity, SyncDataMappingsEntity syncDataMappingsEntity,
                                              Map<String, SyncDataHistoryListResult> sourceObj2Result, Boolean canReSync) {
        SyncDataHistoryListResult result = new SyncDataHistoryListResult();
        result.setCanReSync(canReSync);
        result.setSyncDataMappingId(syncDataMappingsEntity.getId());
        result.setSourceObjectApiName(syncDataMappingsEntity.getSourceObjectApiName());
        result.setSourceDataId(syncDataMappingsEntity.getSourceDataId());
        result.setSourceDataName(syncDataMappingsEntity.getSourceDataName());
        result.setDestDataId(syncDataMappingsEntity.getDestDataId());
        result.setDestDataName(syncDataMappingsEntity.getDestDataName());
        result.setMasterDataId(syncDataMappingsEntity.getMasterDataId());
        sourceObj2Result.put(syncDataMappingsEntity.getSourceObjectApiName(), result);
    }

    @NotNull
    private Result<List<SyncDataHistoryListResult>> getSyncDataHistoryListResult(String tenantId, List<String> sourceObjectApiNames, String sourceDataId, String sourceDataName,
                                                                                 List<String> destObjectApiNames, Long startLogTime, Long endLogTime, String lang,
                                                                                 SyncPloyDetailEntity entity, Map<String, SyncDataMappingsEntity> sourceObj2Mapping,
                                                                                 Map<String, SyncDataHistoryListResult> sourceObj2Result, Boolean canReSync,Boolean needFillLog) {
        List<SyncDataHistoryListResult> dataList = Lists.newArrayList();
        SyncDataHistoryListResult resultList = new SyncDataHistoryListResult();
        resultList.setDataList(Lists.newArrayList());
        resultList.setStartLogTime(startLogTime);
        resultList.setEndLogTime(endLogTime);
        boolean hasMoreData = true;
        Long expireIntervalTime = tenantInfoManager.getExpireIntervalTimeByEi(tenantId);
        if (System.currentTimeMillis() - startLogTime > expireIntervalTime) {
            hasMoreData = false;
        }
        Map<String, String> sourceObjectApiName2Name = getObjSourceObjectName(tenantId, entity);
        resultList.setHasMoreData(hasMoreData);
        resultList.setCanReSync(canReSync);
        if (sourceObj2Result != null && sourceObj2Result.size() > 0) {
            for (SyncDataHistoryListResult result : sourceObj2Result.values()) {
                if (sourceObjectApiName2Name.containsKey(result.getSourceObjectApiName())) {
                    result.setSourceObjectName(sourceObjectApiName2Name.get(result.getSourceObjectApiName()));
                }
                result.setDataList(Lists.newArrayList());
                result.setStartLogTime(startLogTime);
                result.setEndLogTime(endLogTime);
                result.setHasMoreData(hasMoreData);
            }
        }
        //数据范围及之前的日志
        List<CHSyncLogEntity> syncLogList = Lists.newArrayList();
        if (needFillLog) {
            syncLogList = this.getDataFilterLog(tenantId, entity, sourceObjectApiNames, sourceDataId, sourceDataName, startLogTime, endLogTime);
        }
        if (CollectionUtils.isEmpty(syncLogList) && CollectionUtils.isEmpty(sourceObj2Mapping.keySet())) {//如果没有中间表，没有数据范围及之前的日志，直接返回空（只有时间）
            if (CollectionUtils.isEmpty(sourceObj2Result.values())) {
                dataList.add(resultList);
            } else {
                dataList.addAll(sourceObj2Result.values());
            }
            return Result.newSuccess(dataList);
        }
        List<String> sourceDataIds = Lists.newArrayList();
        if (StringUtils.isBlank(sourceDataId)) {//如果通过主属性查，通过中间表取数据id查同步快照，sync_data日志没有存主属性
            for (String sourceObjectApiName : sourceObj2Mapping.keySet()) {
                SyncDataMappingsEntity syncDataMappingsEntity = sourceObj2Mapping.get(sourceObjectApiName);
                if (StringUtils.isNotBlank(syncDataMappingsEntity.getSourceDataId())) {
                    sourceDataIds.add(syncDataMappingsEntity.getSourceDataId());
                }
            }
        } else {
            sourceDataIds.add(sourceDataId);
        }
        //同步快照日志
        List<SyncDataEntity> syncDataEntities = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(sourceDataIds)) {
            syncDataEntities = adminSyncDataDao.setTenantId(tenantId)
                    .listBySourceData(tenantId, tenantId, sourceObjectApiNames, sourceDataIds, tenantId,
                            destObjectApiNames, startLogTime, endLogTime);
        }
        //去除掉多余syncLogList日志
        syncLogList = this.mergeSyncData(tenantId, syncLogList, syncDataEntities);
        if (CollectionUtils.isEmpty(syncDataEntities) && CollectionUtils.isEmpty(syncLogList)) {
            dataList.addAll(sourceObj2Result.values());
            return Result.newSuccess(dataList);
        }
        Map<String, String> employeeIdToNameMap=getEmployeeIdToNameMap(tenantId, syncDataEntities);

        for (SyncDataEntity syncDataEntity : syncDataEntities) {
            String sourceObjectApiName = syncDataEntity.getSourceObjectApiName();
            if (!sourceObj2Result.containsKey(sourceObjectApiName)) {
                sourceObj2Result.put(sourceObjectApiName, BeanUtil.deepCopy(resultList, SyncDataHistoryListResult.class));
            }
            SyncDataHistoryListResult syncDataListResult = sourceObj2Result.get(sourceObjectApiName);
            syncDataListResult.setSourceObjectApiName(sourceObjectApiName);
            if (StringUtils.isNotBlank(syncDataEntity.getSourceDataId())) {
                syncDataListResult.setSourceDataId(syncDataEntity.getSourceDataId());
            }
            if (syncDataEntity.getSourceData() != null && syncDataEntity.getSourceData().getName() != null) {
                syncDataListResult.setSourceDataName(syncDataEntity.getSourceData().getName());
            }
            if (StringUtils.isNotBlank(syncDataEntity.getDestDataId())) {
                syncDataListResult.setDestDataId(syncDataEntity.getDestDataId());
            }
            if (syncDataEntity.getDestData() != null && syncDataEntity.getDestData().getName() != null) {
                syncDataListResult.setDestDataName(syncDataEntity.getDestData().getName());
            }
            SyncDataHistoryResult syncDataHistoryResult = new SyncDataHistoryResult();
            syncDataHistoryResult.setNodeEnum(SyncLogTypeEnum.SYNC_DATA.getType());
            syncDataHistoryResult.setId(syncDataEntity.getId());
            syncDataHistoryResult.setSyncDataId(syncDataEntity.getId());
            syncDataHistoryResult.setOperatorId(syncDataEntity.getOperatorId());
            if (syncDataEntity.getData() != null) {
                syncDataHistoryResult.setDataVersion(syncDataEntity.getData().getDataVersion());
                PloyDetailNodeEnum ployDetailNodeEnum = PloyDetailNodeEnum.getByTypeStr(syncDataEntity.getData().getLastNodeName());
                if (ployDetailNodeEnum != null) {
                    String nodeName = i18NStringManager.get(ployDetailNodeEnum.getI18nKey(), lang, tenantId, ployDetailNodeEnum.getDescription());
                    syncDataHistoryResult.setLastSyncNodeAndStatus(nodeName);
                    if (PloyDetailNodeEnum.REVERSE_WRITE.equals(ployDetailNodeEnum) && syncDataEntity.getData().getReverseWrite2CrmFailed() != null && syncDataEntity.getData().getReverseWrite2CrmFailed()) {
                        syncDataHistoryResult.setLastSyncNodeAndStatus(syncDataHistoryResult.getLastSyncNodeAndStatus() + "(" + syncDataEntity.getData().getReverseWrite2CrmFailedRemark() + ")");
                    }
                    if (PloyDetailNodeEnum.AFTER_FUNCTION.equals(ployDetailNodeEnum) && syncDataEntity.getData().getAfterFuncFailed() != null && syncDataEntity.getData().getAfterFuncFailed()) {
                        syncDataHistoryResult.setLastSyncNodeAndStatus(syncDataHistoryResult.getLastSyncNodeAndStatus() + "(" + syncDataEntity.getData().getAfterFuncFailedRemark() + ")");
                    }
                }
            }
            if (employeeIdToNameMap.get(syncDataEntity.getOperatorId()) != null) {
                syncDataHistoryResult.setOperatorName(employeeIdToNameMap.get(syncDataEntity.getOperatorId()));
            } else {
                syncDataHistoryResult.setOperatorName(i18NStringManager.get(I18NStringEnum.s988, lang, tenantId));
            }
            syncDataHistoryResult.setSourceEventType(syncDataEntity.getSourceEventType());
            syncDataHistoryResult.setSourceEventTypeName(EventTypeEnum.getNameByType(i18NStringManager, lang, tenantId, syncDataEntity.getSourceEventType()));
            syncDataHistoryResult.setDestEventType(syncDataEntity.getDestEventType());
            String dataReceiveType = getDataReceiveType(tenantId, lang, syncDataEntity);
            if (syncDataEntity.getDestEventType() != null) {
                syncDataHistoryResult.setDestEventTypeName(dataReceiveType + EventTypeEnum.getNameByType(i18NStringManager, lang, tenantId, syncDataEntity.getDestEventType()));
            } else {
                syncDataHistoryResult.setDestEventTypeName(dataReceiveType + EventTypeEnum.getNameByType(i18NStringManager, lang, tenantId, syncDataEntity.getSourceEventType()));
            }
            syncDataHistoryResult.setRemark(syncDataEntity.getRemark());
            syncDataHistoryResult.setUpdateTime(syncDataEntity.getUpdateTime());
            syncDataHistoryResult.setStatus(SyncStatusEnum.getBySyncDataStatus(syncDataEntity.getStatus()).getStatus());
            syncDataHistoryResult.setStatusName(SyncStatusEnum.getBySyncDataStatus(syncDataEntity.getStatus()).getNameByLang(i18NStringManager, lang, tenantId));
            syncDataHistoryResult.setSyncLogId(syncDataEntity.getSyncLogId());
            syncDataListResult.getDataList().add(syncDataHistoryResult);
        }
        if (CollectionUtils.isNotEmpty(syncLogList)) {
            for (CHSyncLogEntity chSyncLogEntity : syncLogList) {
                String sourceObjectApiName = chSyncLogEntity.getSourceObjectApiName();
                if (StringUtils.isBlank(sourceObjectApiName)) {//全部当成主的
                    sourceObjectApiName = entity.getSourceObjectApiName();
                }
                if (!sourceObj2Result.containsKey(sourceObjectApiName)) {
                    SyncDataHistoryListResult newResult = BeanUtil.deepCopy(resultList, SyncDataHistoryListResult.class);
                    newResult.setSourceObjectApiName(sourceObjectApiName);
                    sourceObj2Result.put(sourceObjectApiName, newResult);
                }
                SyncDataHistoryListResult syncDataListResult = sourceObj2Result.get(sourceObjectApiName);
                if (StringUtils.isNotBlank(chSyncLogEntity.getErpTempDataDataId()) && StringUtils.isBlank(syncDataListResult.getSourceDataId())) {
                    syncDataListResult.setSourceDataId(chSyncLogEntity.getErpTempDataDataId());
                }
                if (StringUtils.isNotBlank(chSyncLogEntity.getErpTempDataDataNumber()) && StringUtils.isBlank(syncDataListResult.getSourceDataName())) {
                    syncDataListResult.setSourceDataName(chSyncLogEntity.getErpTempDataDataNumber());
                }
                SyncDataHistoryResult syncDataHistoryResult = new SyncDataHistoryResult();
                syncDataHistoryResult.setId(chSyncLogEntity.getId());
                syncDataHistoryResult.setOperatorId("-10000");//先默认系统
                syncDataHistoryResult.setNodeEnum(chSyncLogEntity.getType());
                if (SyncLogTypeEnum.DATA_SYNC_FILTER.getType().equals(chSyncLogEntity.getType())) {
                    syncDataHistoryResult.setLastSyncNodeAndStatus(i18NStringManager.getByEi(SyncLogTypeEnum.DATA_SYNC_FILTER.getI18nKey(), tenantId, SyncLogTypeEnum.DATA_SYNC_FILTER.getDescription()));
                } else if (SyncLogTypeEnum.TEMP.getType().equals(chSyncLogEntity.getType())) {
                    syncDataHistoryResult.setLastSyncNodeAndStatus(i18NStringManager.getByEi(SyncLogTypeEnum.TEMP.getI18nKey(), tenantId, SyncLogTypeEnum.TEMP.getDescription()));
                } else if (SyncLogTypeEnum.CRM_TRIGGER.getType().equals(chSyncLogEntity.getType())) {
                    syncDataHistoryResult.setLastSyncNodeAndStatus(i18NStringManager.getByEi(SyncLogTypeEnum.CRM_TRIGGER.getI18nKey(), tenantId, SyncLogTypeEnum.CRM_TRIGGER.getDescription()));
                }
                if (chSyncLogEntity.getErpTempDataObj() != null) {
                    ErpTempData erpTempData = chSyncLogEntity.getErpTempDataObj();
                    if (erpTempData.getLastSyncTime() != null) {//版本存在了这里
                        syncDataHistoryResult.setDataVersion(erpTempData.getLastSyncTime().toString());
                    }
                }
                if (employeeIdToNameMap.get(syncDataHistoryResult.getOperatorId()) != null) {
                    syncDataHistoryResult.setOperatorName(employeeIdToNameMap.get(syncDataHistoryResult.getOperatorId()));
                } else {
                    syncDataHistoryResult.setOperatorName(i18NStringManager.get(I18NStringEnum.s988, lang, tenantId));
                }
                syncDataHistoryResult.setUpdateTime(chSyncLogEntity.getUpdateTime().getTime());
                syncDataHistoryResult.setSyncLogId(chSyncLogEntity.getLogId());
                syncDataListResult.getDataList().add(syncDataHistoryResult);
            }
        }
        dataList.addAll(sourceObj2Result.values());
        //从中间表补充信息
        for (SyncDataHistoryListResult syncDataHistoryListResult : dataList) {
            if (sourceObjectApiName2Name.containsKey(syncDataHistoryListResult.getSourceObjectApiName())) {
                syncDataHistoryListResult.setSourceObjectName(sourceObjectApiName2Name.get(syncDataHistoryListResult.getSourceObjectApiName()));
            }
            Collections.sort(syncDataHistoryListResult.getDataList(), (o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));
        }
        return Result.newSuccess(dataList);
    }

    private Map<String, String> getEmployeeIdToNameMap(String tenantId, List<SyncDataEntity> syncDataEntities) {
        Map<String, String> employeeIdToNameMap = Maps.newHashMap();
        List<Integer> operatorIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(syncDataEntities)) {
            for(SyncDataEntity entity :syncDataEntities){
                if(StringUtils.isNotEmpty(entity.getOperatorId())){
                    continue;
                }
                try{
                   operatorIds.add(Integer.parseInt(entity.getOperatorId()));
                }catch (Exception e){

                }
            }
        }
        if (CollectionUtils.isNotEmpty(operatorIds)) {
            if (operatorIds.size() == 1 && "-10000".equals(operatorIds.get(0))) {
                return employeeIdToNameMap;
            }
            BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
            batchGetEmployeeDtoArg.setEnterpriseId(Integer.parseInt(tenantId));
            batchGetEmployeeDtoArg.setCurrentEmployeeId(1000);
            batchGetEmployeeDtoArg.setEmployeeIds(operatorIds);
            try {
                List<EmployeeDto> employeeDtos = employeeService.batchGetEmployeeDto(batchGetEmployeeDtoArg).getEmployees();
                employeeIdToNameMap = employeeDtos.stream().collect(Collectors.toMap(x -> String.valueOf(x.getEmployeeId()), EmployeeDto::getName));
            } catch (Exception e) {
                log.warn("getEmployeeIdToNameMap e={}", e);
            }
        }
        return employeeIdToNameMap;
    }

    private String getDataReceiveType(String tenantId, String lang, SyncDataEntity syncDataEntity) {
        String dataReceiveType = "";
        if (syncDataEntity.getDataReceiveType() != null) {
            DataReceiveTypeEnum dataReceiveTypeEnum = DataReceiveTypeEnum.getByType(syncDataEntity.getDataReceiveType());
            if (dataReceiveTypeEnum != null&&!dataReceiveTypeEnum.equals(DataReceiveTypeEnum.OTHER)) {
                dataReceiveType = i18NStringManager.get(dataReceiveTypeEnum.getI18nKey(), lang, tenantId, dataReceiveTypeEnum.getName()) + i18NStringManager.get(I18NStringEnum.s5108, lang, tenantId) + "-";
            }
        }
        return dataReceiveType;
    }

    private Map<String, String> getObjSourceObjectName(String tenantId, SyncPloyDetailEntity entity) {
        Map<String, String> sourceObjectApiName2Name = Maps.newHashMap();
        List<ObjectDescribe> objDesc = null;
        List<String> objApiNames = Lists.newArrayList();
        objApiNames.add(entity.getSourceObjectApiName());
        if (entity.getDetailObjectMappings() != null) {
            for (DetailObjectMappingsData.DetailObjectMappingData mapping : entity.getDetailObjectMappings()) {
                if (StringUtils.isNotBlank(mapping.getSourceObjectApiName())) {
                    objApiNames.add(mapping.getSourceObjectApiName());
                }
            }
        }
        if (TenantType.ERP == entity.getSourceTenantType()) {
            objDesc = erpObjectAndFieldsService.getErpObjAndFields(tenantId, entity.getSourceDataCenterId(), Lists.newArrayList(objApiNames));
        } else {
            objDesc = crmRemoteManager.listObjectAndFieldByApiNames(tenantId, Lists.newArrayList(objApiNames));
        }
        if (CollectionUtils.isNotEmpty(objDesc)) {
            Map<String, ObjectDescribe> erpObjApiName2Desc = objDesc.stream().collect(Collectors.toMap(ObjectDescribe::getApiName, v -> v, (u, v) -> u));
            for (String apiName : erpObjApiName2Desc.keySet()) {
                sourceObjectApiName2Name.put(apiName, erpObjApiName2Desc.get(apiName).getDisplayName());
            }
        }
        return sourceObjectApiName2Name;
    }

    private List<CHSyncLogEntity> mergeSyncData(String tenantId, List<CHSyncLogEntity> syncLogList, List<SyncDataEntity> syncDataEntities) {
        if (CollectionUtils.isNotEmpty(syncDataEntities) && CollectionUtils.isNotEmpty(syncLogList)) {
            List<String> logIds = Lists.newArrayList();
            for (SyncDataEntity syncDataEntity : syncDataEntities) {
                if (StringUtils.isNotBlank(syncDataEntity.getSyncLogId())) {
                    logIds.addAll(LogIdUtil.listLogIdLine(tenantId, syncDataEntity.getSyncLogId()));
                }
            }
            syncLogList = syncLogList.stream().filter(log -> !logIds.contains(log.getLogId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(syncLogList)) {
                List<CHSyncLogEntity> dataFilter = syncLogList.stream().filter(log -> SyncLogTypeEnum.DATA_SYNC_FILTER.getType().equals(log.getType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dataFilter)) {
                    logIds.clear();
                    for (CHSyncLogEntity syncLog : dataFilter) {
                        if (StringUtils.isNotBlank(syncLog.getLogId())) {
                            logIds.addAll(LogIdUtil.listLogIdLine(tenantId, syncLog.getLogId()));
                            logIds.remove(syncLog.getLogId());
                        }
                    }
                    syncLogList = syncLogList.stream().filter(log -> !logIds.contains(log.getLogId())).collect(Collectors.toList());
                }
                return syncLogList;
            }
        } else if (CollectionUtils.isNotEmpty(syncLogList)) {
            List<CHSyncLogEntity> dataFilter = syncLogList.stream().filter(log -> SyncLogTypeEnum.DATA_SYNC_FILTER.getType().equals(log.getType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dataFilter)) {
                List<String> logIds = Lists.newArrayList();
                for (CHSyncLogEntity syncLog : dataFilter) {
                    if (StringUtils.isNotBlank(syncLog.getLogId())) {
                        logIds.addAll(LogIdUtil.listLogIdLine(tenantId, syncLog.getLogId()));
                        logIds.remove(syncLog.getLogId());
                    }
                }
                syncLogList = syncLogList.stream().filter(log -> !logIds.contains(log.getLogId())).collect(Collectors.toList());
            }
            return syncLogList;
        }
        return Lists.newArrayList();
    }

    @Override
    public Result<List<SyncDataHistoryListResult>> listSyncDataHistoryBySource(String tenantId, ListSyncDataHistoryArg arg, String lang) {
        SyncPloyDetailEntity entity = null;
        if (StringUtils.isNotBlank(arg.getStreamId())) {
            entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getStreamId());
        }
        if (entity == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        if (arg.getEndLogTime() == null) {
            arg.setEndLogTime(System.currentTimeMillis());
        }
        if (arg.getStartLogTime() == null) {
            arg.setStartLogTime(arg.getEndLogTime() - 1000 * 60 * 60 * 24 * 7);
        }
        Boolean canReSync = true;
        if (TenantType.ERP == entity.getSourceTenantType()) {
            GetByIdApiStatusArg getByIdArg = new GetByIdApiStatusArg();
            getByIdArg.setSplitObjectApiName(entity.getSourceObjectApiName());
            Result<GetByIdInterfaceStatus> result = integrationStreamService.queryGetByIdStatus(entity.getTenantId(), entity.getSourceDataCenterId(), getByIdArg);
            if (result != null && result.getData() != null&&result.getData().getGetByIdStatus()!=null) {
                canReSync = result.getData().getGetByIdStatus();
            }
        }
        Map<String, SyncDataHistoryListResult> sourceObj2Result = Maps.newHashMap();
        Map<String, SyncDataMappingsEntity> sourceObj2Mapping = Maps.newHashMap();
        List<String> sourceObjectApiNames = Lists.newArrayList(), destObjectApiNames = Lists.newArrayList();
        if (StringUtils.isNotBlank(arg.getSourceObjectApiName()) && StringUtils.isNotBlank(arg.getDestObjectApiName())) {
            sourceObjectApiNames.add(arg.getSourceObjectApiName());
            destObjectApiNames.add(arg.getDestObjectApiName());
            SyncDataMappingsEntity mappingsEntity = getSyncDataMapping(tenantId, arg.getSourceObjectApiName(), arg.getDestObjectApiName(), arg.getSourceDataId(), arg.getSourceDataName());
            if (mappingsEntity != null) {
                sourceObj2Mapping.put(mappingsEntity.getSourceObjectApiName(), mappingsEntity);
                setSyncDataHistoryListResult(entity, mappingsEntity, sourceObj2Result, canReSync);
            }
        } else {
            Boolean needQuery = StringUtils.isBlank(arg.getSourceObjectApiName()) || entity.getSourceObjectApiName().equals(arg.getSourceObjectApiName());
            if (needQuery) {
                sourceObjectApiNames.add(entity.getSourceObjectApiName());
                destObjectApiNames.add(entity.getDestObjectApiName());
                SyncDataMappingsEntity mappingsEntity = getSyncDataMapping(tenantId, entity.getSourceObjectApiName(), entity.getDestObjectApiName(), arg.getSourceDataId(), arg.getSourceDataName());
                if (mappingsEntity != null) {
                    sourceObj2Mapping.put(mappingsEntity.getSourceObjectApiName(), mappingsEntity);
                    setSyncDataHistoryListResult(entity, mappingsEntity, sourceObj2Result, canReSync);
                }
            }
            if (entity.getDetailObjectMappings() != null) {
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                    needQuery = StringUtils.isBlank(arg.getSourceObjectApiName()) || detailObjectMappingData.getSourceObjectApiName().equals(arg.getSourceObjectApiName());
                    if (needQuery) {
                        sourceObjectApiNames.add(detailObjectMappingData.getSourceObjectApiName());
                        destObjectApiNames.add(detailObjectMappingData.getDestObjectApiName());
                        SyncDataMappingsEntity mappingsEntity1 = getSyncDataMapping(tenantId, detailObjectMappingData.getSourceObjectApiName(), detailObjectMappingData.getDestObjectApiName(), arg.getSourceDataId(), arg.getSourceDataName());
                        if (mappingsEntity1 != null) {
                            sourceObj2Mapping.put(mappingsEntity1.getSourceObjectApiName(), mappingsEntity1);
                            setSyncDataHistoryListResult(entity, mappingsEntity1, sourceObj2Result, canReSync);
                        }
                    }
                }
            }
        }
        setSyncDataHistoryListResult(tenantId, arg, entity, sourceObj2Result, sourceObjectApiNames, canReSync);
        if (CollectionUtils.isEmpty(sourceObj2Result.values()) && StringUtils.isNotBlank(arg.getSourceObjectApiName())) {
            SyncDataHistoryListResult result = new SyncDataHistoryListResult();
            result.setSourceDataId(arg.getSourceDataId());
            result.setSourceDataName(arg.getSourceDataName());
            result.setCanReSync(canReSync);
            result.setSourceObjectApiName(arg.getSourceObjectApiName());
            sourceObj2Result.put(arg.getSourceObjectApiName(), result);
        }
        Boolean needFillLog = arg.getNeedFillLog() == null ? true : arg.getNeedFillLog();
        String sourceDataId = arg.getSourceDataId(), sourceDataName = arg.getSourceDataName();
        Long startLogTime = arg.getStartLogTime(), endLogTime = arg.getEndLogTime();
        return getSyncDataHistoryListResult(tenantId, sourceObjectApiNames, sourceDataId, sourceDataName, destObjectApiNames, startLogTime, endLogTime,
                lang, entity, sourceObj2Mapping, sourceObj2Result, canReSync,needFillLog);
    }

    private SyncDataMappingsEntity getSyncDataMapping(String tenantId, String sourceObjectApiName, String destObjectApiName, String sourceDataId, String sourceDataName) {
        if (StringUtils.isNotBlank(sourceDataId)) {
            return syncDataMappingsDao.setTenantId(tenantId)
                    .getBySourceData(tenantId, sourceObjectApiName, destObjectApiName, sourceDataId);
        } else if (StringUtils.isNotBlank(sourceDataName)) {
            return syncDataMappingsDao.setTenantId(tenantId)
                    .getBySourceDataName(tenantId, sourceObjectApiName, destObjectApiName, sourceDataName);
        }
        return null;
    }

    private List<CHSyncLogEntity> getDataFilterLog(String tenantId, SyncPloyDetailEntity entity, List<String> sourceObjectApiName, String sourceDataId, String sourceDataName, Long startLogTime, Long endLogTime) {
        List<String> logTypeList = Lists.newArrayList();
        logTypeList.add(SyncLogTypeEnum.DATA_SYNC_FILTER.getType());
        List<String> realObjectApiNames = Lists.newArrayList();
        if (TenantType.ERP == entity.getSourceTenantType()) {
            logTypeList.add(SyncLogTypeEnum.TEMP.getType());
            for (String objApiName : sourceObjectApiName) {
                ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, objApiName);
                if (relation != null) {
                    if (!realObjectApiNames.contains(relation.getErpRealObjectApiname())) {
                        realObjectApiNames.add(relation.getErpRealObjectApiname());
                    }
                }
            }
        } else {
            realObjectApiNames.addAll(sourceObjectApiName);
            logTypeList.add(SyncLogTypeEnum.CRM_TRIGGER.getType());
        }
        List<CHSyncLogEntity> syncLogs = chSyncLogManager.listLogByType(tenantId, realObjectApiNames, sourceDataId, sourceDataName,
                logTypeList, startLogTime, endLogTime);
        //对于erp->crm的明细对象，realObjApiName记录的是真实主对象apiName,历史数据sourceObjectApiName没有值
        syncLogs = syncLogs.stream()
                .filter(item -> StringUtils.isBlank(item.getSourceObjectApiName()) || sourceObjectApiName.contains(item.getSourceObjectApiName()))
                .filter(item -> StringUtils.isBlank(item.getStreamId()) || entity.getId().equals(item.getStreamId())).collect(Collectors.toList());//其他的节点没有集成流id，数据范围带了需要过滤掉
        return syncLogs;
    }

    private void setEndLogTime(String tenantId, ListSyncDataHistoryArg arg, SyncDataMappingsEntity syncDataMappingsEntity, SyncPloyDetailEntity entity) {
        if (arg.getEndLogTime() != null) {
            return;
        }
        Long endLogTime = null;
        if (TenantType.CRM == entity.getSourceTenantType()) {
            Result2<ObjectData> objectDataResult2 = outerServiceFactory.get(TenantType.CRM).getObjectData(tenantId, TenantType.CRM, arg.getSourceObjectApiName(), arg.getSourceDataId());
            if (objectDataResult2 != null && objectDataResult2.isSuccess() && objectDataResult2.getData() != null) {
                endLogTime = objectDataResult2.getData().getLong("last_modified_time");
            }
        } else {
            String masterObjectId = null, objectApiName = entity.getSourceObjectApiName();
            if (objectApiName.equals(arg.getSourceObjectApiName())) {//主
                masterObjectId = arg.getSourceDataId();
            } else {//从
                if (syncDataMappingsEntity != null) {
                    masterObjectId = syncDataMappingsEntity.getMasterDataId();
                }
            }
            if (masterObjectId != null) {
                ErpIdArg erpIdArg = new ErpIdArg();
                erpIdArg.setDataId(masterObjectId);
                erpIdArg.setTenantId(tenantId);
                erpIdArg.setIncludeDetail(false);
                erpIdArg.setObjAPIName(objectApiName);
                String splitObjApiName = erpIdArg.getObjAPIName();
                ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, splitObjApiName);
                String realObjApiName = relation.getErpRealObjectApiname();
                String dataCenterId = relation.getDataCenterId();
                erpIdArg.setObjAPIName(realObjApiName);
                Result<Document> standardDataResult = erpTempDataManager.getDocument(erpIdArg, dataCenterId);
                if (standardDataResult != null && standardDataResult.isSuccess() && standardDataResult.getData() != null) {
                    endLogTime = standardDataResult.getData().getLong("update_time");
                }
            }
        }
        if (syncDataMappingsEntity != null && syncDataMappingsEntity.getUpdateTime() != null) {
            if (endLogTime == null || syncDataMappingsEntity.getUpdateTime() > endLogTime) {
                endLogTime = syncDataMappingsEntity.getUpdateTime();
            }
        }
        if (endLogTime != null) {
            arg.setEndLogTime(endLogTime + 1000 * 60 * 60L);
        }
        if (arg.getEndLogTime() == null) {
            arg.setEndLogTime(System.currentTimeMillis());
        }
    }

    private void setSyncDataHistoryListResult(String tenantId, ListSyncDataHistoryArg arg, SyncPloyDetailEntity entity,
                                              Map<String, SyncDataHistoryListResult> sourceObj2Result, List<String> sourceObjectApiName, Boolean canReSync) {
        if (TenantType.CRM == entity.getSourceTenantType()) {
            for (String objApiName : sourceObjectApiName) {
                if (sourceObj2Result.containsKey(objApiName)) {
                    continue;
                }
                Result2<ObjectData> objectDataResult2 = null;
                if (StringUtils.isNotBlank(arg.getSourceDataId())) {
                    objectDataResult2 = outerServiceFactory.get(TenantType.CRM).getObjectData(tenantId, TenantType.CRM, objApiName, arg.getSourceDataId());
                } else {
                    objectDataResult2 = outerServiceFactory.get(TenantType.CRM).getObjectDataByName(tenantId, TenantType.CRM, objApiName, arg.getSourceDataName());
                }
                if (objectDataResult2 != null && objectDataResult2.isSuccess() && objectDataResult2.getData() != null) {
                    SyncDataHistoryListResult result = new SyncDataHistoryListResult();
                    result.setSourceDataId(objectDataResult2.getData().getId());
                    result.setSourceDataName(objectDataResult2.getData().getName());
                    result.setCanReSync(canReSync);
                    result.setSourceObjectApiName(objApiName);
                    sourceObj2Result.put(objApiName, result);
                }
            }
        } else {
            if (!sourceObj2Result.containsKey(entity.getSourceObjectApiName())) {
                ErpIdArg erpIdArg = new ErpIdArg();
                erpIdArg.setTenantId(tenantId);
                erpIdArg.setIncludeDetail(false);
                erpIdArg.setObjAPIName(entity.getSourceObjectApiName());
                if (arg.getSourceDataId() != null) {
                    erpIdArg.setDataId(arg.getSourceDataId());
                } else {
                    erpIdArg.setDataId(arg.getSourceDataName());
                }
                String splitObjApiName = erpIdArg.getObjAPIName();
                ErpObjectRelationshipEntity relation = idFieldConvertManager.getRelation(tenantId, splitObjApiName);
                String realObjApiName = relation.getErpRealObjectApiname();
                String dataCenterId = relation.getDataCenterId();
                erpIdArg.setObjAPIName(realObjApiName);
                Result<Document> standardDataResult = null;
                if (arg.getSourceDataId() != null) {
                    standardDataResult = erpTempDataManager.getDocument(erpIdArg, dataCenterId);
                } else {
                    standardDataResult = erpTempDataManager.getDocumentByNumber(erpIdArg, dataCenterId);
                }
                if (standardDataResult != null && standardDataResult.isSuccess() && standardDataResult.getData() != null) {
                    SyncDataHistoryListResult result = new SyncDataHistoryListResult();
                    result.setOnlyQueryTempMasterData(true);
                    result.setSourceDataId(standardDataResult.getData().getString("data_id"));
                    result.setSourceDataName(standardDataResult.getData().getString("data_number"));
                    result.setCanReSync(canReSync);
                    result.setSourceObjectApiName(entity.getSourceObjectApiName());
                    sourceObj2Result.put(entity.getSourceObjectApiName(), result);
                }
            }
        }
    }
}
