package com.fxiaoke.open.erpsyncdata.admin.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TenantData implements Serializable {
    @ApiModelProperty("企业Id")
    private String tenantId;
    @ApiModelProperty("企业名称")
    private String tenantName;

    public static TenantData newDataByTenantId(String tenantId) {
        TenantData data = new TenantData();
        data.setTenantId(tenantId);
        return data;
    }
    public static TenantData newDataByTenantIdAndName(String tenantId,String name) {
        TenantData data = new TenantData();
        data.setTenantId(tenantId);
        data.setTenantName(name);
        return data;
    }
}
