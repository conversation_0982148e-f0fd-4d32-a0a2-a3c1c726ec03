package com.fxiaoke.skywalking.spock

import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.TypeReference
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataSyncNotifyManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldMappingManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.FieldDbManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LocalDispatcherUtil
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SimpleSyncDataArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg
import com.fxiaoke.paasauthrestapi.common.result.Result
import com.fxiaoke.paasauthrestapi.result.RoleUserResult
import com.fxiaoke.paasauthrestapi.service.PaasAuthService
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.googlecode.aviator.AviatorEvaluator
import org.apache.commons.lang3.tuple.MutableTriple
import org.bson.Document
import org.junit.Ignore
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit
import java.util.function.BiConsumer
import java.util.stream.Collectors

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
@Ignore
class K3dataConvertSpec extends  Specification{




    @Unroll
    def "校验数据符合条件的通知"(){
        given:

        String json="{\n" +
                "    \"cache\":{\n" +
                "\n" +
                "    },\n" +
                "    \"detailFieldExtendMap\":{\n" +
                "        \"BD_Customer.BD_CUSTCONTACT\":[\n" +
                "            {\n" +
                "                \"createTime\":1675735647669,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"fieldApiName\":\"DetailId\",\n" +
                "                \"fieldDefineType\":\"id\",\n" +
                "                \"id\":\"erp_642edca1a27ee100018f9dd9\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":0,\n" +
                "                \"queryCode\":\"FT_BD_CUSTCONTACT_FENTRYID\",\n" +
                "                \"saveCode\":\"FENTRYID\",\n" +
                "                \"saveExtend\":\"\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735647669,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"Id\",\n" +
                "                \"viewExtend\":\"\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735637764,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e1\",\n" +
                "                \"fieldApiName\":\"FNUMBER1\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca1a27ee100018f9dda\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":100,\n" +
                "                \"queryCode\":\"FNUMBER1\",\n" +
                "                \"saveCode\":\"FNUMBER1\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735637764,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"NUMBER\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735625543,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e1\",\n" +
                "                \"fieldApiName\":\"FNAME1\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9ddb\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":110,\n" +
                "                \"queryCode\":\"FNAME1\",\n" +
                "                \"saveCode\":\"FNAME1\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735625543,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"NAME\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735645329,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e1\",\n" +
                "                \"fieldApiName\":\"FADDRESS1\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9ddc\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":120,\n" +
                "                \"queryCode\":\"FADDRESS1\",\n" +
                "                \"saveCode\":\"FADDRESS1\",\n" +
                "                \"saveExtend\":\"\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735645329,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"ADDRESS\",\n" +
                "                \"viewExtend\":\"\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735646973,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e3\",\n" +
                "                \"fieldApiName\":\"FTRANSLEADTIME1\",\n" +
                "                \"fieldDefineType\":\"number\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9ddd\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":130,\n" +
                "                \"queryCode\":\"FTRANSLEADTIME1\",\n" +
                "                \"saveCode\":\"FTRANSLEADTIME1\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735646973,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"TRANSLEADTIME\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735644000,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e2\",\n" +
                "                \"fieldApiName\":\"FTAXRATE1\",\n" +
                "                \"fieldDefineType\":\"number\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9dde\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":140,\n" +
                "                \"queryCode\":\"FTAXRATE1\",\n" +
                "                \"saveCode\":\"FTAXRATE1\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735644000,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"TAXRATE\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735641745,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e8\",\n" +
                "                \"fieldApiName\":\"FIsDefaultConsignee\",\n" +
                "                \"fieldDefineType\":\"true_or_false\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9ddf\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":150,\n" +
                "                \"queryCode\":\"FIsDefaultConsignee\",\n" +
                "                \"saveCode\":\"FIsDefaultConsignee\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735641745,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"IsDefaultConsignee\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735637774,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e8\",\n" +
                "                \"fieldApiName\":\"FIsDefaultSettle\",\n" +
                "                \"fieldDefineType\":\"true_or_false\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9de0\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":160,\n" +
                "                \"queryCode\":\"FIsDefaultSettle\",\n" +
                "                \"saveCode\":\"FIsDefaultSettle\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735637774,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"IsDefaultSettle\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735627534,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e8\",\n" +
                "                \"fieldApiName\":\"FIsDefaultPayer\",\n" +
                "                \"fieldDefineType\":\"true_or_false\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9de1\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":170,\n" +
                "                \"queryCode\":\"FIsDefaultPayer\",\n" +
                "                \"saveCode\":\"FIsDefaultPayer\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735627534,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"IsDefaultPayer\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735626717,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e1\",\n" +
                "                \"fieldApiName\":\"FTTel\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9de2\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":180,\n" +
                "                \"queryCode\":\"FTTel\",\n" +
                "                \"saveCode\":\"FTTel\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735626717,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"TTel\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735628566,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e1\",\n" +
                "                \"fieldApiName\":\"FEMail\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9de3\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":190,\n" +
                "                \"queryCode\":\"FEMail\",\n" +
                "                \"saveCode\":\"FEMail\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735628566,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"EMail\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735643496,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e8\",\n" +
                "                \"fieldApiName\":\"FIsUsed\",\n" +
                "                \"fieldDefineType\":\"true_or_false\",\n" +
                "                \"id\":\"erp_642edca2a27ee100018f9de4\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":200,\n" +
                "                \"queryCode\":\"FIsUsed\",\n" +
                "                \"saveCode\":\"FIsUsed\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735643496,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"IsUsed\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735630344,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e13\",\n" +
                "                \"fieldApiName\":\"FTContact.Name\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca3a27ee100018f9de5\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":210,\n" +
                "                \"queryCode\":\"FTContact.FName\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735630344,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"TContact.Name\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735648935,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e13\",\n" +
                "                \"fieldApiName\":\"FTContact.FNumber\",\n" +
                "                \"fieldDefineType\":\"object_reference\",\n" +
                "                \"id\":\"erp_642edca3a27ee100018f9de6\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":211,\n" +
                "                \"queryCode\":\"FTContact.FNumber\",\n" +
                "                \"saveCode\":\"FTContact.FNumber\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735648935,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"TContact.Number\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735628038,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e13\",\n" +
                "                \"fieldApiName\":\"FTContactFCONTACTID\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca3a27ee100018f9de7\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":212,\n" +
                "                \"queryCode\":\"FTContact\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735628038,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"TContact_Id\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735645097,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e1\",\n" +
                "                \"fieldApiName\":\"FMOBILE\",\n" +
                "                \"fieldDefineType\":\"text\",\n" +
                "                \"id\":\"erp_642edca3a27ee100018f9de8\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":220,\n" +
                "                \"queryCode\":\"FMOBILE\",\n" +
                "                \"saveCode\":\"FMOBILE\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735645097,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"MOBILE\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"createTime\":1675735649601,\n" +
                "                \"dataCenterId\":\"780777150699143168\",\n" +
                "                \"erpFieldType\":\"e40\",\n" +
                "                \"fieldApiName\":\"FLocationStatus\",\n" +
                "                \"fieldDefineType\":\"select_one\",\n" +
                "                \"id\":\"erp_642edca3a27ee100018f9de9\",\n" +
                "                \"objApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "                \"priority\":230,\n" +
                "                \"queryCode\":\"FLocationStatus\",\n" +
                "                \"saveCode\":\"FLocationStatus\",\n" +
                "                \"tenantId\":\"84801\",\n" +
                "                \"updateTime\":1675735649601,\n" +
                "                \"usedQuery\":true,\n" +
                "                \"viewCode\":\"FLocationStatus\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"idFieldExtend\":{\n" +
                "        \"createTime\":1679967941760,\n" +
                "        \"dataCenterId\":\"780777150699143168\",\n" +
                "        \"fieldApiName\":\"Number\",\n" +
                "        \"fieldDefineType\":\"id\",\n" +
                "        \"id\":\"642246c50d0eae00011ada03\",\n" +
                "        \"objApiName\":\"BD_Customer\",\n" +
                "        \"priority\":10000,\n" +
                "        \"queryCode\":\"FNumber\",\n" +
                "        \"saveCode\":\"FNumber\",\n" +
                "        \"saveExtend\":\"\",\n" +
                "        \"tenantId\":\"84801\",\n" +
                "        \"updateTime\":1679967941760,\n" +
                "        \"usedQuery\":true,\n" +
                "        \"viewCode\":\"Number\",\n" +
                "        \"viewExtend\":\"\"\n" +
                "    },\n" +
                "    \"masterDetailFieldMap\":{\n" +
                "        \"BD_Customer.BD_CUSTCONTACT\":{\n" +
                "            \"createTime\":1681095554469,\n" +
                "            \"dataCenterId\":\"780777150699143168\",\n" +
                "            \"fieldApiName\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "            \"fieldDefineType\":\"detail\",\n" +
                "            \"id\":\"64337b828b1dfc0001dfc1d1\",\n" +
                "            \"objApiName\":\"BD_Customer\",\n" +
                "            \"priority\":65536,\n" +
                "            \"queryCode\":\"\",\n" +
                "            \"saveCode\":\"FT_BD_CUSTCONTACT\",\n" +
                "            \"saveExtend\":\"\",\n" +
                "            \"tenantId\":\"84801\",\n" +
                "            \"updateTime\":1681095554469,\n" +
                "            \"usedQuery\":true,\n" +
                "            \"viewCode\":\"\",\n" +
                "            \"viewExtend\":\"\"\n" +
                "        },\n" +
                "        \"BD_Customer.BD_CUSTORDERORG\":{\n" +
                "            \"createTime\":*************,\n" +
                "            \"dataCenterId\":\"780777150699143168\",\n" +
                "            \"fieldApiName\":\"BD_Customer.BD_CUSTORDERORG\",\n" +
                "            \"fieldDefineType\":\"detail\",\n" +
                "            \"id\":\"erp_642afa8cdc944f00019f1819\",\n" +
                "            \"objApiName\":\"BD_Customer\",\n" +
                "            \"priority\":8004,\n" +
                "            \"saveCode\":\"FT_BD_CUSTORDERORG\",\n" +
                "            \"tenantId\":\"84801\",\n" +
                "            \"updateTime\":*************,\n" +
                "            \"usedQuery\":true,\n" +
                "            \"viewCode\":\"BD_CUSTORDERORG\"\n" +
                "        },\n" +
                "        \"BD_Customer.BD_CUSTSUBACCOUNT\":{\n" +
                "            \"createTime\":*************,\n" +
                "            \"dataCenterId\":\"780777150699143168\",\n" +
                "            \"fieldApiName\":\"BD_Customer.BD_CUSTSUBACCOUNT\",\n" +
                "            \"fieldDefineType\":\"detail\",\n" +
                "            \"id\":\"erp_642edccda27ee100018f9ded\",\n" +
                "            \"objApiName\":\"BD_Customer\",\n" +
                "            \"priority\":8005,\n" +
                "            \"saveCode\":\"FT_BD_CUSTSUBACCOUNT\",\n" +
                "            \"tenantId\":\"84801\",\n" +
                "            \"updateTime\":*************,\n" +
                "            \"usedQuery\":true,\n" +
                "            \"viewCode\":\"BD_CUSTSUBACCOUNT\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"masterFieldExtends\":[\n" +
                "        {\n" +
                "            \"createTime\":*************,\n" +
                "            \"dataCenterId\":\"780777150699143168\",\n" +
                "            \"erpFieldType\":\"e13\",\n" +
                "            \"fieldApiName\":\"FTaxRate.FNumber\",\n" +
                "            \"fieldDefineType\":\"select_one\",\n" +
                "            \"id\":\"erp_642afa93dc944f00019f186f\",\n" +
                "            \"objApiName\":\"BD_Customer\",\n" +
                "            \"priority\":10085,\n" +
                "            \"queryCode\":\"FTaxRate.FNumber\",\n" +
                "            \"saveCode\":\"FTaxRate.FNumber\",\n" +
                "            \"tenantId\":\"84801\",\n" +
                "            \"updateTime\":*************,\n" +
                "            \"usedQuery\":true,\n" +
                "            \"viewCode\":\"TaxRate.Number\"\n" +
                "        }," +
                "{\n" +
                "            \"createTime\":1679968291479,\n" +
                "            \"dataCenterId\":\"780777150699143168\",\n" +
                "            \"fieldApiName\":\"FName\",\n" +
                "            \"fieldDefineType\":\"long_text\",\n" +
                "            \"id\":\"642248230d0eae00011ada23\",\n" +
                "            \"objApiName\":\"BD_Customer\",\n" +
                "            \"priority\":10004,\n" +
                "            \"queryCode\":\"FName\",\n" +
                "            \"saveCode\":\"FName\",\n" +
                "            \"saveExtend\":\"\",\n" +
                "            \"tenantId\":\"84801\",\n" +
                "            \"updateTime\":1679968291479,\n" +
                "            \"usedQuery\":true,\n" +
                "            \"viewCode\":\"Name\",\n" +
                "            \"viewExtend\":\"\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        def dataJson="{\"detailFieldVals\":{\"BD_Customer.BD_CUSTCONTACT\":[{\"object_describe_api_name\":\"BD_Customer.BD_CUSTCONTACT\",\"tenant_id\":\"84801\",\"fake_master_detail\":\"6433b91ed7e15400012475f2\",\"FIsDefaultPayer\":true,\"_id\":\"6433b91ed7e15400012475f4\"}]},\"masterFieldVal\":{\"object_describe_api_name\":\"BD_Customer\",\"tenant_id\":\"84801\",\"FName\":\"测试数据客户001212389\",\"_id\":\"6433b91ed7e15400012475f2\"},\"objAPIName\":\"BD_Customer\"}";
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append("")

        K3DataConverter k3DataConverter=JSONObject.parseObject(json,K3DataConverter.class);
        def map=Maps.newHashMap();
        map.put("IsVerifyBaseDataField","false")
        k3DataConverter.setSaveArgSettings(map)
        StandardData standardData=JSONObject.parseObject(dataJson,StandardData.class)

        def saveArg=new SaveArg()
        when :
        k3DataConverter.fillSaveArg(standardData,saveArg,"")
        then:
        Boolean.valueOf(saveArg.get("IsVerifyBaseDataField"))==false


    }












}
