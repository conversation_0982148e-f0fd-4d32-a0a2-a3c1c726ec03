package com.fxiaoke.skywalking.spock

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldDataMappingManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.interceptorUtil.SecurityUtil
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.googlecode.aviator.AviatorEvaluator
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
class FieldConvertSpec extends  Specification{

   def erpConnectInfoManager=Mock(ErpConnectInfoManager)
    def erpFieldManager =Mock(ErpFieldManager)
    def erpObjectRelationshipDao =Mock(ErpObjectRelationshipDao)
    def erpObjManager =Mock(ErpObjManager)
    def k3DataManagerMock=Mock(K3DataManager)
    def erpFieldDataMappingManager=Mock(ErpFieldDataMappingManager)
    def k3DataManager=new K3DataManager("erpConnectInfoManager":erpConnectInfoManager,"erpFieldManager":erpFieldManager,"erpObjectRelationshipDao":erpObjectRelationshipDao,"erpObjManager":erpObjManager,"k3DataManager":k3DataManagerMock,"erpFieldDataMappingManager":erpFieldDataMappingManager)
    def "测试K3获取数据的EMP转换逻辑"(){
        given:

        def standardData=JSONObject.parseObject("{\"objAPIName\":null,\"syncLogId\":null,\"dataVersion\":null,\"masterFieldVal\":{\"erp_id\":\"CUST16380\",\"erp_num\":\"CUST16380\",\"FDescription\":\" \",\"FGROUPCUSTID.FNumber\":null,\"FName\":\"\\u5BA2\\u6237\\u591A\\u9009\\u4EBA\\u5458\\u7684\\u6D4B\\u8BD5\",\"FNumber\":\"CUST16380\",\"FSELLER.FNumber\":\"00665_GW000226_1\",\"FWEBSITE\":\" \",\"Number\":\"CUST16380\",\"F_PAEZ_MulSaller\":\"708085\"},\"detailFieldVals\":{\"BD_Customer.BD_CUSTCONTACT\":[],\"BD_Customer.BD_CUSTORDERORG\":[],\"BD_Customer.BD_CUSTBANK\":[],\"BD_Customer.BD_CUSTSUBACCOUNT\":[]}}",StandardData.class);
        def tenantId="88521";
        def dataCenterId="643f7322b54ea80001767d86";
        def erpObjEntites= JSONArray.parseArray("[\n" +
                "    {\n" +
                "        \"channel\":\"ERP_K3CLOUD\",\n" +
                "        \"createTime\":*************,\n" +
                "        \"dataCenterId\":\"643f7322b54ea80001767d86\",\n" +
                "        \"erpRealObjectApiname\":\"BD_Customer\",\n" +
                "        \"erpSplitObjectApiname\":\"BD_Customer.BillHead\",\n" +
                "        \"id\":\"64a3d1e68f6f9100010ec622\",\n" +
                "        \"splitSeq\":1,\n" +
                "        \"splitType\":\"NOT_SPLIT\",\n" +
                "        \"tenantId\":\"88521\",\n" +
                "        \"updateTime\":*************\n" +
                "    },\n" +
                "    {\n" +
                "        \"channel\":\"ERP_K3CLOUD\",\n" +
                "        \"createTime\":*************,\n" +
                "        \"dataCenterId\":\"643f7322b54ea80001767d86\",\n" +
                "        \"erpRealObjectApiname\":\"BD_Customer\",\n" +
                "        \"erpSplitObjectApiname\":\"BD_Customer.BD_CUSTCONTACT\",\n" +
                "        \"id\":\"64a3d1e68f6f9100010ec61d\",\n" +
                "        \"splitSeq\":1,\n" +
                "        \"splitType\":\"DETAIL2DETAIL_SPLIT\",\n" +
                "        \"tenantId\":\"88521\",\n" +
                "        \"updateTime\":*************\n" +
                "    },\n" +
                "    {\n" +
                "        \"channel\":\"ERP_K3CLOUD\",\n" +
                "        \"createTime\":*************,\n" +
                "        \"dataCenterId\":\"643f7322b54ea80001767d86\",\n" +
                "        \"erpRealObjectApiname\":\"BD_Customer\",\n" +
                "        \"erpSplitObjectApiname\":\"BD_Customer.BD_CUSTBANK\",\n" +
                "        \"id\":\"64a3d1e68f6f9100010ec61a\",\n" +
                "        \"splitSeq\":1,\n" +
                "        \"splitType\":\"DETAIL2DETAIL_SPLIT\",\n" +
                "        \"tenantId\":\"88521\",\n" +
                "        \"updateTime\":*************\n" +
                "    }\n" +
                "]", ErpObjectRelationshipEntity.class);
        def erpIdArg=JSONObject.parseObject("{\"dataId\":\"CUST16380\",\"dataIdIsNumber\":false,\"includeDetail\":true,\"objAPIName\":\"BD_Customer\",\"tenantId\":\"88521\"}", ErpIdArg.class);
        erpConnectInfoManager.getByIdAndTenantId(*_)>> JSONObject.parseObject("{\"channel\":\"ERP_K3CLOUD\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://*************/k3cloud/\\\",\\\"dbId\\\":\\\"5ec229fad54306\\\",\\\"dbName\\\":\\\"接口环境\\\",\\\"authType\\\":2,\\\"userName\\\":\\\"ces2\\\",\\\"password\\\":\\\"6cd6369dc6a0420482ff4e85855a3b2c\\\",\\\"appId\\\":\\\"216033_W+cD1agE7mC93+yv4fWsQw8J6Mxb4spF\\\",\\\"pushDataApiNames\\\":[\\\"AR_RECEIVEBILL\\\"],\\\"useFsHttpClient\\\":true,\\\"config\\\":{\\\"useAppToken\\\":false,\\\"enableDebug\\\":false,\\\"removeZeroWidthChar\\\":false},\\\"lang\\\":\\\"zh-CN\\\",\\\"lcid\\\":2052}\",\"createTime\":1695280677121,\"dataCenterName\":\"88521金蝶云·星空\",\"enterpriseName\":\"(88521)新企业测试\",\"id\":\"643f7322b54ea80001767d86\",\"number\":1,\"tenantId\":\"88521\",\"updateTime\":1699442340472}",ErpConnectInfoEntity.class);
        K3Model dataMap=new K3Model();
        dataMap.put("FStaffId.FNumber","20220516")
        k3DataManagerMock.queryK3ObjData(*_) >> Result.newSuccess(Lists.newArrayList()) >>Result.newSuccess(Lists.newArrayList(dataMap))
        erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))>>erpObjectRelationshipDao
        erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(*_) >>erpObjEntites
        erpFieldDataMappingManager.listNoSearch(* _)>>erpFieldDataMappingEntity >>erpFieldDataMappingEntity2
        erpFieldManager.queryAllField(*_) >>JSONArray.parseArray("[\n" +
                "    {\n" +
                "        \"channel\":\"ERP_K3CLOUD\",\n" +
                "        \"createTime\":1699432522669,\n" +
                "        \"dataCenterId\":\"643f7322b54ea80001767d86\",\n" +
                "        \"erpObjectApiName\":\"BD_Customer.BillHead\",\n" +
                "        \"fieldApiName\":\"F_PAEZ_MulSaller\",\n" +
                "        \"fieldDefineType\":\"employee_many\",\n" +
                "        \"fieldExtendValue\":\"{\\\"operatorType\\\":\\\"XSY\\\"}\",\n" +
                "        \"fieldLabel\":\"多选销售员\",\n" +
                "        \"id\":\"654b484a4bb5000001d8d7ef\",\n" +
                "        \"required\":false,\n" +
                "        \"tenantId\":\"88521\",\n" +
                "        \"updateTime\":1699432522669\n" +
                "    },\n" +
                "    {\n" +
                "        \"channel\":\"ERP_K3CLOUD\",\n" +
                "        \"createTime\":1637841700824,\n" +
                "        \"dataCenterId\":\"643f7322b54ea80001767d86\",\n" +
                "        \"erpObjectApiName\":\"BD_Customer.BillHead\",\n" +
                "        \"fieldApiName\":\"FSELLER.FNumber\",\n" +
                "        \"fieldDefineType\":\"employee\",\n" +
                "        \"fieldLabel\":\"销售员(编码)\",\n" +
                "        \"id\":\"63yJB4Xs2Q0n_VlomXM94\",\n" +
                "        \"required\":false,\n" +
                "        \"tenantId\":\"88521\",\n" +
                "        \"updateTime\":1637841700824\n" +
                "    },\n" +
                "    {\n" +
                "        \"channel\":\"ERP_K3CLOUD\",\n" +
                "        \"createTime\":1697110206333,\n" +
                "        \"dataCenterId\":\"643f7322b54ea80001767d86\",\n" +
                "        \"erpObjectApiName\":\"BD_Customer.BillHead\",\n" +
                "        \"fieldApiName\":\"ComId\",\n" +
                "        \"fieldDefineType\":\"text\",\n" +
                "        \"fieldExtendValue\":\"{\\\"isMainAttribute\\\":false}\",\n" +
                "        \"fieldLabel\":\"ComId(id#Number)\",\n" +
                "        \"id\":\"64de0b90be131c0001f2324f\",\n" +
                "        \"required\":false,\n" +
                "        \"tenantId\":\"88521\",\n" +
                "        \"updateTime\":1692339217081\n" +
                "    }\n" +
                "]", ErpObjectFieldEntity.class)
        erpObjManager.getErpObj(*_) >> JSONObject.parseObject("{\"channel\":\"ERP_K3CLOUD\",\"createTime\":1688457706529,\"dataCenterId\":\"643f7322b54ea80001767d86\",\"deleteStatus\":false,\"erpObjectApiName\":\"BD_Customer.BillHead\",\"erpObjectExtendValue\":\"\",\"erpObjectName\":\"客户\",\"erpObjectType\":\"SPLIT_OBJECT\",\"id\":\"64a3d1e68f6f9100010ec621\",\"tenantId\":\"88521\",\"updateTime\":1693393925815}", ErpObjectEntity.class) >> JSONObject.parseObject("{\"channel\":\"ERP_K3CLOUD\",\"createTime\":1688457706502,\"dataCenterId\":\"643f7322b54ea80001767d86\",\"deleteStatus\":false,\"erpObjectApiName\":\"BD_Customer.BD_CUSTCONTACT\",\"erpObjectExtendValue\":\"BD_Customer.BD_CUSTCONTACT\",\"erpObjectName\":\"客户-地址信息\",\"erpObjectType\":\"SPLIT_OBJECT\",\"id\":\"64a3d1e68f6f9100010ec61e\",\"tenantId\":\"88521\",\"updateTime\":1693393925818}",ErpObjectEntity.class)
        when:
        k3DataManager.convertK3DataErpOrDept(standardData,tenantId,dataCenterId,erpIdArg);
        then:
        standardData.getMasterFieldVal().get("FSELLER.FNumber__r").equals(result2)
        standardData.getMasterFieldVal().get("F_PAEZ_MulSaller__r").equals(result)
        1==1;
        where:
          erpFieldDataMappingEntity   |erpFieldDataMappingEntity2       | result|result2
           JSONArray.parseArray("[{\"fsDataId\":\"1000\"}]", ErpFieldDataMappingEntity.class)| Lists.newArrayList() |Lists.newArrayList("1000")| Lists.newArrayList("-10000")


    }





}
