package com.fxiaoke.skywalking.spock

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.admin.service.impl.ConnectInfoServiceImpl
import com.fxiaoke.open.erpsyncdata.admin.service.overseas.LinkedinFieldService
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpAlarmRuleManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
class Connectpec extends Specification {


    def erpConnectInfoDao = Mock(ErpConnectInfoDao)
    def erpConnectInfoManager = Mock(ErpConnectInfoManager.class)
    def erpAlarmRuleManager = Mock(ErpAlarmRuleManager.class)
    def linkedinFieldService = Mock(LinkedinFieldService.class)


    def ConnectInfoServiceImpl = new ConnectInfoServiceImpl("erpConnectInfoDao": erpConnectInfoDao, "erpConnectInfoManager": erpConnectInfoManager);


    @Unroll
    def "判断sap配置"() {
        given:

        erpConnectInfoDao.setTenantId(*_) >> erpConnectInfoDao;
        erpConnectInfoDao.queryInfoByName(*_) >>null
        when:
        ConnectInfoServiceImpl.updateConnectInfo(tenantId, 1000,connect,null, true);
        then:
        1==1
        where:
        tenantId | connect
        "90896"    | getConnect()
    }
    def getConnect(){
        String data="{\n" +
                "    \"id\": \"66cfd855e6bc0e00015d5ad7\",\n" +
                "    \"dataCenterName\": \"sap_connect\",\n" +
                "    \"channel\": \"ERP_SAP\",\n" +
                "    \"connectParams\": {\n" +
                "        \"sap\": {\n" +
                "            \"iconUrl\": \"\",\n" +
                "            \"systemName\": \"666666\",\n" +
                "            \"baseUrl\": \"http://**************:32668/erpdemo/inner\",\n" +
                "            \"headerScript\": \"\",\n" +
                "            \"headerFunctionName\": \"\",\n" +
                "            \"saprfcParams\":{\n" +
                "                \"systemVersion\":\"xxxx\",\n" +
                "                \"sapProxyUrl\":\"http://localhost/\",\n" +
                "                \"jcoHost\":\"xxxxx\",\n" +
                "                \"jcoSystemNumber\":\"xxxx\",\n" +
                "                \"jcoClient\":\"xxxxx\"\n" +
                "            },\n" +
                "\n" +
                "            \"resultFormat\": {\n" +
                "                \"codeName\": \"code\",\n" +
                "                \"msgName\": \"message\",\n" +
                "                \"dataName\": \"data\",\n" +
                "                \"successCode\": \"0\"\n" +
                "            }\n" +
                "    \n" +
                "        }\n" +
                "    },\n" +
                "    \"pushDataApiNames\": [],\n" +
                "    \"currentDcId\": \"66cfd855e6bc0e00015d5ad7\"\n" +
                "}";
        ConnectInfoResult connectInfoResult=JSONObject.parseObject(data, ConnectInfoResult.class);
        return connectInfoResult;

    }

    @Unroll
    def "updateStandardConnectSysName"() {
        given:
        StandardConnectParam connectParam = (StandardConnectParam) connectParamCase

        def erpConnectInfoDao = Mock(ErpConnectInfoDao) {
            setTenantId(_ as String) >> it

            findById(*_) >> {
                ErpConnectInfoEntity entity = GsonUtil.fromJson("{\"id\":\"66570c81415f1f000154ce9b\",\"tenantId\":\"88521\",\"channel\":\"STANDARD_CHANNEL\",\"dataCenterName\":\"自定义02\",\"enterpriseName\":\"(88521)新企业测试\",\"connectParams\":\"{\\\"systemName\\\":\\\"666666\\\",\\\"iconUrl\\\":\\\"\\\",\\\"baseUrl\\\":\\\"http://**************:32668/erpdemo/inner\\\",\\\"servicePath\\\":{\\\"create\\\":\\\"create\\\",\\\"createDetail\\\":\\\"createDetail\\\",\\\"update\\\":\\\"update\\\",\\\"updateDetail\\\":\\\"updateDetail\\\",\\\"view\\\":\\\"queryMasterById\\\",\\\"queryByTime\\\":\\\"queryMasterBatch\\\",\\\"queryInvalidByTime\\\":\\\"queryInvalid\\\"},\\\"headerScript\\\":\\\"\\\",\\\"headerFunctionName\\\":\\\"\\\",\\\"headerMap\\\":{\\\"dataCenterID\\\":\\\"${tenantId}\\\",\\\"tenantId\\\":\\\"${tenantId}\\\"},\\\"resultFormat\\\":{\\\"codeName\\\":\\\"code\\\",\\\"msgName\\\":\\\"message\\\",\\\"dataName\\\":\\\"data\\\",\\\"successCode\\\":\\\"0\\\"},\\\"pushDataApiNames\\\":[],\\\"connectorHandlerType\\\":\\\"REST_API\\\",\\\"lang\\\":\\\"zh-CN\\\",\\\"lcid\\\":2052}\",\"createTime\":1716980865843,\"updateTime\":1728907961684,\"number\":309,\"status\":1}", ErpConnectInfoEntity.class)
                return entity
            }
        }

        def erpConnectInfoManager = Mock(ErpConnectInfoManager) {
            updateById(*_) >> {
                return 1
            }
        }

        def connectInfoService = new ConnectInfoServiceImpl(erpConnectInfoDao:erpConnectInfoDao,erpConnectInfoManager:erpConnectInfoManager)

        when:
        connectInfoService.updateStandardConnectSysName(connectParam, tenantId, 1000,"66570c81415f1f000154ce9b","en");
        then:
        1==1
        where:
        connectParamCase | tenantId
        GsonUtil.fromJson("{\"systemName\":\"666666\"}", StandardConnectParam.class) |"88521"
    }
}
