package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.ErpK3UltimateApiTemplateManager
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateApiTemplateManager
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateObjApiName
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateApiTemplateEntity
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ErpK3UltimateApiTemplateManagerTest extends BaseSpockTest {
    @Autowired
    ErpK3UltimateApiTemplateManager erpK3UltimateApiTemplateManager
    @Autowired
    K3UltimateApiTemplateManager k3UltimateApiTemplateManager

    @Test
    void insert() {
        def apiTemplate = k3UltimateApiTemplateManager.getApiTemplate("81243","64feb0e7a8fadb0001cbaf6f",K3UltimateObjApiName.bd_material)
        def entity = new ErpK3UltimateApiTemplateEntity.ErpK3UltimateApiTemplateEntityBuilder()
                .id("123456789")
                .tenantId("81243")
                .dataCenterId("64feb0e7a8fadb0001cbaf6f")
                .erpObjApiName(K3UltimateObjApiName.bd_material)
                .apiTemplate(JSONObject.toJSONString(apiTemplate))
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build()
        def count = erpK3UltimateApiTemplateManager.insert(entity)
        assert count==1
    }

    @Test
    void update() {
        def apiTemplate = k3UltimateApiTemplateManager.getApiTemplate("81243","64feb0e7a8fadb0001cbaf6f",K3UltimateObjApiName.bd_material)
        def entity = new ErpK3UltimateApiTemplateEntity.ErpK3UltimateApiTemplateEntityBuilder()
                .id("123456789")
                .tenantId("81243")
                .dataCenterId("64feb0e7a8fadb0001cbaf6f")
                .erpObjApiName(K3UltimateObjApiName.bd_material)
                .apiTemplate(JSONObject.toJSONString(apiTemplate))
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build()
        def count = erpK3UltimateApiTemplateManager.update(entity)
        assert count==1
        findData()
        findApiTemplate()
    }

    @Test
    void findData() {
        def entity = erpK3UltimateApiTemplateManager.findData("81243",
                "64feb0e7a8fadb0001cbaf6f",
                K3UltimateObjApiName.bd_material)
        assert entity!=null
    }

    @Test
    void findApiTemplate() {
        def apiTemplate = k3UltimateApiTemplateManager.getApiTemplate("81243","64feb0e7a8fadb0001cbaf6f",K3UltimateObjApiName.bd_material)
        def entity = erpK3UltimateApiTemplateManager.findApiTemplate("81243",
                "64feb0e7a8fadb0001cbaf6f",
                K3UltimateObjApiName.bd_material)
        assert entity!=null
    }
}
