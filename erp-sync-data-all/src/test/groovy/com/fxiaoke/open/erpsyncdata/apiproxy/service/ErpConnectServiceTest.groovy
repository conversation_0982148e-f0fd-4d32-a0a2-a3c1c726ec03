package com.fxiaoke.open.erpsyncdata.apiproxy.service

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.apiproxy.service.impl.ErpConnectServiceImpl
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/9
 */
@Ignore
class ErpConnectServiceTest extends BaseSpockTest {
    @Autowired
    ErpConnectServiceImpl erpConnectService

    @Test
    void testGetK3CloudDbInfos() {

        def result = erpConnectService.getK3CloudDbInfos("88466","64e5cff5360421000104c537","https://api.kingdee.com/galaxyapi/")
        println(result)
    }

    @Test
    public void checkParam() {
        def param = K3CloudConnectParam.newUserParam(
                "http://*************/k3cloud/",
                "5ec229fad54306",
                "ces1",
                "8888888"
        )
        def result = erpConnectService.checkK3CloudParam(param)
        println(result)
    }
}
