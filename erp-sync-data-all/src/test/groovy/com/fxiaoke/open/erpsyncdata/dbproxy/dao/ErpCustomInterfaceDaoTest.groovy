package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.InterfaceFormatService
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.HttpDataType
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpCustomInterfaceEntity
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.RequestMethod

@Ignore
class ErpCustomInterfaceDaoTest extends BaseSpockTest {
    @Autowired
    ErpCustomInterfaceDao erpCustomInterfaceDao
    @Autowired
    private InterfaceFormatService interfaceFormatService

    @Test
    void insert() {
        def headers = new JSONObject()
        headers.put("name","wubb")
        headers.put("gender","male")

        def xpathData = new JSONObject()
        xpathData.put("master","//response/master/item")

        def entity = new ErpCustomInterfaceEntity.ErpCustomInterfaceEntityBuilder()
        //.id("id101")
                .tenantId("84801")
                .dataCenterId("780777150699143168")
        .objApiName("BD_MATERIAL")
        .interfaceType(ErpObjInterfaceUrlEnum.queryMasterById)
        .url("https://www.fxiaoke.com/")
        .requestMethod(RequestMethod.GET)
        .requestDataType(HttpDataType.XML)
        .requestHeaders(headers)
        .requestBody("<request><master><id>id100</id></master></request>")
        .responseDataType(HttpDataType.XML)
        .responseDataDemo("<response><master><id>id100</id></master></response>")
        .xpathData(xpathData)
        .createTime(System.currentTimeMillis())
        .updateTime(System.currentTimeMillis())
                .build()
        def insert = interfaceFormatService.updateCustomInterfaceData(entity)
        println(insert)
    }

    @Test
    void findData() {
        def list = erpCustomInterfaceDao.findData("84801",
                "780777150699143168",
                "BD_MATERIAL",
                ErpObjInterfaceUrlEnum.queryMasterById)
        println(list)
    }
}
