package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.arg.GetAlarmRecordArg
import com.fxiaoke.open.erpsyncdata.admin.service.ErpAlarmManagementService
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpAlarmRuleManager
import com.fxiaoke.open.erpsyncdata.dbproxy.util.CrmObjectCommonUtils
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ErpAlarmManagementServiceImplTest extends BaseSpockTest {
    @Autowired
    private ErpAlarmManagementService erpAlarmManagementService
    @Autowired
    private ErpAlarmRuleManager erpAlarmRuleManager;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;

    @Test
    void addAlarmRule() {
        def entity = new ErpAlarmRuleEntity.ErpAlarmRuleEntityBuilder()
        .tenantId("81243")
        .dataCenterId("64feb0e7a8fadb0001cbaf6f")
        .ployDetailIds("10e860b3be0e4261898c5b7ec8fd7237")
        .alarmRuleType(AlarmRuleType.CUSTOM)
        .alarmRuleName("自定义告警")
        .alarmType(AlarmType.SYNC_EXCEPTION)
        .alarmLevel(AlarmLevel.GENERAL)
        .threshold(10)
        .userIds("1065")
        .createTime(System.currentTimeMillis())
        .updateTime(System.currentTimeMillis())
                .build()

        def json = JSONObject.toJSONString(entity)
        println(json)
        def result = erpAlarmManagementService.saveAlarmRule(entity)
        assert result.success
    }

    @Test
    void updateAlarmRule() {
        def json = "{\"alarmRuleName\":\"重要告警\",\"alarmType\":\"POLLING_ERP_API_EXCEPTION\",\"threshold\":3,\"alarmLevel\":\"IMPORTANT\",\"userIdList\":[1071,1069],\"roleIdList\":[],\"dataCenterId\":\"64feb0e7a8fadb0001cbaf6f\",\"id\":\"65d1c7c43f0f7d0001d2a375\",\"alarmRuleType\":\"CUSTOM\",\"ployDetailIdList\":[\"all\"],\"currentDcId\":\"641419f20fd9c000016be987\"}"
        ErpAlarmRuleEntity entity = JSONObject.parseObject(json,ErpAlarmRuleEntity.class)
        entity.setRoleIds("00000000000000000000000000000006")
        entity.setUserIds("")

        entity.setPloyDetailIds("all")
        entity.setTenantId("81243")
        //def list = erpAlarmRuleManager.findData("81243","64feb0e7a8fadb0001cbaf6f",AlarmRuleType.CUSTOM,null)
        def result = erpAlarmManagementService.saveAlarmRule(entity)
        assert result.success
    }

    @Test
    void deleteAlarmRule() {
        def result = erpAlarmManagementService.deleteAlarmRule("81243","6581327e89731932dc5e998c");
        assert result.success
    }

    @Test
    void getAlarmRuleList() {
        def list = erpAlarmManagementService.getAlarmRuleList("88521",
                "643f7322b54ea80001767d86",
                20,
                0,
                "en")
        assert list.success && list.data.dataList.size() > 0
    }

    @Test
    void getAlarmRuleDcList() {
        def list = erpAlarmManagementService.getAlarmRuleDcList("81243",
                "en")
        assert list.success && list.data.size() > 0
    }

    @Test
    void getAlarmRecordDcList() {
        def list = erpAlarmManagementService.getAlarmRecordDcList("81243",
                "en")
        assert list.success && list.data.size() > 0
    }

    @Test
    void getAlarmRecordList() {
        GetAlarmRecordArg arg = new GetAlarmRecordArg()
        arg.setTenantId("88521")
        arg.setDataCenterId("643f7322b54ea80001767d86")
        def list = erpAlarmManagementService.getAlarmRecordList(arg)
        assert list.success && list.data.dataList.size() > 0
    }


    @Test
    void getIntegrationStreamList() {
        def result = erpAlarmManagementService.getIntegrationStreamList("88466","6436278b3dcc6b0001e76652",null)
        assert result.isSuccess()
    }
}
