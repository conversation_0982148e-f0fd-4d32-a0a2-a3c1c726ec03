package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FileUtils
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.gson.Gson
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/3
 */
@Ignore
class ErpObjectFieldDaoTest extends BaseSpockTest {
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao

    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService

    @Test
    void findIdField() {
        def result = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .findIdField("84801", "SAL_SC_CustMat.BillHead")
        println(result)
    }
    @Test
    void countByTenantIdAndObjectApiName() {
        def result = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).countByTenantIdAndObjectApiName("79675","saleout_fake_1600327793984","出")
        def result2 = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).queryFieldsByTenantIdAndObjectApiName("79675","saleout_fake_1600327793984","出",2,1);
        println(result)
    }

    @Test
    void updateErpObjectFieldsInOrder() {
        def json = FileUtils.readString("D:\\test\\test.json")
        ErpObjectRelationshipResult result = new Gson().fromJson(json,ErpObjectRelationshipResult.class)
        System.out.println(result)

        Result<Void> result2 = erpObjectFieldsService.updateErpObjectFieldsInOrder("89029","65548e83e74602000142b400",result)
        System.out.println(result2)
    }

    @Test
    void fieldTest() {
        ErpObjectFieldEntity entity = new ErpObjectFieldEntity();
        entity.setTenantId("80774")
        entity.setErpObjectApiName("test_object_md.child")
        List<ErpObjectFieldEntity> list = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("80774")).queryList(entity)
        System.out.println(list)
    }

    @Test
    void test2() {
        ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
        erpObjectDescResult.id="683686940065005568"
        erpObjectDescResult.erpObjectApiName="test_object_md"
        erpObjectDescResult.splitSeq=2
        Result<ErpObjectRelationshipResult> result = erpObjectFieldsService.queryErpObjectAndFieldsByActualObjAndDcId("80774",1000,
        erpObjectDescResult,"663712151783211008")
        System.out.println(result)
    }
}
