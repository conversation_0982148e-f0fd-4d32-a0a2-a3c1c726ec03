package com.fxiaoke.open.erpsyncdata.admin.service

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ExportSyncDataMapping
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile
import com.fxiaoke.open.erpsyncdata.admin.model.excel.TestExcelMod
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Ignore
class FileServiceImplTest extends BaseSpockTest {
    @Autowired
    private FileService fileService

    @Test
    void buildExcelTest() {
        BuildExcelFile.Arg<TestExcelMod> arg = new BuildExcelFile.Arg()
        arg.setTenantId("89029")
        arg.setFileName("testExcel")
        arg.setSheetNames(Lists.newArrayList("testsheet1","testsheet2"))
        def dataList = [new TestExcelMod("学生1","语文",100)]
        arg.setDataList(dataList)
        def result = fileService.buildExcelFile(arg,"en")
        println(result)
        assert result.success
    }

    @Test
    void testImportExcelFile() {
        File file1=new File("D:\\i18n\\GetByPath (12).xlsx");
        InputStream inputStream=new FileInputStream(file1);
        MultipartFile file=new MockMultipartFile("GetByPath (12).xlsx",inputStream)
        ImportExcelFile.FieldDataMappingArg arg=new ImportExcelFile.FieldDataMappingArg();
        arg.setTenantId("89029");
        arg.setDataType(ErpFieldTypeEnum.employee);
        arg.setExcelType(ExcelTypeEnum.FIELD_DATA_MAPPING);
        arg.setFile(file)
        arg.setDataCenterId("64a3c3a5199f940001344d1f")
        def list = fileService.importExcelFile(arg,"64a3c3a5199f940001344d1f","en");
        println(list)
        assert list.success
    }

    @Test
    void test() {
        ImportExcelFile.FieldDataMappingArg arg = new ImportExcelFile.FieldDataMappingArg()
        arg.setExcelType(ExcelTypeEnum.FIELD_DATA_MAPPING)
        arg.setDataType(ErpFieldTypeEnum.category)
        def template = fileService.buildExcelTemplate("81772", arg);
        println(template)
        assert template.success
    }

    @Test
    void buildUserExcelTemplate() {
        ImportExcelFile.FieldDataMappingArg arg = new ImportExcelFile.FieldDataMappingArg()
        arg.setExcelType(ExcelTypeEnum.FIELD_DATA_MAPPING)
        arg.setDataType(ErpFieldTypeEnum.user)
        def template = fileService.buildExcelTemplate("84801", arg);
        println(template)
        assert template.success
    }

    @Test
    void importUserFieldDataMapping() {
        File file1=new File("C:\\Users\\<USER>\\Downloads\\ERP用户数据映射导入模板.xlsx");
        InputStream inputStream=new FileInputStream(file1);
        MultipartFile file=new MockMultipartFile("ERP用户数据映射导入模板.xlsx",inputStream)
        ImportExcelFile.FieldDataMappingArg arg=new ImportExcelFile.FieldDataMappingArg();
        arg.setTenantId("84801");
        arg.setDataCenterId("780777150699143168")
        arg.setUserId(1000)
        arg.setExcelType(ExcelTypeEnum.FIELD_DATA_MAPPING);
        arg.setDataType(ErpFieldTypeEnum.user)
        arg.setFile(file);
        def list = fileService.importExcelFile(arg,"780777150699143168")
        println(list)
        assert list.success
    }

    @Test
    void testImportObjectDataMapping() {
        File file1=new File("D:\\i18n\\GetByPath (6).xlsx");
        InputStream inputStream=new FileInputStream(file1);
        MultipartFile file=new MockMultipartFile("GetByPath (6).xlsx",inputStream)
        ImportExcelFile.ObjectDataMappingArg arg=new ImportExcelFile.ObjectDataMappingArg();
        arg.setTenantId("89029");
        arg.setUserId(1000)
        arg.setPloyDetailId("64a3c3ac199f94000134739e");
        arg.setExcelType(ExcelTypeEnum.OBJ_DATA_MAPPING);
        arg.setFile(file);
        def list = fileService.importObjectDataMapping(arg,"en");
        println(list)
        assert list.success
    }

    @Test
    void testExportObjectDataMapping() {
        ExportSyncDataMapping.ExportSyncDataMappingArg arg =new ExportSyncDataMapping.ExportSyncDataMappingArg();
        arg.setTenantId("79675")
        arg.setUserId(1002);
        arg.setStartTime(1623313159000L)
        arg.setEndTime(1623572359000L);
        arg.setPloyDetailId("162a9aba85ff44e38dfe81899ade089a")
        arg.setStatus(0)
        def list = fileService.asyncExportSyncDataMappingData(arg,"603396635238203392");
        println(list)
        assert list.success
    }

    @Test
    void buildObjectDataMappingTemplateTest() {
        def result = fileService.buildObjectDataMappingTemplate( "80787","4da9098955334cbd9cd837d5713b9aeb",null)
        println(result)
        assert result.success
    }

    @Test
     void testGetDeptExcels() {
        def result = fileService.getCrmDeptExcel(81772,1001)
        println(result)
    }
}
