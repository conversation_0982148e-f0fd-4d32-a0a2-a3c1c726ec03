package com.fxiaoke.open.erpsyncdata.preprocess.manager

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.assertj.core.util.Lists
import spock.lang.Specification

class ProbeErpDataManagerTest extends Specification {

    ProbeErpDataManager probeErpDataManager;
    String tenantId="testid";
    String sourceObjAPIName="testObj";
    Integer offset= 60; //从30页开始
    Integer limit = 2;
    Map<Integer, Integer> allRepeatIdNumMap = Maps.newHashMap();
    Set<String> allOldPageErpIdSet = Sets.newHashSet();
    List< SyncDataContextEvent> curErpObjDataResultList1 = Lists.newArrayList();
    List< SyncDataContextEvent> curErpObjDataResultList2 = Lists.newArrayList();
    List< SyncDataContextEvent> curErpObjDataResultList3 = Lists.newArrayList();
    List< SyncDataContextEvent> curErpObjDataResultList4 = Lists.newArrayList();
    List<String> historyComparePageList = com.google.common.collect.Lists.newArrayList();

    boolean sendNoticeSuperMsg = false;
    def "setup" () {
        probeErpDataManager = new ProbeErpDataManager() {
        };
        IdFieldConvertManager idFieldConvertManager = Mock(IdFieldConvertManager) {
            getIdField(*_) >> {
                ErpObjectFieldEntity idField = ErpObjectFieldEntity.builder().fieldApiName("id").build();
                return idField;
            }
        };
        I18NStringManager i18NStringManager = Mock(I18NStringManager) {
            getByEi2(*_) >> {
                return "msg2";
            };
            getByEi(*_) >> {
                return  "msg";
            }
        }
        NotificationService notificationService = Mock(NotificationService) {
            sendSuperAdminNoticeToManageTool(*_) >> {
                System.out.println("mock send msg");
                sendNoticeSuperMsg = true;
            }
        }

        probeErpDataManager.setIdFieldConvertManager(idFieldConvertManager);
        probeErpDataManager.setI18NStringManager(i18NStringManager);
        probeErpDataManager.setNotificationService(notificationService);
    }


    //测试是否有连续相同的分页
    def "testisAllIDRepeatAsLastPage" (expectedRet1,expectedRet2,expectedRet3, int testcaseNo) {
        setup:
        cooktestisAllIDRepeatAsLastPage3Page(testcaseNo);

        when:
        if(testcaseNo==4){
            int x =1;
        }
        boolean testRet1 = probeErpDataManager.isAllIDRepeatAsLastPage(tenantId, sourceObjAPIName, offset, limit,
                curErpObjDataResultList1,historyComparePageList);
        boolean testRet2 = probeErpDataManager.isAllIDRepeatAsLastPage(tenantId, sourceObjAPIName, offset, limit,
                curErpObjDataResultList2,historyComparePageList);
        boolean testRet3 = probeErpDataManager.isAllIDRepeatAsLastPage(tenantId, sourceObjAPIName, offset, limit,
                curErpObjDataResultList3,historyComparePageList);
        then:
        System.out.println("testcaseNo: " + testcaseNo);
        testRet1 == expectedRet1
        testRet2 == expectedRet2
        testRet3 == expectedRet3

        where :
        expectedRet1 | expectedRet2 | expectedRet3   | testcaseNo
        /**第1，2，3页完全不重复**/
        false        |     false     |     false     | 1
        /**第1，2，3页完全重复， **/
        false        |     true     |     true       | 2
        /**第2，3页重复， 第1页和他们不重复**/
        false        |     false     |     true      | 3
        /**第1，2页重复， 第3页和他们不重复**/
        false        |     true     |     false      | 4
    }

    def "cooktestisAllIDRepeatAsLastPage3Page"(testcaseNo) {
        curErpObjDataResultList1.clear();//第1页数据
        curErpObjDataResultList2.clear();//第1页数据
        curErpObjDataResultList3.clear();//第1页数据
        ObjectData sourceData1 = new ObjectData();
        sourceData1.putApiName(sourceObjAPIName);
        ObjectData sourceData2 = new ObjectData();
        sourceData2.putApiName(sourceObjAPIName);
        ObjectData sourceData3 = new ObjectData();
        sourceData3.putApiName(sourceObjAPIName);
        ObjectData sourceData4 = new ObjectData();
        sourceData4.putApiName(sourceObjAPIName);
        ObjectData sourceData5 = new ObjectData();
        sourceData5.putApiName(sourceObjAPIName);
        ObjectData sourceData6 = new ObjectData();
        sourceData6.putApiName(sourceObjAPIName);
        ObjectData sourceData7 = new ObjectData();
        sourceData7.putApiName(sourceObjAPIName);
        ObjectData sourceData8 = new ObjectData();
        sourceData8.putApiName(sourceObjAPIName);
        ObjectData sourceData9 = new ObjectData();
        sourceData9.putApiName(sourceObjAPIName);
        ObjectData sourceData10 = new ObjectData();
        sourceData10.putApiName(sourceObjAPIName);
        ObjectData sourceData11 = new ObjectData();
        sourceData11.putApiName(sourceObjAPIName);
        ObjectData sourceData12 = new ObjectData();
        sourceData12.putApiName(sourceObjAPIName);

        SyncDataContextEvent d1 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData1).build();
        SyncDataContextEvent d2 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData2).build();
        SyncDataContextEvent d3 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData3).build();
        SyncDataContextEvent d4 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData4).build();
        SyncDataContextEvent d5 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData5).build();
        SyncDataContextEvent d6 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData6).build();
        SyncDataContextEvent d7 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData7).build();
        SyncDataContextEvent d8 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData8).build();
        SyncDataContextEvent d9 = SyncDataContextEvent.builder().sourceTenantId(tenantId).sourceData(sourceData9).build();

        if (1 == testcaseNo) {
            sourceData1.put("id", "id1");
            sourceData2.put("id", "id2");
            sourceData3.put("id", "id3");

            sourceData4.put("id", "id4");
            sourceData5.put("id", "id5");
            sourceData6.put("id", "id6");

            sourceData7.put("id", "id7");
            sourceData8.put("id", "id8");
            sourceData9.put("id", "id9");
        } else if (2 == testcaseNo) {
            sourceData1.put("id", "id1");
            sourceData2.put("id", "id2");
            sourceData3.put("id", "id3");

            sourceData4.put("id", "id1");
            sourceData5.put("id", "id2");
            sourceData6.put("id", "id3");

            sourceData7.put("id", "id1");
            sourceData8.put("id", "id2");
            sourceData9.put("id", "id3");
        } else if (3 == testcaseNo) {
            sourceData1.put("id", "id4");
            sourceData2.put("id", "id5");
            sourceData3.put("id", "id6");

            sourceData4.put("id", "id1");
            sourceData5.put("id", "id2");
            sourceData6.put("id", "id3");

            sourceData7.put("id", "id1");
            sourceData8.put("id", "id2");
            sourceData9.put("id", "id3");
        } else if (4 == testcaseNo) {
            sourceData1.put("id", "id1");
            sourceData2.put("id", "id2");
            sourceData3.put("id", "id3");

            sourceData4.put("id", "id1");
            sourceData5.put("id", "id2");
            sourceData6.put("id", "id3");

            sourceData7.put("id", "id4");
            sourceData8.put("id", "id5");
            sourceData9.put("id", "id6");
        }
        if(d1.getSourceData().get("id")) {curErpObjDataResultList1.add(d1);}
        if(d2.getSourceData().get("id")) {curErpObjDataResultList1.add(d2);}
        if(d3.getSourceData().get("id")) {curErpObjDataResultList1.add(d3);}
        if(d4.getSourceData().get("id")) {curErpObjDataResultList2.add(d4);}
        if(d5.getSourceData().get("id")) {curErpObjDataResultList2.add(d5);}
        if(d6.getSourceData().get("id")) {curErpObjDataResultList2.add(d6);}
        if(d7.getSourceData().get("id")) {curErpObjDataResultList3.add(d7);}
        if(d8.getSourceData().get("id")) {curErpObjDataResultList3.add(d8);}
        if(d9.getSourceData().get("id")) {curErpObjDataResultList3.add(d9);}
    }

}
