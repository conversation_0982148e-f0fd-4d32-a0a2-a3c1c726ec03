package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.facishare.converter.EIEAConverter
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants
import com.fxiaoke.crmrestapi.common.data.HeaderObj
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum
import org.assertj.core.util.Lists
import spock.lang.Specification

class ErpObjectServiceImplTest extends Specification {
    def "importObjectApiNameData"() {
        given:
        def ea = "88521"
        def tenantId = 88521
        def dcId = "643f7322b54ea80001767d86"
        def npath = "test"
        def userId = -10000

        def eieaConverter = Mock(EIEAConverter) {
            enterpriseAccountToId(*_) >> {
                return tenantId
            }
        }

        def objectDescribeService = Mock(ObjectDescribeService) {

            getDescribe(HeaderObj.newInstance(Integer.valueOf(tenantId), SuperUserConstants.USER_ID), "AccountObj") >> {
                Result result = new Result()
                result.setCode(0)
                return result
            }

            getDescribe(HeaderObj.newInstance(Integer.valueOf(tenantId), SuperUserConstants.USER_ID), "AccountAddrObj") >> {
                Result result = new Result()
                result.setCode(0)
                return result
            }
        }

        I18NStringManager i18NStringManager = new I18NStringManager()

        def erpObjectDao = Mock(ErpObjectDao) {
            setTenantId(_) >> it

            ErpObjectEntity query = new ErpObjectEntity();
            query.setTenantId(String.valueOf(tenantId));
            query.setDataCenterId(dcId);
            query.setErpObjectApiName("BD_CustomerD");
            queryList(query) >> {
                return Lists.newArrayList(new ErpObjectEntity())
            }

            ErpObjectEntity query1 = new ErpObjectEntity();
            query1.setTenantId(String.valueOf(tenantId));
            query1.setDataCenterId(dcId);
            query1.setErpObjectApiName("BD_CustomerD.BD_FININFO");
            queryList(query1) >> {
                return Lists.newArrayList()
            }

            ErpObjectEntity query2 = new ErpObjectEntity();
            query2.setTenantId(String.valueOf(tenantId));
            query2.setDataCenterId(dcId);
            query2.setErpObjectApiName("BD_CustomerE");
            queryList(query2) >> {
                return Lists.newArrayList()
            }

            insert(*_) >> {
                return 1
            }

            queryByApiNames2(String.valueOf(tenantId), dcId, ["BD_CustomerD.BD_FININFO_1","BD_CustomerD.BillHead_1"]) >> {
                return [//ErpObjectEntity.builder()
//                                .id("667021da9fa8f00001983496")
//                                .tenantId(String.valueOf(tenantId))
//                                .channel(erpChannelEnum)
//                                .dataCenterId(dcId)
//                                .erpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT)
//                                .erpObjectName("客户客户123-财务信息")
//                                .erpObjectApiName("BD_CustomerD.BD_FININFO_1")
//                                .erpObjectExtendValue("BD_CustomerD.BD_FININFO")
//                                .deleteStatus(false).build(),
                        ErpObjectEntity.builder()
                        .id("667021da9fa8f00001983498")
                        .tenantId(String.valueOf(tenantId))
                        .channel(erpChannelEnum)
                        .dataCenterId(dcId)
                        .erpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT)
                        .erpObjectName("客户客户123")
                        .erpObjectApiName("BD_CustomerD.BillHead_1")
                        .deleteStatus(false).build(),
                        ErpObjectEntity.builder()
                                .id("667021da9fa8f00001983494")
                                .tenantId(String.valueOf(tenantId))
                                .channel(erpChannelEnum)
                                .dataCenterId(dcId)
                                .erpObjectType(ErpObjectTypeEnum.REAL_OBJECT)
                                .erpObjectName("客户")
                                .erpObjectApiName("BD_CustomerD")
                                .deleteStatus(false).build()]
            }

            queryByApiNames2(String.valueOf(tenantId), dcId, ["BD_CustomerE.BillHead_1"]) >> {
                return []
            }
        }

        def erpObjectRelationshipDao = Mock(ErpObjectRelationshipDao) {
            setTenantId(_) >> it

            ErpObjectRelationshipEntity queryAllObject = new ErpObjectRelationshipEntity();
            queryAllObject.setTenantId(String.valueOf(tenantId));
            queryAllObject.setDataCenterId(dcId);
            queryAllObject.setErpRealObjectApiname("BD_CustomerD");
            queryList(queryAllObject) >> {
                return [ErpObjectRelationshipEntity.builder()
                                .tenantId(String.valueOf(tenantId))
                                .channel(erpChannelEnum)
                                .splitSeq(1)
                                .erpRealObjectApiname("BD_CustomerD")
                                .erpSplitObjectApiname("BD_CustomerD.BD_FININFO_1")
                                .build(),
                        ErpObjectRelationshipEntity.builder()
                        .tenantId(String.valueOf(tenantId))
                        .channel(erpChannelEnum)
                        .splitSeq(1)
                        .erpRealObjectApiname("BD_CustomerD")
                        .erpSplitObjectApiname("BD_CustomerD.BillHead_1")
                        .build()]
            }

            ErpObjectRelationshipEntity queryAllObject1 = new ErpObjectRelationshipEntity();
            queryAllObject1.setTenantId(String.valueOf(tenantId));
            queryAllObject1.setDataCenterId(dcId);
            queryAllObject1.setSplitSeq(1)
            queryAllObject1.setErpRealObjectApiname("BD_CustomerD");
            queryList(queryAllObject1) >> {
                return [ErpObjectRelationshipEntity.builder()
                                .tenantId(String.valueOf(tenantId))
                                .channel(erpChannelEnum)
                                .splitSeq(1)
                                .erpRealObjectApiname("BD_CustomerD")
                                .erpSplitObjectApiname("BD_CustomerD.BD_FININFO_1")
                                .build(),
                        ErpObjectRelationshipEntity.builder()
                                .tenantId(String.valueOf(tenantId))
                                .channel(erpChannelEnum)
                                .splitSeq(1)
                                .erpRealObjectApiname("BD_CustomerD")
                                .erpSplitObjectApiname("BD_CustomerD.BillHead_1")
                                .build()]
            }

            ErpObjectRelationshipEntity queryAllObject2 = new ErpObjectRelationshipEntity();
            queryAllObject2.setTenantId(String.valueOf(tenantId));
            queryAllObject2.setDataCenterId(dcId);
            queryAllObject2.setErpRealObjectApiname("BD_CustomerE");
            queryList(queryAllObject2) >> {
                return []
            }

            ErpObjectRelationshipEntity queryAllObject3 = new ErpObjectRelationshipEntity();
            queryAllObject3.setTenantId(String.valueOf(tenantId));
            queryAllObject3.setDataCenterId(dcId);
            queryAllObject3.setSplitSeq(1)
            queryAllObject3.setErpRealObjectApiname("BD_CustomerE");
            queryList(queryAllObject3) >> {
                return []
            }

            insert(*_) >> 1

            invalidCacheErpObj(*_) >> null
        }

        def dataCenterManager = Mock(DataCenterManager) {
            getDataCenterSeq(*_) >> {
                return 1
            }
        }

        def idGenerator = Mock(IdGenerator) {
            get() >> "123456789"
        }

        def erpObjectService = new ErpObjectServiceImpl(objectDescribeService:objectDescribeService,
                erpObjectDao:erpObjectDao,
                i18NStringManager:i18NStringManager,
                erpObjectRelationshipDao:erpObjectRelationshipDao,
                dataCenterManager:dataCenterManager,
                idGenerator:idGenerator)

        def result = erpObjectService.importObjectApiNameData(String.valueOf(tenantId), userId, dcId, erpChannelEnum, lang, integrationStreamExcelVos)

        expect:
        result.success

        where:
        erpChannelEnum        |  lang                |  integrationStreamExcelVos
        ErpChannelEnum.ERP_K3CLOUD  |  "zh-CN"             |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name1")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("id")
                        .thirdPartyFieldApiName("id")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
        ErpChannelEnum.ERP_K3CLOUD  |  "zh-CN"             |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户-地址")
                        .crmObjectApiName("AccountAddrObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("客户-地址信息")
                        .thirdPartyObjectApiName("BD_CustomerD.BD_FININFO")
                        .thirdPartyFieldLabel("邮件")
                        .thirdPartyFieldApiName("FEMail")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
        ErpChannelEnum.ERP_K3CLOUD  |  "zh-CN"             |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerE")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name1")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerE")
                        .thirdPartyFieldLabel("id")
                        .thirdPartyFieldApiName("id")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
    }
}
