package com.fxiaoke.open.erpsyncdata

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SpecialWayDataService
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.HeaderManager
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.preprocess.arg.HeaderFunctionArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.googlecode.aviator.AviatorEvaluator
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
class HeaderFunctionSpec extends  Specification{


    def specialWayDataService=Mock(SpecialWayDataService)
    def headerManager=new HeaderManager("specialWayDataService":specialWayDataService);

//    @Unroll
//    def "函数测试 #uid"(){
//        given:
//        specialWayDataService.executeCustomFunction(*_) >> result
//        def headerFunctionArg=new HeaderFunctionArg();
//        when:
//       def exResult= headerManager.getHeaderMapByFunction(headerFunctionArg)
//        then:
//        println exResult;
//        exResult.errCode==type
//        where: "表格方式验证订单信息的分支场景"
//        uid | result        || type
//        2   | getRelation() || ResultCodeEnum.SUCCESS.errCode
//        2   | getRelation2() || ResultCodeEnum.SUCCESS.errCode
//
//    }

    def getRelation(){
       Result<String> value=JSONObject.parseObject("{\n" +
                "    \"errCode\":\"s106240000\",\n" +
                "    \"errMsg\":\"错误信息\",\n" +
                "    \"data\":{\n" +
                "        \"status\":\"1\"\n" +
                "    }\n" +
                "}", Result.class)
        value.setData(value.getData().toString())
        return value;
    }

    def getRelation2(){
        Result<String> value=JSONObject.parseObject("{\n" +
                "    \"errCode\":\"s106240000\",\n" +
                "    \"errMsg\":\"错误信息\",\n" +
                "    \"data\":\"[1,2,3]\"\n" +
                "}", Result.class)
        value.setData(value.getData().toString())
        return value;
    }





}
