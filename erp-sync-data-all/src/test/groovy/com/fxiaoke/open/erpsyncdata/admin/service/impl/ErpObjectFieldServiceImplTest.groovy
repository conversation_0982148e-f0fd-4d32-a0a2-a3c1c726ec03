package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import spock.lang.Specification

class ErpObjectFieldServiceImplTest extends Specification {

    def "importObjectFiledApiNameData"() {
        def ea = "88521"
        def tenantId = 88521
        def dcId = "643f7322b54ea80001767d86"
        def npath = "test"
        def userId = -10000

        Map<String, String> erpObjectMap = new HashMap<>()
        if (id == 1) {
            erpObjectMap.put("BD_CustomerD","BD_CustomerD.BillHead_1")
        } else if (id == 2) {
            erpObjectMap.put("BD_CustomerD","BD_CustomerD.BillHead_1")
            erpObjectMap.put("BD_CustomerD.BD_FININFO","BD_CustomerD.BD_FININFO_1")
        } else if (id == 3) {
            erpObjectMap.put("BD_CustomerE","BD_CustomerE.BillHead_1")
        }

        def erpObjectFieldDao = Mock(ErpObjectFieldDao) {
            setTenantId(_) >> it

            ErpObjectFieldEntity query = new ErpObjectFieldEntity();
            query.setTenantId(String.valueOf(tenantId));
            query.setDataCenterId(dcId);
            query.setErpObjectApiName(erpObjectMap.get("BD_CustomerD"));
            query.setFieldApiName("name");
            queryList(query) >> {
                return [new ErpObjectFieldEntity()]
            }
            queryList(*_) >> {
                return []
            }

            insert(*_) >> 1
        }

        def idGenerator = Mock(IdGenerator) {
            get() >> "123456789"
        }

        def erpObjectFieldsService = new ErpObjectFieldsServiceImpl(erpObjectFieldDao:erpObjectFieldDao,
                idGenerator:idGenerator)



        def result = erpObjectFieldsService.importObjectFiledApiNameData(String.valueOf(tenantId), dcId, erpChannelEnum, lang, erpObjectMap, integrationStreamExcelVos)

        expect:
        result.success

        where:
        id  |  erpChannelEnum        |  lang                |  integrationStreamExcelVos
        1  |  ErpChannelEnum.ERP_K3CLOUD |  "zh-CN" |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name1")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("id")
                        .thirdPartyFieldApiName("id")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
        2  |  ErpChannelEnum.ERP_K3CLOUD  |  "zh-CN"             |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户-地址")
                        .crmObjectApiName("AccountAddrObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("客户-地址信息")
                        .thirdPartyObjectApiName("BD_CustomerD.BD_FININFO")
                        .thirdPartyFieldLabel("邮件")
                        .thirdPartyFieldApiName("FEMail")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
        3  |  ErpChannelEnum.ERP_K3CLOUD  |  "zh-CN"             |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerE")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name1")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerE")
                        .thirdPartyFieldLabel("id")
                        .thirdPartyFieldApiName("id")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
    }
}
