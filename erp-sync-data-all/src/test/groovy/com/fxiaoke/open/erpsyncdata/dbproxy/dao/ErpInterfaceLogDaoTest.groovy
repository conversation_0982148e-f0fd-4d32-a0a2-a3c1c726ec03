package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpHistoryDataTasksArg
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/3
 */
@Ignore
class ErpInterfaceLogDaoTest extends BaseSpockTest {
    @Autowired
    private ErpInterfaceLogDao erpInterfaceLogDao
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;
    @Test
    void findByIdTest() {
        def entity = erpInterfaceLogDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("tenantId")).findById("save")
        println(entity)
    }
    @Test
    public void testIntegration(){
        QueryErpHistoryDataTasksArg queryErpHistoryDataTasksArg=new QueryErpHistoryDataTasksArg();
        queryErpHistoryDataTasksArg.setId("65f97654ee839a00012419c9");
        erpHistoryDataTaskService.queryErpTaskSnapshotList("89029","64a3c3a5199f940001344d1f",1000,queryErpHistoryDataTasksArg,null)

//       List<SyncPloyDetailEntity> allPloyDetailBySplitName = syncPloyDetailManager.getAllPloyDetailBySplitName("88521", "SAL_OUTSTOCK.BillHead");

    }
}
