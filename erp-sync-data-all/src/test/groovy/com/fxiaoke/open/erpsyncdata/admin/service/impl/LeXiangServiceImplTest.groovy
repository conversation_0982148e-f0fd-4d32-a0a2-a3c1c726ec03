package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.alibaba.fastjson.JSONArray
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload
import com.facishare.fsi.proxy.service.NFileStorageService
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.model.NPathModel
import com.fxiaoke.open.erpsyncdata.admin.model.lexiang.LeXiangDoc
import com.fxiaoke.open.erpsyncdata.admin.service.LeXiangService
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter
//import okhttp3.MediaType
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class LeXiangServiceImplTest extends BaseSpockTest {
    @Autowired
    private LeXiangService leXiangService
    @Autowired
    private NFileStorageService nFileStorageService

    @Test
    void getAccessToken() {
        def result = leXiangService.getAccessToken(ConfigCenter.lexiangAppId, ConfigCenter.lexiangAppSecret)
        println(result)
        assert StringUtils.isNotEmpty(result.data)
    }

    @Test
    void getSuperAdminList() {
        def result = leXiangService.getSuperAdminList(ConfigCenter.lexiangAppId, ConfigCenter.lexiangAppSecret)
        println(result)
        assert result.code==0
    }

    @Test
    void sendDoc() {
        def attributes = new LeXiangDoc.LeXiangData.Attributes()
        attributes.setTitle("通过开放接口创建的文档2")
        attributes.setIs_markdown(0)
        attributes.setContent("<p>富文本内容</p><p>test only 2</p>")
        attributes.setSource("original")
        attributes.setSignature("WuBeiBei")
        attributes.setPicture_url("https://alifei04.cfp.cn/creative/vcg/veer/1600water/veer-303764513.jpg")
        attributes.setOnly_team(1)
        attributes.setAllow_comment(1)
        attributes.setPrivilege_type(0)

        def relationships = new LeXiangDoc.LeXiangData.Relationships()
        relationships.setTeam(new LeXiangDoc.LeXiangData.Team())
        relationships.getTeam().setData(new LeXiangDoc.LeXiangData.DataItem())
        relationships.getTeam().getData().setType("team")
        relationships.getTeam().getData().setId("38288e426fc011ed942066b2030f1ad7")


        relationships.setDirectory(new LeXiangDoc.LeXiangData.Directory())
        relationships.getDirectory().setData(new LeXiangDoc.LeXiangData.DataItem())
        relationships.getDirectory().getData().setType("directory")
        relationships.getDirectory().getData().setId("ef2c556cfae211ed91eeb2bb2aa0e678")


        def doc = new LeXiangDoc()
        doc.setData(new LeXiangDoc.LeXiangData())
        doc.getData().setType("doc")
        doc.getData().setAttributes(attributes)
        doc.getData().setRelationships(relationships)


        def result = leXiangService.sendDoc(ConfigCenter.lexiangAppId, ConfigCenter.lexiangAppSecret,doc)
        println(result)
        assert result.success
    }

    @Test
    void uploadFile() {
        def filePath = "C:\\Users\\<USER>\\Downloads\\test.jpg"
        def bytes = FileUtils.readFileToByteArray(new File(filePath))
        def arg = new NTempFileUpload.Arg()
        arg.setEa("84801")
        arg.setSourceUser("E.84801.1000")
        arg.setData(bytes)
        arg.setBusiness("lx")
        arg.setOriginName("test")
        arg.setExtensionName("jpg")
        def result = nFileStorageService.nTempFileUpload(arg,"84801")

        println(result)
    }

    @Test
    void uploadAsset() {
        def filePath = "C:\\Users\\<USER>\\Downloads\\test.jpg"
        def bytes = FileUtils.readFileToByteArray(new File(filePath))
        def result = leXiangService.uploadAsset(ConfigCenter.lexiangAppId,
                ConfigCenter.lexiangAppSecret,
                "image",
                1,
                "123.jpg",
                MediaType.parse("image/jpeg"),
                bytes)
        println(result)
    }

    @Test
    void uploadAsset2() {
        def result = leXiangService.uploadAsset(ConfigCenter.lexiangAppId,
                ConfigCenter.lexiangAppSecret,
                "image",
                1,
                "123.jpg",
                MediaType.parse("image/jpeg"),
                "84801",
                "N_202305_29_0be9e89a166e49708499ae60820ef527")
        println(result)
    }

    @Test
    void sendHtmlWorkOrder() {
        String html = "<h2>【问题现象和操作步骤】</h2><p>1.客户在crm新建预测单，已经传到金蝶之后，返回值提示更新中间记录表失败；写接口调用中是有成功返回数据的；\\n2.预测单同步到ERP，状态按已审核状态</p><br/><h2>【问题图片】</h2><img src='{N_202303_07_ef1dc3cb046140f896241389d0521f33}'/><img src='{N_202303_07_74cb9ab93a5f4b7384645d4094da74db}'/><img src='{N_202303_07_96d7cff1b04d42aab98653b85ea3b3c4}'/><h2>【区域排查问题原因】</h2><p>问题1：金蝶接口已调用成功，但记录中间表时报错。排查中间表并没有重复的ID，需研发排查。\\n问题2：需研发调整</p><h2>【总部排查问题原因】</h2><p>问题1：Detail: Key (tenant_id, source_object_api_name, dest_object_api_name, dest_data_id)=(714544, object_OV16a__c, PLN_FORECAST.PLN_FORECASTENTRY, ) already exists。客户之前建立了PLN_FORECAST，后来删掉。又用工具刷了一下该对象，判断是数据库中没有删除干净。需要研发排查。\\n问题2请研发配置。</p><h2>【研发填写问题原因】</h2><p>从对象id字段的saveCode大小写问题</p><h2>【研发排查问题原因】</h2><p><p style=\\\"margin: 0px;\\\"><img src=\\\"https://www.fxiaoke.com/FSC/EM/File/GetByPath?path=N_202303_10_a8bb6dc55249438eae7217fb7aacf401.jpg&amp;filetype=webp\\\" width=\\\"485\\\" height=\\\"316\\\" style=\\\"width: 485px; height: 316px;\\\"></p>\\n<p style=\\\"margin: 0px;\\\"><img src=\\\"https://www.fxiaoke.com/FSC/EM/File/GetByPath?path=N_202303_10_e352f86261474357a1b51ee4123c8309.jpg&amp;filetype=webp\\\" width=\\\"534\\\" height=\\\"320\\\" style=\\\"width: 534px; height: 320px;\\\"></p>\\n<p style=\\\"margin: 0px;\\\"><br></p></p><h2>【来源】</h2><p>工单编码：GD-20230307-10818</p>"
        def json = "[{\"ext\":\"png\",\"path\":\"N_202303_07_ef1dc3cb046140f896241389d0521f33\",\"filename\":\"微信截图_20230307112846.png\",\"size\":201091},{\"ext\":\"png\",\"path\":\"N_202303_07_74cb9ab93a5f4b7384645d4094da74db\",\"filename\":\"微信截图_20230307112925.png\",\"size\":155066},{\"ext\":\"png\",\"path\":\"N_202303_07_96d7cff1b04d42aab98653b85ea3b3c4\",\"filename\":\"微信截图_20230307113017.png\",\"size\":155177}]"
        def nPathList = JSONArray.parseArray(json, NPathModel.class)

        def result = leXiangService.sendHtmlWorkOrder(ConfigCenter.lexiangAppId,
                ConfigCenter.lexiangAppSecret,
                "82777",
                "工单转乐享测试",
                html,
                nPathList)
        println(result)
        assert result.isSuccess()
    }
}
