package com.fxiaoke.PureTest


import groovy.util.logging.Slf4j

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/1/11
 */
@Slf4j
class GroovyTest {
    static void main(String[] args) {
        Map<String,String> params = new HashMap<>()
        params.put("APPSECRET","crmBank")
        params.put("STARTTIME","*************")
        params.put("ENDTIME","*************")
        params.put("OFFSET","0")
        params.put("LIMIT","50")
        //params.put("USER_ID","100001")
        //params.put("DATAID","740189")
        generateToken(params)
        params.each {entry->
            if(entry.value.contains("Bank")) {
                println(entry.key)
            }
        }
    }


}
