package com.fxiaoke.PureTest

import org.junit.Test
import spock.lang.Specification

import java.math.RoundingMode

/**
 *
 * <AUTHOR> (^_−)☆
 */
class BigDecimalTest extends Specification {

    @Test
    void testScale() {
        expect:
        ret == new BigDecimal(input).setScale(1, RoundingMode.HALF_DOWN).toString()
        println("input:$input,ret:$ret")
        where:
        input   | ret
        "3.44"  | "3.4"
        "3.45"  | "3.4"
        "3.46"  | "3.5"
        "3.451" | "3.5"
        "3.450" | "3.4"
        "3.449" | "3.4"
    }


    @Test
    void testScale2() {
        expect:
        ret == new BigDecimal(input).setScale(1, RoundingMode.HALF_UP).toString()
        println("input:$input,ret:$ret")
        where:
        input   | ret
        "3.44"  | "3.4"
        "3.45"  | "3.5"
        "3.46"  | "3.5"
        "3.451" | "3.5"
        "3.450" | "3.5"
        "3.449" | "3.4"
    }
}
