package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.K3CloudStockArg;
import com.fxiaoke.open.erpsyncdata.admin.service.K3CStockService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class K3CStockServiceImplTest extends BaseTest {
    @Autowired
    private K3CStockService k3CStockService;

    @Test
    public void syncStockData() {
        K3CloudStockArg arg = new K3CloudStockArg();
        arg.setTenantId("88466");
        arg.setStockNumberList(Lists.newArrayList("CK148","CK148"));
        arg.setMaterialNumberList(Lists.newArrayList("CH4942","CH4943"));
        Result<Void> result = k3CStockService.syncStockData(arg);
        System.out.println(result);
    }
}
