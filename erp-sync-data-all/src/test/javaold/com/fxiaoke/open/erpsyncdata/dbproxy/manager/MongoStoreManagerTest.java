package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.manager.MongoStoreManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 9:54 2021/6/29
 * @Desc:
 */
@Ignore
public class MongoStoreManagerTest extends BaseTest {
    @Autowired
    private MongoStoreManager mongoStoreManager;

    @Test
    public void getAllStoreCollections() {
        List<String> allStoreCollections = mongoStoreManager.getAllStoreCollections();
        System.out.println("");
    }
    @Test
    public void getAllStoreCollectionSize() {
        Map<String, Long> allStoreCollectionSize = mongoStoreManager.getAllStoreCollectionSize();
        System.out.println("");
    }
}