package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.SystemFieldService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 系统字段服务单元测试类
 * <AUTHOR>
 * @date 2023.03.20
 */
@Ignore
public class SystemFieldServiceImplTest extends BaseTest {
    @Autowired
    private SystemFieldService systemFieldService;

    @Test
    public void exportEmployeeMappingData() {
        Result<Void> result = systemFieldService.exportSystemFieldMapping("84801",
                1000,
                "780777150699143168",
                ErpFieldTypeEnum.employee,
                Lists.newArrayList("796786756082106368", "63b689fa8c24cf000188af69"),null);
        System.out.println(result);
    }

    @Test
    public void exportProvinceMappingData() {
        Result<Void> result = systemFieldService.exportSystemFieldMapping("84801",
                1000,
                "780777150699143168",
                ErpFieldTypeEnum.province, null,null);
        System.out.println(result);
    }

    @Test
    public void exportCategoryMappingData() {
        Result<Void> result = systemFieldService.exportSystemFieldMapping("84801",
                1000,
                "780777150699143168",
                ErpFieldTypeEnum.category, null,null);
        System.out.println(result);
    }

    @Test
    public void batchDeleteFieldMapping() {
        Result<Long> result = systemFieldService.batchDeleteFieldMapping("84801",
                1000,
                "780777150699143168",
                ErpFieldTypeEnum.province, null,null);
        System.out.println(result);
    }
}
