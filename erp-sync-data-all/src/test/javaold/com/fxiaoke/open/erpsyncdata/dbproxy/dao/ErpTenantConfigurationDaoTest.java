package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/12
 */
@Ignore
public class ErpTenantConfigurationDaoTest extends BaseDbTest {
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private IdGenerator idGenerator;

    @Test
    public void insertConfig() {
        ErpTenantConfigurationEntity entity = new ErpTenantConfigurationEntity();
        entity.setId(idGenerator.get());
        entity.setTenantId("81138");
        entity.setDataCenterId("694414447636742144");
        entity.setChannel("ERP_K3CLOUD");
        entity.setType("ListFilter_BD_MATERIAL.BillHead");
        FilterData filter = FilterData.builder().fieldApiName("FDescription").operate("ISN").fieldValue(Collections.singletonList("0")).build();
        String filters = JacksonUtil.toJson(Collections.singletonList(filter));
        entity.setConfiguration(filters);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        int insert = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81138")).insert(entity);
        System.out.println(insert);
    }
}