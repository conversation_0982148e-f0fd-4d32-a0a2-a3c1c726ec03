package com.fxiaoke.open.erpsyncdata.main;

import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import io.vavr.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;

import java.util.function.Consumer;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-10-20
 */
@Ignore
@Slf4j
public class MainTest extends BaseTest {

    private static class StatConsumer implements Consumer<Tuple3<String, String, Object>> {
        private final FileWriter fileWriter = new FileWriter("C:\\Users\\<USER>\\Desktop\\stat.txt");

        @Override
        public void accept(Tuple3<String, String, Object> tuple3) {
            String param = JSON.toJSONString(tuple3._3, SerializerFeature.WriteMapNullValue);
            String sql = tuple3._2;
            sql = StrUtil.removeAllLineBreaks(sql);
            String tenantId = tuple3._1;
            log.info("stat-{},{},{}", tenantId, sql, param);
            fileWriter.append(StrFormatter.format("{},{},{},{}\n", TraceUtil.get(), tenantId, sql, param));
        }
    }

}

