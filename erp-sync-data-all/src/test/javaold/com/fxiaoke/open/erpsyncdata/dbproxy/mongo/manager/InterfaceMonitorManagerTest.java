package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileReader;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.BaseDbTest;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.SyncTrace;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/22
 */
@Ignore
@Slf4j
public class InterfaceMonitorManagerTest extends BaseDbTest {
    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;

    @Test
    public void testUpsert() {
//        String filePath = "D:/visiual/data.json";  // 请替换为你的txt文件的实际路径
//        Result<InterfaceMonitorData> byId = interfaceMonitorDataDao.getById("88521", "666c22c54c42503f902b6e17");
//
//        // 使用Hutool的FileReader类读取文件内容
//        FileReader fileReader = new FileReader(FileUtil.file(filePath));
//
//        // 将文件内容存储到字符串中
//        String content = fileReader.readString();
//        Map result= JSONObject.parseObject(content,Map.class);
//
//        // 打印读取的字符串内容
//        System.out.println("文件内容为：\n" + content);
//        byId.getData().setResult(result.get("result").toString());
//        byId.getData().setArg(result.get("arg").toString());
//        interfaceMonitorDataDao.batchUpsertInterfaceMonitorData("88521",Lists.newArrayList(byId.getData()));
//        Result<InterfaceMonitorData> test001 = saveAndGet();
//        System.out.println(test001);
    }

    private Result<InterfaceMonitorData> saveAndGet() {
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(0L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        List<List<FilterData>> filter = Lists.newArrayList();
        filter.add(Lists.newArrayList(FilterData.builder().fieldApiName("fapi").operate("op").fieldValue(Collections.singletonList("fval")).build()));
        timeFilterArg.setFilters(filter);
        String syncDataId = "test1103";
        String tenantId = "test001";
        SyncTrace.set(SyncTrace.SyncInfo.builder().syncDataId(syncDataId).build());
//        interfaceMonitorManager.saveErpInterfaceMonitor(tenantId,"001","testObj", ErpObjInterfaceUrlEnum.queryMasterBatch.name(),"arg","result",1,0L,1L,"","trace001",1L,timeFilterArg);
        Result<InterfaceMonitorData> test001 = chInterfaceMonitorManager.getBySyncDataId(tenantId, syncDataId,System.currentTimeMillis(),System.currentTimeMillis());
        return test001;
    }
}