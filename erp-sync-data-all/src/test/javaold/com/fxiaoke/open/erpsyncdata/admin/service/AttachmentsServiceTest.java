package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AttachmentsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.AttachmentsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AttachmentsService;

import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class AttachmentsServiceTest extends BaseTest {
    @Autowired
    private AttachmentsService attachmentsService;

    @Test
    public void testDownAndUploadAttachments() {
        AttachmentsArg attachmentsArg=new AttachmentsArg();
        attachmentsArg.setTenantId("88521");
        Result<AttachmentsResult> result=attachmentsService.downAndUploadAttachments(attachmentsArg);
        System.out.println("");
    }
}
