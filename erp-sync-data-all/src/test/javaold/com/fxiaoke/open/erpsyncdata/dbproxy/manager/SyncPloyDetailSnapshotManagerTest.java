package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 21:38 2022/4/26
 * @Desc:
 */
@Ignore
public class SyncPloyDetailSnapshotManagerTest extends BaseTest {
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private SyncDataManager syncDataManager;

    @Test
    public void getEntryBySnapshotId() {
        long now=System.currentTimeMillis();
        SyncPloyDetailSnapshotEntity entryBySnapshotId = syncPloyDetailSnapshotManager.getEntryBySnapshotId("88521","1c063c2aac0a47528339d10641653f0d");
        Map<String, Set<String>> res=syncDataManager.getObjFieldApiName(entryBySnapshotId);
        long ll=System.currentTimeMillis()-now;
        SyncPloyDetailSnapshotEntity entryBySnapshotId1 = syncPloyDetailSnapshotManager.getEntryBySnapshotId("81243","ff962c298202428fa17246b1b9fa67b8");
        long lll=System.currentTimeMillis()-now;
        System.out.println();
    }
}