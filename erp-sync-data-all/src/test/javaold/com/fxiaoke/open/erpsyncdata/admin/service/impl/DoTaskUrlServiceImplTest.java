package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.DoTaskUrlService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 15:02 2021/1/13
 * @Desc:
 */
@Ignore
public class DoTaskUrlServiceImplTest extends BaseTest {
    @Autowired
    private DoTaskUrlService doTaskUrlService;

    @Test
    public void manualExecutePloys() {
        String url=ConfigCenter.MANUAL_EXECUTE_PLOYS_URL;
        Result<String> result = doTaskUrlService.manualExecutePloys( "79675", "person_1eoh57vi6");
        System.out.println("");
    }

    @Test
    public void postTaskUrl() {
    }
}