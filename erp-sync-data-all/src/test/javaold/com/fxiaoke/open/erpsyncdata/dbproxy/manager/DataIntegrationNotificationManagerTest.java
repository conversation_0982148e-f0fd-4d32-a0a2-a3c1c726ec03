package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.model.AlarmRuleListModel;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpAlarmManagementService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Ignore
public class DataIntegrationNotificationManagerTest extends BaseTest {
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;
    @Autowired
    private ErpAlarmManagementService erpAlarmManagementService;

    @Test
    public void getDataListByPage() {
        DataIntegrationNotificationModel model = dataIntegrationNotificationManager.getDataListByPage("88521",
                "643f7322b54ea80001767d86",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                20,
                0);
        System.out.println(model);
    }

    @Test
    public void getAlarmRuleList() {
        Result<AlarmRuleListModel> result = erpAlarmManagementService.getAlarmRuleList("88521",
                "643f7322b54ea80001767d86",20,0,null);
        System.out.println(result);
    }

    @Test
    public void getAlarmRuleDcList() {
        Result<List<DataCenterModel>> result = erpAlarmManagementService.getAlarmRuleDcList("88521", null);
        System.out.println(result);
    }

    @Test
    public void saveAlarmRule() {
        ErpAlarmRuleEntity entity = new ErpAlarmRuleEntity();
        entity.setTenantId("89029");
        entity.setDataCenterId("64a3c3a5199f940001344d1f");
        entity.setAlarmLevel(AlarmLevel.IMPORTANT);
        entity.setAlarmRuleName("test");
        entity.setAlarmRuleType(AlarmRuleType.CUSTOM);
        entity.setAlarmType(AlarmType.POLLING_ERP_API_EXCEPTION);
        entity.setPloyDetailIds("64a3c3ac199f9400013473a0");
        entity.setThreshold(50);
        entity.setUserIds("1034");
        Result<Void> result = erpAlarmManagementService.saveAlarmRule(entity);
        System.out.println(result);
    }

    @Test
    public void deleteByPloyDetailId() {
        long result = dataIntegrationNotificationManager.deleteByPloyDetailId("81243","a40c8bc679bb445d8b5dfa2879ddfc6f");
        System.out.println(result);
    }

    @Test
    public void updateAlarmStatus() {
        //insert();
        long result = dataIntegrationNotificationManager.updateAlarmStatus("81243",
                "a40c8bc679bb445d8b5dfa2879ddfc6f",
                AlarmType.INTEGRATION_STREAM_BREAK,
                true);
        System.out.println(result);
    }

    @Test
    public void insert() {
        deleteByPloyDetailId();
        dataIntegrationNotificationManager.insert("81243",
                "64feb0e7a8fadb0001cbaf6f",
                Lists.newArrayList("10e860b3be0e4261898c5b7ec8fd7237"),
                AlarmRuleType.GENERAL,
                "通知告警",
                AlarmType.POLLING_ERP_API_EXCEPTION,
                AlarmLevel.URGENT,
                "test 1",
                Lists.newArrayList(1000),null);
        dataIntegrationNotificationManager.insert("81243",
                "64feb0e7a8fadb0001cbaf6f",
                Lists.newArrayList("10e860b3be0e4261898c5b7ec8fd7237"),
                AlarmRuleType.GENERAL,
                "通知告警",
                AlarmType.POLLING_ERP_API_EXCEPTION,
                AlarmLevel.URGENT,
                "test 1.1",
                Lists.newArrayList(1000),null);
        dataIntegrationNotificationManager.insert("81243",
                "64feb0e7a8fadb0001cbaf6f",
                Lists.newArrayList("a40c8bc679bb445d8b5dfa2879ddfc6f"),
                AlarmRuleType.GENERAL,
                "通知告警",
                AlarmType.INTEGRATION_STREAM_BREAK,
                AlarmLevel.URGENT,
                "test 2",
                Lists.newArrayList(1000),null);
        System.out.println("OK");
    }
}
