package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdListArg;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpTempDataMonitorService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpHistoryTaskNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpTempDataMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpTempDataMonitorSimpleArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:22 2021/8/19
 * @Desc:
 */
@Ignore
public class ErpTempDataMonitorServiceImplTest extends BaseTest {
    @Autowired
    private ErpTempDataMonitorService erpTempDataMonitorService;

    @Test
    public void queryTempDataMonitorList() {
        QueryErpTempDataMonitorArg arg = new QueryErpTempDataMonitorArg();
        arg.setErpFakeObjectApiName("SAL_SaleOrder.BillHead");
        arg.setErpRealObjectApiName("SAL_SaleOrder");
        arg.setPloyDetailId("936dbf22e1294752a80a4e272df9b916");
        arg.setTempDataSyncStatus(10030);
//        arg.setIdOrNum("104205");
//        arg.setSearchType(SearchTypeEnum.DATA_ID);
        Result<QueryResult<List<ErpTempDataMonitorResult>>> queryResultResult = erpTempDataMonitorService.queryTempDataMonitorList("84801","780777150699143168", 13142, arg,null);
        System.out.println("");
    }

    @Test
    public void queryAllTempDataMonitorList() {
        QueryErpTempDataMonitorSimpleArg arg=new QueryErpTempDataMonitorSimpleArg();

        arg.setObjectApiName("SalesOrderObj");
        Result<QueryResult<List<ErpTempDataMonitorSimpleMsgResult>>> queryResultResult = erpTempDataMonitorService.queryAllTempDataMonitorList("81961", "650085855625084928", 1111, arg);

        System.out.println("");
    }

    @Test
    public void removeTempDataMonitorByIdList() {
        IdListArg arg=new IdListArg();
        arg.setIds(Lists.newArrayList("61722175cb696470547ad623"));
        arg.setPloyDetailId("");
        Result<Long> longResult = erpTempDataMonitorService.removeTempDataMonitorByIdList("81243", "", 1000, arg);
        System.out.println("");
    }

    @Test
    public void queryErpTrueAndFakeApiName() {
        IdListArg arg=new IdListArg();
        arg.setIds(Lists.newArrayList("61722175cb696470547ad623"));
        Result<ErpRealAndFakeApiNameResult> erpRealAndFakeApiNameResultResult = erpTempDataMonitorService.queryErpTrueAndFakeApiName("81961", "766345561596624896", "62fd221da9b34833ae28915cce003431");
        System.out.println("");
    }

    @Test
    public void queryAllHistoryTask() {
        QueryErpHistoryTaskNameArg arg=new QueryErpHistoryTaskNameArg();
        arg.setErpFakeObjectApiName("BD_MATERIAL.BillHead");
        arg.setDataCenterId("64a3c3a5199f940001344d1f");
        arg.setErpRealObjectApiName("BD_MATERIAL");
        Result<List<ErpHistoryTaskNameResult>> listResult = erpTempDataMonitorService.queryAllHistoryTask("89029", "64a3c3a5199f940001344d1f", 2000, arg,null);
        System.out.println("");
    }

    @Test
    public void downloadTempDataMonitor() {
    }
}