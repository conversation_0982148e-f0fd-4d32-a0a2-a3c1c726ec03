package com.fxiaoke.PureTest;

import com.fxiaoke.common.PasswordUtil;
import com.google.common.net.UrlEscapers;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MonoPwdUtils {
    private static final Pattern MONGO_URI = Pattern.compile("mongodb://((.+):(.*)@)");
    private static String decodePassword(final String servers) {
        String uri = servers;
        Matcher m = MONGO_URI.matcher(servers);
        if (m.find()) {
            try {
                String pwd = UrlEscapers.urlFormParameterEscaper().escape(PasswordUtil.decode(m.group(3)));
                uri = servers.substring(0, m.end(2) + 1) + pwd + servers.substring(m.end(1) - 1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return uri;
    }

    public static void main(String[] args) {
        String pwd = PasswordUtil.decode("923EC510DAAA8797ADE301A12BAAA3C5814A6BFBF856AE11A345461908928C71");
        System.out.println(pwd);
    }
}
