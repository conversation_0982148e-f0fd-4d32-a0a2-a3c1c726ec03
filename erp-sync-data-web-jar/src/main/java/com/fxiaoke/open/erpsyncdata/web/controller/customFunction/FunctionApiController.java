package com.fxiaoke.open.erpsyncdata.web.controller.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.annotation.CustomFunctionRateLimit;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import com.fxiaoke.open.erpsyncdata.web.factory.CustomFunctionFactory;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.api.FuncApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 自定义函数 API统一入口
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Api(tags = "自定义函数api统一接口")
@RestController()
@RequestMapping("inner/erp/syncdata/func/api")
public class FunctionApiController {
    @Autowired
    private CustomFunctionFactory customFunctionFactory;

    @ApiOperation(value = "自定义函数api统一入口")
    @RequestMapping(value = "/{type}", method = RequestMethod.POST)
    @CustomFunctionRateLimit
    public Result<?> executeByType(@ControllerTenantIDArg @RequestHeader(value = "x-fs-ei") Integer ei,
                                   @RequestHeader(value = "x-fs-locale", required = false) String lang,
                                   @PathVariable String type,
                                   HttpServletRequest request) {
        if (ei == null) {
            log.info("FunctionApiController executeByType ei={}", ei);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String tenantId = String.valueOf(ei);
        TraceUtil.setLocale(lang);
        TraceUtil.setEi(tenantId);
        FuncApiService funcApiService = customFunctionFactory.getFuncApiService(type);
        if (funcApiService == null) {
            return Result.newError(I18NStringEnum.s26, type);
        }
        return funcApiService.executeByRequest(tenantId,request);
    }
}
