package com.fxiaoke.open.erpsyncdata.web.controller.open;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.data.bizlog.PushHeaderLog;
import com.fxiaoke.open.erpsyncdata.common.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.common.util.ServletUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushIdentifyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushIdentifyEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerFsAuthArg;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import com.fxiaoke.ps.ProtostuffUtil;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.fxiaoke.open.erpsyncdata.common.util.ServletUtil.getHeaderIgnoreCase;

/**
 * 推送接口
 */
@Slf4j
@RestController("erpObjDataPushController")
@RequestMapping("erp/syncdata/open")
public class ErpObjDataPushController {

    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpPushIdentifyDao erpPushIdentifyDao;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * <p>第三方主动推送数据到平台。数据格式有第三方设置。给到平台，通过groovy脚本解析</p>
     *
     * @param request
     * @param headers
     * @param token
     * @param tenantId
     * @param objectApiName
     * @param version
     * @param fsAuth
     * @param toTempData 是否直接进入临时库（减少一次轮询，降低漏数据概率）
     * <AUTHOR>
     * @return
     * @throws UnsupportedEncodingException
     */
    @ControllerMethodProxy()
    @RequestMapping(value = {"/objdata/push"}, method = RequestMethod.POST)
    public Result<?> erpPushDataToDss(HttpServletRequest request, @RequestHeader HttpHeaders headers,
                                         @RequestParam(value = "token", required = false) String token,
                                         @RequestParam(value = "tenantId", required = false) @ControllerTenantIDArg String tenantId,
                                         @RequestHeader(value = "tenantId", required = false) @ControllerTenantIDArg String headerTenantId,
                                         @RequestParam(value = "objectApiName", required = false) String objectApiName,
                                         @RequestParam(value = "version", required = false) String version,
                                         @RequestParam(value = "dataCenterId", required = false) String dataCenterId,
                                         @RequestParam(value = "dataCenterNumber", required = false) String dataCenterNumber,
                                         @RequestParam(value = "operationType", required = false) String operationType,
                                         @ControllerFsAuthArg @RequestParam(value = "fsAuth", required = false) String fsAuth,
                                         @RequestParam(value = "toTempData",defaultValue = "true") boolean toTempData,
                                         @RequestParam(value = "directSync",required = false,defaultValue = "false") boolean directSync,
                                         @RequestParam(value = "destObjectApiName", required = false) String destObjectApiName,
                                         @RequestParam(value = "postId", required = false) String postId) throws UnsupportedEncodingException {
        if (headers.containsKey("directSync")){
            directSync = Boolean.parseBoolean(headers.getFirst("directSync"));
        }
        destObjectApiName = destObjectApiName == null ? headers.getFirst("destObjectApiName") : destObjectApiName;
        postId = postId == null ? headers.getFirst("postId") : postId;
        Result<?> result = doPush(request,headers,token,tenantId,headerTenantId,objectApiName,version,dataCenterId,dataCenterNumber,operationType,
                fsAuth,toTempData,directSync,destObjectApiName);
        if(StringUtils.isNotBlank(postId)){
            result.setTraceMsg(postId+"-"+result.getTraceMsg());
        }
        return result;
    }

    @ControllerMethodProxy()
    @RequestMapping(value = {"/objdata/asyncpush"}, method = RequestMethod.POST)
    public Result<?> erpAsyncPushDataToDss(HttpServletRequest request, @RequestHeader HttpHeaders headers,
                                                      @RequestParam(value = "token", required = false) String token,
                                                      @RequestParam(value = "tenantId", required = false) @ControllerTenantIDArg String tenantId,
                                                      @RequestHeader(value = "tenantId", required = false) @ControllerTenantIDArg String headerTenantId,
                                                      @RequestParam(value = "objectApiName", required = false) String objectApiName,
                                                      @RequestParam(value = "version", required = false) String version,
                                                      @RequestParam(value = "dataCenterId", required = false) String dataCenterId,
                                                      @RequestParam(value = "dataCenterNumber", required = false) String dataCenterNumber,
                                                      @RequestParam(value = "operationType", required = false) String operationType,
                                                      @ControllerFsAuthArg @RequestParam(value = "fsAuth", required = false) String fsAuth,
                                                      @RequestParam(value = "toTempData",defaultValue = "true") boolean toTempData) throws UnsupportedEncodingException {

        return doPush(request,headers,token,tenantId,headerTenantId,objectApiName,version,dataCenterId,dataCenterNumber,operationType,
                fsAuth,toTempData,false,null);
    }

    private Result<?> doPush(HttpServletRequest request,
                                             HttpHeaders headers,
                                             String token,
                                             String tenantId,
                                             String headerTenantId,
                                             String objectApiName,
                                             String version,
                                             String dataCenterId,
                                             String dataCenterNumber,
                                             String operationType,
                                             String fsAuth,
                                             boolean toTempData,
                                             boolean directSync,
                                             String destObjectApiName) throws UnsupportedEncodingException {
        log.info("erpPushDataToDss request={},headers={},tenantId={},objectApiName={},version={},fsAuth={}", request, headers, tenantId,
                objectApiName, version,fsAuth);

        String locale = request.getHeader("x-fs-locale");
        if (StringUtils.isEmpty(locale)) {
            if (Objects.nonNull(request.getHeader("Accept-Language"))) {
                locale = request.getLocale().toLanguageTag();
            }
        }
        //设置locale
        TraceUtil.setLocale(locale);
        String xRealIp = request.getHeader("x-real-ip");
        tenantId = tenantId == null ? headerTenantId : tenantId;
        token = token == null ? headers.getFirst("token") : token;
        objectApiName = objectApiName == null ? headers.getFirst("objectApiName") : objectApiName;
        version = version == null ? headers.getFirst("version") : version;
        dataCenterId = dataCenterId == null ? headers.getFirst("dataCenterId") : dataCenterId;
        dataCenterNumber = dataCenterNumber == null ? headers.getFirst("dataCenterNumber") : dataCenterNumber;
        operationType = operationType == null ? headers.getFirst("operationType") : operationType;
        String id = headers.getFirst("id");
        String contentLength=headers.getFirst("content-length");
        // 兼容某些外部系统填写回调地址时传多个URL参数有问题的情况
        // 回调地址格式 https:xxx/objdata/push?fsAuth=${JSON字符串的Base64编码}
        if(StringUtils.isNotBlank(fsAuth)){
            //如果长时间没有该日志打印，则这段代码要删掉
            log.info("trace fsAuth tenantId:{},objectApiName:{} ",tenantId,objectApiName);

            String rawStr = new String(Base64.getDecoder().decode(fsAuth),"utf-8");
            Map<String,String> fsAuthMap =  GsonUtil.fromJson(rawStr,Map.class);
            if(StringUtils.isNotBlank(fsAuthMap.get("token"))){
                token = fsAuthMap.get("token");
            }
            if(StringUtils.isNotBlank(fsAuthMap.get("tenantId"))){
                tenantId = fsAuthMap.get("tenantId") ;
            }
            if(StringUtils.isNotBlank(fsAuthMap.get("objectApiName"))){
                objectApiName = fsAuthMap.get("objectApiName");
            }
            if(StringUtils.isNotBlank(fsAuthMap.get("version"))){
                version = fsAuthMap.get("version");
            }
        }

        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(objectApiName)
                || StringUtils.isEmpty(version)) {
            return Result.newError(ResultCodeEnum.VALID_ERROR);
        }

        TraceUtil.initTraceWithFormat(tenantId);
        TraceUtil.addChildTrace("push");

        Result<Void> checkIpResult = erpObjDataPushManager.checkIp(tenantId, request);
        if (!checkIpResult.isSuccess()) {
            return checkIpResult;
        }

        // header包含idEncode且为1时，对header的id进行URL解码
        String idEncode = headers.getFirst("idEncode");
        if(StringUtils.isNotBlank(idEncode) && CommonConstant.ONE.equals(idEncode) && StringUtils.isNotBlank(id)){
            //如果长时间没有该日志打印，则这段代码要删掉
            log.info("trace idEncode tenantId:{},objectApiName:{} ",tenantId,objectApiName);
            id = URLDecoder.decode(id,"UTF-8");
        }

//        没有传数据中心id，根据数据中心编码获取数据中心id
        if (StringUtils.isBlank(dataCenterId) && StringUtils.isNumeric(dataCenterNumber)) {
            final Integer number = Integer.valueOf(dataCenterNumber);
            List < ErpConnectInfoEntity > entities = erpConnectInfoManager.listByTenantId(tenantId);
            dataCenterId = entities.stream()
                    .filter(info -> info.getNumber().equals(number))
                    .findFirst()
                    .map(ErpConnectInfoEntity::getId)
                    .orElseGet(() -> entities.stream().findFirst().map(ErpConnectInfoEntity::getId).orElse(null));
        }

        if(StringUtils.isBlank(dataCenterId)){
            List<ErpConnectInfoEntity>  entities= erpConnectInfoManager.listByTenantId(tenantId);
            dataCenterId=entities.get(0).getId();//默认第一个数据中心
        }
        Result<Void> checkAuthRes = erpObjDataPushManager.checkAuth(tenantId, dataCenterId, headers, token, version,ServletUtil.isTlsV10OrV11(request));
        try {
            PushHeaderLog dumpLog = PushHeaderLog.builder()
                    .xrealIp(xRealIp).tenantId(tenantId).appName("erpdss").dataCenterId(dataCenterId).dataCenterNumber(dataCenterNumber)
                    .version(version).tlsVersion(getHeaderIgnoreCase(request, "X-TLS-Version")).objectApiName(objectApiName)
                    .objectId(id).contentLength(Long.valueOf(contentLength)).stamp(System.currentTimeMillis())
                    .build();
            BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
        }catch (Exception e){log.warn("send biz log exception,", e);}
        if (!checkAuthRes.isSuccess()){
            return Result.copy(checkAuthRes);
        }

        String data;
        try {
            //读http请求要控制大小，避免OOM
            long limitSize=ConfigCenter.LIST_CONTENT_LENGTH_LIMIT;
            if(directSync || StringUtils.isNotEmpty(id)) {
                limitSize=ConfigCenter.CONTENT_LENGTH_LIMIT;
            }
            if(StringUtils.isNotEmpty(contentLength)){
                if(Long.valueOf(contentLength)>limitSize){
                    return Result.newErrorExtra(ResultCodeEnum.CONTROL_ERP_PUSH_DATA_LENGTH, String.valueOf(ConfigCenter.CONTENT_LENGTH_LIMIT));
                }
            }

            Result<String> dataResult = HttpRspLimitLenUtil.convertStreamToString(request.getInputStream(),limitSize);
            if(!dataResult.isSuccess()){
                return Result.copy(dataResult);
            }
            data=dataResult.getData();
        } catch (Exception e) {
            log.warn("convertStreamToString exception", e);
            return Result.newErrorExtra(ResultCodeEnum.SYSTEM_ERROR_EXTRA, e.getMessage());
        }

        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
        if (connectInfoEntity==null){
            return Result.newSystemError(I18NStringEnum.s2027);
        }
/**
 * 暂时不能通过ConnectParams里面的pushDataApiNames判断是否启用了推送，因为同时启用推送和轮询，pushDataApiNames不存在推送对象apiName
 */
//        if (StringUtils.isEmpty(connectInfoEntity.getConnectParams())||!connectInfoEntity.getConnectParams().contains(objectApiName)){
//            result.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
//            result.setErrMsg(String.format("当前账套%s对象未启用推送功能：%s",dataCenterId,objectApiName));
//            return result;
//        }

        //根据这个代码推测，objectApiName和erpVisualApiName应该是不能为空的。应该吧
        String erpVisualApiName = erpObjDataPushManager.validateErpObjApiName(tenantId, objectApiName,dataCenterId);
        if (erpVisualApiName == null) {
            return Result.newSystemError(I18NStringEnum.s1163, dataCenterId, objectApiName);
        }
        String logId = syncLogManager.initLogId(tenantId, objectApiName);
        TraceUtil.initTrace(logId);
        Result<?> objectResult = erpObjDataPushManager.stdErpPushDataToDss(tenantId, dataCenterId, objectApiName, operationType, data,(headers), id, directSync,destObjectApiName);
        TraceUtil.removeTrace();
        LogIdUtil.clear();
        return objectResult;

    }

    /**
     * 钉钉专用的接口
     * @param request
     * @param headers
     * @param token
     * @param tenantId
     * @param objectApiName
     * @param version
     * @param toTempData
     * @return
     * @throws UnsupportedEncodingException
     */
    @ControllerMethodProxy()
    @RequestMapping(value = {"/objdata/push2"}, method = RequestMethod.POST)
    public Result<Void> erpPushDataToDss2(HttpServletRequest request,
                                          @RequestHeader HttpHeaders headers,
                                          @RequestParam(value = "token", required = false) String token,
                                          @RequestParam(value = "tenantId") String tenantId,
                                          @RequestParam(value = "objectApiName") String objectApiName,
                                          @RequestParam(value = "version", required = false) String version,
                                          @RequestParam(value = "toTempData",defaultValue = "true") boolean toTempData,
                                          @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) throws UnsupportedEncodingException {
        String operationType = headers.getFirst("operationType");
        String id = headers.getFirst("id");
        log.info("ErpObjDataPushController.erpPushDataToDss2,headers={}",JSONObject.toJSONString(headers));
        //直接进入临时库，不进入pushData表
        if (headers.containsKey("toTempData")){
            toTempData = Boolean.parseBoolean(headers.getFirst("toTempData"));
        }

        // header包含idEncode且为1时，对header的id进行URL解码
        String idEncode = headers.getFirst("idEncode");
        if(StringUtils.isNotBlank(idEncode) && CommonConstant.ONE.equals(idEncode) && StringUtils.isNotBlank(id)){
            id = URLDecoder.decode(id,"UTF-8");
        }

        version = StringUtils.isEmpty(version) ? "v1" : version;
        if (Objects.isNull(lang)) {
            lang = Objects.nonNull(request.getHeader("Accept-Language")) ? request.getLocale().toLanguageTag() : null;
        }

        if(StringUtils.isEmpty(token)) {
            ErpPushIdentifyEntity entity = erpPushIdentifyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTokenByTenantId(tenantId);
            if(entity==null) {
                try {
                    token = erpObjDataPushManager.generatorTokenKey(tenantId, version,lang);
                } catch (Exception e) {
                    return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                }
            } else {
                token = entity.getToken();
            }
        }

        TraceUtil.initTraceWithFormat(tenantId);
        TraceUtil.addChildTrace("push2"); // 黑人问号？

        List<ErpConnectInfoEntity>  entities= erpConnectInfoManager.listByTenantId(tenantId);
        String dataCenterId=entities.get(0).getId();//默认第一个数据中心

        Result result = new Result();
        Result<Void> checkAuthRes = erpObjDataPushManager.checkAuth(tenantId, dataCenterId, headers, token, version,ServletUtil.isTlsV10OrV11(request));
        if (!checkAuthRes.isSuccess()) {
            return Result.copy(checkAuthRes);
        }

        String data;
        try {
            Result<String> dataResult = HttpRspLimitLenUtil.convertStreamToString(request.getInputStream(),ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
            if(!dataResult.isSuccess()){
                return Result.copy(dataResult);
            }
            data=dataResult.getData();
        } catch (Exception e) {
            result.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
            result.setErrMsg(e.getMessage());
            return result;
        }

        String erpVisualApiName = erpObjDataPushManager.validateErpObjApiName(tenantId, objectApiName,dataCenterId);
        if (erpVisualApiName == null) {
            result.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
            result.setErrMsg(i18NStringManager.get(I18NStringEnum.s2026,lang,tenantId));
            return result;
        }
        String logId = syncLogManager.initLogId(tenantId, objectApiName);
        TraceUtil.initTrace(logId);
        Result<?> objectResult = erpObjDataPushManager.stdErpPushDataToDss(tenantId,
                dataCenterId, objectApiName,
                operationType,
                data,
                (headers),
                id,
                false,
                null);
        TraceUtil.removeTrace();
        LogIdUtil.clear();
        return Result.copy(objectResult);
    }
}
