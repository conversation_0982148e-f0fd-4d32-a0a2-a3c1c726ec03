package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.admin.model.AplTemplate;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.AplTemplateServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * apl模板
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("erp/syncdata/superadmin")
@Slf4j
public class SuperAdminAplTemplateController {
    @Autowired
    private AplTemplateServiceImpl aplTemplateService;

    @GetMapping(value = "aplTemplate/list")
    public Result<List<AplTemplate.Info>> listAll() {
        //获取模板基本信息
        Result<List<AplTemplate.Info>> listResult = aplTemplateService.listAll();
        return listResult;
    }


    @GetMapping(value = "aplTemplate/get")
    public Result<AplTemplate.Info> get(@RequestParam String aplClassName) {
        //获取模板基本信息
        Result<AplTemplate.Info> listResult = aplTemplateService.getInfo(new AplTemplate.AplClassNameArg(aplClassName));
        return listResult;
    }


    @PostMapping(value = "aplTemplate/upsert")
    public Result<Void> upsert(@RequestBody AplTemplate.UpsertArg arg) {
        return aplTemplateService.upsert(arg);
    }


    @PostMapping(value = "aplTemplate/delete")
    public Result<Void> delete(@RequestBody AplTemplate.AplClassNameArg arg) {
        return aplTemplateService.delete(arg.getAplClassName());
    }


    @PostMapping("aplTemplate/saveOrders")
    public Result<Void> saveOrders(@RequestBody Dict dict) {
        List<String> aplClassNames = StrUtil.split(dict.getStr("aplClassNames"), ",");
        return aplTemplateService.saveOrders(aplClassNames);
    }
}
