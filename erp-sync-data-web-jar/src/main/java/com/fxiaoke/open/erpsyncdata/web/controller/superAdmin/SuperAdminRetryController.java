package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.result.PollingTempRecordResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ReTrySendMqResult;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.AdminRetryDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.arg.RetryConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RetryDataEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.PollingTempFailDao;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AdminRetryConfigArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/5/30 10:53
 * @desc
 */
@RestController
@Slf4j
@RequestMapping("erp/syncdata/superadmin/retryData")
public class SuperAdminRetryController extends BaseController {
    @Autowired
    private AdminRetryDataService adminRetryDataService;

    @Autowired
    private PollingTempFailDao pollingTempFailDao;

    /**
     * 数据列表
     */
    @PostMapping("/listData")
    public Result<Amis.Crud<ReTrySendMqResult>> listData(@RequestBody AdminRetryConfigArg adminRetryConfigArg) {
        RetryConfig retryConfig=new RetryConfig();
        retryConfig.setTenantId(adminRetryConfigArg.getTenantId());
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getSelect())){
            List<String> timeUtils = Splitter.on(",").splitToList(adminRetryConfigArg.getSelect());
            retryConfig.setStartTime(Long.valueOf(timeUtils.get(0)));
            retryConfig.setEndTime(Long.valueOf(timeUtils.get(1)));
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getSelectStatus())){
            List<Integer> selectValues= Lists.newArrayList();
            for (Map<String, String> status : adminRetryConfigArg.getSelectStatus()) {
                String value = status.get("value");
                selectValues.add(Integer.valueOf(value));
            }
            retryConfig.setStatus(selectValues);
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getRetryDataEnum())){
            retryConfig.setRetryDataEnum(adminRetryConfigArg.getRetryDataEnum());
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getDataIds())){
            retryConfig.setDataIds(adminRetryConfigArg.getDataIds());
        }
        retryConfig.setLimit(100);
        retryConfig.setOffset(0);
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getPage())){
            int offset=(adminRetryConfigArg.getPage()-1)*100;
            retryConfig.setOffset(offset);
        }
        Result<Amis.Crud<ReTrySendMqResult>> crudResult = adminRetryDataService.queryDataByType(retryConfig.getTenantId(),
                retryConfig.getRetryDataEnum(), retryConfig.getStatus(), retryConfig.getStartTime(), retryConfig.getEndTime(), retryConfig.getLimit(), retryConfig.getOffset());

        return crudResult;
    }

    @PostMapping("/returnType")
    public Result<Dict> returnType() {
        List<Dict> options = new ArrayList<>();
        for (RetryDataEnum value : RetryDataEnum.values()) {
            options.add(Dict.of("label", value.name(), "value", value.name()));
        }
        return Result.newSuccess(Dict.of("options", options));
    }

    /**
     * 批量重试
     */
    @PostMapping("/retryConditionData")
    public  Result<Integer> retryData(@RequestBody AdminRetryConfigArg adminRetryConfigArg) {
        RetryConfig retryConfig=new RetryConfig();
        retryConfig.setTenantId(adminRetryConfigArg.getTenantId());
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getSelect())){
            List<String> timeUtils = Splitter.on(",").splitToList(adminRetryConfigArg.getSelect());
            retryConfig.setStartTime(Long.valueOf(timeUtils.get(0)));
            retryConfig.setEndTime(Long.valueOf(timeUtils.get(1)));
        }
        try {
            if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getSelectStatus())){
                List<Integer> selectValues= Lists.newArrayList();
                for (Map<String, String> status : adminRetryConfigArg.getSelectStatus()) {
                    String value = status.get("value");
                    selectValues.add(Integer.valueOf(value));
                }
                retryConfig.setStatus(selectValues);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getRetryDataEnum())){
            retryConfig.setRetryDataEnum(adminRetryConfigArg.getRetryDataEnum());
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getIds())){
            List<String> dataIds=Splitter.on(",").splitToList(adminRetryConfigArg.getIds());
            retryConfig.setDataIds(dataIds);
        }
        Result<Integer> dataResult = adminRetryDataService.retryDataByType(retryConfig);
        return dataResult;
    }

    @PostMapping("/returnTempData")
    public Result<Amis.Crud<PollingTempRecordResult>> returnTempData(@RequestBody AdminRetryConfigArg adminRetryConfigArg) {
        RetryConfig retryConfig=new RetryConfig();
        if(StringUtils.isNotBlank(adminRetryConfigArg.getTenantId())){
            retryConfig.setTenantId(adminRetryConfigArg.getTenantId());
        }
        if(StringUtils.isNotBlank(adminRetryConfigArg.getSelect())){
            List<String> timeUtils = Splitter.on(",").splitToList(adminRetryConfigArg.getSelect());
            retryConfig.setStartTime(Long.valueOf(timeUtils.get(0)));
            retryConfig.setEndTime(Long.valueOf(timeUtils.get(1)));
        }
        try {
            if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getSelectStatus())){
                List<Integer> selectValues= Lists.newArrayList();
                for (Map<String, String> status : adminRetryConfigArg.getSelectStatus()) {
                    String value = status.get("value");
                    selectValues.add(Integer.valueOf(value));
                }
                retryConfig.setStatus(selectValues);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getRetryDataEnum())){
            retryConfig.setRetryDataEnum(adminRetryConfigArg.getRetryDataEnum());
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getIds())){
            List<String> dataIds=Splitter.on(",").splitToList(adminRetryConfigArg.getIds());
            retryConfig.setDataIds(dataIds);
        }
        retryConfig.setLimit(100);
        retryConfig.setOffset(0);
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getPage())){
            int offset=(adminRetryConfigArg.getPage()-1)*100;
            retryConfig.setOffset(offset);
        }
//        if(CollectionUtils.isNotEmpty(retryConfig.getDataIds())){
//            List<ObjectId> objectIds = retryConfig.getDataIds().stream().map(ObjectId::new).collect(Collectors.toList());
//            return retryFailDataService.retryDataByIds(objectIds);
//        }
        Result<Amis.Crud<PollingTempRecordResult>> crudResult = adminRetryDataService.queryDataByTempType(retryConfig.getTenantId(), retryConfig.getStatus(), retryConfig.getStartTime(), retryConfig.getEndTime(), retryConfig.getLimit(), retryConfig.getOffset());

        return crudResult;
    }

    @PostMapping("/retryDataById")
    public Result<Amis.Crud<PollingTempRecordResult>> retryDataById(@RequestBody AdminRetryConfigArg adminRetryConfigArg) {
        RetryConfig retryConfig=new RetryConfig();
        retryConfig.setTenantId(adminRetryConfigArg.getTenantId());
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getSelect())){
            List<String> timeUtils = Splitter.on(",").splitToList(adminRetryConfigArg.getSelect());
            retryConfig.setStartTime(Long.valueOf(timeUtils.get(0)));
            retryConfig.setEndTime(Long.valueOf(timeUtils.get(1)));
        }
        try {
            if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getSelectStatus())){
                List<Integer> selectValues= Lists.newArrayList();
                for (Map<String, String> status : adminRetryConfigArg.getSelectStatus()) {
                    String value = status.get("value");
                    selectValues.add(Integer.valueOf(value));
                }
                retryConfig.setStatus(selectValues);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getRetryDataEnum())){
            retryConfig.setRetryDataEnum(adminRetryConfigArg.getRetryDataEnum());
        }
        List<String> ids=Lists.newArrayList();
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getIds())){
            ids=Splitter.on(",").splitToList(adminRetryConfigArg.getIds());
            retryConfig.setDataIds(ids);
        }
        if(ObjectUtils.isNotEmpty(adminRetryConfigArg.getId())){
            if(CollectionUtils.isEmpty(ids)){
                retryConfig.setDataIds(Lists.newArrayList(adminRetryConfigArg.getId()));
            }

        }
//        if(CollectionUtils.isNotEmpty(retryConfig.getDataIds())){
//            List<ObjectId> objectIds = retryConfig.getDataIds().stream().map(ObjectId::new).collect(Collectors.toList());
//            return retryFailDataService.retryDataByIds(objectIds);
//        }
        Result<Amis.Crud<PollingTempRecordResult>> crudResult = adminRetryDataService.retryDataByTempType(retryConfig);

        return crudResult;
    }

}
