package com.fxiaoke.open.erpsyncdata.web.controller.inner;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.TenantConfigurationService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@RestController
@RequestMapping("inner/config")
public class InnerConfigController {
    @Autowired
    private TenantConfigurationService configurationService;

    @ApiOperation(value = "根据参数查询所有配置")
    @RequestMapping(value = "/getAllConfigByKey", method = RequestMethod.POST)
    public Result<Dict> getAllConfigByKey(String tenantId, String dataCenterId) {
        return configurationService.getAllConfigByKey(tenantId, dataCenterId);
    }
}
