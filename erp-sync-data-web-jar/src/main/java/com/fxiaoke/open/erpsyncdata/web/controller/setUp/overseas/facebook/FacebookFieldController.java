package com.fxiaoke.open.erpsyncdata.web.controller.setUp.overseas.facebook;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erp.connertor.facebook.FacebookService;
import com.facishare.open.erp.connertor.sdk.model.dto.FormMetaData;
import com.facishare.open.erp.connertor.sdk.model.dto.IdAndName;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetAllForm;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetAllPage;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetFormMetaData;
import com.fxiaoke.open.erpsyncdata.admin.model.GetFacebookFormByPage;
import com.fxiaoke.open.erpsyncdata.admin.model.GetFacebookPage;
import com.fxiaoke.open.erpsyncdata.admin.model.InitFacebookObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpOverseasProxyDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.setUp.overseas.OverseasFieldController;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/27 10:23:50
 */
@Slf4j
@Api(tags = "facebook对象字段特殊设置相关接口")
@RestController
@RequestMapping("cep/setUp/erpObjectFields/facebook")
public class FacebookFieldController extends OverseasFieldController {

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    @Autowired
    private FacebookService facebookService;

    @ApiOperation(value = "获取Facebook所有主页,用于设置线索的extend字段")
    @PostMapping(value = "/getPage")
    public Result<GetFacebookPage.Result> getPage(@RequestBody(required = false) GetFacebookPage.Arg arg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();

        final ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dataCenterId);

        final GetAllPage.Arg pageArg = ErpOverseasProxyDataManager.initConnectorArg(connectInfo, new GetAllPage.Arg());
        final GetAllPage.Result allPage = facebookService.getAllPage(pageArg);

        return Result.newSuccess(new GetFacebookPage.Result(convertList(allPage.getPages(), com.fxiaoke.open.erpsyncdata.admin.model.IdAndName.class)));
    }

    @ApiOperation(value = "获取Facebook主页所有表格,用于设置线索的extend字段")
    @PostMapping(value = "/getFormsByPage")
    public Result<GetFacebookFormByPage.Result> getFormsByPage(@RequestBody GetFacebookFormByPage.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getPageId())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();

        final ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dataCenterId);

        final GetAllForm.Arg formArg = ErpOverseasProxyDataManager.initConnectorArg(connectInfo, new GetAllForm.Arg(arg.getPageId()));
        final List<IdAndName> forms = facebookService.getAllForm(formArg).getForms();

        return Result.newSuccess(new GetFacebookFormByPage.Result(convertList(forms, com.fxiaoke.open.erpsyncdata.admin.model.IdAndName.class)));
    }

    @ApiOperation(value = "获取Facebook表格元数据,用于初始化线索对象")
    @PostMapping(value = "/initObject")
    public Result<Void> initObject(@RequestBody InitFacebookObject.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getPageId()) || Strings.isNullOrEmpty(arg.getFormId())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        final String objectName = arg.getSplitObjectApiName();

        final ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dataCenterId);

        final GetFormMetaData.Arg formArg = ErpOverseasProxyDataManager.initConnectorArg(connectInfo, new GetFormMetaData.Arg(arg.getPageId(), arg.getFormId()));
        final List<FormMetaData> metaDataList = facebookService.getFormMetaData(formArg).getMetaDataList();
        final String idFieldExtendValue = JSON.toJSONString(ImmutableMap.of("pageId", arg.getPageId(), "formId", arg.getFormId()));
        incrementalInsertErpObjectField(tenantId, dataCenterId, objectName, metaDataList, "id", idFieldExtendValue);

        return Result.newSuccess();
    }

    @Override
    public ErpChannelEnum getChannel() {
        return ErpChannelEnum.ERP_FACEBOOK;
    }

    @Override
    protected ErpFieldTypeEnum convert2ErpFieldTypeEnum(final String type) {
        // CUSTOM, CITY, COMPANY_NAME, COUNTRY, DOB, EMAIL, GENDER, FIRST_NAME, FULL_NAME, JOB_TITLE, LAST_NAME, MARITIAL_STATUS, PHONE, PHONE_OTP, POST_CODE, PROVINCE, RELATIONSHIP_STATUS, STATE, STREET_ADDRESS, ZIP, WORK_EMAIL, MILITARY_STATUS, WORK_PHONE_NUMBER, STORE_LOOKUP, STORE_LOOKUP_WITH_TYPEAHEAD, DATE_TIME, ID_CPF, ID_AR_DNI, ID_CL_RUT, ID_CO_CC, ID_EC_CI, ID_PE_DNI, ID_MX_RFC
        switch (type) {
            case FormMetaData.TYPE_SELECT_ONE:
                return ErpFieldTypeEnum.select_one;
            case "COUNTRY":
                return ErpFieldTypeEnum.country;
            case "CITY":
                return ErpFieldTypeEnum.city;
            case "EMAIL":
                return ErpFieldTypeEnum.email;
            case "PHONE":
            case "WORK_PHONE_NUMBER":
                return ErpFieldTypeEnum.phone_number;
            case "PROVINCE":
                return ErpFieldTypeEnum.province;
            case "DATE_TIME":
                return ErpFieldTypeEnum.date_time;
            default:
                return ErpFieldTypeEnum.text;
        }
    }
}
