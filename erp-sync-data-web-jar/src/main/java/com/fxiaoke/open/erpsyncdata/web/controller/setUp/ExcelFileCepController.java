package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import cn.hutool.core.collection.CollUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ListIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.RealObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.*;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.IntegrationViewResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ConnectInfoService;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.admin.service.impl.FileServiceImpl;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DBFileManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/31
 */
@Slf4j
@Api(tags = "excel文件导出接口")
@RestController()
@RequestMapping("cep/setUp/excelFile")
public class ExcelFileCepController extends AsyncSupportController {
    @Autowired
    private FileServiceImpl fileService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private IntegrationStreamService integrationStreamService;
    @Autowired
    private DBFileManager dbFileManager;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;

    @ApiOperation(value = "获取导入模板")
    @RequestMapping(value = "/getTemplate", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> getTemplate(
            @RequestBody ImportExcelFile.FieldDataMappingArg arg,
            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String ea = getEa();
        arg.setLang(lang);
        return fileService.buildExcelTemplate(ea, arg);
    }

    @ApiOperation(value = "导出数据")
    @RequestMapping(value = "/asyncExportSyncDataMappingData", method = RequestMethod.POST)
    public DeferredResult<Result<ExportSyncDataMapping.Result>> asyncExportSyncDataMappingData(@RequestBody ExportSyncDataMapping.ExportSyncDataMappingArg arg,
                                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) throws IOException {

        String tenantId = getLoginUserTenantId();
        arg.setTenantId(tenantId);
        Integer loginUserId = getLoginUserId();
        arg.setUserId(loginUserId);
        arg.setLang(lang);

        ExportSyncDataMapping.Result timeOutResult = new ExportSyncDataMapping.Result();

        DeferredResult<Result<ExportSyncDataMapping.Result>> result = new DeferredResult<>(ConfigCenter.EXPORT_TIME_OUT, Result.newSuccess(timeOutResult));
        result.onTimeout(() -> {
            //发送企信消息
            SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
            sendTextNoticeArg.setTenantId(tenantId);
            sendTextNoticeArg.setDataCenterId(getDcId());
            sendTextNoticeArg.setPloyDetailId(arg.getPloyDetailId());
            sendTextNoticeArg.setReceivers(Collections.singletonList(arg.getUserId()));
            sendTextNoticeArg.setMsg(i18NStringManager.get(I18NStringEnum.s1089.getI18nKey(),lang,tenantId,I18NStringEnum.s1089.getI18nValue()));
            sendTextNoticeArg.setMsgTitle("");
            notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,
                    AlarmRuleType.OTHER,
                    AlarmRuleType.OTHER.getName(i18NStringManager,lang,tenantId),
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL);
        });
        String dataCenterId = getUserVo().getDataCenterId();

        if (StringUtils.isBlank(dataCenterId)) {
            result.setErrorResult(Result.newError(ResultCodeEnum.NOT_RECEIVE_DCID));
            return result;
        }
        arg.setDataCenterId(dataCenterId);

        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                Result<ExportSyncDataMapping.Result> resultResult = fileService.asyncExportSyncDataMappingData(arg, dataCenterId,lang);
                if (!resultResult.isSuccess()) {
                    result.setErrorResult(resultResult);
                } else {
                    result.setResult(resultResult);
                }
            } catch (Exception e) {
                log.warn("asyncExportSyncDataMappingData get error", e);
            }
        }).run();

        return result;

    }

    @ApiOperation(value = "获取对象数据映射导入模板")
    @RequestMapping(value = "/getObjectDataMappingTemplate", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> getObjectDataMappingTemplate(
            @RequestBody ImportExcelFile.ObjectDataMappingTemplateArg arg,
            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return fileService.buildObjectDataMappingTemplate(tenantId, arg.getPloyDetailId(),lang);
    }

    @ApiOperation(value = "获取对象字段导入模板")
    @RequestMapping(value = "/getObjectFieldTemplate", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> getObjectFieldTemplate(@RequestBody RealObjectApiNameArg arg,
                                                                @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String realObjectApiName = arg.getRealObjectApiName();
        if (StringUtils.isEmpty(realObjectApiName)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return fileService.buildObjectFieldTemplate(tenantId, dataCenterId, realObjectApiName,lang);
    }

    @ApiOperation(value = "获取数据核对导入模板")
    @RequestMapping(value = "/getDataVerificationTemplate", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> getDataVerificationTemplate(@RequestBody CepArg arg,
                                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return fileService.buildDataVerificationTemplate(tenantId, dataCenterId,lang);
    }

    @ApiOperation(value = "获取历史任务按ids同步导入模板")
    @RequestMapping(value = "/getHistoryTaskTypeIdsTemplate", method = RequestMethod.POST)
    public Result<BuildExcelFile.Result> getHistoryTaskTypeIdsTemplate(@RequestBody CepArg arg,
                                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return fileService.buildDataVerificationTemplate(tenantId, dataCenterId,lang);
    }

    @ApiOperation(value = "导出集成流映射")
    @RequestMapping(value = "/exportIntegrationStreamMapping", method = RequestMethod.POST)
    public Result<ExportIntegrationStreamMapping.Result> exportIntegrationStreamMapping(@RequestBody ExportIntegrationStreamMapping.Arg arg,
                                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) throws IOException {

        String tenantId = getLoginUserTenantId();
        final String dcId = getDcId();

        // 获取所有集成流信息
        final ListIntegrationStreamArg listIntegrationStreamArg = new ListIntegrationStreamArg();
        listIntegrationStreamArg.setDcId(dcId);
        Result<QueryResult<List<IntegrationViewResult>>> queryResultResult = integrationStreamService.newQueryIntegrationStreamList(tenantId, listIntegrationStreamArg, false, lang);
        if (!queryResultResult.isSuccess()) {
            return Result.copy(queryResultResult);
        }
        // 需要校验同名的集成流,sheet名称不能重复
        List<IntegrationViewResult> integrationViewResultList = queryResultResult.getData().getDataList();
        // 重名的集成流在后面加(n) 没有名称的给个默认名称
        Map<String, Integer> integrationStreamNameCountMap = new HashMap<>();
        for (IntegrationViewResult integrationViewResult : integrationViewResultList) {
            resetName(tenantId, integrationStreamNameCountMap, integrationViewResult);
        }

        final List<ErpIntegrationStreamExcelVoSheet> excelSheetArgs = getExcelSheetList(tenantId, dcId, integrationViewResultList, lang);

        // 组装为导出数据ExcelSheetArg
        // 数据流向图
        final ExcelSheetArg firstSheetArg = new ExcelSheetArg();
        firstSheetArg.setSheetName(i18NStringManager.get(I18NStringEnum.s186, lang, tenantId));
        firstSheetArg.setDataList(ExportIntegrationStreamMapping.convert2DataFlowDiagram(i18NStringManager, lang, tenantId, integrationViewResultList));
        firstSheetArg.setClazz(ExportIntegrationStreamMapping.DataFlowDiagram.class);
        firstSheetArg.setHeaders(ExportIntegrationStreamMapping.DataFlowDiagram.getHeads(i18NStringManager, lang, tenantId));

        // 文件名称
        final ConnectInfoResult data = connectInfoService.getConnectInfoByDataCenterId(tenantId, getLoginUserId(), dcId).getData();
        final String now = DateTimeFormatter.ofPattern("yyyyMMdd_HHmm").format(LocalDateTime.now());
        final String fileName = data.getDataCenterName() + "_" + now;
        BuildExcelFile.Result uploadResult = fileService.buildStreamExcelVoAndUpload2NPath(getEa(), getLang(), tenantId, fileName, excelSheetArgs, firstSheetArg);
        final String npath = uploadResult.getTnFilePath();

        // 返回文件路径
        final ExportIntegrationStreamMapping.Result result = new ExportIntegrationStreamMapping.Result();
        String url = String.format(userCenterService.getDownloadFilePath(tenantId), npath, fileName + ".xlsx");
        result.setDownloadUrl(url);

        return Result.newSuccess(result);
    }

    public void resetName(String tenantId, final Map<String, Integer> integrationStreamNameCountMap, final IntegrationViewResult integrationViewResult) {
        String integrationStreamName = integrationViewResult.getIntegrationStreamName();
        // 没有名称的给个默认名称
        if (StringUtils.isEmpty(integrationStreamName)) {
            integrationViewResult.setIntegrationStreamName(i18NStringManager.getByEi(I18NStringEnum.s3749, tenantId));
            integrationStreamName = integrationViewResult.getIntegrationStreamName();
        }
        // 重名的后面加(n)
        if (integrationStreamNameCountMap.containsKey(integrationStreamName)) {
            Integer count = integrationStreamNameCountMap.get(integrationStreamName);
            integrationStreamNameCountMap.put(integrationStreamName, count + 1);
            integrationViewResult.setIntegrationStreamName(integrationStreamName + "(" + count + ")");
            // 可能还是重名,递归
            resetName(tenantId, integrationStreamNameCountMap, integrationViewResult);
        } else {
            integrationStreamNameCountMap.put(integrationStreamName, 1);
        }
    }

    public List<ErpIntegrationStreamExcelVoSheet> getExcelSheetList(final String tenantId,
                                                                    final String dcId,
                                                                    final List<IntegrationViewResult> integrationViewResultList,
                                                                    String lang) {
        ErpObjectFieldEntity fieldQuery = new ErpObjectFieldEntity();
        fieldQuery.setTenantId(tenantId);
        fieldQuery.setDataCenterId(dcId);
        //当前连接器所有字段
        List<ErpObjectFieldEntity> erpObjectFieldEntities = erpObjectFieldDao.queryList(fieldQuery);
        Map<String, Map<String, ErpObjectFieldEntity>> erpObjFieldMap = erpObjectFieldEntities.stream().collect(Collectors.groupingBy(v -> v.getErpObjectApiName(), Collectors.toMap(f -> f.getFieldApiName(), u -> u, (f, u) -> f)));
        // 集成流映射
        List<ErpIntegrationStreamExcelVoSheet> sheets = new ArrayList<>();
        for (IntegrationViewResult view : integrationViewResultList) {
            List<ErpIntegrationStreamExcelVo> excelVos = new ArrayList<>();
            boolean crm2erp = TenantType.CRM.equals(view.getSourceTenantType());
            String label = crm2erp ? "CRM" : "External";
            String sheetName = view.getIntegrationStreamName();
            sheets.add(new ErpIntegrationStreamExcelVoSheet(sheetName, label, excelVos));
            FieldMappingsResult result = adminSyncPloyDetailService.getFieldMappingsById(tenantId, view.getId()).getData();
            integrationStreamService.fillFieldMappingsResult(tenantId, dcId, view.getSourceTenantType(), view.getDestTenantType(), result.getMasterObjectMappings(), result.getDetailObjectMappings(), tenantId, tenantId);
            List<ObjectMappingResult> allObjMappings = CollUtil.newArrayList(result.getMasterObjectMappings());
            if (result.getDetailObjectMappings() != null) {
                allObjMappings.addAll(result.getDetailObjectMappings());
            }
            for (ObjectMappingResult objectMappingResult : allObjMappings) {
                if (objectMappingResult.getFieldMappings() == null) {
                    continue;
                }
                String crmObjApiName = crm2erp ? objectMappingResult.getSourceObjectApiName() : objectMappingResult.getDestObjectApiName();
                String crmObjName = crm2erp ? objectMappingResult.getSourceObjectName() : objectMappingResult.getDestObjectName();
                String erpObjApiName = !crm2erp ? objectMappingResult.getSourceObjectApiName() : objectMappingResult.getDestObjectApiName();
                String erpObjName = !crm2erp ? objectMappingResult.getSourceObjectName() : objectMappingResult.getDestObjectName();
                for (FieldMappingResult fieldMapping : objectMappingResult.getFieldMappings()) {
                    ErpIntegrationStreamExcelVo excelVo = new ErpIntegrationStreamExcelVo();
                    excelVos.add(excelVo);
                    excelVo.setCrmObjectApiName(crmObjApiName);
                    excelVo.setCrmObjectLabel(crmObjName);
                    excelVo.setCrmFileApiName(crm2erp ? fieldMapping.getSourceApiName() : fieldMapping.getDestApiName());
                    excelVo.setCrmFileName(crm2erp ? fieldMapping.getSourceName() : fieldMapping.getDestName());

                    excelVo.setThirdPartyObjectApiName(erpObjApiName);
                    excelVo.setThirdPartyObjectLabel(erpObjName);
                    excelVo.setThirdPartyFieldApiName(!crm2erp ? fieldMapping.getSourceApiName() : fieldMapping.getDestApiName());
                    excelVo.setThirdPartyFieldLabel(!crm2erp ? fieldMapping.getSourceName() : fieldMapping.getDestName());
                    excelVo.setThirdPartyFieldType(!crm2erp ? fieldMapping.getSourceType() : fieldMapping.getDestType());
                    ErpObjectFieldEntity erpObjectField = erpObjFieldMap.computeIfAbsent(erpObjApiName, v -> new HashMap<>()).get(excelVo.getThirdPartyFieldApiName());
                    if (erpObjectField != null) {
                        excelVo.setThirdPartyFieldRequired(erpObjectField.getRequired());
                        excelVo.setThirdPartyFieldExtendInfo(erpObjectField.getFieldExtendValue());
                    }
                }
            }
        }
        return sheets;
    }

    private ExcelSheetArg convert2ExcelSheetArg(final ObjectMappingResult objectMapping, final String sheetName, final String lang,String tenantId) {
        final ExcelSheetArg sheetArg1 = new ExcelSheetArg();
        sheetArg1.setSheetName(sheetName);
        // 补全字段映射
        final List<FieldMappingResult> fieldMappings = objectMapping.getFieldMappings();
        sheetArg1.setDataList(ExportIntegrationStreamMapping.convert2IntegrationStreamMapping(i18NStringManager,lang,tenantId,fieldMappings));
        sheetArg1.setClazz(ExportIntegrationStreamMapping.IntegrationStreamMapping.class);
        final String source = i18NStringManager.get(I18NStringEnum.s399,lang,tenantId) + objectMapping.getSourceObjectName() + "(" + objectMapping.getSourceObjectApiName() + ")";
        final String dest = i18NStringManager.get(I18NStringEnum.s400,lang,tenantId) + objectMapping.getDestObjectName() + "(" + objectMapping.getDestObjectApiName() + ")";
        final List<List<String>> headers = Stream.concat(
                ExportIntegrationStreamMapping.IntegrationStreamMapping.getSourceHeads(i18NStringManager,lang,tenantId).stream()
                        .map(s -> Lists.newArrayList(source, s)),
                ExportIntegrationStreamMapping.IntegrationStreamMapping.getDestHeads(i18NStringManager,lang,tenantId).stream()
                        .map(s -> Lists.newArrayList(dest, s))
        ).collect(Collectors.toList());
        sheetArg1.setHeaders(headers);
        return sheetArg1;
    }
}
