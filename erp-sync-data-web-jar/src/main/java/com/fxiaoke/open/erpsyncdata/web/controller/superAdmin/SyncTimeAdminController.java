package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncTimeService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date: 10:03 2021/7/5
 * @Desc:
 */
@Slf4j
@Api(tags = "最后同步时间管理接口")
@RestController("syncTimeAdminController")
@RequestMapping("/erp/syncdata/superadmin/syncTime")
public class SyncTimeAdminController extends BaseController {
    @Autowired
    private AdminSyncTimeService adminSyncTimeService;


    @ApiOperation(value = "更新syncTime")
    @RequestMapping(value = "/updateSyncTime", method = RequestMethod.GET)
    public Result<Void> updateSyncTime(@RequestParam String tenantId,@RequestParam String erpObjApiName,@RequestParam Integer eventType,@RequestParam Long lastSyncTime) {
        if(StringUtils.isBlank(tenantId)||StringUtils.isBlank(erpObjApiName)||eventType==null||lastSyncTime==null){
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        log.info("user={} deleteByPloyDetailId tenantId={} erpObjApiName={} eventType={} lastSyncTime={}",getLoginUserId(),tenantId,erpObjApiName,eventType,lastSyncTime);
        return adminSyncTimeService.updateLastSyncTime(tenantId,erpObjApiName,eventType,lastSyncTime);
    }

}
