package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.text.StrFormatter;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/8
 */
public abstract class SuperAdminBaseController extends AsyncSupportController {

    private static NotificationService notificationService;
    private static ErpConnectInfoManager erpConnectInfoManager;

    @Autowired
    public void setNotificationService(NotificationService notificationService) {
        SuperAdminBaseController.notificationService = notificationService;
    }

    @Autowired
    public void setErpConnectInfoManager(ErpConnectInfoManager erpConnectInfoManager) {
        SuperAdminBaseController.erpConnectInfoManager = erpConnectInfoManager;
    }

    @Setter
    protected static class SendMsgHelper {
        private StringBuilder msgBuilder;
        private SendTextNoticeArg sendTextNoticeArg;

        public SendMsgHelper append(Object msg) {
            this.msgBuilder.append(msg);
            return this;
        }

        public void  sendMsg(){
            sendMsg(AlarmRuleType.OTHER,
                    "OTHER",
                    AlarmType.OTHER,
                    AlarmLevel.GENERAL);
        }

        public Result<Void> sendMsg(AlarmRuleType alarmRuleType,
                                    String alarmRuleName,
                                    AlarmType alarmType,
                                    AlarmLevel alarmLevel) {
            sendTextNoticeArg.setMsg(msgBuilder.toString());
            return notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg,alarmRuleType,alarmRuleName,alarmType,alarmLevel);
        }
        public String getMsg() {
            return msgBuilder.toString();
        }
    }

    public SendMsgHelper initSendMsgHelper(String tenantId, String msgTitle) {
        SendMsgHelper sendMsgHelper = new SendMsgHelper();
        StringBuilder msg = new StringBuilder();
        if (tenantId != null) {
            List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoManager.listByTenantId(tenantId);
            if (!erpConnectInfoEntities.isEmpty()) {
                msg.append(StrFormatter.format("tenant：{}（{}）\n{}", tenantId, erpConnectInfoEntities.get(0).getEnterpriseName(), TraceUtil.get()));
            }
        }
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(getLoginUserTenantId());
        sendTextNoticeArg.setReceivers(Collections.singletonList(getLoginUserId()));
        sendTextNoticeArg.setMsgTitle(msgTitle);
        sendMsgHelper.setMsgBuilder(msg);
        sendMsgHelper.setSendTextNoticeArg(sendTextNoticeArg);
        return sendMsgHelper;
    }
}
