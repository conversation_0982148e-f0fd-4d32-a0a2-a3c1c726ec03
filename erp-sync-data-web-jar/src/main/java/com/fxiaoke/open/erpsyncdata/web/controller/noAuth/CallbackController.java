package com.fxiaoke.open.erpsyncdata.web.controller.noAuth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR> (^_−)☆
 */
@Controller
@RequestMapping("erp/syncdata/noAuth")
@Slf4j
public class CallbackController {


    @GetMapping("/standardConnector/callback")
    @ResponseBody
    public String home() {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "  <body>\n" +
                "    <script>\n" +
                "      var queryParams = (location.search || \"\").split(\"?\")[1];\n" +
                "      window.opener.postMessage({ queryParams }, \"*\");\n" +
                "      window.close();\n" +
                "    </script>\n" +
                "  </body>\n" +
                "</html>";
    }

}
