package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.arg.GetDataIntegrationNotificationArg;
import com.fxiaoke.open.erpsyncdata.admin.service.DataIntegrationNotificationService;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 集成平台数据集成通知相关接口，通知接入CRM提醒
 *
 * <AUTHOR>
 * @date 2023/08/10
 */
@Slf4j
@RestController()
@RequestMapping("cep/setUp/data/integration/notification")
public class DataIntegrationNotificationController extends BaseController {
    @Autowired
    private DataIntegrationNotificationService dataIntegrationNotificationService;

    /**
     * 分页获取数据集成通知数据
     *
     * @return
     */
    @PostMapping("/getDataList")
    public Result<DataIntegrationNotificationModel> getDataList(@RequestBody @Valid GetDataIntegrationNotificationArg arg) {
        if (StringUtils.isEmpty(arg.getTenantId())) {
            arg.setTenantId(getLoginUserTenantId());
        }

        if (StringUtils.isEmpty(arg.getTenantId())) {
            return Result.newError(ResultCodeEnum.NO_USER);
        }

        arg.setPage(arg.getPage() - 1);
        if (arg.getPage() < 0 || arg.getPageSize() <= 0) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        arg.setUserId(getLoginUserId());

        return dataIntegrationNotificationService.getDataList(arg);
    }

    /**
     * 检查当前登录的用户是否有CRM管理员权限或系统管理员权限
     *
     * @return
     */
    @PostMapping("/checkAdmin")
    public Result<Boolean> checkAdmin() {
        if(StringUtils.isEmpty(getLoginUserTenantId()) || getLoginUserId()==null) {
            return Result.newError(ResultCodeEnum.NO_USER);
        }

        return dataIntegrationNotificationService.checkAdmin(getLoginUserTenantId(),getLoginUserId()+"");
    }

    /**
     *  清空数据集成通知表的数据
     *
     * @return
     */
    @PostMapping("/clearData")
    public Result<Long> clearData() {
        if(StringUtils.isEmpty(getLoginUserTenantId()) || getLoginUserId()==null) {
            return Result.newError(ResultCodeEnum.NO_USER);
        }

        return dataIntegrationNotificationService.clearData();
    }

    /**
     * 获取连接器列表，包括连接器的ID和名称
     *
     * @return
     */
    @PostMapping("/getDcList")
    public Result<List<DataCenterModel>> getDcList() {
        String tenantId = getLoginUserTenantId();
        if(StringUtils.isEmpty(tenantId) || getLoginUserId()==null) {
            return Result.newError(ResultCodeEnum.NO_USER);
        }

        return dataIntegrationNotificationService.getDcList(tenantId);
    }

    /**
     * 判断当前企业是否有数据集成通知入口的权限
     *
     * @return
     */
    @PostMapping("/hasData")
    public Result<Boolean> hasData() {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        if (StringUtils.isEmpty(tenantId) || userId == null) {
            return Result.newError(ResultCodeEnum.NO_USER);
        }

        return dataIntegrationNotificationService.hasData(tenantId, userId);
    }
}
