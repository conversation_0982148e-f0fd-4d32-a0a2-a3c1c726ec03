package com.fxiaoke.open.erpsyncdata.web.controller.inner;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.facishare.uc.api.model.fscore.SimpleEnterprise;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.github.trace.TraceContext;
import groovy.util.logging.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
public class BaseInnerController {

    private static final Logger log = LoggerFactory.getLogger(BaseInnerController.class);
    @Autowired(required = false)
    private EnterpriseEditionService enterpriseEditionService;

    /**
     * 从header解析到的ei
     * x-fs-ei
     * @return
     */
    protected String getTenantId() {
        String ei = TraceContext.get().getEi();
        if (StrUtil.isBlank(ei)) {
            throw new ErpSyncDataException(ResultCodeEnum.NO_USER);
        }
        return ei;
    }


    protected Integer getTenantIdInt() {
        String tenantId = getTenantId();
        return Convert.toInt(tenantId);
    }


    protected String getLocale() {
        return I18nUtil.getLocaleFromTrace();
    }

    protected Integer getUserId() {
        String employeeId = TraceContext.get().getEmployeeId();
        //inner不传userId将默认使用系统
        Integer userId = Convert.toInt(employeeId, -10000);
        return userId;
    }


    public @Nullable String getEnterpriseName() {
        if (enterpriseEditionService == null) {
            return null;
        }
        try {
            GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
            getSimpleEnterpriseArg.setEnterpriseId(getTenantIdInt());
            GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(getSimpleEnterpriseArg);
            SimpleEnterprise simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
            if (Objects.isNull(simpleEnterprise)) {
                return null;
            }
            return simpleEnterprise.getEnterpriseName();
        } catch (Exception e) {
            log.error("getEnterpriseName error", e);
            return null;
        }
    }
}

