package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.model.AplTemplate;
import com.fxiaoke.open.erpsyncdata.admin.service.AplTemplateService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Apl类模板管理
 *
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping("cep/setUp/aplTemplate")
@Slf4j
public class AplTemplateController {
    @Autowired
    private AplTemplateService aplTemplateService;

    /**
     * 获取启用的APL类模板，不含说明文档
     *
     * @return
     */
    @PostMapping("listEnabled")
    public Result<List<AplTemplate.Info>> listEnabled() {
        return aplTemplateService.listEnabled();
    }


    /**
     * 获取详情，包含使用说明
     * @param arg
     * @return
     */
    @PostMapping("getInfo")
    public Result<AplTemplate.Info> getInfo(@RequestBody AplTemplate.AplClassNameArg arg) {
        return aplTemplateService.getInfo(arg);
    }
}
