package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.multi.ListValueMap;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.Page;
import cn.hutool.db.PageResult;
import com.fxiaoke.common.redisson.RedissonFactoryBean;
import com.fxiaoke.log.GenericBizLog;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.AmisResult;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.PG;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.SuperAdminBrushDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RecycleType;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigRouteManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MigrateTableManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PgClassInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RecycleBinDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorBizLogUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.mybatis.local.TenantThreadLocal;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * pg数据库相关管理
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/6/9
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/pg")
@Slf4j
public class SuperAdminPgController extends SuperAdminBaseController {
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    @Qualifier("hikariDataSource")
    private DataSource dataSource;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpTableDao erpTableDao;
    @Autowired
    private SuperAdminBrushDataService superAdminBrushDataService;
    @Autowired
    private RecycleBinDao recycleBinDao;

    @Autowired
    private RedissonClient redissonClient;


    /**
     * 获取初始数据
     */
    @PostMapping("preInitData")
    public Result<Dict> preInitData() {
        Dict data = new Dict();
        Map<String, List<String>> db2Tenants = configRouteManager.getDb2Tenants();
        ArrayList<String> dbs = new ArrayList<>(db2Tenants.keySet());
        data.put("dbs", dbs);
        return Result.newSuccess(data);
    }

    /**
     * 获取数据库列表
     */
    @PostMapping("listDbName")
    public Result<List<String>> listDbName() {
        Map<String, List<String>> db2Tenants = configRouteManager.getDb2Tenants();
        ArrayList<String> dbs = new ArrayList<>(db2Tenants.keySet());
        return Result.newSuccess(dbs);
    }

    /**
     * 查询索引信息，只是查中间表的
     *
     * @return
     * @throws SQLException
     */
    @GetMapping("listIndexInfos")
    public Result<Amis.Crud<PG.TableIndexInfo>> listIndexInfos(@RequestParam int page,
                                                               @RequestParam int perPage,
                                                               @RequestParam String dbName,
                                                               @RequestParam(defaultValue = "sync_data_mappings") String tableNamePrefix,
                                                               @RequestParam(required = false) String tenantId,
                                                               @RequestParam(required = false) String indexDef,
                                                               @RequestParam(required = false) String missIndexDef
    ) throws SQLException {
        Db db = buildDb(dbName);
        String tableNameLike;
        if (StrUtil.isNotBlank(tenantId)) {
            tableNameLike = tableNamePrefix + "_" + tenantId + "%";
        } else {
            tableNameLike = tableNamePrefix + "%";
        }
        Page pageO = Page.of(page - 1, perPage);
        PageResult<Entity> tablePage;
        if (StrUtil.isNotBlank(missIndexDef)) {
            String missIndexDefLike = "%" + missIndexDef;
            //其他查询条件失效
            String sql = "select tablename\n" +
                    "from pg_tables tablename\n" +
                    "where tablename like ?" +
                    "and tablename not in (select pt.tablename\n" +
                    "                        from pg_tables pt\n" +
                    "                                 left join pg_indexes pi on pt.tablename = pi.tablename\n" +
                    "                        where pi.tablename like ?\n" +
                    "                          and pi.indexdef like ?)";
            tablePage = db.page(sql, pageO, tableNameLike, tableNameLike, missIndexDefLike);
        } else if (StrUtil.isNotBlank(indexDef)) {
            String indexDefLike = "%" + indexDef;
            //其他查询条件失效
            String sql = "select tablename\n" +
                    "from pg_tables tablename\n" +
                    "where tablename like ?" +
                    "and tablename in (select pt.tablename\n" +
                    "                        from pg_tables pt\n" +
                    "                                 left join pg_indexes pi on pt.tablename = pi.tablename\n" +
                    "                        where pi.tablename like ?\n" +
                    "                          and pi.indexdef like ?)";
            tablePage = db.page(sql, pageO, tableNameLike, tableNameLike, indexDefLike);
        } else {
            Entity tableWhere = Entity.create("pg_tables")
                    .setFieldNames("tablename");
            tableWhere.set("tablename", "like " + tableNameLike);
            tablePage = db.page(tableWhere, pageO);
        }
        Amis.Crud<PG.TableIndexInfo> crud = Amis.Crud.of(new ArrayList<>());
        if (tablePage.isEmpty()) {
            return Result.newSuccess(crud);
        }
        Object[] tableNames = tablePage.stream().map(v -> v.getStr("tablename")).toArray();
        Entity where = Entity.create("pg_indexes")
                .setFieldNames("tablename", "indexname", "indexdef");
        where.set("tablename", tableNames);
        List<Entity> indexEntities = db.find(where);
        List<String> tenantIds = erpConnectInfoDao.listTenantId();
        HashSet<String> eiSet = new HashSet<>(tenantIds);
        ListValueMap<String, PG.TableIndexInfo> indexMap = new ListValueMap<>();
        for (Entity indexEntity : indexEntities) {
            String tablename = indexEntity.getStr("tablename");
            String indexname = indexEntity.getStr("indexname");
            String indexdef = indexEntity.getStr("indexdef");
            //支持分区表后缀
            PG.TableIndexInfo indexInfo = new PG.TableIndexInfo();
            indexInfo.setDbName(dbName)
                    .setTableName(tablename)
                    .setIndexName(indexname)
                    .setIndexDef(indexdef)
            ;
            indexMap.putValue(tablename, indexInfo);
        }
        List<PG.TableIndexInfo> resultData = new ArrayList<>();
        for (Entity tableEntity : tablePage) {
            String tablename = tableEntity.getStr("tablename");
            //支持分区表后缀
            String tenantIdParse = StrUtil.removePrefix(tablename, tableNamePrefix);
            tenantIdParse = StrUtil.removePrefix(tenantIdParse, "_");
            tenantIdParse = StrUtil.subBefore(tenantIdParse, "_", false);
            tenantIdParse = StrUtil.blankToDefault(tenantIdParse, "global");
            PG.TableIndexInfo tableInfo = new PG.TableIndexInfo();
            List<PG.TableIndexInfo> indexInfos = indexMap.get(tablename);
            tableInfo.setDbName(dbName)
                    .setTenantId(tenantIdParse)
                    .setTableName(tablename)
                    .setExistConnectInfo(eiSet.contains(tenantId))
                    .setChildren(indexInfos)
            ;
            if (indexInfos != null) {
                for (PG.TableIndexInfo indexInfo : indexInfos) {
                    indexInfo.setTenantId(tableInfo.getTenantId())
                            .setExistConnectInfo(tableInfo.isExistConnectInfo())
                            .setIndexNamePattern(indexInfo.getIndexName().replace(tenantIdParse, "{tenantId}"));
                }
            }
            resultData.add(tableInfo);
        }
        crud.setRows(resultData)
                .setTotal(tablePage.getTotal());
        return Result.newSuccess(crud);
    }

    /**
     * 自动setTenantId
     *
     * @param dbName
     * @return
     */
    private Db buildDb(String dbName) {
        Map<String, List<String>> db2Tenants = configRouteManager.getDb2Tenants();
        List<String> eis = db2Tenants.get(dbName);
        if (CollUtil.isEmpty(eis)) {
            throw new ErpSyncDataException(I18NStringEnum.s3630, null);
        }
        String routeEi = eis.get(0);
        TenantThreadLocal.set(routeEi);
        Db db = new Db(dataSource) {
            @Override
            public Connection getConnection() throws SQLException {
                //setTenantId
                TenantThreadLocal.set(routeEi);
                return super.getConnection();
            }
        };
        return db;
    }


    @PostMapping("createIndexes")
    public DeferredResult<Result<Dict>> createPgIndexes(@RequestBody PG.ModIndexArg modIndexArg) {
        return asyncExecute(() -> {
            if (StrUtil.isBlank(modIndexArg.getIndexNamePattern())) {
                return Result.newError("索引名称不允许为空");   // ignoreI18n   实施和开发自用
            }
            if (!modIndexArg.getIndexNamePattern().contains("{tenantId}")) {
                return Result.newError("索引名称必须包含{tenantId}");   // ignoreI18n   实施和开发自用
            }
            return createPgIndexesSync(modIndexArg, false);
        }, 5, true, "创建索引" + modIndexArg.getIndexNamePattern(), null);   // ignoreI18n   实施和开发自用
    }

    @PostMapping("compareIndexes")
    public DeferredResult<Result<Dict>> compareIndexes(@RequestBody PG.CompareIndexArg modIndexArg) {
        return asyncExecute(() -> {
            return comparePgIndexesSync(modIndexArg);
        }, 5, true, "比较索引", null);   // ignoreI18n   实施和开发自用
    }

    @ApiOperation(value = "刷sync_data,sync_data_mappings数据")
    @RequestMapping(value = "/brushTableData", method = RequestMethod.POST)
    public DeferredResult<Result<String>> brushTableData(@RequestBody PG.BrushTableDataArg brushTableDataArg) {
        String sourceTenantIdStr = brushTableDataArg.getSourceTenantId();
        String destDataBaseTenantId = brushTableDataArg.getDestDataBaseTenantId();
        List<String> sourceTenantId = Splitter.on(";").splitToList(sourceTenantIdStr);
        return asyncExecute(() -> {
            return superAdminBrushDataService.brushTableData(sourceTenantId, destDataBaseTenantId);
        }, 10, true, "刷sync_data,sync_data_mappings数据", null);   // ignoreI18n   实施和开发自用
    }

    private Result<Dict> comparePgIndexesSync(PG.CompareIndexArg indexArg) {
        Map<String, List<String>> db2Tenants = configRouteManager.getDb2Tenants();
        List<String> eis = db2Tenants.get(indexArg.getDbName());
        if (CollUtil.isEmpty(eis)) {
            throw new ErpSyncDataException(I18NStringEnum.s3630, null);
        }
        String routeEi = eis.get(0);
        List<String> failedTenants = new ArrayList<>();
        Map<String, String> tenantTableMap = new LinkedHashMap<>();
        String tablePrefix = "sync_data_mappings_";
        //所有mapping表
        List<String> allMappings = erpTableDao.setTenantId(routeEi).listAllTableLeftMatching(tablePrefix);
        for (String table : allMappings) {
            String tenantId = StrUtil.removePrefix(table, tablePrefix);
            if (NumberUtil.isInteger(tenantId)) {
                tenantTableMap.put(tenantId, table);
            }
        }

        if (StrUtil.isNotBlank(indexArg.getTenantIdStr())) {
            //筛选企业
            HashSet<String> tenantIds = Sets.newHashSet(Splitter.on(",").split(indexArg.getTenantIdStr()));
            tenantTableMap.keySet().retainAll(tenantIds);
        }
        for (Map.Entry<String, String> entry : tenantTableMap.entrySet()) {
            String tenantId = entry.getKey();
            String table = entry.getValue();
            try {
                String query = String.format("select indexname from pg_indexes where tablename = '%s'", table);
                List<Dict> res = erpTableDao.setTenantId(routeEi).superQuerySql2(query, tenantId);
                List<String> oldIndexNames = res.stream().map(v -> v.getStr("indexname")).filter(v -> !v.contains("_id_pk")).collect(Collectors.toList());
                Map<String, String> newIndexName2SQL = getNewIndexes(tenantId);
                for (String indexName : newIndexName2SQL.keySet()) {
                    if (!oldIndexNames.contains(indexName)) {//创建
                        String sql = newIndexName2SQL.get(indexName);
                        erpTableDao.setTenantId(routeEi).superUpdateSql(sql);
                    }
                }
                if (indexArg.getDeleteIndex()) {
                    for (String indexName : oldIndexNames) {
                        if (!newIndexName2SQL.keySet().contains(indexName)) {//删除
                            String sql = String.format("drop index if exists %s", indexName);
                            log.info("删除索引，sql={}", sql);
                            erpTableDao.setTenantId(routeEi).superUpdateSql(sql);
                        }
                    }
                }
            } catch (Exception e) {
                failedTenants.add(tenantId);
            }
        }
        String res = "失败企业：" + failedTenants;   // ignoreI18n   实施和开发自用
        return AmisResult.of(res);
    }

    private Map<String, String> getNewIndexes(String tenantId) {
        Map<String, String> newIndexName2SQL = Maps.newHashMap();

        List<String> indexSqls = MigrateTableManager.getSql(tenantId, false);
        for (String index : indexSqls) {
            if (index.startsWith("--")) {
                continue;
            }
            String str = "\"";
            String indexName = index.substring(index.indexOf(str) + str.length(), index.lastIndexOf(str));
            newIndexName2SQL.put(indexName, index);
        }
        return newIndexName2SQL;
    }

    @PostMapping("dropIndexes")
    public DeferredResult<Result<Dict>> dropPgIndexes(@RequestBody PG.ModIndexArg modIndexArg) {
        return asyncExecute(() -> {
            return createPgIndexesSync(modIndexArg, true);
        }, 5, true, "删除索引" + modIndexArg.getIndexNamePattern(), null);   // ignoreI18n   实施和开发自用
    }


    @PostMapping("dropIndexByName")
    public DeferredResult<Result<Void>> dropIndexByName(@RequestBody PG.TableIndexInfo indexInfo) {
        return asyncExecute(() -> {
            return dropIndexByNameSync(indexInfo);
        }, 5, true, "删除索引" + indexInfo.getIndexName(), null);   // ignoreI18n   实施和开发自用
    }

    private Result<Void> dropIndexByNameSync(PG.TableIndexInfo indexInfo) throws SQLException {
        Db db = buildDb(indexInfo.getDbName());
        //找到一条索引
        String dropIndexSql = String.format("DROP INDEX CONCURRENTLY IF EXISTS %s", indexInfo.getIndexName());
        db.execute(dropIndexSql);
        return Result.newSuccess();
    }

    private Result<Dict> createPgIndexesSync(PG.ModIndexArg modIndexArg, boolean isDelete) {
        if (StrUtil.isBlank(modIndexArg.getIndexDef())) {
            return Result.newError("索引def不允许为空");   // ignoreI18n   实施和开发自用
        }
        String indexDef = modIndexArg.getIndexDef().trim();
        if (!indexDef.startsWith("(")) {
            return Result.newError("索引def必须以'('开头");   // ignoreI18n   实施和开发自用
        }
        String dbName = modIndexArg.getDbName();
        Db db = buildDb(dbName);
        Map<String, List<String>> db2Tenants = configRouteManager.getDb2Tenants();
        List<String> eis = db2Tenants.get(dbName);
        String tenantIdStr = modIndexArg.getTenantIdStr();
        if (StrUtil.isNotBlank(tenantIdStr)) {
            List<String> filterEIs = Splitter.on(",").splitToList(tenantIdStr);
            eis.retainAll(filterEIs);
        }
        //逐个企业执行,处理简单一点,不考虑分区表
        List<String> successEis = new ArrayList<>();
        List<String> failedEis = new ArrayList<>();
        Dict result = new Dict();
        result.put("successEis", successEis);
        result.put("failedEis", failedEis);
        result.put("eis", eis);
        for (String ei : eis) {
            try {
                if (isDelete) {
                    dropIndex(indexDef, db, successEis, ei);
                } else {
                    createIndexEi(modIndexArg, db, successEis, ei);
                }
            } catch (Exception e) {
                log.info("mod sql exception,{}", ei, e);
                failedEis.add(ei);
            }
        }

        return Result.newSuccess(result);

    }

    private static void dropIndex(String indexDef, Db db, List<String> successEis, String ei) throws SQLException {
        String tableName = "sync_data_mappings_" + ei;
        //查索引
        Entity where = Entity.create("pg_indexes")
                .setFieldNames("tablename", "indexname", "indexdef");
        where.set("tablename", tableName);
        where.set("indexdef", "like %" + indexDef);
        List<Entity> indexEntities = db.find(where);
        if (indexEntities.size() > 1) {
            //找到多条,按道理不应该，直接报错
            throw new ErpSyncDataException(I18NStringEnum.s3631, null);
        }
        if (indexEntities.size() == 1) {
            //找到一条索引
            String dropIndexSql = String.format("DROP INDEX  CONCURRENTLY   IF EXISTS  %s", indexEntities.get(0).getStr("indexname"));
            db.execute(dropIndexSql);
            successEis.add(ei);
        }
    }

    private static void createIndexEi(PG.ModIndexArg modIndexArg, Db db, List<String> successEis, String ei) throws SQLException {
        String tableName = "sync_data_mappings_" + ei;
        String indexName = StrUtil.replace(modIndexArg.getIndexNamePattern(), "{tenantId}", ei);
        //只能修改mapping表
        Entity where = Entity.create("pg_indexes")
                .setFieldNames("tablename", "indexname", "indexdef");
        where.set("tablename", tableName);
        List<Entity> indexEntities = db.find(where);
        if (indexEntities.isEmpty()) {
            //索引为空，认为不存在表。
            return;
        }
        String indexSqlPrefix;
        if (modIndexArg.getUniqueIndex() != null && modIndexArg.getUniqueIndex()) {
            indexSqlPrefix = "create unique index concurrently if not exists %s on %s %s";
        } else {
            indexSqlPrefix = "create index concurrently if not exists %s on %s %s";
        }
        String createIndexSql = String.format(indexSqlPrefix, indexName, tableName, modIndexArg.getIndexDef());
        int execute = db.execute(createIndexSql);
        log.info("create sql,{},{},{}", ei, createIndexSql, execute);
        successEis.add(ei);
    }


    @PostMapping("executeBatchUpdate")
    public Result<List<Entity>> executeBatchUpdate(@RequestBody PG.BatchUpdateArg arg) throws SQLException {
        String dbName = arg.getDbName();
        String tableName = arg.getTableName();
        String updateField = arg.getUpdateField();
        String resultIdField = arg.getResultIdField();
        String updateIdField = arg.getUpdateIdField();
        String newValueField = arg.getNewValueField();
        if (ObjectUtil.hasNull(dbName, tableName, updateField, resultIdField, updateIdField, newValueField)) {
            log.info("executeBatchUpdate some field is null,not support,{}", arg);
            return Result.newError("executeBatchUpdate some field is null,not support:" + arg);
        }
        Db db = buildDb(dbName);
        Map<String, Object> argMap = BeanUtil.beanToMap(arg);
        argMap.remove(PG.BatchUpdateArg.Fields.items);
        String querySql = StrFormatter.format("select * from {tableName} where {updateIdField} = ? limit 2", argMap, false);
        String updateSql = StrFormatter.format("update {tableName} set {updateField} = ? where {updateIdField} = ?", argMap, false);
        log.info("executeBatchUpdate arg:{},querySql:{},updateSql:{}", argMap, querySql, updateSql);
        List<Entity> srcEntities = new ArrayList<>();
        for (Dict item : arg.getItems()) {
            log.info("executeBatchUpdate handle begin,{}", item);
            Object id = item.get(resultIdField);
            Object newVal = item.get(newValueField);
            //不支持更新为null
            if (id == null || newVal == null) {
                log.info("executeBatchUpdate id or new val is null,not support,{}", item);
                return Result.newError("executeBatchUpdate id or new val is null,not support:" + item);
            }
            //limit 2,如果返回2条会报错
            //伝傪蜩鼡汸炷叺撿恻，娸實迯蔀應姟嘸琺汸問菿適個帹囗，啪禸蔀運惟给嚗岥孒。。。
            Entity entity = db.queryOne(querySql, id);
            if (entity == null) {
                log.info("executeBatchUpdate not found,{}", id);
                continue;
            }
            srcEntities.add(entity);
            //逐条处理，数据多，想快的别用这个。。。
            //先进入回收站
            recycleBinDao.batchInsertJson("1", RecycleType.SUPER_ADMIN_BULK_DB, Collections.singletonList(entity), "u_" + tableName + "_" + id);
            //执行更新
            int execute = db.execute(updateSql, newVal, id);
            log.info("executeBatchUpdate execute,{}", execute);
        }
        return Result.newSuccess(srcEntities);
    }


    @ApiOperation(value = "获取数据库数据量分布预估值")
    @RequestMapping(value = "/getDbDistributeFromPgClass", method = RequestMethod.POST)
    public Result<List<PgClassInfo>> getDbDistributeFromPgClass() {
        Map<String, List<String>> db2Tenants = configRouteManager.getDb2Tenants();
        if (CollUtil.isEmpty(db2Tenants)) {
            throw new ErpSyncDataException(I18NStringEnum.s3630.getText());
        }
        List<PgClassInfo> pgClassInfos = new ArrayList<>();
        db2Tenants.forEach((dbName, eis) -> {
            String routeEi = eis.get(0);
            List<PgClassInfo> pgClassInfos1 = erpTableDao.setTenantId(routeEi).queryReltuplesFromPgClass();
            pgClassInfos1.forEach(v -> {
                v.setDbName(dbName);
                GenericBizLog.Builder builder = MonitorBizLogUtil.genericBuilder("erpdss-getDbDistributeFromPgClass");
                //计算回时间
                builder.setType1(dbName);
                builder.setType2(v.getTableName());
                builder.setLong1(v.getReltuples());
                MonitorBizLogUtil.sendGeneric(builder.build());
            });
            pgClassInfos.addAll(pgClassInfos1);
        });
        return Result.newSuccess(pgClassInfos);
    }


    @ApiOperation(value = "计算单表数据分布")
    @RequestMapping(value = "/calTableDistribute", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> calTableDistribute(@RequestBody PG.CalTableDistributeArg arg) {
        return asyncExecuteJustName("calTableDistribute", () -> {
            //加锁执行
            RLock lock = redissonClient.getLock("calTableDistribute");
            if (lock.tryLock()) {
                try {
                    return calTableDistributeNoLock(arg);
                }finally {
                    lock.unlock();
                }
            }
            return Result.newError("calTableDistribute get lock failed");
        });
    }

    @NotNull
    private Result<Void> calTableDistributeNoLock(PG.CalTableDistributeArg arg) throws SQLException {
        Db db = buildDb(arg.getDbName());
        String timeField = arg.getTimeField();
        String groupFieldStr = arg.getGroupFieldStr();
        List<String> groupFields = StrSplitter.split(groupFieldStr, ",", true, true);
        Long intervalDay = arg.getIntervalDay();
        if (intervalDay == null || intervalDay < 1) {
            intervalDay = 30L;
        }
        Long interval = TimeUnit.DAYS.toMillis(intervalDay);
        //固定id字段
        List<String> selectFields = CollUtil.newArrayList("id", timeField);
        CollUtil.addAllIfNotContains(selectFields, groupFields);
        String selectFieldsStr = StrJoiner.of(",").append(selectFields).toString();
        Dict argMap = Dict.create();
        argMap.put("selectFields", selectFieldsStr);
        argMap.put("tableName", arg.getTableName());
        //先查第一批
        String firstSql = StrUtil.format("select {selectFields} from {tableName} order by id desc limit 1000", argMap);
        String sqlAfterFirst = StrUtil.format("select {selectFields} from {tableName} where id <? order by id desc limit 1000", argMap);
        Map<String, PG.CalTableDistributePart> partCountMap = new HashMap<>();
        String maxId = null;
        long startTime = System.currentTimeMillis();
        long total = 0L;
        for (int i = 0; i < 10000000; i++) {
            //最多跑统计100亿数据
            List<Entity> entities;
            if (maxId == null) {
                entities = db.query(firstSql);
            } else {
                entities = db.query(sqlAfterFirst, maxId);
            }
            //收集数据
            collectQueryResult(entities, timeField, interval, groupFields, partCountMap);
            total += entities.size();
            //10w打印一下
            if (total % 100000 == 0) {
                log.info("calTableDistribute total count:{}", total);
            }
            if (entities.size() < 1000) {
                break;
            }
            maxId = entities.get(entities.size() - 1).getStr("id");
            if (maxId == null) {
                //异常
                throw new ErpSyncDataException("calTableDistribute minId is null.");
            }
        }
        //上报日志
        Long finalInterval = interval;
        partCountMap.values().forEach(part -> {
            GenericBizLog.Builder builder = MonitorBizLogUtil.genericBuilder("erpdss-calTableDistribute");
            builder.setType1(arg.getDbName());
            builder.setType2(arg.getTableName());
            for (int i = 0; i < part.getGroupFieldValues().size(); i++) {
                String value = part.getGroupFieldValues().get(i);
                //最多设置6个分组字段
                switch (i) {
                    case 0:
                        builder.setString1(value);
                        break;
                    case 1:
                        builder.setString2(value);
                        break;
                    case 2:
                        builder.setString3(value);
                        break;
                    case 3:
                        builder.setString4(value);
                        break;
                    case 4:
                        builder.setString5(value);
                        break;
                    case 5:
                        builder.setString6(value);
                        break;
                    default:
                        break;
                }
            }
            //计算回时间
            builder.setTime1(part.getIntervalIndex() * finalInterval);
            //数量
            builder.setLong1(part.getCount().get());
            // 同时设置stringArray看看效果
            builder.addAllStringArray1(groupFields);
            builder.addAllStringArray2(part.getGroupFieldValues());
            MonitorBizLogUtil.sendGeneric(builder.build());
        });
        //发送一个汇总信息
        GenericBizLog.Builder builder = MonitorBizLogUtil.genericBuilder("erpdss-calTableDistributeSummary");
        builder.setType1(arg.getDbName());
        builder.setType2(arg.getTableName());
        //总耗时
        long endTime = System.currentTimeMillis();
        long cost = endTime - startTime;
        //执行时间
        builder.setTime1(startTime);
        builder.setTime2(endTime);
        builder.setCost((int) cost);
        //总数
        builder.setLong1(total);
        builder.addAllStringArray1(groupFields);
        builder.setString1(timeField);
        builder.setInt1(intervalDay.intValue());
        MonitorBizLogUtil.sendGeneric(builder.build());
        return Result.newSuccess();
    }

    private void collectQueryResult(List<Entity> entities, String timeField, Long interval, List<String> groupFields, Map<String, PG.CalTableDistributePart> partCountMap) {
        entities.forEach(entity -> {
            StrJoiner keyJoiner = StrJoiner.of(",");
            Long time = entity.get(timeField, 0L);
            long intervalIndex = time / interval + 1;
            keyJoiner.append(intervalIndex);
            for (String groupField : groupFields) {
                keyJoiner.append(entity.get(groupField));
            }
            String key = keyJoiner.toString();
            PG.CalTableDistributePart part = partCountMap.computeIfAbsent(key, k -> {
                PG.CalTableDistributePart calTableDistributePart = new PG.CalTableDistributePart();
                calTableDistributePart.setIntervalIndex(intervalIndex);
                List<String> groupFieldValueList = groupFields.stream().map(v -> entity.getStr(v)).collect(Collectors.toList());
                calTableDistributePart.setGroupFieldValues(groupFieldValueList);
                calTableDistributePart.setCount(new AtomicLong());
                return calTableDistributePart;
            });
            long currentCount = part.getCount().incrementAndGet();
            //10w打印一下
            if (currentCount % 100000 == 0) {
                log.info("calTableDistribute part:{}", part);
            }
        });
        //每批数据处理后，检查partCountMap是否超出限制
        if (partCountMap.size() > 100000) {
            //超过10万分组直接报错
            throw new ErpSyncDataException("calTableDistribute part count over 100000,pleas control groupFields or time interval.");
        }
    }
}
