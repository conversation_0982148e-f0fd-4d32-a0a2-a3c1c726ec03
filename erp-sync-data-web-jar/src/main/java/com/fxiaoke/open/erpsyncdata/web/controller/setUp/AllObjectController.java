package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.model.PriorityConfig;
import com.fxiaoke.open.erpsyncdata.admin.service.AllObjConfigService;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ObjDispatchPriorityConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = "对象设置相关接口")
@RestController
@RequestMapping("cep/setUp/allObject")
@ManagedTenantIntercept
public class AllObjectController extends AsyncSupportController {
    @Autowired
    private AllObjConfigService allObjConfigService;

    @ApiOperation(value = "获取数据优先级配置")
    @PostMapping("getObjDispatchPriorityConfig")
    public Result<ObjDispatchPriorityConfig> getObjDispatchPriorityConfig(@RequestBody PriorityConfig.GetArg arg) {
        return allObjConfigService.getObjDispatchPriorityConfig(getLoginUserTenantId(), arg);
    }

    @ApiOperation(value = "设置数据优先级配置")
    @PostMapping("setObjDispatchPriorityConfig")
    public Result<Void> setObjDispatchPriorityConfig(@RequestBody PriorityConfig.SetArg arg) {
        return allObjConfigService.setObjDispatchPriorityConfig(getLoginUserTenantId(), arg);
    }
}
