package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.text.StrSplitter;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.result.CheckQuotaResult;
import com.fxiaoke.open.erpsyncdata.admin.service.CleanResourceService;
import com.fxiaoke.open.erpsyncdata.admin.service.SyncQuotaService;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.OpsToolService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.main.manager.SandboxEventManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpSyncService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ScanSyncWarnningService;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/9/30
 */
@RestController
@RequestMapping("erp/syncdata/superadmin")
public class SuperAdminJobController extends SuperAdminBaseController {
    @Autowired
    private ScanSyncWarnningService scanSyncWarnningService;
    @Autowired
    private OpsToolService opsToolService;
    @Autowired
    private SandboxEventManager sandboxEventManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpSyncService erpSyncService;
    @Autowired
    private SyncQuotaService syncQuotaService;
    @Autowired
    private CleanResourceService cleanResourceService;



    @RequestMapping("/scanSyncWarnning/{tenantId}")
    public DeferredResult<Result<String>> pollingTempDataJob(
            @PathVariable("tenantId") String tenantId) {
        DeferredResult<Result<String>> result = new DeferredResult<Result<String>>(1000 * 10L, Result.newSuccess("请关注企信通知！"));   // ignoreI18n   实施和开发自用
        ParallelUtils.createBackgroundTask().submit(() -> {
            Result<String> result1 = scanSyncWarnningService.scanSyncDataMappingErrorNumber(tenantId);
            result.setResult(result1);
        }).run();
        return result;
    }


    @PostMapping("/scanErpTemp")
    public DeferredResult<Result<Integer>> scanErpTemp(@RequestBody Dict arg) {
        String tenantId = arg.getStr("tenantId");
        return asyncExecute(() -> {
            Result<Integer> integerResult = opsToolService.scanErpTempBetweenId(tenantId, arg.getLong("beginTime"), arg.getLong("endTime"));
            return integerResult;
        }, 5, true, "手动执行扫描临时库" + tenantId,null);   // ignoreI18n   实施和开发自用
    }

    @PostMapping("/copyErpSyncConfig")
    public DeferredResult<Result<Void>> copyErpSyncConfig(@RequestBody Dict arg) {
        String from = arg.getStr("from");
        String to = arg.getStr("to");
        return asyncExecute(() -> {
            List<ErpConnectInfoEntity> erpConnectInfoEntities1 = erpConnectInfoManager.listByTenantId(to);
            if (!erpConnectInfoEntities1.isEmpty()) {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL, "不允许复制到已存在连接信息的企业，请先清除");   // ignoreI18n   实施和开发自用
            }
            sandboxEventManager.copyErpSyncConfig(from, to);
            return Result.newSuccess();
        }, 5, true, String.format("复制企业,from:%s,to:%s", from, to),null);   // ignoreI18n   实施和开发自用
    }

    /**
     * 删除企业配置，逗号分隔
     * @param arg
     * @return
     */
    @PostMapping("/deleteErpSyncConfig")
    public DeferredResult<Result<Void>> deleteErpSyncConfig(@RequestBody Dict arg) {
        String tenantIds = arg.getStr("tenantIds");
        return asyncExecute(() -> {
            if (StringUtils.isEmpty(tenantIds)) {
                return Result.newError("企业必填");   // ignoreI18n   实施和开发自用
            }
            List<String> tenantIdList = StrUtil.split(tenantIds, ",");
            for (String tenantId : tenantIdList) {
                sandboxEventManager.deleteErpSyncConfig(tenantId);
            }
            return Result.newSuccess();
        }, 5, true, String.format("删除企业,tenantId:%s", tenantIds),null);   // ignoreI18n   实施和开发自用
    }


    /**
     * 删除表，
     * @param arg
     * @return
     */
    @PostMapping("/dropTableIfNeed")
    public DeferredResult<Result<Dict>> dropTableIfNeed(@RequestBody Dict arg) {
        int needDeleteBeforeDay = arg.getInt("needDeleteBeforeDay");
        return asyncExecute(() -> {
            Result<Dict> dictResult = cleanResourceService.cleanDeletedTables(needDeleteBeforeDay);
            return dictResult;
        }, 5, true, String.format("drop应删除的表,needDeleteBeforeDay:%s", needDeleteBeforeDay),null);   // ignoreI18n   实施和开发自用
    }

    @PostMapping("/sendDoDispatchMq")
    public Result<Void> sendDoDispatchMq(@RequestBody Dict arg) {
        String tenantId = arg.getStr("tenantId");
        String eventDataStr = arg.getStr("eventDataList");
        List<SyncDataContextEvent> eventDataList = JacksonUtil.fromJson(eventDataStr, new TypeReference<List<SyncDataContextEvent>>() {
        });
        return erpSyncService.sendDoDispatchMq(tenantId, eventDataList);
    }

    @PostMapping("/checkQuotaTask")
    public DeferredResult<Result<Void>> checkQuotaTask(@RequestBody Dict arg,
                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        List<String> tenantIds = StrSplitter.split(arg.getStr("tenantIds"),",",true,true);
        Boolean alert = arg.get("alert",false);
        Boolean cleanLog = arg.get("cleanLog",false);
        Boolean vacuum = arg.get("vacuum",false);
        return asyncExecute(() -> {
            syncQuotaService.checkQuotaTask(tenantIds,alert,cleanLog,vacuum,lang);
            return new Result<>();
        }, 5, true, String.format("检查配额,tenantId:%s", tenantIds),null);   // ignoreI18n   实施和开发自用
    }

    @GetMapping("/listCheckQuotaResult")
    public Result<List<CheckQuotaResult>> listCheckQuotaResult(){
        return syncQuotaService.listCheckQuotaResult();
    }


    /**
     * 删除已逻辑删除的中间表数据
     */
    @PostMapping("/cleanDeletedData")
    public DeferredResult<Result<Dict>> cleanDeletedData(@RequestBody Dict arg) {
        return asyncExecute(() -> {
            List<String> tenantIds= Lists.newArrayList();
            if(arg!=null&&arg.get("tenantIdStr")!=null&&StringUtils.isNotBlank(arg.get("tenantIdStr").toString())){
                tenantIds= Splitter.on(",").splitToList(arg.get("tenantIdStr").toString());
            }
            Result<Dict> dictResult = cleanResourceService.cleanDeletedData(tenantIds);
            return dictResult;
        }, 5, true, "删除已逻辑删除的中间表数据",null);   // ignoreI18n   实施和开发自用
    }

}
