package com.fxiaoke.open.erpsyncdata.web.annontation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date: 15:46 2022/10/19
 * @Desc: 走代理方法注解，只能在controller的方法上添加，配合ControllerTenantIDArg注解使用，会根据企业id路由到企业对应的环境执行
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ControllerMethodProxy {
    boolean needProxy() default true;
}
