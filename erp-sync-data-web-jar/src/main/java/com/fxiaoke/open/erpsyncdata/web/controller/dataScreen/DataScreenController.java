package com.fxiaoke.open.erpsyncdata.web.controller.dataScreen;

import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.dataScreen.BIManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.dataScreen.DataScreenArg;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.DataDashboardResult;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ERPBITypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @create 2024/1/3 19:49
 * @desc
 */
@Slf4j
@Api(tags = "数据大屏")
@RestController("dataScreenController")
@RequestMapping("cep/dataScreen")
public class DataScreenController  extends BaseController {
    @Autowired
    private BIManager biManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @ApiOperation(value = "大屏总览")
    @Deprecated
    @RequestMapping("/screenOverview")
    public Result<DataScreenResult> screenOverview(@RequestBody DataScreenArg dataScreenArg,
                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        dataScreenArg.setTenantId(tenantId);
        DataScreenResult dataScreenResult = biManager.allViewData(dataScreenArg);
        return Result.newSuccess(dataScreenResult);
    }

    @ApiOperation(value = "刷描述")
    @RequestMapping("/brushData")
    public Result<DataScreenResult> brushData(@RequestBody DataScreenArg dataScreenArg,
                                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        biManager.extractedBrushFiled(tenantId);
        return Result.newSuccess();
    }

    @ApiOperation(value = "查询数据")
    @RequestMapping("/queryField")
    public Result<DataScreenResult> queryField(@RequestBody DataScreenArg.ScreenFieldType screenFieldType) {
        String tenantId=getLoginUserTenantId();
        if(StringUtils.isNotEmpty(screenFieldType.getTenantId())){
            tenantId=getLoginUserTenantId();
        }
        ERPBITypeEnum erpbiTypeEnum = ERPBITypeEnum.valueOf(screenFieldType.getType());
        biManager.queryField(tenantId,erpbiTypeEnum);
        return Result.newSuccess();
    }

    @ApiOperation(value = "banner统计")
    @RequestMapping("/screenBannerData")
    public Result<DataScreenResult> screenBannerData(@RequestBody DataScreenArg dataScreenArg,
                                                   @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        dataScreenArg.setTenantId(tenantId);
        Result<DataScreenResult> dataScreenResultResult = biManager.statisticsBannerData(dataScreenArg);
        //这个实时不缓存
        biManager.getAlertIntegrationStreamCount(dataScreenArg,dataScreenResultResult.getData());
        //当dataCenterList为0或者size!=1的时候，说明是第一次开始查询数据，根据保存的数据中心做一次缓存预热。
        ParallelUtils.ParallelTask dataScreenExecutor = ParallelUtils.createDataScreenExecutor();
        dataScreenExecutor.submit(new Runnable() {
            @Override
            public void run() {
                generateCache(dataScreenArg);
            }
        });
        dataScreenExecutor.run();
        return dataScreenResultResult;
    }

    @ApiOperation(value = "crm同步数量看板")
    @RequestMapping("/screenMultiDashboard")
    public Result<Map<String, List<DataDashboardResult>>> screenMultiDashboard(@RequestBody DataScreenArg dataScreenArg,
                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        dataScreenArg.setTenantId(tenantId);
        return biManager.screenMultiDashboard(dataScreenArg);
    }

    @ApiOperation(value = "crm读写")
    @RequestMapping("/queryOperateInterfaceCount")
    public Result<Map<String,String>> queryOperateInterfaceCount(@RequestBody DataScreenArg dataScreenArg,
                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        dataScreenArg.setTenantId(tenantId);
        return biManager.queryOperateInterfaceCount(dataScreenArg);
    }

    @ApiOperation(value = "业务统计")
    @RequestMapping("/screenBusinessFlow")
    public Result<List<ScreenSystemDetailResult>> screenBusinessFlow(@RequestBody DataScreenArg dataScreenArg,
                                                         @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        dataScreenArg.setTenantId(tenantId);
        return biManager.systemBusinessFlowCount(dataScreenArg);
    }

    @ApiOperation(value = "同步实时")
    @RequestMapping("/screenRealTimeStatistics")
    public Result<List<ScreenIncrementDataResult>> screenRealTimeStatistics(@RequestBody DataScreenArg dataScreenArg,
                                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        dataScreenArg.setTenantId(tenantId);
        return biManager.incrementDataResult(dataScreenArg);
    }

    @ApiOperation(value = "返回筛选数据中心，crm对象的选项")
    @RequestMapping("/queryFilterOptional")
    public Result<DataScreenFilterResult> queryFilterOptional(@RequestBody CepArg cepArg,
                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        DataScreenFilterResult dataScreenResult = biManager.queryScreenFilter(tenantId);
        return Result.newSuccess(dataScreenResult);
    }

    @ApiOperation(value = "返回保存的筛选条件")
    @RequestMapping("/queryFilterInfo")
    public Result<ScreenFilterInfoResult> queryFilterInfo(@RequestBody CepArg cepArg,
                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        ScreenFilterInfoResult screenFilterInfoResult = biManager.queryTenantConfig(tenantId);
        return Result.newSuccess(screenFilterInfoResult);
    }

    @ApiOperation(value = "保存筛选")
    @RequestMapping("/saveFilterCondition")
    public Result<Void> saveFilterCondition(@RequestBody DataScreenArg saveScreenArg,
                                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        biManager.saveFilter(saveScreenArg,tenantId);
        return Result.newSuccess();
    }

    @ApiOperation(value = "返回相关的连接器")
    @RequestMapping("/queryDataCenterInfo")
    public Result<List<DataCenterInfoResult>> queryDataCenterInfo(@RequestBody CepArg cepArg,
                                                                  @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        //配置了筛选系统
        List<DataCenterInfoResult> dataCenterInfoResults = biManager.queryConnectInfos(tenantId,null,true);
        //刷描述，只有初始化的时候才刷描述
        biManager.brushField(tenantId);
        return Result.newSuccess(dataCenterInfoResults);
    }

    private void generateCache(DataScreenArg dataScreenArg){
        if(dataScreenArg.getDataCenterList().size()==0||dataScreenArg.getDataCenterList().size()>1){
            List<DataCenterInfoResult> dataCenterInfoResults = biManager.queryConnectInfos(dataScreenArg.getTenantId(),null,true);
            for (DataCenterInfoResult dataCenterInfoResult : dataCenterInfoResults) {
                DataScreenArg.ScreenOptional screenOptional=new DataScreenArg.ScreenOptional();
                screenOptional.setLabel(dataCenterInfoResult.getDataCenterName());
                screenOptional.setValue(dataCenterInfoResult.getId());
                dataScreenArg.setDataCenterList(Lists.newArrayList(screenOptional));
                biManager.statisticsBannerData(dataScreenArg);
                biManager.screenMultiDashboard(dataScreenArg);
                biManager.queryOperateInterfaceCount(dataScreenArg);
                biManager.incrementDataResult(dataScreenArg);
                biManager.systemBusinessFlowCount(dataScreenArg);
            }
        }

    }

    @ApiOperation(value = "触发缓存生效")
    @RequestMapping("/triggerData")
    public Result<Void> triggerData(@RequestBody DataScreenArg dataScreenArg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        dataScreenArg.setTenantId(getLoginUserTenantId());
        ParallelUtils.ParallelTask dataScreenExecutor = ParallelUtils.createDataScreenExecutor();
        dataScreenExecutor.submit(new Runnable() {
            @Override
            public void run() {
                generateCache(dataScreenArg);
            }
        });
        dataScreenExecutor.run();
        return Result.newSuccess();
    }
}
