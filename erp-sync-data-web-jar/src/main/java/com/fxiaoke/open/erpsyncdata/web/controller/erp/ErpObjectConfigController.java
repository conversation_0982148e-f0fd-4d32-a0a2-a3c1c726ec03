package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.fxiaoke.open.erpsyncdata.admin.arg.ErpDBProxyConfigArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ErpEaiConfigArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.admin.result.CheckDBProxyConfigResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectConfigService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

@Api(tags = "ERP对象配置")
@RestController
@RequestMapping({"cep/erpobj/config"})
@Slf4j
@ManagedTenantIntercept
public class ErpObjectConfigController extends AsyncSupportController {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private ErpObjectConfigService erpObjectConfigService;

    @ApiOperation(value = "获取对象EAI配置")
    @RequestMapping(value = "/getEaiConfig", method = RequestMethod.POST)
    public Result<ErpEaiConfigArg> loadEaiConfig(@RequestBody ObjectApiNameArg arg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        if (StringUtils.isEmpty(arg.getObjectApiName())&&StringUtils.isEmpty(arg.getSplitObjectApiName())) {
            return Result.newSystemError(I18NStringEnum.s50);
        }
        Result<ErpEaiConfigArg> erpEaiConfigArg =
          erpObjectConfigService.loadErpEaiConfig(tenantId, dataCenterId, arg.getObjectApiName(), arg.getSplitObjectApiName());
        return erpEaiConfigArg;
    }

    @ApiOperation(value = "新增更新对象EAI配置")
    @RequestMapping(value = "/updateEaiConfig", method = RequestMethod.POST)
    public Result<ErpEaiConfigArg> updateEaiConfig(@RequestBody ErpEaiConfigArg erpEaiConfigArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        Integer userId = getLoginUserId();
        erpEaiConfigArg.setTenantId(tenantId);
        erpEaiConfigArg.setDataCenterId(dataCenterId);
        erpEaiConfigArg.setLastModifyBy(String.valueOf(userId));
        erpEaiConfigArg.getDetails().forEach((k, v) -> {
            v.setTenantId(tenantId);
            v.setDataCenterId(dataCenterId);
            v.setLastModifyBy(String.valueOf(userId));
        });
        Result<ErpEaiConfigArg> result = erpObjectConfigService.updateErpEaiConfig(erpEaiConfigArg);
        return result;
    }


    @ApiOperation(value = "获取对象数据库配置")
    @RequestMapping(value = "/getDBConfig", method = RequestMethod.POST)
    public Result<ErpDBProxyConfigArg> loadDBConfig(@RequestBody ObjectApiNameArg arg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        if (StringUtils.isEmpty(arg.getObjectApiName())&&StringUtils.isEmpty(arg.getSplitObjectApiName())) {
            return Result.newSystemError(I18NStringEnum.s50);
        }
        Result<ErpDBProxyConfigArg> erpDBProxyConfig =
          erpObjectConfigService.loadErpDBProxyConfig(tenantId, dataCenterId, arg.getObjectApiName(),arg.getSplitObjectApiName());
        return erpDBProxyConfig;
    }

    @ApiOperation(value = "新增更新对象数据库配置")
    @RequestMapping(value = "/updateDBProxyConfig", method = RequestMethod.POST)
    public Result<ErpDBProxyConfigArg> updateDBProxyConfig(@RequestBody ErpDBProxyConfigArg erpDBProxyConfigArg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        Integer userId = getLoginUserId();
        erpDBProxyConfigArg.setTenantId(tenantId);
        erpDBProxyConfigArg.setDataCenterId(dataCenterId);
        erpDBProxyConfigArg.setLastModifyBy(String.valueOf(userId));
        erpDBProxyConfigArg.getDetails().forEach((k, v) -> {
            v.setTenantId(tenantId);
            v.setDataCenterId(dataCenterId);
            v.setLastModifyBy(String.valueOf(userId));
        });
        Result<ErpDBProxyConfigArg> result = erpObjectConfigService.updateErpDBProxyConfig(erpDBProxyConfigArg);
        return result;
    }


    @ApiOperation(value = "检查数据库配置")
    @RequestMapping(value = "/checkDBProxyConfig", method = RequestMethod.POST)
    public DeferredResult<Result<CheckDBProxyConfigResult>> checkDBProxyConfig(@RequestBody ErpDBProxyConfigArg erpDBProxyConfigArg,
                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        Integer userId = getLoginUserId();
        erpDBProxyConfigArg.setTenantId(tenantId);
        erpDBProxyConfigArg.setDataCenterId(dataCenterId);
        erpDBProxyConfigArg.setLastModifyBy(String.valueOf(userId));
        erpDBProxyConfigArg.setLang(lang);
        erpDBProxyConfigArg.getDetails().forEach((k, v) -> {
            v.setTenantId(tenantId);
            v.setDataCenterId(dataCenterId);
            v.setLastModifyBy(String.valueOf(userId));
        });
        return asyncExecuteWithTimeOutResult(i18NStringManager.get(I18NStringEnum.s3714, getLang(), getLoginUserTenantId()), deferredResult -> {
                    Result<CheckDBProxyConfigResult> result = erpObjectConfigService.checkDBProxyConfig(erpDBProxyConfigArg);
                    result.setI18nKey(null);
                    result.setI18nExtra(null);
                    deferredResult.setResult(result);
                }, 10,
                Result.newSystemError(I18NStringEnum.s51)
        );
    }
}
