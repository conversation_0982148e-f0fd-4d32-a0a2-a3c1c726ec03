{"type": "page", "remark": null, "title": "全集成流启用", "toolbar": [], "body": {"type": "crud", "name": "streams", "api": "../streamInfoQuery?tenantId=${TRIM(SPLIT(tenantIds,',')[0])}", "loadDataOnce": true, "defaultParams": {"perPage": 100}, "filter": {"body": [{"type": "textarea", "name": "tenantIds", "label": "tenantIds", "clearable": true, "minRows": 1, "trimContents": true, "placeholder": "企业id以 , 分隔"}, {"type": "divider"}, {"type": "input-text", "name": "upStreamId", "label": "上游企业Id", "trimContents": true, "placeholder": "优先级低"}, {"type": "divider"}, {"type": "group", "body": [{"type": "input-text", "name": "tenantId", "trimContents": true, "label": "启动通知企业Id"}, {"type": "input-text", "name": "employeeIds", "label": "启动通知员工Id", "trimContents": true, "placeholder": "以 , 分隔"}]}], "filterTogglable": true, "headerToolbar": ["pagination", "statistics", "filter-toggler"], "actions": [{"type": "button", "actionType": "ajax", "label": "全部启用", "method": "get", "loading": true, "loadingOn": "启用中", "api": {"method": "post", "url": "../tenantList/streamEnable", "data": {"tenantIds": "${tenantIds|split:,}", "notifyTenantId": "${tenantId}", "upStreamId": "${upStreamId}", "notifyEmployeeIds": "${employeeIds|split:,}"}}, "closeOnOutside": true}, {"type": "submit", "level": "primary", "label": "查询第一个企业的集成流"}]}, "columns": [{"name": "streamId", "label": "集成流id"}, {"name": "streamName", "label": "集成流名称"}, {"name": "sourceObjectApiName", "label": "源对象", "sortable": true}, {"name": "destObjectApiName", "label": "目标对象", "sortable": true}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "combineNum": 0, "bodyClassName": "panel-default"}}