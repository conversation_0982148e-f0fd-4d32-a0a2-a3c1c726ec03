{"type": "page", "name": "templateParse", "body": [{"type": "form", "title": "从现有企业解析", "api": "post:../template/parseTemplateFromStreams", "reload": "detailForm?streamInfos=${streamInfos}&erpObjInfos=${erpObjInfos}", "data": {"streamIds": []}, "body": [{"type": "input-group", "name": "input-group", "label": "企业Id", "body": [{"name": "tenantId", "label": "企业Id", "type": "input-text", "autoRefresh": false, "required": true}, {"type": "button", "label": "获取集成流", "level": "primary", "actionType": "reload", "target": "streamIds"}]}, {"name": "streamIds", "label": "集成流", "type": "select", "selectMode": "group", "source": {"method": "get", "url": "../template/listStreams?tenantId=${tenantId}", "autoRefresh": false}, "multiple": true, "joinValues": false, "extractValue": true, "searchable": true}]}, {"type": "form", "name": "detailForm", "title": "返回结果", "actions": [], "body": [{"name": "erpObjInfos", "label": "对象数据", "type": "textarea"}, {"name": "streamInfos", "label": "集成流数据", "type": "textarea"}]}]}