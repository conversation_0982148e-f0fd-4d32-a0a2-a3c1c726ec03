package com.fxiaoke.open.erpsyncdata.web.controller.inner

import com.facishare.uc.api.model.fscore.SimpleEnterprise
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult
import com.facishare.uc.api.service.EnterpriseEditionService
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import com.github.trace.TraceContext
import spock.lang.Specification
import spock.lang.Subject

/**
 *
 * <AUTHOR> (^_−)☆
 */
class BaseInnerControllerTest extends Specification {

    @Subject
    BaseInnerController controller

    EnterpriseEditionService enterpriseEditionService = Mock(EnterpriseEditionService)

    def setup() {
        controller = new BaseInnerController()
        controller.enterpriseEditionService = enterpriseEditionService
        TraceContext.get().setEi("1").setEmployeeId("100")
    }

    def "should return enterprise name when service returns valid result"() {
        given: "A valid tenant ID and a mock enterprise service response"
        def simpleEnterprise = new SimpleEnterprise(enterpriseName: "Test Enterprise")
        def getSimpleEnterpriseResult = new GetSimpleEnterpriseResult(simpleEnterprise: simpleEnterprise)
        enterpriseEditionService.getSimpleEnterprise(_) >> getSimpleEnterpriseResult

        when: "getEnterpriseName is called"
        def result = controller.getEnterpriseName()

        then: "The result should be the enterprise name"
        result == "Test Enterprise"
    }

    def "should return null when enterprise service returns null"() {
        given: "A valid tenant ID with a mock enterprise service response that returns null"
        def getSimpleEnterpriseResult = new GetSimpleEnterpriseResult(simpleEnterprise: null)
        enterpriseEditionService.getSimpleEnterprise(_) >> getSimpleEnterpriseResult

        when: "getEnterpriseName is called"
        def result = controller.getEnterpriseName()

        then: "The result should be null"
        result == null
    }

    def "should return null when enterpriseEditionService is not set"() {
        given: "The enterpriseEditionService is not set"
        controller.enterpriseEditionService = null

        when: "getEnterpriseName is called"
        def result = controller.getEnterpriseName()

        then: "The result should be null"
        result == null
    }

    def "should log error and return null when exception occurs"() {
        given: "A valid tenant ID and an exception thrown from the service"
        enterpriseEditionService.getSimpleEnterprise(_) >> { throw new RuntimeException("Service error") }

        when: "getEnterpriseName is called"
        def result = controller.getEnterpriseName()

        then: "The result should be null and an error should be logged"
        result == null
        1 * enterpriseEditionService.getSimpleEnterprise(_)
    }

    def "tenantId为空时异常"() {
        given: "A invalid tenant ID "
        TraceContext.get().setEi(null)
        BaseInnerController controller = new BaseInnerController() {
            String testGetTenantId() {
                return getTenantId()
            }
        }
        when: "getEnterpriseName is called"
        def ei = controller.getTenantId()

        then: "The result should be the enterprise name"
        thrown(ErpSyncDataException)
    }



    def "测试 getLocale 方法"() {
        given: "准备静态方法的模拟"
        TraceContext.get().setLocale("en_US")
        when: "调用 getLocale 方法"
        def result = controller.getLocale()

        then: "验证返回的结果"
        result == "en_US"
    }

    def "测试 getUserId 方法"() {
        when: "调用 getUserId 方法"
        def result = controller.getUserId()

        then: "验证返回的结果"
        result == 100
    }

    def "测试 getUserId 方法，当员工ID无效时"() {
        given: "准备静态方法的模拟"
        TraceContext.get().setEmployeeId(null)
        when: "调用 getUserId 方法"
        def result = controller.getUserId()
        then: "验证返回的结果为默认值"
        result == -10000
    }
}
