package com.facishare.open.erp.connertor.rest.annotation;

import com.facishare.open.erp.connertor.rest.codec.DefaultRestCodec;
import com.facishare.open.erp.connertor.rest.codec.RestCodeC;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023/3/21 14:12:16
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface RestResource {
    String value();

    String desc() default "";

    Class<? extends RestCodeC> codec() default DefaultRestCodec.class;
}
