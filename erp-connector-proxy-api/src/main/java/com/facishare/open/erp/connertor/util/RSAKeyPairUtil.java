package com.facishare.open.erp.connertor.util;

import java.io.FileOutputStream;
import java.io.ObjectOutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2022/9/28 11:18:57
 */
public class RSAKeyPairUtil {

    /**
     * 指定加密算法为RSA
     */
    private static final String ALGORITHM = "RSA";
    /**
     * 密钥长度，用来初始化
     */
    private static final int KEYSIZE = 1024;
    /**
     * 指定公钥存放文件
     */
    private static String PUBLIC_KEY_FILE = "/Users/<USER>/Downloads/rsa.pub";
    /**
     * 指定私钥存放文件
     */
    private static String PRIVATE_KEY_FILE = "/Users/<USER>/Downloads/rsa";

    public static void main(String[] args) throws Exception {
        // generateKeyPair();
        genKeyPair();
    }

    /**
     * 生成密钥对
     */
    private static void generateKeyPair() throws Exception {

        //     /** RSA算法要求有一个可信任的随机数源 */
        //     SecureRandom secureRandom = new SecureRandom();
        /** 为RSA算法创建一个KeyPairGenerator对象 */
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);

        /** 利用上面的随机数据源初始化这个KeyPairGenerator对象 */
        //     keyPairGenerator.initialize(KEYSIZE, secureRandom);
        keyPairGenerator.initialize(KEYSIZE);

        /** 生成密匙对 */
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        /** 得到公钥 */
        Key publicKey = keyPair.getPublic();

        /** 得到私钥 */
        Key privateKey = keyPair.getPrivate();


        /** 用对象流将生成的密钥写入文件 */
        try (final FileOutputStream outPub = new FileOutputStream(PUBLIC_KEY_FILE);
             ObjectOutputStream oos1 = new ObjectOutputStream(outPub);
             final FileOutputStream outPri = new FileOutputStream(PRIVATE_KEY_FILE);
             ObjectOutputStream oos2 = new ObjectOutputStream(outPri)) {
            oos1.writeObject(publicKey);
            oos2.writeObject(privateKey);
        } catch (Exception e) {
            throw e;
        }
    }

    private static void genKeyPair() throws NoSuchAlgorithmException, UnsupportedEncodingException {

        /** RSA算法要求有一个可信任的随机数源 */
        SecureRandom secureRandom = new SecureRandom();

        /** 为RSA算法创建一个KeyPairGenerator对象 */
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);

        /** 利用上面的随机数据源初始化这个KeyPairGenerator对象 */
        keyPairGenerator.initialize(KEYSIZE, secureRandom);
        //keyPairGenerator.initialize(KEYSIZE);

        /** 生成密匙对 */
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        /** 得到公钥 */
        Key publicKey = keyPair.getPublic();

        /** 得到私钥 */
        Key privateKey = keyPair.getPrivate();

        byte[] publicKeyBytes = publicKey.getEncoded();
        byte[] privateKeyBytes = privateKey.getEncoded();

        String publicKeyBase64 = new String(Base64.getEncoder().encode(publicKeyBytes), StandardCharsets.UTF_8);
        String privateKeyBase64 = new String(Base64.getEncoder().encode(privateKeyBytes), StandardCharsets.UTF_8);

        System.out.println("publicKeyBase64.length():" + publicKeyBase64.length());
        // System.out.println("publicKeyBase64:\n" + publicKeyBase64);
        System.out.println("publicKeyBase64--:\n" + publicKeyBase64.replace("\r\n","\\n"));

        System.out.println("privateKeyBase64.length():" + privateKeyBase64.length());
        // System.out.println("privateKeyBase64:\n" + privateKeyBase64);
        System.out.println("privateKeyBase64--:\n" + privateKeyBase64.replace("\r\n","\\n"));
    }
}
