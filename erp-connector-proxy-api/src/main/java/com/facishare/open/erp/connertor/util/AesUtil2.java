package com.facishare.open.erp.connertor.util;

import com.github.autoconf.ConfigFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

public class AesUtil2 {

    private static final int GCM_IV_LENGTH = 12;
    private static final int TAG_LENGTH = 128;
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static String secretKey = "4F84F4D78A0D7A90D482036CC45DA46F";

    static {
        ConfigFactory.getConfig("erp-sync-data-all", iConfig -> {
            secretKey = iConfig.get("aesSecretKey", "4F84F4D78A0D7A90D482036CC45DA46F");
        });
    }

    public static String encrypt(String data) throws Exception {
        byte[] iv = new byte[GCM_IV_LENGTH];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);
        SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(TAG_LENGTH, iv);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmParameterSpec);
        byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        byte[] result = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, result, 0, iv.length);
        System.arraycopy(encrypted, 0, result, iv.length, encrypted.length);
        return Base64.getEncoder().encodeToString(result);
    }

    public static String decrypt(String encode) throws Exception {
        final byte[] encrypted = Base64.getDecoder().decode(encode);

        byte[] iv = new byte[GCM_IV_LENGTH];
        System.arraycopy(encrypted, 0, iv, 0, iv.length);
        byte[] encryptedData = new byte[encrypted.length - iv.length];
        System.arraycopy(encrypted, iv.length, encryptedData, 0, encryptedData.length);
        SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(TAG_LENGTH, iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmParameterSpec);
        byte[] decrypted = cipher.doFinal(encryptedData);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) throws Exception {
        String data = "Hello World";
        String encrypted = encrypt(data);
        String decrypted = decrypt(encrypted);
        System.out.println("Original Data: " + data);
        System.out.println("Encrypted Data: " + encrypted);
        System.out.println("Decrypted Data: " + decrypted);
    }
}