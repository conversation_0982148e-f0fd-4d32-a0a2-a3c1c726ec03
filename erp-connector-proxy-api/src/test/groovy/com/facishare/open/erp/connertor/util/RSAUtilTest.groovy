package com.facishare.open.erp.connertor.util

import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/4/10 14:45:48
 */
class RSAUtilTest extends Specification {
    def "测试rsa"() {
        when:
        def publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZsDCDrT9IfYA43o/uoGQjJE5E3nLy8yOtpPyLLGA/PAfM6QsyLjfCYXN7RLGhgAmgNGQG2hwm+vJydkjZFcf7YGNPynRpoEk9CirLGuuUnwRZGHTdauM+uUwyGXX3YQT5kpNBj84gk9wAPjuaMKBxx/gQFZYOttsGRmb0KfIxAwIDAQAB"
        def privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJmwMIOtP0h9gDjej+6gZCMkTkTecvLzI62k/IssYD88B8zpCzIuN8Jhc3tEsaGACaA0ZAbaHCb68nJ2SNkVx/tgY0/KdGmgST0KKssa65SfBFkYdN1q4z65TDIZdfdhBPmSk0GPziCT3AA+O5owoHHH+BAVlg622wZGZvQp8jEDAgMBAAECgYAGqsEABVT4dRFJ5kXCKU0OxTVsAWZlKa0DjGO0OeGW/Kit9/IXZC/hC88xqsI16lMAkyU/stiDzGEWHBF+8VXyfXssDV+E2iHGaH+xldP7OTgAx7Xig2YtYV9e8/vHNJLvbP2mCwu+kYl5b60N9PUGo8DKR8NWbBThZDU/rsr8aQJBAM8mv5hXJfm2RLQn7ZfVIkmi+QUh/jr0qa+tL7RChT9bYmFmB2u3Kld1CoQixQ4IH0qCXqLRb88LSpoPu1ijcdUCQQC97f7QF9dba2QvpH1i4+0jS2d8KsgFGRqGoILEw4CB0ZIn/D064/DO0AuvP1CtRFiLBJe8R57eJjl1iQbts6t3AkBHuo4bTQnsRq3RiAXgTj/aKaF6ooOqnG9pUlmzXFlRT+5n9XFaXgk+UVu712JOcoeLQEtOq6DAm5NQd2QS1D2lAkANNxITyJTjd4VxCzDuQI2vdv+sVA22zRhbZa+zoSa/4aUZwyM/hbyrpy4o827Zn3fJtHgaUaFsM5yr4OzqfSXhAkEAlHO69y9a/htzssIs645BH/NHqCHCgU/2ZtGhCiVtlgdGDkpepzeJM6xX9t7Wbp6QXk+COCcX6gcEg8PNg0KQ+w=="

        def s = "{'a':'qwe'}"
        def enc = RSAUtil.encryptByPublicKey(s, publicKey)
        println enc
        def dec = RSAUtil.decryptByPrivateKey(enc, privateKey)
        println dec

        then:
        s == dec
    }
}
