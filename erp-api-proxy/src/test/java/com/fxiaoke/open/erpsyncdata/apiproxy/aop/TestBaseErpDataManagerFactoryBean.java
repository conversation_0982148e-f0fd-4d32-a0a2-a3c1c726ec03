package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import com.google.common.reflect.Reflection;
import org.springframework.beans.factory.FactoryBean;

/**
 * <AUTHOR>
 * @date 2024/1/26 10:33:43
 */
public class TestBaseErpDataManagerFactoryBean implements FactoryBean<TestBaseErpDataManager> {
    @Override
    public TestBaseErpDataManager getObject() throws Exception {
        return Reflection.newProxy(TestBaseErpDataManager.class, (proxy, method, args) -> {
            System.out.println("proxy:" + proxy + " method:" + method + " args:" + args);
            return null;
        });
    }

    @Override
    public Class<?> getObjectType() {
        return TestBaseErpDataManager.class;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }
}
