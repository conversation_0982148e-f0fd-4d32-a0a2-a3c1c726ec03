- name: 获取数据
  arg:
    tenantId: ${tenantId}
    objAPIName: customers
    dataId: "1"
  result:
    errCode: s106240000
    errMsg: 成功
    data:
      objAPIName: customers
      masterFieldVal:
        id: 1
        name: 张三
        address: 北京市朝阳区
        code: ZS001
        created_at: "2024-04-12 13:37:15.644"
        updated_at: "2024-04-12 13:37:15.644"
  config:
    querySql: |
      #YAML
      querySql: select * from customers where ${__where}
      batchWhere:
        - updated_at >= ${startTime__d} and updated_at < ${endTime__d}
      idWhere:
        - id = ${dataId}::INTEGER
  detailConfig: []

