package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2023/4/19 11:29:16
 */
class FilterStringBuilderTest extends Specification {

    static int num = 1

    @Unroll
    def "#id - 物料分组生成filter"() {
        when:
        def objApiName = "SAL_MATERIALGROUP"
        def tenantId = "84801"
        def k3DataManager = new K3DataManager(configCenterConfig: Mock(ConfigCenterConfig) {
            getNO_APPROVE_FORM_SET() >> []
            getK3_ALLOW_QUERY_FORBIDDATA() >> []
            isNoTimeFilterObj(*_) >> true
        })

        def arg = new TimeFilterArg()
        arg.setTenantId(tenantId)
        arg.setStartTime(1681788872000L)
        arg.setEndTime(1681875272000L)
        arg.setFilters(JSON.parseObject(JSON.toJSONString(filter), new TypeReference<List<List<FilterData>>>() {}))
        arg.setOperationType(o)

        arg.setObjAPIName(objApiName)

        def build = k3DataManager.getFilterString(arg)

        then:
        build == sql

        where:
        id            | o | filter                                                                                                                                                                                                                                                                                              || sql
        "默认修改"    | 2 | []                                                                                                                                                                                                                                                                                                  || ""


        "无时间修改"  | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IS"]]]                                                                                                                                                                                                                              || "(test='1')"
        "无时间修改2" | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"], ["fieldApiName": "test4", "fieldValue": ["val"], "operate": "LIKE"]]]             || "(test in ('1','2','3') and test2 like 'val')"


        "时间修改"    | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IS"]], [["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"]]]                                                                                                                                                  || "(test='1') or ((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ))"
        "时间修改1"   | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IS"], ["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"]]]                                                                                                                                                    || "(test='1' and (time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ))"
        "时间修改3"   | 2 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "time2", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"]]] || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val') or ((time2> {ts'2023-04-18 11:34:32'} and time2<= {ts'2023-04-19 11:34:32'} ) and test3 in ('1','2','3'))"


        "无时间作废"  | 3 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"]]]                                                                                                                                                                                                                              || "(test in ('1','2','3'))"
        "时间作废"    | 3 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"]]]                                                                            || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val') or (test3 in ('1','2','3'))"

    }

    @Unroll
    def "#id-生成filter"() {
        when:
        def objApiName = "STK_TransferDirect"
        def tenantId = "84801"
        def centerConfig = Mock(ConfigCenterConfig) {
            getNO_APPROVE_FORM_SET() >> { nApp ? [objApiName] : [] }
            getK3_ALLOW_QUERY_FORBIDDATA() >> { forb ? [tenantId + ":" + objApiName] : [] }
        }
        def k3DataManager = new K3DataManager(configCenterConfig: centerConfig)

//        2023-04-18 11:34:32 1681788872000L
//        2023-04-19 11:34:32 1681875272000L
        def arg = new TimeFilterArg()
        arg.setTenantId(tenantId)
        arg.setStartTime(1681788872000L)
        arg.setEndTime(1681875272000L)
        arg.setFilters(JSON.parseObject(JSON.toJSONString(filter), new TypeReference<List<List<FilterData>>>() {}))
        arg.setOperationType(o)

        arg.setObjAPIName(objApiName)

        def build = k3DataManager.getFilterString(arg)

        then:
        build == sql

        where:
        id                    | nApp  | forb  | o | filter                                                                                                                                                                                                                                                                                                   || sql
        num++ + "默认修改"    | false | false | 2 | []                                                                                                                                                                                                                                                                                                       || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FApproveDate> {ts'2023-04-18 11:34:32'} and FApproveDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B'"
        num++ + "默认修改2"   | true  | false | 2 | []                                                                                                                                                                                                                                                                                                       || "((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} ) and FCancelStatus!='B')"
        num++ + "默认修改3"   | false | true  | 2 | []                                                                                                                                                                                                                                                                                                       || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FApproveDate> {ts'2023-04-18 11:34:32'} and FApproveDate<= {ts'2023-04-19 11:34:32'} )) or ((FForbidDate> {ts'2023-04-18 11:34:32'} and FForbidDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B'"
        num++ + "默认修改4"   | true  | true  | 2 | []                                                                                                                                                                                                                                                                                                       || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FForbidDate> {ts'2023-04-18 11:34:32'} and FForbidDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B'"


        num++ + "默认作废"    | false | false | 3 | []                                                                                                                                                                                                                                                                                                       || "((FCancelDate> {ts'2023-04-18 11:34:32'} and FCancelDate<= {ts'2023-04-19 11:34:32'} ) and FCancelStatus='B')"
        num++ + "默认作废2"   | true  | false | 3 | []                                                                                                                                                                                                                                                                                                       || "((FCancelDate> {ts'2023-04-18 11:34:32'} and FCancelDate<= {ts'2023-04-19 11:34:32'} ) and FCancelStatus='B')"
        num++ + "默认作废3"   | false | true  | 3 | []                                                                                                                                                                                                                                                                                                       || "((FCancelDate> {ts'2023-04-18 11:34:32'} and FCancelDate<= {ts'2023-04-19 11:34:32'} ) and FCancelStatus='B')"
        num++ + "默认作废4"   | true  | true  | 3 | []                                                                                                                                                                                                                                                                                                       || "((FCancelDate> {ts'2023-04-18 11:34:32'} and FCancelDate<= {ts'2023-04-19 11:34:32'} ) and FCancelStatus='B')"


        num++ + "无时间修改"  | false | false | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IS"]]]                                                                                                                                                                                                                                   || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FApproveDate> {ts'2023-04-18 11:34:32'} and FApproveDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B' and test='1'"
        num++ + "无时间修改2" | false | false | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"]]]                                                                                                                                                                                                                                   || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FApproveDate> {ts'2023-04-18 11:34:32'} and FApproveDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B' and test in ('1','2','3')"
        num++ + "无时间修改3" | true  | true  | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"]]]                                                                                                                                                                                                                                   || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FForbidDate> {ts'2023-04-18 11:34:32'} and FForbidDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B' and test in ('1','2','3')"
        num++ + "无时间修改4" | true  | true  | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]]]                                                                                                                                                              || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FForbidDate> {ts'2023-04-18 11:34:32'} and FForbidDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B' and test in ('1','2','3') and test2 like 'val'"
        num++ + "无时间修改5" | true  | true  | 2 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"], ["fieldApiName": "test4", "fieldValue": ["val"], "operate": "LIKE"]]]                  || "(((FModifyDate> {ts'2023-04-18 11:34:32'} and FModifyDate<= {ts'2023-04-19 11:34:32'} )) or ((FForbidDate> {ts'2023-04-18 11:34:32'} and FForbidDate<= {ts'2023-04-19 11:34:32'} ))) and FCancelStatus!='B' and test in ('1','2','3') and test2 like 'val'"


        num++ + "无时间作废"  | false | false | 3 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"]]]                                                                                                                                                                                                                                   || "((FCancelDate> {ts'2023-04-18 11:34:32'} and FCancelDate<= {ts'2023-04-19 11:34:32'} ) and FCancelStatus='B' and test in ('1','2','3'))"
        num++ + "无时间作废2" | true  | true  | 3 | [[["fieldApiName": "test", "fieldValue": [1, 2, 3], "operate": "IN"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]]]                                                                                                                                                              || "((FCancelDate> {ts'2023-04-18 11:34:32'} and FCancelDate<= {ts'2023-04-19 11:34:32'} ) and FCancelStatus='B' and test in ('1','2','3') and test2 like 'val')"


        num++ + "时间修改1"   | true  | true  | 2 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]]]                                                                                                                                                        || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val' and FCancelStatus!='B')"
        num++ + "时间修改2"   | true  | true  | 2 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "time2", "isVariableBetween": true, "fieldType": "date_time"], ["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"]]] || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val' and FCancelStatus!='B') or ((time2> {ts'2023-04-18 11:34:32'} and time2<= {ts'2023-04-19 11:34:32'} ) and test3 in ('1','2','3') and FCancelStatus!='B')"
        num++ + "时间修改3"   | true  | true  | 2 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"]]]                                                                                 || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val' and FCancelStatus!='B') or (test3 in ('1','2','3') and FCancelStatus!='B')"

        num++ + "时间作废1"   | true  | true  | 3 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]]]                                                                                                                                                        || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val' and FCancelStatus='B')"
        num++ + "时间作废2"   | true  | true  | 3 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "time2", "isVariableBetween": true, "fieldType": "date_time"], ["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"]]] || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val' and FCancelStatus='B') or ((time2> {ts'2023-04-18 11:34:32'} and time2<= {ts'2023-04-19 11:34:32'} ) and test3 in ('1','2','3') and FCancelStatus='B')"
        num++ + "时间作废3"   | true  | true  | 3 | [[["fieldApiName": "time", "isVariableBetween": true, "fieldType": "date"], ["fieldApiName": "test2", "fieldValue": ["val"], "operate": "LIKE"]], [["fieldApiName": "test3", "fieldValue": [1, 2, 3], "operate": "IN"]]]                                                                                 || "((time> {ts'2023-04-18 11:34:32'} and time<= {ts'2023-04-19 11:34:32'} ) and test2 like 'val' and FCancelStatus='B') or (test3 in ('1','2','3') and FCancelStatus='B')"
    }

    def "测试Match"() {
        expect:
        def list = Lists.newArrayList()
        println list.stream().anyMatch({ it -> false })
        println list.stream().anyMatch({ it -> true })
        println list.stream().allMatch({ it -> false })
        println list.stream().allMatch({ it -> true })
    }
}
