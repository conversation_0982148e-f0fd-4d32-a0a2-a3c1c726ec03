package com.fxiaoke.open.erpsyncdata.apiproxy.manager


import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDBProxyConfigEntity
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class DBProxyManagerTest extends Specification {


    static  {
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.name", "fs-erp-sync-data-api")
    }

    def "Name"() {
        expect:
        true
    }

    def "testBuildQueryRequestArg"(String inputSql, String sql) {
        expect:
        def dbProxyManager = new DBProxyManager()
        ErpDBProxyConfigEntity entity = new ErpDBProxyConfigEntity()
        entity.setQuerySql(inputSql)
        entity.setDbKey("id")
        def arg = dbProxyManager.buildQueryRequestArg(entity, "dataIdTest")
        sql == arg.getAdaptSqlParams().getMasterSql()
        println(arg)
        where:
        inputSql << [
                'SELECT id,update_time,* FROM customer WHERE\n' +
                        '/*BatchCondBegin*/\n' +
                        ' update_time >= ${START_TIME} and update_time <= ${END_TIME}\n' +
                        '/*BatchCondEnd*/'
        ]
        sql << [
                'SELECT id,update_time,* FROM customer WHERE\n' +
                        ' and (id=\'dataIdTest\' )'
        ]

    }

    static void main(String[] args) {
        def inputSql = 'SELECT id,update_time,* FROM customer WHERE\n' +
                '/*BatchCondBegin*/\n' +
                ' update_time >= ${START_TIME} and update_time <= ${END_TIME}\n' +
                '/*BatchCondEnd*/'
        def dbProxyManager = new DBProxyManager()
        ErpDBProxyConfigEntity entity = new ErpDBProxyConfigEntity(
                querySql: inputSql,
                dbKey: 'id'

        )
        def arg = dbProxyManager.buildQueryRequestArg(entity, "dataIdTest")
    }
}
