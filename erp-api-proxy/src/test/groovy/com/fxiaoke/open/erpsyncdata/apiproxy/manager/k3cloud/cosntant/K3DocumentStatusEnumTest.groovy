package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ResponseStatus
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.Submit
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.atomic.AtomicInteger

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum.*

/**
 * <AUTHOR> 
 * @date 2023/4/27 10:19:27
 */
class K3DocumentStatusEnumTest extends Specification {

    @Unroll
    def "#from -> #to"() {
        when:
        AtomicInteger submitCount = new AtomicInteger(0)
        AtomicInteger auditCount = new AtomicInteger(0)
        AtomicInteger unAuditCount = new AtomicInteger(0)
        AtomicInteger cancelCount = new AtomicInteger(0)
        def success = Result.newSuccess(new Submit.Result(
                result: new Submit.SubmitResult(
                        responseStatus: new ResponseStatus(
                                isSuccess: true
                        )
                )
        ))
        def mock = Mock(K3CloudApiClient) {
            it.submit(*_) >> {
                submitCount.incrementAndGet()
                return success
            }
            it.audit(*_) >> {
                auditCount.incrementAndGet()
                return success
            }
            it.unAudit(*_) >> {
                unAuditCount.incrementAndGet()
                return success
            }
            it.cancelAssign(*_) >> {
                cancelCount.incrementAndGet()
                return success
            }
        }

        def result = Result.newSuccess(new ErpIdResult())
        K3DocumentStatusEnum.changeStatus(from, to, mock, "zx", "asd", result, null)

        then:
        result.getErrMsg() == "成功"
        submitCount.get() == s
        auditCount.get() == a
        unAuditCount.get() == u
        cancelCount.get() == c

        where:
//        升
        from | to  || s | a | u | c
        "A"  | "B" || 1 | 0 | 0 | 0
        "A"  | "C" || 1 | 1 | 0 | 0
        "B"  | "C" || 0 | 1 | 0 | 0

        "D"  | "C" || 1 | 1 | 0 | 0
        "D"  | "B" || 1 | 0 | 0 | 0

//        降
        "B"  | "D" || 0 | 0 | 0 | 1
        "C"  | "D" || 0 | 0 | 1 | 0

//        特殊
        "C"  | "B" || 1 | 0 | 1 | 0

//        不变
        "A"  | "A" || 0 | 0 | 0 | 0
        "B"  | "B" || 0 | 0 | 0 | 0

//        错误
        "C"  | "A" || 0 | 0 | 0 | 0

        "Z"  | "B" || 0 | 0 | 0 | 0
        "A"  | "Z" || 0 | 0 | 0 | 0

        "B"  | "A" || 0 | 0 | 0 | 0
        "A"  | "D" || 0 | 0 | 0 | 0
        "D"  | "A" || 0 | 0 | 0 | 0
    }

    @Unroll
    def "#from -> #to url"() {
        when:
        def urls = K3DocumentStatusEnum.getErpObjInterfaceUrls(from, to)

        then:
        urls == url

        where:
//        升
        from | to  || url
        "A"  | "B" || [submit]
        "A"  | "C" || [submit, audit]
        "B"  | "C" || [audit]

        "D"  | "C" || [submit, audit]
        "D"  | "B" || [submit]

//        降
        "B"  | "D" || [cancelAssign]
        "C"  | "D" || [unAudit]

//        特殊
        "C"  | "B" || [unAudit, submit]

//        不变
        "A"  | "A" || []
        "B"  | "B" || []

//        错误
        "C"  | "A" || []

        "Z"  | "B" || []
        "A"  | "Z" || []

        "B"  | "A" || []
        "A"  | "D" || []
        "D"  | "A" || []
    }
}
