package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud

import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/12
 */
class QueryStdPushArgTest extends Specification {

    def "测试清除重复字段"(String before, String after) {
        given:
        def arg = new QueryArg()
        arg.setFieldKeys(before)
        arg.removeDuplicateFieldKeys()
        expect:
        Objects.equals(after,arg.getFieldKeys())
        where:
        before                  | after
        "id"                    | "id"
        "id,number"             | "id,number"
        null                    | null
        ""                      | ""
        "id,number,id,f1,f2,f3" | "id,number,f1,f2,f3"
        "id,number,Id,f1,id,f2,f3" | "id,number,f1,f2,f3"
    }
}
