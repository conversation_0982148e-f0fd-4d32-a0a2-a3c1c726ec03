package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class SOAPClientPojo implements Serializable {

    private String tenantId;
    private String wsdlLocation;
    private String targetNamespace;
    private String serviceName;
    private String prefix;
    private String portName;
    private Integer connectTimeOut=1000*60;
    private Integer requestTimeOut=1000*60;
    private String userName;
    private String password;
}
