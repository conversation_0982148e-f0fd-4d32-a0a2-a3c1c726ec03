package com.fxiaoke.open.erpsyncdata.apiproxy.model.admin;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Builder
public class FunctionInfo {
    @SerializedName("api_name")
    private String apiName;
    private String application;
    @SerializedName("binding_object_api_name")
    private String bindingObjectApiName;
    private String body;
    @SerializedName("commit_log")
    private String commitLog;
    @SerializedName("create_by")
    private String createBy;

    @SerializedName("create_time")
    private Long createTime;

    @SerializedName("data_source")
    private String dataSource;
    @SerializedName("dialog_status")
    @Builder.Default
    private String dialogStatus = "open";
    @SerializedName("function_name")
    private String functionName;
    @SerializedName("is_active")
    @Builder.Default
    private boolean isActive = true;

    @SerializedName("is_control")
    @Builder.Default
    private boolean isControl = false;

    @SerializedName("is_current")
    @Builder.Default
    private boolean isCurrent = true;

    @Builder.Default
    private int lang = 0;

    @SerializedName("name_space")
    private String nameSpace;

    @Builder.Default
    private List<Object> parameters = new ArrayList<>();

    private String remark;

    @SerializedName("return_type")
    private String returnType;

    private String status;
    private String type;

    @SerializedName("update_time")
    private Long updateTime;

    private Integer version;
}
