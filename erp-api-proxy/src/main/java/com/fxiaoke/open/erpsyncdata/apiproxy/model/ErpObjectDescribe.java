package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.LinkedHashMap;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/17
 */
@Getter
@Setter
@ToString
public class ErpObjectDescribe {

    /**
     * 真实对象
     */
    private ErpObjectEntity realObj;

    /**
     * 拆分对象描述map，key：splitObjApiName,value:拆分对象描述
     */
    private LinkedHashMap<String, SplitObjDescribe> splitObjDescribes;

    /**
     * 拆分对象描述
     */
    @ToString
    @Getter
    @Setter
    public static class SplitObjDescribe {
        /**
         * 是否主对象
         */
        private boolean isMain;
        private String parentObjApiName;
        /**
         * 拆分对象列表
         */
        private ErpObjectEntity splitObj;
        /**
         * 拆分对象关系
         */
        private ErpObjectRelationshipEntity objRelation;
        /**
         * 字段列表，key：fieldApiName
         */
        private LinkedHashMap<String,ErpObjectFieldEntity> fields = new LinkedHashMap<>();
        /**
         * 字段扩展列表,key：fieldApiName
         *
         * 会比fields多明细字段
         */
        private LinkedHashMap<String,ErpFieldExtendEntity> fieldExtends= new LinkedHashMap<>();
    }

}
