package com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandlerImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SyncCpqService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:48 2020/12/22
 * @Desc:
 */
@Component
@Slf4j
public class SourceObjSalesOrderHandler implements SpecialObjHandler {
    @Autowired
    private SyncCpqService syncCpqService;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;

    private static String FAKEORDERNO="fakeOrderNo";

    @Override
    public void afterQueryListData(TimeFilterArg timeFilterArg, List<StandardData> standardDataList,String dataCenterId) {

    }

    @Override
    public void afterReSyncDataById(ErpIdArg erpIdArg, List<StandardData> standardDataList,String dataCenterId) {

    }

    @Override
    public String getObjApiName() {
        return ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName();
    }

    @Override
    public void beforeCreateErpObjData(String tenantId, StandardData standardData,String dataCenterId) {
        this.beforeOperateSalesOrder2Sap(tenantId, standardData,dataCenterId);
    }

    @Override
    public void afterCreateErpObjData(String tenantId, StandardData standardData, Result<ErpIdResult> result,String dataCenterId) {
        this.afterOperateSalesOrder2Sap(tenantId, standardData, result,dataCenterId);
    }

    @Override
    public void beforeUpdateErpObjData(String tenantId, StandardData standardData,String dataCenterId) {
        this.beforeOperateSalesOrder2Sap(tenantId, standardData,dataCenterId);
    }

    @Override
    public void afterUpdateErpObjData(String tenantId, StandardData standardData, Result<ErpIdResult> result,String dataCenterId) {
        this.afterOperateSalesOrder2Sap(tenantId, standardData, result,dataCenterId);
    }

    public void beforeOperateSalesOrder2Sap(String tenantId, StandardData standardData,String dataCenterId) {
        final OperateSalesOrder2SapParams result = getOperateSalesOrder2SapParamsByDcId(tenantId, dataCenterId);
        if (result == null){
            return;
        }

        List<ObjectData> salesOrderProducts = standardData.getDetailFieldVals().get(result.sapSalesOrderProductObjApiName);//sap销售订单明细数据
        if(CollectionUtils.isEmpty(salesOrderProducts)){
            return;
        }
        List<ObjectData> parentOrStandardSalesOrderProduct= Lists.newArrayList();
        List<ObjectData> childSalesOrderProduct= Lists.newArrayList();
        int i=1;
        for(ObjectData salesOrderProduct:salesOrderProducts){
            salesOrderProduct.put(FAKEORDERNO,i++);//序号,后处理需要用到
            if(salesOrderProduct.get(result.root_prod_pkg_key)==null){//标准产品(不带cpq)
                parentOrStandardSalesOrderProduct.add(salesOrderProduct);
            }else{
                if(salesOrderProduct.get(result.parent_prod_pkg_key)==null){//CPQ父产品
                    parentOrStandardSalesOrderProduct.add(salesOrderProduct);
                }else {//CPQ子产品
                    childSalesOrderProduct.add(salesOrderProduct);
                }
            }
        }
        standardData.getDetailFieldVals().put(result.sapSalesOrderProductObjApiName,parentOrStandardSalesOrderProduct);
        standardData.getDetailFieldVals().putIfAbsent(ObjectApiNameEnum.SAP_CHILD_SALESORDER_PRODUCT_OBJ.getObjApiName(),childSalesOrderProduct);
    }

    public List<SyncPloyDetailEntity> getAllSapCrm2ErpCpqPloyDetails(String tenantId) {
        List<SyncPloyDetailEntity> ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantTypeAndObjApiName(tenantId, TenantType.CRM, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName());
        return ployDetailEntities.stream()
                .filter(syncPloyDetailEntity -> {
                    // 兼容部分连接器已经删除
                    return Objects.nonNull(erpConnectInfoManager.getByIdAndTenantId(tenantId, syncPloyDetailEntity.getDestDataCenterId())) &&
                            Objects.nonNull(getOperateSalesOrder2SapParams(tenantId, syncPloyDetailEntity));
                })
                .collect(Collectors.toList());
    }

    public @Nullable OperateSalesOrder2SapParams getOperateSalesOrder2SapParamsByDcId(String tenantId, String dataCenterId) {
        SyncPloyDetailEntity ployDetail = syncCpqService.getPloyDetailByTypeAndTenantIdAndObjApiName(SyncPloyTypeEnum.OUTPUT.getType(), tenantId, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName(), dataCenterId);
        if(ployDetail==null){
            log.warn("ployDetail is null type={},tenantId={},objApiName={}",SyncPloyTypeEnum.OUTPUT.getType(), tenantId, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName());
            return null;
        }
        return getOperateSalesOrder2SapParams(tenantId, ployDetail);
    }

    private @Nullable OperateSalesOrder2SapParams getOperateSalesOrder2SapParams(String tenantId, SyncPloyDetailEntity ployDetail) {
        String dataCenterId = ployDetail.getDestDataCenterId();
        if (!syncCpqService.isNeedHandleSapCpq(tenantId, dataCenterId)) {//如果渠道不是sap,或者没有开启cpq直接返回
            return null;
        }
        String prod_pkg_key=null,parent_prod_pkg_key=null,root_prod_pkg_key=null;
        String sapFakeSalesOrderProductObjApiName=null;//找到订单产品的sap侧的虚拟apiName
        String sapSalesOrderProductObjApiName=null;//找到订单产品的sap侧的apiName
        for(DetailObjectMappingsData.DetailObjectMappingData detailObj: ployDetail.getDetailObjectMappings()){
            if(ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName().equals(detailObj.getSourceObjectApiName())){
                sapFakeSalesOrderProductObjApiName=detailObj.getDestObjectApiName();
                for(FieldMappingData fieldMappingData:detailObj.getFieldMappings()){
                    if("prod_pkg_key".equals(fieldMappingData.getSourceApiName())){
                        prod_pkg_key=fieldMappingData.getDestApiName();
                    }else if("parent_prod_pkg_key".equals(fieldMappingData.getSourceApiName())){
                        parent_prod_pkg_key=fieldMappingData.getDestApiName();
                    }if("root_prod_pkg_key".equals(fieldMappingData.getSourceApiName())){
                        root_prod_pkg_key=fieldMappingData.getDestApiName();
                    }
                }
                break;
            }
        }
        if(sapFakeSalesOrderProductObjApiName==null){
            log.warn("sapFakeSalesOrderProductObjApiName is null ployDetail={}", ployDetail);
            return null;
        }
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, Lists.newArrayList(sapFakeSalesOrderProductObjApiName));
        if(CollectionUtils.isNotEmpty(erpObjectEntities)){
            sapSalesOrderProductObjApiName=erpObjectEntities.get(0).getErpObjectExtendValue();
        }
        if(sapSalesOrderProductObjApiName==null){
            log.warn("sapSalesOrderProductObjApiName is null ployDetail={}", ployDetail);
            return null;
        }
        if(prod_pkg_key==null||parent_prod_pkg_key==null||root_prod_pkg_key==null){
            log.warn("some fields(prod_pkg_key、parent_prod_pkg_key、root_prod_pkg_key) must be binded but not binding ployDetail={}", ployDetail);
            return null;
        }
        OperateSalesOrder2SapParams result = new OperateSalesOrder2SapParams(parent_prod_pkg_key, root_prod_pkg_key, sapSalesOrderProductObjApiName);
        return result;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OperateSalesOrder2SapParams {
        public String parent_prod_pkg_key;
        public String root_prod_pkg_key;
        public String sapSalesOrderProductObjApiName;
    }

    private void afterOperateSalesOrder2Sap(String tenantId, StandardData standardData, Result<ErpIdResult> result,String dataCenterId) {
        if (!syncCpqService.isNeedHandleSapCpq(tenantId, dataCenterId)) {//如果渠道不是sap,或者没有开启cpq直接返回
            return;
        }
        SyncPloyDetailEntity ployDetail = syncCpqService.getPloyDetailByTypeAndTenantIdAndObjApiName(SyncPloyTypeEnum.OUTPUT.getType(), tenantId, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName(),dataCenterId);
        if(ployDetail==null){
            log.warn("ployDetail is null type={},tenantId={},objApiName={}",SyncPloyTypeEnum.OUTPUT.getType(), tenantId, ObjectApiNameEnum.FS_SALESORDER_OBJ.getObjApiName());
            return;
        }
        String sapFakeSalesOrderProductObjApiName=null;//找到订单产品的sap侧的虚拟apiName
        String sapSalesOrderProductObjApiName=null;//找到订单产品的sap侧的apiName
        for(DetailObjectMappingsData.DetailObjectMappingData detailObj:ployDetail.getDetailObjectMappings()){
            if(ObjectApiNameEnum.FS_SALESORDER_PRODUCT_OBJ.getObjApiName().equals(detailObj.getSourceObjectApiName())){
                sapFakeSalesOrderProductObjApiName=detailObj.getDestObjectApiName();
                break;
            }
        }
        if(sapFakeSalesOrderProductObjApiName==null){
            log.warn("sapFakeSalesOrderProductObjApiName is null ployDetail={}",ployDetail);
            return;
        }
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, Lists.newArrayList(sapFakeSalesOrderProductObjApiName));
        if(CollectionUtils.isNotEmpty(erpObjectEntities)){
            sapSalesOrderProductObjApiName=erpObjectEntities.get(0).getErpObjectExtendValue();
        }
        if(sapSalesOrderProductObjApiName==null){
            log.warn("sapSalesOrderProductObjApiName is null ployDetail={}",ployDetail);
            return;
        }
        ErpIdResult erpIdResult = result.getData();
        if(erpIdResult!=null&&erpIdResult.getDetailDataIds()!=null){
            List<String> parentIds = erpIdResult.getDetailDataIds().get(sapSalesOrderProductObjApiName);
            List<String> childIds = erpIdResult.getDetailDataIds().get(ObjectApiNameEnum.SAP_CHILD_SALESORDER_PRODUCT_OBJ.getObjApiName());
            if(CollectionUtils.isNotEmpty(parentIds)&&CollectionUtils.isNotEmpty(childIds)&& standardData.getDetailFieldVals().get(sapSalesOrderProductObjApiName).size()==parentIds.size()&&
                    standardData.getDetailFieldVals().get(ObjectApiNameEnum.SAP_CHILD_SALESORDER_PRODUCT_OBJ.getObjApiName()).size()==childIds.size()){
                List<String> detailIds=Lists.newArrayList();
                Map<Integer,String> orderNo2Id= Maps.newHashMap();
                for(int i=0;i<standardData.getDetailFieldVals().get(sapSalesOrderProductObjApiName).size();i++){
                    ObjectData objectData=standardData.getDetailFieldVals().get(sapSalesOrderProductObjApiName).get(i);
                    orderNo2Id.put(Integer.valueOf(String.valueOf(objectData.get(FAKEORDERNO))),parentIds.get(i));
                }
                for(int i=0;i<standardData.getDetailFieldVals().get(ObjectApiNameEnum.SAP_CHILD_SALESORDER_PRODUCT_OBJ.getObjApiName()).size();i++){
                    ObjectData objectData=standardData.getDetailFieldVals().get(ObjectApiNameEnum.SAP_CHILD_SALESORDER_PRODUCT_OBJ.getObjApiName()).get(i);
                    orderNo2Id.put(Integer.valueOf(String.valueOf(objectData.get(FAKEORDERNO))),childIds.get(i));
                }
                for(int i=1;i<=orderNo2Id.size();i++){
                    detailIds.add(orderNo2Id.get(i));
                }
                erpIdResult.getDetailDataIds().put(sapSalesOrderProductObjApiName,detailIds);
                erpIdResult.getDetailDataIds().remove(ObjectApiNameEnum.SAP_CHILD_SALESORDER_PRODUCT_OBJ.getObjApiName());
            }
        }
    }
}
