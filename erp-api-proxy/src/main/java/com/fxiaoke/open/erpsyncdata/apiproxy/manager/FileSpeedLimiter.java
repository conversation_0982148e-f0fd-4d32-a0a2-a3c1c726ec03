package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 16:33 2023/4/19
 * @Desc:
 */
@Component
@Data
@Slf4j
public class FileSpeedLimiter {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    private Map<String, RateLimiter> tenantRateLimiter = Maps.newConcurrentMap();
    private Long lastClearTime = System.currentTimeMillis();
    //容器本地限速,

    public synchronized RateLimiter getFileRateLimiter(String tenantId) {
        if (System.currentTimeMillis() - lastClearTime > 1000 * 60 * 10L) {//超过10分钟，重置一下map
            tenantRateLimiter.clear();
            lastClearTime = System.currentTimeMillis();
        }
        if (!tenantRateLimiter.containsKey(tenantId)) {
            Map<String, Integer> fileUploadRateLimit = tenantConfigurationManager.getFileUploadRateLimit();
            Integer tenantLimit = fileUploadRateLimit.get(tenantId);
            if (tenantLimit == null) {
                tenantLimit = fileUploadRateLimit.get("defaultTenant");//默认单企业限速
                if (tenantLimit == null) {//
                    tenantLimit = 1024 * 1024 * 2;
                }
                RateLimiter rateLimiter = RateLimiter.create(tenantLimit);
                tenantRateLimiter.put(tenantId, rateLimiter);
            }
        }
        return tenantRateLimiter.get(tenantId);
    }

    /**
     * 文件上传下载限速
     * @param tenantId
     * @param size
     */
    public void fileRateLimiter(String tenantId, Integer size) {
        if(size==null){
            return;
        }
        String allTenant = "allTenant";
        RateLimiter allTenantRateLimiter = this.getFileRateLimiter(allTenant);
        if (allTenantRateLimiter != null) {
            allTenantRateLimiter.acquire(size);//所有企业
        }
        RateLimiter rateLimiter = this.getFileRateLimiter(tenantId);
        if (rateLimiter != null) {
            rateLimiter.acquire(size);//单企业
        }
    }
}
