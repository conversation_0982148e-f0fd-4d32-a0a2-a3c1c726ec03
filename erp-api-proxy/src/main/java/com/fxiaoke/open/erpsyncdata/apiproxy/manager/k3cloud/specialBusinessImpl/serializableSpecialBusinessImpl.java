package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;


import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> (^_−)☆
 * @date 2021/03/11
 */
@Slf4j
@Component("BD_SerialMainFile")
public class serializableSpecialBusinessImpl extends CommonStockBusinessImpl implements SpecialBusiness {


    /**
     * 查看erp数据后置动作
     * 一些单据增加虚拟字段
     *
     * @param erpIdArg
     * @param standardData
     * @param apiClient
     */
    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        //增加虚拟字段 仓库混合字段
        if (standardData == null) {
            return;
        }
        ObjectData masterFieldVal = standardData.getMasterFieldVal();
        if (masterFieldVal != null) {
            String stockNumber = masterFieldVal.getString("FSubHeadEntity.FStockId.FNumber");
            String materialIdNumber = masterFieldVal.getString("FMaterialID.FNumber");
            masterFieldVal.put("StockMultiCode", Joiner.on("#").useForNull("null").join(stockNumber, materialIdNumber));
            String warehouseComId = getWarehouseComId(stockNumber,
                    masterFieldVal.getString("FSubHeadEntity.FStockLocId.Id"),
                    apiClient);
            masterFieldVal.put("warehouseComId",warehouseComId);
        }
        log.info("serializableSpecialBusinessImpl.afterRunView,masterFieldVal={}",masterFieldVal);
    }
}
