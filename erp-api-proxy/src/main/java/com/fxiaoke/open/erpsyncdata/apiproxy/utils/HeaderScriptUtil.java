package com.fxiaoke.open.erpsyncdata.apiproxy.utils;


import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.util.LangUtil;

import javax.script.*;
import java.util.Map;
import java.util.Set;

import static io.protostuff.CollectionSchema.MessageFactories.Set;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */



@Slf4j
public class HeaderScriptUtil {

    private static Map<String, CompiledScript> compileHeaderScriptMap = Maps.newHashMap();
    public static CompiledScript getCompiledScript(String scriptCode) {
        try {
            ScriptEngineManager manager = new ScriptEngineManager();
            Compilable engine = (Compilable) manager.getEngineByName("groovy");
            if(null == compileHeaderScriptMap.get(scriptCode)) {
                compileHeaderScriptMap.put(scriptCode, engine.compile(scriptCode));
            }
            return compileHeaderScriptMap.get(scriptCode);
        }catch (Exception e) {
            log.error("compile groovy code:{}, exception, ", scriptCode, e);
            return null;
        }
    }

    public static Map<String, String> getHeaderMap(String tenantId,String scriptCode, String url) {
        if(StringUtils.isEmpty(scriptCode)) {
            return Maps.newHashMap();
        }

        //循环三次获取header
        for (int i = 0; i < 3; i++) {
            StopWatch stopWatch = StopWatch.createStarted();
            try {
                // 这个地方需要使用缓存，达到编译一次，多次执行。
                CompiledScript script = getCompiledScript(scriptCode);

                if (script != null) {
                    ScriptContext scriptContext = new SimpleScriptContext();

                    Bindings bindings = scriptContext.getBindings(100);
                    bindings.put("url", url);
                    bindings.put("log", log);
                    Map<String, String> headerMap = (Map<String, String>) script.eval(scriptContext); //执行groovy脚本
                    log.debug("trace exec scriptCode:{}, get resultmap:{} ", scriptCode, headerMap);
                    return headerMap;
                }
            } catch (Exception e) {
                log.error("exec groovy code:{}, get exception: ", scriptCode, e);
            } finally {
                stopWatch.stop();
                log.debug("script get header map cost:{}", stopWatch.toString());
            }
        }
        throw new ErpSyncDataException(ResultCodeEnum.HEADER_SCRIPT_RUN_ERROR,tenantId);
    }

    public static Map<String, String> getHeaderMapOld(String tenantId,String scriptCode, String url) {
        ScriptEngineManager factory = new ScriptEngineManager();
        ScriptEngine engine = factory.getEngineByName("groovy");
        //循环三次获取header
        for (int i = 0; i < 3; i++) {
            StopWatch stopWatch = StopWatch.createStarted();
            try {
                Bindings bindings = engine.createBindings();
                bindings.put("url", url);
                bindings.put("log", log);
                Map<String, String> headerMap = (Map<String, String>) engine.eval(scriptCode, bindings);
                log.debug("trace exec scriptCode:{}, get resultmap:{} ", scriptCode, headerMap);
                return headerMap;
            } catch (Exception e) {
                log.warn("exec groovy code:{}, get exception: ", scriptCode, e);
            } finally {
                stopWatch.stop();
                log.info("script get header map cost:{}", stopWatch.toString());
            }
        }
        throw new ErpSyncDataException(ResultCodeEnum.HEADER_SCRIPT_RUN_ERROR,tenantId);
    }

    public static String getUrl(String tenantId,String scriptCode, String url) {
        if (StringUtils.isEmpty(scriptCode)) {
            return url;
        }
        ScriptEngineManager factory = new ScriptEngineManager();
        ScriptEngine engine = factory.getEngineByName("groovy");
        //循环三次获取header
        for (int i = 0; i < 3; i++) {
            StopWatch stopWatch = StopWatch.createStarted();
            try {
                Bindings bindings = engine.createBindings();
                bindings.put("url", url);
                bindings.put("log", log);
                String resultUrl = (String) engine.eval(scriptCode, bindings);
                log.debug("trace exec scriptCode:{}, get resultUrl:{} ", scriptCode, resultUrl);
                return resultUrl;
            } catch (Exception e) {
                log.warn("exec groovy code:{}, get exception: ", scriptCode, e);
            } finally {
                stopWatch.stop();
                log.info("script get header map cost:{}", stopWatch.toString());
            }
        }
        throw new ErpSyncDataException(ResultCodeEnum.HEADER_SCRIPT_RUN_ERROR,tenantId);
    }

    public static void main(String args[]) {

        String keshunExampleHeaderScript = "import groovy.json.JsonSlurper\n" +
                "  import sun.net.www.protocol.https.DelegateHttpsURLConnection\n" +
                "  import javax.crypto.Mac\n" +
                "  import javax.crypto.spec.SecretKeySpec\n" +
                "  \n" +
                "  public String getToken() {\n" +
                "      String token = null;\n" +
                "      String userName = \"fxxk\";       // 填写客户提供的账号\n" +
                "      String password = \"d22a97a5-e542-4c51-9b4c-5750d80a143d\";   // 填写客户提供的密码\n" +
                "      String requestUrl = \"http://**************/seeyon/rest/token/\" + userName + \"/\" + password;  // 修改请求地址\n" +
                "      HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();\n" +
                "      connection.setRequestMethod(\"GET\");\n" +
                "              // 设置连接主机服务器的超时时间：15000毫秒\n" +
                "              connection.setConnectTimeout(15000); connection.setUseCaches(false); connection.setRequestProperty(\"Content-Type\", \"text/xml; charset=UTF-8\");\n" +
                "                  // 设置读取远程返回的数据时间：60000毫秒\n" +
                "                  connection.setReadTimeout(60000); connection.setDoOutput(true); connection.setDoInput(true); connection.connect(); int code = connection.getResponseCode();\n" +
                "                  if (code == 200) {\n" +
                "                      InputStream inputStream = connection.getInputStream();\n" +
                "                      BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));\n" +
                "                      String line;\n" +
                "                      StringBuffer buffer = new StringBuffer();\n" +
                "                      while ((line = bufferedReader.readLine()) != null) {\n" +
                "                          buffer.append(line);\n" +
                "                      }\n" +
                "                      String str = buffer.toString();\n" +
                "                      token = str" +
                "                      }\n" +
                "                      else {\n" +
                "                          throw new RuntimeException(\"握手异常(\" + connection.getResponseCode() + \")！\" + connection.getResponseMessage())\n" +
                "                          }\n" +
                "                          return url + \"?token=\" + token;\n" +
                "                      }\n" +
                "                      url = getToken();\n" +
                "                      return url;";

        String url = getUrl("",keshunExampleHeaderScript, "http://dev.keshun.com.cn:909/rest/transmit/fxiaoke/queryMasterById");
//        System.out.println("headerMap : " + JSON.toJSON(headerMap));
//
//        headerMap = getHeaderMap(null, "http://dev.keshun.com.cn:909/rest/transmit/fxiaoke/queryMasterById");
//        System.out.println("headerMap for null code : " + JSON.toJSON(headerMap));
    }
}
