package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3FilterStringBuilder;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.TimeUtils;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 可销控制-客户物料特殊处理类
 *
 * <AUTHOR>
 * @date 2021/4/22
 */
@Slf4j
@Component("SAL_SC_CustMat")
public class CustMatBusinessImpl implements SpecialBusiness {
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private CommonBusinessManager commonBusinessManager;

    /**
     * 独立获取数据接口
     *
     * @param objApiName
     * @return
     */
    @Override
    public boolean needSpecialGetAndQuery(String objApiName) {
        return true;
    }

    /**
     * 特殊获取K3C可销控制的数据
     *
     * @param erpIdArg
     * @param apiClient
     * @return
     */
    @Override
    public Result<StandardData> specialGetErpObjData(ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        String customerNum = erpIdArg.getDataId();
        // 根据客户分页查询所有数据
        Result<List<K3Model>> result = getCustMat(apiClient, Lists.newArrayList(customerNum));
        if (!result.isSuccess()) {
            return Result.newError(result.getErrCode(), result.getErrMsg());
        }
        Result<StandardListData> standardListDataResult = assemblyCustMatData(result.getData(), apiClient);
        if (!standardListDataResult.isSuccess()) {
            return Result.newError(standardListDataResult.getErrCode(), standardListDataResult.getErrMsg());
        }
        if (CollectionUtils.isEmpty(standardListDataResult.getData().getDataList())) {
            return Result.newError(ResultCodeEnum.RESULT_NULL);
        }
        updateStandListData(standardListDataResult.getData().getDataList());
        StandardData standardData = standardListDataResult.getData().getDataList().get(0);
        log.info("CustMatBusinessImpl.specialGetErpObjData,standardData={}",standardData);
        return Result.newSuccess(standardData);
    }

    /**
     * 特殊获取K3C可销控制的数据
     *
     * @param timeFilterArg
     * @param apiClient
     * @return
     */
    @Override
    public Result<StandardListData> specialListErpObjData(TimeFilterArg timeFilterArg, K3CloudApiClient apiClient) {
        if (timeFilterArg.getOperationType() == EventTypeEnum.INVALID.getType()) {
            return Result.newError(ResultCodeEnum.UNSUPPORTED_CHANNEL);
        }
        //查询当前时间段内的可销控制数据
        Result<List<K3Model>> result = getCustMatDataList(timeFilterArg,apiClient);
        log.info("CustMatBusinessImpl.specialListErpObjData,result={}",result);
        if (!result.isSuccess()) {
            return Result.newError(result.getErrCode(), result.getErrMsg());
        }
        // 组装数据
        Result<StandardListData> standardListDataResult = assemblyCustMatData(result.getData(), apiClient);
        log.info("CustMatBusinessImpl.specialListErpObjData,standardListDataResult={}",standardListDataResult);
        if (!standardListDataResult.isSuccess()) {
            return Result.newError(standardListDataResult.getErrCode(), standardListDataResult.getErrMsg());
        }
        updateStandListData(standardListDataResult.getData().getDataList());
        log.info("CustMatBusinessImpl.specialListErpObjData,standardListDataResult2={}",standardListDataResult);
        return Result.newSuccess(standardListDataResult.getData());
    }

    private void updateStandListData(List<StandardData> dataList) {
        for(StandardData standardData : dataList) {
            String fid = standardData.getMasterFieldVal().getString("FID");
            standardData.getMasterFieldVal().put("id",fid);
            standardData.getMasterFieldVal().put("erp_id",fid);
            if(StringUtils.isEmpty(standardData.getObjAPIName())) {
                standardData.setObjAPIName(K3CloudForm.SAL_SC_CUSTMAT);
            }
        }
    }

    private Result<StandardListData> assemblyCustMatData(List<K3Model> data, K3CloudApiClient apiClient) {
        List<StandardData> dataList = Lists.newArrayList();
        StandardListData standardListData = new StandardListData();
        while (data.size() != 0) {
            K3Model masterK3Model = data.get(0);
            log.info("CustMatBusinessImpl.assemblyCustMatData,masterK3Model={}",masterK3Model);
            String customerNum = masterK3Model.getString("FCustomerId.FNumber");
            String customerId = masterK3Model.getString("FCustomerId");
            String customerName = masterK3Model.getString("FCustomerId.FName");
            String fId = data.get(0).get("FID").toString();
            data.get(0).put("FID", customerNum);
            StandardData standardData = new StandardData();
            standardData.setMasterFieldVal(ObjectData.convert(masterK3Model));
            Map<String, List<ObjectData>> detailValues = standardData.getDetailFieldVals();
            // 三个伪造从对象
            List<ObjectData> accountDataList = Lists.newArrayList();
            List<ObjectData> productDataList = Lists.newArrayList();
            detailValues.put("SAL_SC_CustMat.AvailableAccountObj", accountDataList);
            detailValues.put("SAL_SC_CustMat.AvailableProductObj", productDataList);

            // 组装客户
            ObjectData accountData = new ObjectData();
            accountData.put("AvailableAccountObj", customerNum);
            accountData.put("FCustomerId", customerId);
            accountData.put("FCustomerId.FName", customerName);
            accountData.put("DetailId", customerNum);
            accountDataList.add(accountData);
            Set<String> productSet = Sets.newHashSet();
            // 组装产品
            while (data.size() != 0 && customerNum.equals(data.get(0).get("FCustomerId.FNumber").toString())) {
                K3Model k3Model = data.get(0);
                log.info("CustMatBusinessImpl.specialListErpObjData,k3Model={}",k3Model);
                String productNum = k3Model.getString("FMaterialId.FNumber");
                log.info("CustMatBusinessImpl.specialListErpObjData,productNum={}",productNum);
                if(StringUtils.isEmpty(productNum)) {
                    data.remove(0);
                    continue;
                }
                ObjectData productData = new ObjectData();
                if (productSet.contains(productNum)) {
                    log.info("CustMatBusinessImpl.specialListErpObjData,repeat productNum={}",productNum);
                    data.remove(0);
                    continue;
                }
                productData.put("AvailableProductObj", productNum);
                productData.put("DetailId", k3Model.get("FID"));
                productDataList.add(productData);
                productSet.add(productNum);
                data.remove(0);
            }
            if (productDataList.size() != 0) {
                productDataList.get(0).put("DetailId", fId);
            }
            dataList.add(standardData);
        }
        standardListData.setDataList(dataList);
        standardListData.setTotalNum(standardListData.getDataList().size());
        return Result.newSuccess(standardListData);
    }

    private Result<List<K3Model>> getCustMat(K3CloudApiClient apiClient, List<String> customerList) {
        List<K3Model> k3Models = Lists.newArrayList();
        String customerStr = Joiner.on("','").join(customerList);

        int size = 2000;
        int limit = 2000;
        int offset = 0;
        while (size == limit) {
            QueryArg queryArg = new QueryArg();
            queryArg.setFormId("SAL_SC_CustMat");
            queryArg.setOrderString("FCustomerId.FNumber");
            queryArg.setFieldKeysByList(configCenterConfig.getSAL_SC_CustMat_FIELDS());
            queryArg.setLimit(limit);
            queryArg.setFilterString(String.format("FCustomerId.FNumber in ('%s')", customerStr));
            queryArg.setStartRow(offset);
            commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
            Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
            if (!result.isSuccess()) {
                log.warn("Query SAL_SC_CustMat customer error,result={}", result);
                return Result.newError(result.getErrCode(), result.getErrMsg());
            }
            k3Models.addAll(result.getData());
            offset += limit;
            size = result.getData().size();
        }
        return Result.newSuccess(k3Models);
    }

    /**
     * 查询当前时间段可销控制对应的增量客户数据以及客户关联的所有可销控制数据
     * @param timeFilterArg
     * @param apiClient
     * @return
     */
    private Result<List<K3Model>> getCustMatDataList(TimeFilterArg timeFilterArg, K3CloudApiClient apiClient) {
        List<String> customerNumList = new ArrayList<>();
        int size = 2000;
        int limit = 2000;
        int offset = 0;

        List<K3Model> k3Models = Lists.newArrayList();

        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("SAL_SC_CustMat");
        queryArg.setOrderString("FCustomerId.FNumber");
        queryArg.setFieldKeys("FCustomerId.FNumber");
        //        如果是自定义历史任务,以filter为准
        if (StringUtils.isNotEmpty(timeFilterArg.getCustomFilterString())) {
            queryArg.setFilterString(timeFilterArg.getCustomFilterString());
        } else {
            String startDtf = TimeUtils.getDateTime(timeFilterArg.getStartTime());
            String endDtf = TimeUtils.getDateTime(timeFilterArg.getEndTime());
            queryArg.setFilterString(String.format("FCreateDate >= {ts'%s'} and FCreateDate < {ts'%s'}", startDtf, endDtf));
        }

        while (size == limit) {
            queryArg.setLimit(limit);
            queryArg.setStartRow(offset);
            Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg, timeFilterArg);
            if (!result.isSuccess()) {
                log.warn("CustMatBusinessImpl.getCustMatDataList,failed,result={}", result);
                return Result.newError(result.getErrCode(), result.getErrMsg());
            }
            k3Models.addAll(result.getData());
            offset += limit;
            size = result.getData().size();
            List<String> customerList = result.getData()
                    .stream()
                    .map(v -> String.valueOf(v.get("FCustomerId.FNumber")))
                    .collect(Collectors.toList());
            customerNumList.addAll(customerList);
        }
        log.info("CustMatBusinessImpl.getCustMatDataList,size={},customerNumList={}",customerNumList.size(),customerNumList);
        customerNumList = customerNumList.stream().distinct().collect(Collectors.toList());
        log.info("CustMatBusinessImpl.getCustMatDataList,size2={},customerNumList={}",customerNumList.size(),customerNumList);
        return getCustMat(apiClient,customerNumList);
    }
}
