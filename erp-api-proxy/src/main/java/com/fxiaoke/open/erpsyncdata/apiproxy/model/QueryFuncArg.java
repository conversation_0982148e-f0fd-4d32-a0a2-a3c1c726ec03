package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/3/6 15:05
 * @desc
 */
@Data
public class QueryFuncArg implements Serializable {
    @SerializedName("binding_object_api_name")
    private String bindingObjectApiName;

    @SerializedName("function_name")
    private String functionName;

    @SerializedName("is_include_used")
    private String isIncludeUsed;


    private Integer pageNumber=1;

    private Integer pageSize=20;

}
