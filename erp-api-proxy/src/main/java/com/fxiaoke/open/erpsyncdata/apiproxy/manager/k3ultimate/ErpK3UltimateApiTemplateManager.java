package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateBaseApiTemplate;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpK3UltimateApiTemplateDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateApiTemplateEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ErpK3UltimateApiTemplateManager {
    @Autowired
    private ErpK3UltimateApiTemplateDao k3UltimateApiTemplateDao;

    public int insert(ErpK3UltimateApiTemplateEntity entity) {
        int count = k3UltimateApiTemplateDao.insert(entity);
        return count;
    }

    public int update(ErpK3UltimateApiTemplateEntity entity) {
        int count = k3UltimateApiTemplateDao.updateById(entity);
        return count;
    }

    public ErpK3UltimateApiTemplateEntity findData(String tenantId, String dataCenterId, String erpObjApiName) {
        ErpK3UltimateApiTemplateEntity entity = k3UltimateApiTemplateDao.findData(tenantId, dataCenterId, erpObjApiName);
        return entity;
    }

    public K3UltimateBaseApiTemplate findApiTemplate(String tenantId, String dataCenterId, String erpObjApiName) {
        ErpK3UltimateApiTemplateEntity entity = k3UltimateApiTemplateDao.findData(tenantId, dataCenterId, erpObjApiName);
        if(entity==null) return null;
        K3UltimateBaseApiTemplate apiTemplate = JSONObject.parseObject(entity.getApiTemplate(),K3UltimateBaseApiTemplate.class);
        return apiTemplate;
    }
}
