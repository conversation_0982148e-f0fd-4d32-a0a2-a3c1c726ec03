package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.cosntant;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CSaveAction;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.Submit;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/14 18:14:47
 */
@Getter
public enum K3DocumentStatusEnum {
    DRAFT("Z", "暂存"),

    ADD("A", "创建", new StatusPath("B", K3CloudApiClient::submit, ErpObjInterfaceUrlEnum.submit)),

    AUDITING("B", "审核中", new StatusPath("C", K3CloudApiClient::audit, ErpObjInterfaceUrlEnum.audit), new StatusPath("D", K3CloudApiClient::cancelAssign, ErpObjInterfaceUrlEnum.cancelAssign)),

    AUDITED("C", "已审核", new StatusPath("D", K3CloudApiClient::unAudit, ErpObjInterfaceUrlEnum.unAudit)),

    RE_AUDIT("D", "重新审核", new StatusPath("B", K3CloudApiClient::submit, ErpObjInterfaceUrlEnum.submit)),

    ;

    private final String status;
    private final String detail;

    // 路径
    private final List<StatusPath> path;

    @Getter
    @AllArgsConstructor
    private static class StatusPath {
        private String status;
        private Execute execute;
        private ErpObjInterfaceUrlEnum urlEnum;
    }

    public interface Execute {
        Result<Submit.Result> execute(K3CloudApiClient apiClient, String objApiName, Submit.BaseArg arg);
    }

    K3DocumentStatusEnum(final String status, final String detail, StatusPath... path) {
        this.status = status;
        this.detail = detail;
        this.path = Arrays.asList(path);
    }

    private static Map<String, K3DocumentStatusEnum> statusMap = Arrays.stream(K3DocumentStatusEnum.values()).collect(Collectors.toMap(K3DocumentStatusEnum::getStatus, Function.identity()));

    // 所有状态变更的路径
    public static final Map<Pair<String, String>, List<StatusPath>> statusChangeMap = new HashMap<>();

    static {
        // 计算所有状态之间的路径
        for (K3DocumentStatusEnum start : K3DocumentStatusEnum.values()) {
            Map<String, PathNode> pathNodes = new HashMap<>();
            PriorityQueue<PathNode> queue = new PriorityQueue<>();
            PathNode startNode = new PathNode(start.getStatus(), null, 0, null);
            pathNodes.put(start.getStatus(), startNode);
            queue.offer(startNode);
            // 计算到所有节点的最短路径
            while (!queue.isEmpty()) {
                PathNode node = queue.poll();
                for (StatusPath path : statusMap.get(node.getStatus()).path) {
                    String nextStatus = path.getStatus();
                    PathNode nextNode = pathNodes.get(nextStatus);
                    if (nextNode == null) {
                        nextNode = new PathNode(nextStatus, node, node.getDistance() + 1, path);
                        pathNodes.put(nextStatus, nextNode);
                        queue.offer(nextNode);
                    } else if (nextNode.getDistance() > node.getDistance() + 1) {
                        // 将这个节点设置为更优的节点
                        nextNode.setDistance(node.getDistance() + 1);
                        nextNode.setPrev(node);
                        nextNode.setPath(path);
                        if (!queue.contains(nextNode)) {
                            // 将后面的节点全部重新计算排序
                            queue.offer(nextNode);
                        }
                    }
                }
            }
            // 保存到所有节点的路径
            for (K3DocumentStatusEnum end : K3DocumentStatusEnum.values()) {
                String endStatus = end.getStatus();
                PathNode endNode = pathNodes.get(endStatus);
                if (endNode != null) {
                    List<StatusPath> path = new ArrayList<>();
                    while (endNode.getPrev() != null) {
                        path.add(0, endNode.getPath());
                        endNode = endNode.getPrev();
                    }
                    statusChangeMap.put(Pair.of(start.getStatus(), endStatus), path);
                }
            }
        }

        // A->D不支持
        statusChangeMap.remove(Pair.of("A", "D"));
    }

    @Data
    @AllArgsConstructor
    private static class PathNode implements Comparable<PathNode> {
        private String status;
        private PathNode prev;
        private int distance;
        private StatusPath path;

        @Override
        public int compareTo(PathNode o) {
            return Integer.compare(distance, o.distance);
        }
    }

    public static void changeStatus(String fromStatus, String toStatus, K3CloudApiClient apiClient, String objApiName, String id, Result<ErpIdResult> idResult,Map<String,Map<String,Map<String,Object>>> interfaceArgSettings) {
        List<StatusPath> list = statusChangeMap.get(Pair.of(fromStatus, toStatus));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (StatusPath statusPath : list) {
            Submit.BaseArg arg = new Submit.BaseArg();
            arg.setIds(id);
            setInterfaceArg(arg,objApiName,statusPath.getUrlEnum(),interfaceArgSettings);
            final Execute execute = statusPath.getExecute();
            final Result<Submit.Result> result = execute.execute(apiClient, objApiName, arg);
            String executeErrMsg = Submit.checkResult(result);
            if (executeErrMsg != null) {
                idResult.setErrMsg("执行方法:" + statusPath.getUrlEnum().name() + "失败：" + executeErrMsg);
                return;
            }
        }
    }

    /**
     * 保存前改变状态,只调用反审核/反撤销
     */
    public static Result<Void> changeStatusBeforeSave(String fromStatus, String toStatus, K3CloudApiClient apiClient, String objApiName, String id, boolean useId,Map<String,Map<String,Map<String,Object>>> interfaceArgSettings) {
        List<StatusPath> list = statusChangeMap.get(Pair.of(fromStatus, toStatus));
        if (CollectionUtils.isEmpty(list)) {
            return Result.newSuccess();
        }

        Submit.BaseArg arg = new Submit.BaseArg();
        if (useId) {
            arg.setIds(id);
        } else {
            arg.setNumbers(Lists.newArrayList(id));
        }
        for (StatusPath statusPath : list) {
            if (statusPath.getUrlEnum() == ErpObjInterfaceUrlEnum.unAudit || statusPath.getUrlEnum() == ErpObjInterfaceUrlEnum.cancelAssign) {
                Submit.BaseArg newArg= BeanUtil.deepCopy(arg,Submit.BaseArg.class);
                setInterfaceArg(newArg,objApiName,statusPath.getUrlEnum(),interfaceArgSettings);
                final Execute execute = statusPath.getExecute();
                final Result<Submit.Result> result = execute.execute(apiClient, objApiName, newArg);
                String executeErrMsg = Submit.checkResult(result);
                if (executeErrMsg != null) {
                    return Result.newError("执行方法:" + statusPath.getUrlEnum().name() + "失败：" + executeErrMsg);
                }
            }
        }
        return Result.newSuccess();
    }

    private static void setInterfaceArg(Submit.BaseArg arg, String objApiName, ErpObjInterfaceUrlEnum urlEnum, Map<String, Map<String, Map<String, Object>>> interfaceArgSettings) {
        K3CSaveAction action = null;
        if (urlEnum == null || interfaceArgSettings == null || objApiName == null) {
            return;
        }
        switch (urlEnum) {
            case submit:
                action = K3CSaveAction.SUBMIT;
                break;
            case audit:
                action = K3CSaveAction.AUDIT;
                break;
            case unAudit:
                action = K3CSaveAction.UN_AUDIT;
                break;
            case cancelAssign:
                action = K3CSaveAction.CANCEL_ASSIGN;
                break;
            default:
                break;
        }
        if (action != null && interfaceArgSettings.containsKey(objApiName)
                && interfaceArgSettings.get(objApiName).containsKey(action.getType())
                && interfaceArgSettings.get(objApiName).get(action.getType()) != null) {
            arg.putAll(interfaceArgSettings.get(objApiName).get(action.getType()));
        }
    }

    public static List<ErpObjInterfaceUrlEnum> getErpObjInterfaceUrls(String fromStatus, String toStatus) {
        List<StatusPath> list = statusChangeMap.get(Pair.of(fromStatus, toStatus));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(StatusPath::getUrlEnum).collect(Collectors.toList());
    }
}
