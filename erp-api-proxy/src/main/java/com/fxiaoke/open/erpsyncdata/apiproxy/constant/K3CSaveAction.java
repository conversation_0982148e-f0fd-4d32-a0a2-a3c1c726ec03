package com.fxiaoke.open.erpsyncdata.apiproxy.constant;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/3 15:02:14
 */
@Getter
public enum K3CSaveAction {
    DRAFT("1", "暂存", K3CloudApiClient.DRAFT, 1),

    SAVE("2", "保存", K3CloudApiClient.SAVE, 2),

    SUBMIT("3", "提交", K3CloudApiClient.SUBMIT, 5),

    AUDIT("4", "审核", K3CloudApiClient.AUDIT, 6),
    UN_AUDIT("5", "反审核", K3CloudApiClient.UN_AUDIT, 3),
    CANCEL_ASSIGN("6", "撤销", K3CloudApiClient.CANCEL_ASSIGN, 4),
    ;

    private String type;
    private String detail;
    private String urlSuffix;
    private Integer order;

    K3CSaveAction(final String type, final String detail, final String urlSuffix, final Integer order) {
        this.type = type;
        this.detail = detail;
        this.urlSuffix = urlSuffix;
        this.order = order;
    }

    private static Map<String, K3CSaveAction> typeMap = Arrays.stream(K3CSaveAction.values()).collect(Collectors.toMap(K3CSaveAction::getType, Function.identity()));

    public static K3CSaveAction getK3CSaveActionByType(String type) {
        return typeMap.get(type);
    }

    @Override
    public String toString() {
        return "K3CSaveType{" +
                "type=" + type +
                ", detail='" + detail + '\'' +
                "} " + super.toString();
    }
}
