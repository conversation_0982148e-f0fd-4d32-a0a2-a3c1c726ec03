package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.common.IpUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.ErpDataMemLogDTO;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JavaObjectUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * ERP数据内存上报
 * 改为在{@link com.fxiaoke.open.erpsyncdata.apiproxy.aop.ErpInvokeMonitorAspect}处理，并且去除了轮询临时库的。
 * <AUTHOR>
 * @date 2022/2/25
 * @deprecated
 */
//@Component
@Slf4j
@Deprecated
public class ErpDataMemManager {
    @Autowired
    private ErpFieldManager erpFieldManager;

    public void send(String tenantId,String objApiName,String dataId,Object object,Long createTime) {
        if(StringUtils.isEmpty(tenantId)
                || StringUtils.isEmpty(objApiName)
                || StringUtils.isEmpty(dataId)
                || object==null) return;

        if(createTime==null) {
            createTime = System.currentTimeMillis();
        }

        ErpDataMemLogDTO logDTO = ErpDataMemLogDTO.builder()
                .appName(ConfigHelper.getProcessInfo().getName())
                .traceId(TraceUtil.get())
                .createTime(createTime)
                .serverIp(IpUtil.getSiteLocalIp())
                .tenantId(tenantId)
                .objApiName(objApiName)
                .dataId(dataId)
                .dataSizeInBytes(JavaObjectUtils.computeObjectSizeInByte(object))
                .build();

        try {
            BizLogClient.send("biz-log-erpdatamem", Pojo2Protobuf.toMessage(logDTO, com.fxiaoke.log.ErpDataMemLog.class).toByteArray());
        } catch (Exception e) {
            log.info("ErpDataMemManager.send,exception={}",e.getMessage());
        }
    }

    public void send(String tenantId,String objApiName,StandardData data) {
        if(data==null || data.getMasterFieldVal()==null) return;
        String dataId = data.getMasterFieldVal().getId();
        send(tenantId,objApiName,dataId,data,null);
    }

    public void batchSend(String tenantId,String dataCenterId,String objApiName,List<StandardData> dataList) {
        if(CollectionUtils.isEmpty(dataList)) return;
        long createTime = System.currentTimeMillis();
        List<ErpFieldExtendEntity> list = erpFieldManager.queryIdField(tenantId,dataCenterId, objApiName);
        if(CollectionUtils.isEmpty(list)) return;
        String fieldApiName = list.get(0).getFieldApiName();

        for(int i=0;i<dataList.size();i++) {
            StandardData data = dataList.get(i);
            if(data==null || data.getMasterFieldVal()==null) continue;
            String dataId = data.getMasterFieldVal().getString(fieldApiName);
            if(StringUtils.isEmpty(dataId)) continue;
            send(tenantId,objApiName,dataId,data,createTime);
        }
    }
}
