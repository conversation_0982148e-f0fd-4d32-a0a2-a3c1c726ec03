package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.ApiClientHolder;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.MetaDataInfoManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.ErpObjectDescribe;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryBusinessInfoArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SuperAdminReplaceViewService;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.otherrestapi.openmessage.arg.SendTextMessageArg;
import com.fxiaoke.otherrestapi.openmessage.common.MessageData;
import com.fxiaoke.otherrestapi.openmessage.common.MessageSendTypeEnum;
import com.fxiaoke.otherrestapi.openmessage.common.MessageTypeEnum;
import com.fxiaoke.otherrestapi.openmessage.result.SendTextMessageResult;
import com.fxiaoke.otherrestapi.openmessage.service.SendMessageService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SuperAdminReplaceViewServiceImpl implements SuperAdminReplaceViewService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private MetaDataInfoManager metaDataInfoManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ApiClientHolder apiClientHolder;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private UserCenterService userCenterService;

    public Map<String,Long> lastCompareTime=Maps.newHashMap();

    @Override
    public Result<String> useView(String tenantId) {
        ErpConnectInfoEntity query = new ErpConnectInfoEntity();
        if (StringUtils.isNotBlank(tenantId)) {
            query.setTenantId(tenantId);
        }
        query.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        List<ErpConnectInfoEntity> list = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        for (ErpConnectInfoEntity entity : list) {
            List<String>formList = getAllErpRealObj(entity.getTenantId(),entity.getId());
            formList.add("*");
            ErpTenantConfigurationEntity config = new ErpTenantConfigurationEntity();
            config.setChannel(ErpChannelEnum.ERP_K3CLOUD.name());
            config.setDataCenterId(entity.getId());
            config.setTenantId(entity.getTenantId());
            config.setType(TenantConfigurationTypeEnum.USE_BILLQUERY_INTERFACE_TO_VIEW.name());
            List<ErpTenantConfigurationEntity> oldConfig = tenantConfigurationManager.queryList(entity.getTenantId(),config);
            if(CollectionUtils.isEmpty(oldConfig)){
                config.setId(idGenerator.get());
                config.setConfiguration(JSONObject.toJSONString(formList));
                config.setCreateTime(System.currentTimeMillis());
                config.setUpdateTime(System.currentTimeMillis());
                int insert = tenantConfigurationManager.insert(entity.getTenantId(),config);
            }else{
                config=oldConfig.get(0);
                config.setConfiguration(JSONObject.toJSONString(formList));
                int update = tenantConfigurationManager.updateById(tenantId,config);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> brushQueryCode(String tenantId, String dcId, String formId) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(dcId)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        ErpConnectInfoEntity byTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dcId);
        K3CloudConnectParam k3CloudConnectParam = JacksonUtil.fromJson(byTenantId.getConnectParams(), K3CloudConnectParam.class);

        K3CloudApiClient apiClient = K3CloudApiClient.newInstance(tenantId, k3CloudConnectParam, dcId);
        Boolean ignoreCustomField = false;
        Result<ErpObjectDescribe> erpObjectDescribe1Result = metaDataInfoManager.getErpObjectDescribe(apiClient, new QueryBusinessInfoArg(K3CloudForm.BD_Customer), ignoreCustomField,null);
        if (!erpObjectDescribe1Result.isSuccess()) {//找不到使用默认连接信息
            k3CloudConnectParam.setBaseUrl("http://172.31.100.60/k3cloud/");
            k3CloudConnectParam.setDbId("5ec229fad54306");
            k3CloudConnectParam.setDbName(i18NStringManager.getByEi(I18NStringEnum.s3732, tenantId));
            k3CloudConnectParam.setAuthType(1);
            k3CloudConnectParam.setUserName("ces2");
            k3CloudConnectParam.setPassword("8888888");
            ignoreCustomField = true;
            apiClient = K3CloudApiClient.newInstance(tenantId, k3CloudConnectParam, dcId);
        }
        List<String> formList = Lists.newArrayList();
        if (StringUtils.isNotBlank(formId)) {
            formList.add(formId);
        } else {
            formList = getAllErpRealObj(byTenantId.getTenantId(),byTenantId.getId());
        }
        StringBuilder remark = new StringBuilder();
        remark.append(i18NStringManager.getByEi(I18NStringEnum.s622, tenantId)+tenantId).append(System.lineSeparator());
        for (String obj : formList) {
            erpObjectDescribe1Result = metaDataInfoManager.getErpObjectDescribe(apiClient, new QueryBusinessInfoArg(obj), ignoreCustomField,null);
            if (erpObjectDescribe1Result == null || !erpObjectDescribe1Result.isSuccess()) {
                remark.append(i18NStringManager.getByEi2(I18NStringEnum.s3733, tenantId, obj)).append(System.lineSeparator());
                continue;
            }
            ErpObjectDescribe erpObjectDescribe = erpObjectDescribe1Result.getData();
            K3DataConverter converter = k3DataManager.buildConverter(tenantId,dcId, obj);
            ErpObjectDescribe.SplitObjDescribe mainObj = erpObjectDescribe.getSplitObjDescribes().values().stream().filter(v -> v.isMain()).collect(Collectors.toList()).get(0);
            Map<String, ErpFieldExtendEntity> viewCode2Extend = Maps.newHashMap();

            for (ErpFieldExtendEntity v : mainObj.getFieldExtends().values()) {
                if (StringUtils.isNotBlank(v.getViewCode())) {
                    viewCode2Extend.put(v.getViewCode(), v);
                }
            }
            remark.append(i18NStringManager.getByEi2(I18NStringEnum.s3734, tenantId, converter.getMasterFieldExtends().get(0).getObjApiName()));
            for (ErpFieldExtendEntity extendEntity : converter.getMasterFieldExtends()) {//主数据
                if (StringUtils.isBlank(extendEntity.getViewCode())) {
                    remark.append(extendEntity.getFieldApiName()).append(",");
                    continue;
                }

            }
            remark.append(i18NStringManager.getByEi(I18NStringEnum.s3735, tenantId)).append(System.lineSeparator());
            //从数据
            for (String detailObj : converter.getDetailFieldExtendMap().keySet()) {
                String fieldStr;
                List<ErpFieldExtendEntity> oldFieldExtend = converter.getDetailFieldExtendMap().get(detailObj);
                remark.append(i18NStringManager.getByEi2(I18NStringEnum.s3734, tenantId, detailObj));
                if (erpObjectDescribe.getSplitObjDescribes().containsKey(detailObj)) {
                    fieldStr=dealWithDetailFieldExtend(tenantId,oldFieldExtend, detailObj, erpObjectDescribe.getSplitObjDescribes().get(detailObj));
                } else {//忽略_1,_2,_3,_4后缀
                    detailObj = detailObj.replace("_1", "").replace("_2", "").replace("_3", "").replace("_4", "");
                    if (erpObjectDescribe.getSplitObjDescribes().containsKey(detailObj)) {
                        fieldStr=dealWithDetailFieldExtend(tenantId,oldFieldExtend, detailObj, erpObjectDescribe.getSplitObjDescribes().get(detailObj));
                    } else {//特殊处理
                        if("BD_Customer.FT_BD_CUSTCONTACT".equals(detailObj)){
                            fieldStr=dealWithDetailFieldExtend(tenantId,oldFieldExtend, detailObj, erpObjectDescribe.getSplitObjDescribes().get("BD_Customer.BD_CUSTCONTACT"));
                        }else if("StockFlexItem".equals(detailObj)){
                            fieldStr=dealWithDetailFieldExtend(tenantId,oldFieldExtend, detailObj, erpObjectDescribe.getSplitObjDescribes().get("BD_STOCK.StockFlexItem"));
                        }else if("SAL_SaleOrder.FSaleOrderEntry".equals(detailObj)){
                            fieldStr=dealWithDetailFieldExtend(tenantId,oldFieldExtend, detailObj, erpObjectDescribe.getSplitObjDescribes().get("SAL_SaleOrder.SaleOrderEntry"));
                        }else if("SAL_OUTSTOCK.Entity".equals(detailObj)){
                            fieldStr=dealWithDetailFieldExtend(tenantId,oldFieldExtend, detailObj, erpObjectDescribe.getSplitObjDescribes().get("SAL_OUTSTOCK.SAL_OUTSTOCKENTRY"));
                        }else{
                            remark.append(i18NStringManager.getByEi(I18NStringEnum.s3736, tenantId));
                        }
                    }
                }
                remark.append(i18NStringManager.getByEi(I18NStringEnum.s3735, tenantId)).append(System.lineSeparator());
            }
        }
        addCompareConfig(tenantId,dcId,formList);
        return Result.newSuccess(remark.toString());
    }

    private String dealWithDetailFieldExtend(String tenantId,List<ErpFieldExtendEntity> oldFieldExtend, String detailObj, ErpObjectDescribe.SplitObjDescribe splitObjDescribe) {
        StringBuilder remark = new StringBuilder();
        Map<String, ErpFieldExtendEntity> viewCode2Extend = Maps.newHashMap();
        for (ErpFieldExtendEntity v : splitObjDescribe.getFieldExtends().values()) {
            if (StringUtils.isNotBlank(v.getViewCode())) {
                viewCode2Extend.put(v.getViewCode(), v);
            }
        }
        for (ErpFieldExtendEntity extendEntity : oldFieldExtend) {
            if (StringUtils.isBlank(extendEntity.getViewCode())) {
                remark.append(extendEntity.getFieldApiName()).append(",");
                continue;
            }
            if (viewCode2Extend.containsKey(extendEntity.getViewCode())) {
                ErpFieldExtendEntity newExtend = viewCode2Extend.get(extendEntity.getViewCode());
                if(StringUtils.isBlank(newExtend.getQueryCode())&&StringUtils.isBlank(newExtend.getErpFieldType())){
                    continue;
                }
                erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateQueryCodeById(extendEntity.getId(),newExtend.getQueryCode(),newExtend.getErpFieldType());
            }else {
                remark.append(extendEntity.getFieldApiName()).append(",");
            }
        }
        return remark.toString();
    }

    @Override
    public List<String> getAllErpRealObj(String tenantId,String dcId) {
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dcId);
        query.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        return erpObjectEntities.stream().map(ErpObjectEntity::getErpObjectApiName).collect(Collectors.toList());
    }

    @Override
    public Result<ViewResult> getByExecuteBillQuery(String tenantId,String dcId, String formId, String dataId) {
        ErpConnectInfoEntity byTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dcId);
        K3CloudApiClient k3ApiClient = apiClientHolder.getK3ApiClient(tenantId, byTenantId.getConnectParams(), byTenantId.getId());
        ViewArg viewArg = new ViewArg();
        viewArg.setNumber(dataId);
        K3DataConverter converter = k3DataManager.buildConverter(tenantId,dcId, formId);
        Result<ViewResult> result = k3DataManager.getAndLog(tenantId, viewArg,formId, k3ApiClient, viewArg, converter, false);
        return result;
    }

    @Override
    public Result<Object> compareViewAndBillQueryResult(String tenantId, String dcId, String formId, String dataId) {
        ErpConnectInfoEntity byTenantId = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dcId);
        K3CloudApiClient k3ApiClient = apiClientHolder.getK3ApiClient(tenantId, byTenantId.getConnectParams(), byTenantId.getId());
        ViewArg viewArg = new ViewArg();
        viewArg.setNumber(dataId);
        K3DataConverter converter = k3DataManager.buildConverter(tenantId,dcId, formId);
        return compareByViewArg(tenantId,dcId,formId,k3ApiClient,viewArg,converter);

    }

    @LogLevel(LogLevelEnum.DEBUG)
    public Result<Object> compareAndSendMessage(String tenantId, String formId, K3CloudApiClient k3ApiClient, ViewArg viewArg, K3DataConverter converter) {
        ErpTenantConfigurationEntity config = tenantConfigurationManager.findOne("*", "*", ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.COMPARE_BILLQUERY_VIEW_RESULT.name());
        if(config==null){
            config = tenantConfigurationManager.findOne(tenantId, k3ApiClient.getDataCenterId(), ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.COMPARE_BILLQUERY_VIEW_RESULT.name());
        }
        if(config!=null&&StringUtils.isNotBlank(config.getConfiguration())){
            JSONObject jsonObject=JSONObject.parseObject(config.getConfiguration());
            if(jsonObject.getString("formId")!=null&&(jsonObject.getString("formId").contains(formId)||"*".equals(jsonObject.getString("formId")))){//对比的对象
                Result<Object> compareResult=null;
                if(jsonObject.getBoolean("compareAll")!=null&&jsonObject.getBoolean("compareAll")){//一直对比
                    compareResult = compareByViewArg(tenantId,k3ApiClient.getDataCenterId(), formId, k3ApiClient, viewArg, converter);
                }else {
                    String key=tenantId+"_"+formId;
                    if(!this.lastCompareTime.containsKey(tenantId+"_"+formId)
                    ||System.currentTimeMillis()-this.lastCompareTime.get(key)>1000*60*60L){//第一次后，超过1小时才再次对比
                        compareResult = compareByViewArg(tenantId,k3ApiClient.getDataCenterId(), formId, k3ApiClient, viewArg, converter);
                        this.lastCompareTime.put(key,System.currentTimeMillis());
                    }
                }
                if(compareResult!=null&&!compareResult.isSuccess()&&jsonObject.getInteger("receiver")!=null){//发送企业消息
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setTenantId(tenantId);
                    arg.setMsgTitle(i18NStringManager.getByEi2(I18NStringEnum.s3737, tenantId, tenantId, formId, viewArg.getNumber(), viewArg.getId()));
                    arg.setReceivers(Lists.newArrayList(jsonObject.getInteger("receiver")));
                    arg.setMsg(JSONObject.toJSONString(compareResult));
                    Result<Void> sendResult = this.sendErpSyncDataAppNotice(arg);
                }
                if(compareResult!=null&&!compareResult.isSuccess()&&jsonObject.getInteger("adminFsReceiver")!=null){//发送纷享消息
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setEnterpriseAccount("fs");
                    arg.setMsgTitle(i18NStringManager.getByEi2(I18NStringEnum.s3737, "1", tenantId, formId, viewArg.getNumber(), viewArg.getId()));
                    arg.setReceivers(Lists.newArrayList(jsonObject.getInteger("adminFsReceiver")));
                    arg.setMsg(JSONObject.toJSONString(compareResult));
                    Result<Void> sendResult = this.sendErpSyncDataAppNotice(arg);
                }
            }
        }
        return Result.newSuccess();
    }

    private Result<Object> compareByViewArg(String tenantId,String dataCenterId, String formId, K3CloudApiClient k3ApiClient, ViewArg viewArg, K3DataConverter converter) {
        Result<ViewResult> billResult = k3DataManager.getAndLog(tenantId, viewArg,formId, k3ApiClient, viewArg, converter, false);
        if (!billResult.isSuccess()||billResult.getData()==null||billResult.getData().getResult()==null
        ||billResult.getData().getResult().getResult()==null) {
            return Result.copy(billResult);
        }
        Result<ViewResult> viewResult = k3DataManager.getAndLog(tenantId, viewArg,formId, k3ApiClient, viewArg, converter, true);
        if (!viewResult.isSuccess()||viewResult.getData()==null||viewResult.getData().getResult()==null
        ||viewResult.getData().getResult().getResult()==null) {
            return Result.copy(viewResult);
        }
        return compareResult(tenantId,dataCenterId, formId,converter,billResult,viewResult);
    }

    @Override
    public Result<Object> compareResult(String tenantId,String dataCenterId, String formId,K3DataConverter converter,Result<ViewResult> billResult, Result<ViewResult> viewResult) {
        boolean isSuccess=true;
        //获取编码字段
        ErpFieldExtendEntity erpFieldExtendEntity = erpFieldManager.queryNumFieldExtend(tenantId,dataCenterId, formId);
        Result<StandardData> stdViewResult= converter.convertViewResult(tenantId,erpFieldExtendEntity, viewResult.getData());
        Result<StandardData> stdBillResult = converter.convertQueryResult(tenantId,converter.getIdFieldExtend(),erpFieldExtendEntity, billResult.getData());

        ErpObjectRelationshipEntity query=new ErpObjectRelationshipEntity();
        query.setTenantId(tenantId);
        query.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        query.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
        query.setErpRealObjectApiname(formId);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryList(query);
        Map<String, Set<String>> obj2Field=Maps.newHashMap();
        Map<String, Set<String>> obj2Field1=Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(erpObjectRelationshipEntities)){
            List<String> splitErpObjList = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
            List<SyncPloyDetailEntity> ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .listByDestTenantId(tenantId, splitErpObjList, null, null, null, 0, 100);
            for(SyncPloyDetailEntity syncPloyDetailEntity:ployDetailEntities){
                List<ErpObjectEntity> erpObjectEntities;
                String masterObj="masterObj";
                if(!obj2Field.containsKey(masterObj)){
                    obj2Field.put(masterObj, Sets.newHashSet());
                    obj2Field1.put(masterObj, Sets.newHashSet());
                }
                //主数据
                for (FieldMappingData fieldMappingData : syncPloyDetailEntity.getFieldMappings()) {
                    if (StringUtils.isEmpty(fieldMappingData.getSourceApiName())) {
                        continue;
                    }
                    obj2Field.get(masterObj).add(fieldMappingData.getSourceApiName());
                    obj2Field1.get(masterObj).add(fieldMappingData.getSourceApiName());
                }
                //主数据范围
                if (syncPloyDetailEntity.getSyncConditions().getFilters() != null) {
                    for (List<FilterData> filterDataList : syncPloyDetailEntity.getSyncConditions().getFilters()) {
                        if (filterDataList != null) {
                            for (FilterData filterData : filterDataList) {
                                obj2Field.get(masterObj).add(filterData.getFieldApiName());
                                obj2Field1.get(masterObj).add(filterData.getFieldApiName());
                            }
                        }
                    }
                }
                //明细数据
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : syncPloyDetailEntity.getDetailObjectMappings()) {
                    erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, Lists.newArrayList(detailObjectMappingData.getSourceObjectApiName()));
                    String detailObj=erpObjectEntities.get(0).getErpObjectExtendValue();
                    if(!obj2Field.containsKey(detailObj)){
                        obj2Field.put(detailObj, Sets.newHashSet());
                        obj2Field1.put(detailObj, Sets.newHashSet());
                    }
                    for (FieldMappingData fieldMappingData : detailObjectMappingData.getFieldMappings()) {
                        if (StringUtils.isEmpty(fieldMappingData.getSourceApiName())||"fake_master_detail".equals(fieldMappingData.getSourceApiName())) {
                            continue;
                        }
                        obj2Field.get(detailObj).add(fieldMappingData.getSourceApiName());
                        obj2Field1.get(detailObj).add(fieldMappingData.getSourceApiName());
                    }
                }
                //明细数据范围
                for (SyncConditionsData syncConditionsData : syncPloyDetailEntity.getDetailObjectSyncConditions()) {
                    erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, Lists.newArrayList(syncConditionsData.getApiName()));
                    String detailObj=erpObjectEntities.get(0).getErpObjectExtendValue();
                    if (syncConditionsData.getFilters() != null) {
                        for (List<FilterData> filterDataList : syncConditionsData.getFilters()) {
                            if (filterDataList != null) {
                                for (FilterData filterData : filterDataList) {
                                    obj2Field.get(detailObj).add(filterData.getFieldApiName());
                                    obj2Field1.get(detailObj).add(filterData.getFieldApiName());
                                }
                            }
                        }
                    }
                }
            }
        }
        if(!obj2Field.containsKey("masterObj")){//如果没有找到策略，返回成功
            return Result.newSuccess();
        }
        Map<String,Object>result=Maps.newHashMap();
        result.put("stdBillResult",stdBillResult.getData());
        result.put("stdViewResult",stdViewResult.getData());
        ObjectData viewMainObj = stdViewResult.getData().getMasterFieldVal();
        ObjectData billMainObj = stdBillResult.getData().getMasterFieldVal();
        Map<String,Object>main=buildCompareResult(viewMainObj,billMainObj);
        result.put("masterObj",main);
        obj2Field.get("masterObj").removeAll(((Map) main.get("commonTrue")).keySet());
        if(CollectionUtils.isNotEmpty(obj2Field.get("masterObj"))){
            for(String field:obj2Field.get("masterObj")){
                if(((Map) main.get("viewDifference")).containsKey(field)){
                    isSuccess=false;
                }
            }
        }
        Map<String, List<ObjectData>> viewDetailFieldVals = stdViewResult.getData().getDetailFieldVals();
        Map<String, List<ObjectData>> billDetailFieldVals = stdBillResult.getData().getDetailFieldVals();
        for(String detailObj:viewDetailFieldVals.keySet()){
            List<ObjectData> viewDataList = viewDetailFieldVals.get(detailObj);
            if(billDetailFieldVals.containsKey(detailObj)){
                List<ObjectData> billDataList = billDetailFieldVals.get(detailObj);
                if(viewDataList.size()==billDataList.size()){
                    if(viewDataList.size()==0){
                        continue;
                    }
                    for(int i=0;i<viewDataList.size();i++){
                        Map<String, Object> detail = buildCompareResult(viewDataList.get(i), billDataList.get(i));
                        if(obj2Field.containsKey(detailObj)){
                            obj2Field.get(detailObj).removeAll(((Map) detail.get("commonTrue")).keySet());
                            if(CollectionUtils.isNotEmpty(obj2Field.get("detailObj"))){
                                for(String field:obj2Field.get("detailObj")){
                                    if(((Map) detail.get("viewDifference")).containsKey(field)){
                                        isSuccess=false;
                                    }
                                }
                            }
                        }
                        result.put(detailObj,detail);
                    }
                }else{
                    if(viewDataList.size()==1&&viewDataList.get(0).containsKey("DetailId")&&"0".equals(viewDataList.get(0).getString("DetailId"))){//如果view只有一条id为0的数据跳过
                        continue;
                    }
                    if(obj2Field.containsKey(detailObj)){
                        isSuccess=false;
                    }
                    result.put(detailObj,i18NStringManager.getByEi(I18NStringEnum.s3715, tenantId));
                }
            }else {
                if(obj2Field.containsKey(detailObj)){
                    isSuccess=false;
                }
                result.put(detailObj,i18NStringManager.getByEi(I18NStringEnum.s3716, tenantId));
            }
        }
        result.put("bindField",obj2Field1);
        result.put("notCommonTrueBindField",obj2Field);
        if(isSuccess){
            return Result.newSuccess(result);
        }else{
            return Result.newErrorWithData(ResultCodeEnum.SYSTEM_ERROR,result);
        }

    }

    @Override
    public Result<String> brushAllQueryCode(String ignoreTenantIds) {
        ErpConnectInfoEntity query = new ErpConnectInfoEntity();

        query.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        List<ErpConnectInfoEntity> list = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId))
                .queryList(query);
        Map<String,List<Result<String>>>resultMap=Maps.newHashMap();
        for (ErpConnectInfoEntity entity : list) {
            if(StringUtils.isNotBlank(ignoreTenantIds)&&ignoreTenantIds.contains(entity.getTenantId())){
                continue;
            }
            List<String> allErpRealObj = this.getAllErpRealObj(entity.getTenantId(), entity.getId());
            List<Result<String>> resultList = Lists.newArrayList();
            for (String objApiName : allErpRealObj) {
                try{
                    Result<String> result = this.brushQueryCode(entity.getTenantId(), entity.getId(), objApiName);
                    resultList.add(result);
                }catch (Exception e){
                    Result<String> result=Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                    result.setData(i18NStringManager.getByEi2(I18NStringEnum.s3739, entity.getTenantId(), entity.getTenantId(), objApiName, e.toString()));
                    resultList.add(result);
                }
            }
            addCompareConfig(entity.getTenantId(),entity.getId(),allErpRealObj);
            resultMap.put(entity.getTenantId(),resultList);
        }
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setEnterpriseAccount("fs");
        arg.setMsgTitle("brushAllQueryCode");
        arg.setReceivers(Lists.newArrayList(8110));
        arg.setMsg(JSONObject.toJSONString(resultMap));
        Result<Void> sendResult = this.sendErpSyncDataAppNotice(arg);
        return Result.newSuccess();
    }

    private void addCompareConfig(String tenantId, String id, List<String>objList) {
        ErpTenantConfigurationEntity one = tenantConfigurationManager.findOne(tenantId, id,
                ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.COMPARE_BILLQUERY_VIEW_RESULT.name());
        Map<String,Object> config=Maps.newHashMap();
        config.put("formId",objList.stream() .collect(Collectors.joining(",")));
        config.put("compareAll",false);
        config.put("adminFsReceiver",8110);
        if(one!=null){
            one.setConfiguration(JSONObject.toJSONString(config));
            tenantConfigurationManager.updateById(tenantId,one);
        }else{
            one = ErpTenantConfigurationEntity.builder()
                    .id(idGenerator.get())
                    .tenantId(tenantId)
                    .dataCenterId(id)
                    .channel(ErpChannelEnum.ERP_K3CLOUD.name())
                    .type(TenantConfigurationTypeEnum.COMPARE_BILLQUERY_VIEW_RESULT.name())
                    .configuration(JSONObject.toJSONString(config))
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .build();
            tenantConfigurationManager.insert(tenantId,one);
        }
    }

    @Override
    public Result<String> deleteConfig(String tenantId,String type) {
        if(StringUtils.isBlank(tenantId)||StringUtils.isBlank(type)){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        ErpConnectInfoEntity query = new ErpConnectInfoEntity();
        query.setTenantId(tenantId);
        query.setChannel(ErpChannelEnum.ERP_K3CLOUD);
        List<ErpConnectInfoEntity> list = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        if(CollectionUtils.isEmpty(list)){
            return Result.newSuccess();
        }
        for (ErpConnectInfoEntity entity : list) {
            ErpTenantConfigurationEntity one = tenantConfigurationManager.findOne(tenantId, entity.getId(), ErpChannelEnum.ERP_K3CLOUD.name(), type);
            if(one!=null){
                tenantConfigurationManager.deleteById(tenantId,one.getId());
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<Result<ListErpObjDataResult>>> executeBillQuery(String tenantId) {
        List<Result<ListErpObjDataResult>> list=Lists.newArrayList();
        List<SyncPloyDetailEntity> ployDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listBySourceType(tenantId,  2);
        TimeFilterArg timeFilterArg=new TimeFilterArg();
        timeFilterArg.setTenantId(tenantId);
        timeFilterArg.setStartTime(0L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setOffset(0);
        timeFilterArg.setLimit(1);
        timeFilterArg.setOperationType(2);
        for(SyncPloyDetailEntity entity:ployDetailEntities){
            timeFilterArg.setObjAPIName(entity.getSourceObjectApiName());
            Result<ListErpObjDataResult> listErpObjDataResultResult = erpDataPreprocessService.listErpObjDataByTime(timeFilterArg);
            list.add(listErpObjDataResultResult);
        }
        return Result.newSuccess(list);
    }

    private Map<String, Object> buildCompareResult(ObjectData viewMainObj, ObjectData billMainObj) {
        Map<String,Object>objectMap=Maps.newHashMap();
        Map<String,String>commonTrue=Maps.newHashMap();
        Map<String,String>commonFalse=Maps.newHashMap();
        List<String> spaceKey=Lists.newArrayList();
        for(String key:viewMainObj.keySet()){
            if(viewMainObj.get(key)!=null&&StringUtils.isBlank(viewMainObj.get(key).toString())){//view接口值为空
                spaceKey.add(key);
            }
            if(billMainObj.containsKey(key)){
                if(viewMainObj.get(key)!=null&&billMainObj.get(key)!=null){
                    if(viewMainObj.get(key).toString().equals(billMainObj.get(key).toString())){
                        commonTrue.put(key,"view["+viewMainObj.get(key)+"]bill["+billMainObj.get(key)+"]");
                        continue;
                    }
                }
                commonFalse.put(key,"view["+viewMainObj.get(key)+"]bill["+billMainObj.get(key)+"]");
            }
        }
        ObjectData viewMainObj1 = BeanUtil.deepCopy(viewMainObj,ObjectData.class);
        ObjectData billMainObj1 = BeanUtil.deepCopy(billMainObj,ObjectData.class);
        for(String key:commonTrue.keySet()){
            viewMainObj1.remove(key);
            billMainObj1.remove(key);
        }
        for(String key:commonFalse.keySet()){
            viewMainObj1.remove(key);
            billMainObj1.remove(key);
        }
        if(CollectionUtils.isNotEmpty(spaceKey)){
            for(String key:spaceKey){
                viewMainObj1.remove(key);
            }
        }
        objectMap.put("commonTrue",commonTrue);
        objectMap.put("commonFalse",commonFalse);
        objectMap.put("viewDifference",viewMainObj1);
        objectMap.put("billDifference",billMainObj1);
        return objectMap;
    }

    public Result<Void> sendErpSyncDataAppNotice(SendTextNoticeArg arg) {
        String msg = arg.getMsg();
        String ea;
        if(StringUtils.isBlank(arg.getEnterpriseAccount())){
            String tenantId = arg.getTenantId();
            int intTenantId = Integer.parseInt(tenantId);
            ea = eieaConverter.enterpriseIdToAccount(intTenantId);
        }else {
            ea=arg.getEnterpriseAccount();
        }
        List<Integer> receivers = arg.getReceivers();
        String appId = ConfigCenter.ERP_SYNC_DATA_APP_ID;
        String msgTitle = arg.getMsgTitle();
        if (StringUtils.isNotBlank(msgTitle)){
            msg = String.format("---%s---\n%s", msgTitle,msg);
        }
        //如果文本信息过长，则转换成文本文件发送
        if (msg.length()<ConfigCenter.NOTICE_MAX_SIZE){
            sendTextMessage(ea,receivers,appId,msg);
        }else {
            sendTextTransFileMsg(ea,receivers,appId,msgTitle,msg);
        }
        return Result.newSuccess();
    }

    private void sendTextTransFileMsg(String ea, List<Integer> toUserList, String appId, String title,String content){
        String tnPath = uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, content.getBytes(Charset.defaultCharset()));
        if (tnPath==null){
            log.error("sendTextTransFileMsg uploadTnFile failed,ea:{},toUserList:{},content:{}",ea,toUserList,content);
            return;
        }
        String previewUrl = String.format(userCenterService.getPreviewFilePathFormat(ea), tnPath+".txt");
        String msg = i18NStringManager.getByEi2(I18NStringEnum.s3690, String.valueOf(eieaConverter.enterpriseAccountToId(ea)),title, previewUrl);
        sendTextMessage(ea,toUserList,appId,msg);
    }

    private void sendTextMessage(String ea, List<Integer> toUserList, String appId, String content) {
        //最大长度为2000
        if (content.length() > 1998) {
            content = content.substring(0, 1900) + "...";
        }
        SendTextMessageArg arg = new SendTextMessageArg();
        arg.setMessageSendType(MessageSendTypeEnum.THIRD_PARTY_PUSH.getValue());
        MessageData textMessageVO = new MessageData();
        textMessageVO.setEnterpriseAccount(ea);
        textMessageVO.setToUserList(toUserList);
        textMessageVO.setAppId(appId);
        textMessageVO.setContent(content);
        textMessageVO.setType(MessageTypeEnum.TEXT.getType());
        textMessageVO.setPostId(idGenerator.get());
        arg.setTextMessageVO(textMessageVO);
        SendTextMessageResult sendTextMessageResult = sendMessageService.sendTextMessage(arg);
        if (!sendTextMessageResult.isSuccess()) {
            log.error("send text msg failed,arg:{},result:{}", arg, sendTextMessageResult);
        }
    }

    public String uploadTnFile(String ea, Integer userId, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile failed");
        }
        String ei = eieaConverter.enterpriseAccountToId(ea)+"";
        throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,ei);
    }

}
