package com.fxiaoke.open.erpsyncdata.apiproxy.service;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.data.GetAccessTokenData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3UltimateConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * 云星空旗舰版open api接口服务
 * <AUTHOR>
 * @date 2023-09-06
 */
public interface K3UltimateApiService {

    /**
     * 获取app token之后再获取access token
     * @return
     */
    Result<GetAccessTokenData> getAccessTokenEx(K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 单据查询
     * @param arg
     * @return
     */
    Result<K3UltimateResponseByQuery> batchQuery(String tenantId,
                                                 String dataCenterId,
                                                 String objApiName,
                                                 K3UltimateRequestByQuery arg,
                                                 boolean queryInvalid,
                                                 K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量新建
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param dataList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchAdd(String tenantId,
                                               String dataCenterId,
                                               String objApiName,
                                               List<Object> dataList,
                                               K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量更新
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param dataList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchUpdate(String tenantId,
                                                  String dataCenterId,
                                                  String objApiName,
                                                  List<Object> dataList,
                                                  K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 业务操作，比如订单变更，关闭单据等
     *
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param id
     * @param interfaceUrl 通过接口URL来标识当前操作的类型，比如订单变更，关闭单据等
     * @return
     */
    Result<K3UltimateResponseBySave2> bizChange(String tenantId,
                                                String dataCenterId,
                                                String objApiName,
                                                String id,
                                                ErpObjInterfaceUrlEnum interfaceUrl,
                                                K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量提交
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param idList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchSubmit(String tenantId,
                                                  String dataCenterId,
                                                  String objApiName,
                                                  List<String> idList,
                                                  K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量撤销提交
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param idList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchUnSubmit(String tenantId,
                                                    String dataCenterId,
                                                    String objApiName,
                                                    List<String> idList,
                                                    K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量审核
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param idList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchAudit(String tenantId,
                                                 String dataCenterId,
                                                 String objApiName,
                                                 List<String> idList,
                                                 K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量反审核
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param idList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchUnAudit(String tenantId,
                                                   String dataCenterId,
                                                   String objApiName,
                                                   List<String> idList,
                                                   K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量启用
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param idList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchEnable(String tenantId,
                                                  String dataCenterId,
                                                  String objApiName,
                                                  List<String> idList,
                                                  K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量禁用
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param idList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchDisable(String tenantId,
                                                   String dataCenterId,
                                                   String objApiName,
                                                   List<String> idList,
                                                   K3UltimateConnectParam k3UltimateConnectParam);

    /**
     * 批量删除，只有暂存状态的单据才可以删除
     * @param tenantId
     * @param dataCenterId
     * @param objApiName
     * @param idList
     * @return
     */
    Result<K3UltimateResponseBySave2> batchDelete(String tenantId,
                                                  String dataCenterId,
                                                  String objApiName,
                                                  List<String> idList,
                                                  K3UltimateConnectParam k3UltimateConnectParam);

    Result<K3UltimateResponseByT<String>> getFormIdMetaData(String tenantId,
                                                  String dataCenterId,
                                                  String formId,
                                                  K3UltimateConnectParam k3UltimateConnectParam);
}
