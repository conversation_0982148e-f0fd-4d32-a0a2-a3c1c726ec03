package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.header;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushIdentifyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpPushIdentifyEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/8/30 15:22:57
 */
@Component
public class ErpdssTokenHeaderPlaceholderServiceImpl implements HeaderPlaceholderService {

    @Autowired
    private ErpPushIdentifyDao erpPushIdentifyDao;

    @Override
    public String getPlaceholder() {
        return "${erpdss-token}";
    }

    @Override
    public String replace(String tenantId) {
        ErpPushIdentifyEntity entity = erpPushIdentifyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTokenByTenantId(tenantId);
        return entity == null ? null : entity.getToken();
    }
}
