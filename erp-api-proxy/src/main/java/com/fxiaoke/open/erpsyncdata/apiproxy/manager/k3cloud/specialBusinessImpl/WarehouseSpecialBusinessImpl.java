package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import cn.hutool.core.collection.CollectionUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3Constant;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.TimeUtils;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpOrganizationObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpOrganizationObj;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 仓库特殊逻辑
 * 仓位仓库的id使用[仓库id__仓位1id.仓位2id...]
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/24
 */
@Slf4j
@Component("BD_STOCK")
public class WarehouseSpecialBusinessImpl extends CommonStockBusinessImpl implements SpecialBusiness {

    private final Joiner warehouseComIdJoiner = Joiner.on(K3Constant.WAREHOUSE_LOC_SEPARATOR).useForNull("");
    @Autowired
    private ErpOrganizationObjManager erpOrganizationObjManager;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    /**
     * 是否仓位
     */
    private static final String IS_LOC = "isLoc";
    /**
     * 仓库编码
     */
    private static final String PARENT_WAREHOUSE = "parentWarehouseNumber";
    /**
     * 仓位值集编码
     */
    private static final String FLEX_ID_NUMBER = "FlexId.Number";

    /**
     * 获取列表数据的后动作。
     * 获取变化的仓位数据
     *
     * @param timeFilterArg
     * @param standardDataList
     * @param k3CloudApiClient
     */
    @Override
    @LogLevel(LogLevelEnum.TRACE)
    public void afterRunListData(TimeFilterArg timeFilterArg, List<StandardData> standardDataList, K3CloudApiClient k3CloudApiClient) {
//         自定义条件不同步仓位
        if (CollectionUtils.isEmpty(standardDataList) || StringUtils.isNotEmpty(timeFilterArg.getCustomFilterString())) {
            return;
        }
        Map<String, StandardData> warehouseMap = standardDataList.stream()
                .collect(Collectors.toMap(v -> v.getMasterFieldVal().getString("Number"), u -> u, (o,o2)->o));
        //不通过仓库来拉取仓位，而是通过库存变化拉取 key:仓库编码， value：仓位id
        Map<String, List<String>> whLocsMap = queryModifiedWarehouseLocs(timeFilterArg, k3CloudApiClient);
        //填充需要用到的仓库数据
        fillWarehouseMap(timeFilterArg, k3CloudApiClient, whLocsMap.keySet(), warehouseMap);
        //获取需要填充的仓位数据
        //仓库编码，仓位id，pair<仓位复合编码，仓位复合名称>
        List<K3LocVo> modifiedLocs = getModifyLocVos(whLocsMap, k3CloudApiClient);
        for (K3LocVo k3LocVo : modifiedLocs) {
            StandardData standardData = warehouseMap.get(k3LocVo.getWarehouseNumber());
            if(standardData==null){
                continue;
            }
            StandardData locStandardData = buildLocStandardData(standardData, k3LocVo.getLocComNumber(), k3LocVo.getLocComName());
            standardDataList.add(locStandardData);
        }
    }

    private void fillWarehouseMap(TimeFilterArg timeFilterArg, K3CloudApiClient apiClient,
                                  Set<String> needWhNo, Map<String, StandardData> warehouseMap) {
        Collection<String> addWhNo = CollectionUtils.removeAll(needWhNo, warehouseMap.keySet());
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setObjAPIName(timeFilterArg.getObjAPIName());
        erpIdArg.setTenantId(timeFilterArg.getTenantId());
        erpIdArg.setSyncPloyDetailSnapshotId(timeFilterArg.getSnapshotId());
        for (String s : addWhNo) {
            erpIdArg.setDataId(s);
            Result<StandardData> standardDataResult = k3DataManager.commonGet(erpIdArg, this, apiClient);
            if (standardDataResult.isSuccess()) {
                String number = standardDataResult.getData().getMasterFieldVal().getString("Number");
                warehouseMap.put(number, standardDataResult.getData());
            } else {
                log.error("get erp by id failed,result:{}", erpIdArg);
            }
        }
    }

    /**
     * @param whLocsMap key:仓库编码， value：仓位id
     */
    private List<K3LocVo> getModifyLocVos(Map<String, List<String>> whLocsMap,
                                          K3CloudApiClient k3CloudApiClient) {
        //仓库编码，仓位值字段编码列表
        Map<String, List<String>> flexValueFieldsMap = new HashMap<>();
        whLocsMap.keySet().forEach(k -> flexValueFieldsMap.put(k, getFlexValueFields(k, k3CloudApiClient)));
        //仓位字段列表
        Set<String> flexValueFieldSet = flexValueFieldsMap.values().stream()
                .flatMap(Collection::stream).collect(Collectors.toSet());
        //仓位id列表
        Set<String> locSet = whLocsMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        //从仓位值组合拉取仓位数据
        //前面有逗号
        StringBuilder flexFieldKeys = new StringBuilder().append("FID");
        for (String flexValueField : flexValueFieldSet) {
            String noField = String.format("%s.FNumber", flexValueField);
            String nameField = String.format("%s.FName", flexValueField);
            flexFieldKeys.append(",").append(noField).append(",").append(nameField);
        }
        //通过BD_FLEXVALUESDETAIL获取需要的仓位信息，可能存在库存关联了仓位id，但是BD_FLEXVALUESDETAIL不存在的情况，跳过这些数据。
        QueryArg kcArg = new QueryArg();
        kcArg.setFieldKeys(flexFieldKeys.toString());
        kcArg.appendInFilter("FID", locSet);
        kcArg.setFormId(K3CloudForm.FLEX_VALUES_DETAIL);
        Result<List<K3Model>> result = k3CloudApiClient.queryAllNoDup(kcArg);
        if (!result.isSuccess()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s254.getI18nKey(),
                    k3CloudApiClient.getTenantId(),
                    String.format(I18NStringEnum.s254.getI18nValue(), result.getErrMsg()),
                    Lists.newArrayList(result.getErrMsg())),
                    null,
                    null);
        }
        //key:locId,value:locData
        Map<String, K3Model> locDataMap = result.getData().stream().collect(Collectors.toMap(k -> k.getString("FID"), u -> u));
        List<K3LocVo> locVos = new ArrayList<>();
        //组织仓位信息
        List<String> notFoundLocs = new ArrayList<>();
        whLocsMap.forEach((wh, locs) -> {
            for (String loc : locs) {
                K3Model locData = locDataMap.get(loc);
                if (locData == null) {
                    notFoundLocs.add(loc);
                    continue;
                }
                List<String> flexValueFields = flexValueFieldsMap.get(wh);
                String noJoin = flexValueFields.stream().map(p -> locData.getString(p + ".FNumber")).collect(Collectors.joining(K3Constant.LOC_SEPARATOR));
                String nameJoin = flexValueFields.stream().map(p -> locData.getString(p + ".FName")).collect(Collectors.joining(K3Constant.LOC_SEPARATOR));
                locVos.add(K3LocVo.of(wh, loc, noJoin, nameJoin));
            }
        });
        if (!notFoundLocs.isEmpty()) {
            log.info("exist not found locs:{}", notFoundLocs);
        }
        return locVos;
    }

    /**
     * 独立获取数据接口
     *
     * @return
     */
    @Override
    public boolean needSpecialGetAndQuery(String key) {
        //获取仓位
        return key != null && key.contains(K3Constant.WAREHOUSE_LOC_SEPARATOR);
    }

    /**
     * 独立获取仓位数据
     *
     * @param erpIdArg
     * @param apiClient
     * @return
     */
    @Override
    public Result<StandardData> specialGetErpObjData(ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        String locComId = erpIdArg.getDataId();
        Iterator<String> iterator = Splitter.on(K3Constant.WAREHOUSE_LOC_SEPARATOR).split(locComId).iterator();
        String warehouseNumber = iterator.next();
        String locComNo = iterator.next();
        ErpIdArg idArg2 = new ErpIdArg();
        BeanUtils.copyProperties(erpIdArg, idArg2);
        idArg2.setDataId(warehouseNumber);
        //获取仓库数据
        Result<StandardData> standardDataResult = k3DataManager.commonGet(idArg2, this, apiClient);
        if (!standardDataResult.isSuccess()) {
            return standardDataResult;
        }
        //避开仓位值有用.拆分的情况
        QueryArg queryArg = new QueryArg();
        //库存组织编码，仓库id，仓库编码，仓库名称，批号（应该为null），仓位id
        queryArg.setFormId(K3CloudForm.STK_Inventory);
        queryArg.setFieldKeys("FStockOrgId.FNumber,FStockId,FStockId.FNumber,FStockName,FLot.FNumber,FStocklocId");
        queryArg.appendFilterString("FStocklocId != 0");
        queryArg.appendEqualFilter("FStockId.FNumber", warehouseNumber);
        Result<List<K3Model>> result = apiClient.queryAllNoDup(queryArg);
        if (!result.isSuccess()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s255.getI18nKey(),
                    apiClient.getTenantId(),
                    String.format(I18NStringEnum.s255.getI18nValue(), result.getErrMsg()),
                    Lists.newArrayList(result.getErrMsg())),
                    null,
                    null);
        }
        Map<String, List<String>> modifyLoc = result.getData().stream().collect(
                Collectors.groupingBy(k -> k.getString("FStockId.FNumber"),
                        Collectors.mapping(u -> u.getString("FStocklocId"), Collectors.toList())));
        List<K3LocVo> allLocVos = getModifyLocVos(modifyLoc, apiClient);
        Optional<K3LocVo> k3LocVo = allLocVos.stream()
                .filter(v -> v.getLocComNumber().equals(locComNo))
                .findAny();
        if (k3LocVo.isPresent()) {
            StandardData standardData = standardDataResult.getData();
            String locComNumber = k3LocVo.get().getLocComNumber();
            String locComName = k3LocVo.get().getLocComName();
            StandardData locStandardData = buildLocStandardData(standardData, locComNumber, locComName);
            return Result.newSuccess(locStandardData);
        }
        throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s256.getI18nKey(),
                apiClient.getTenantId(),
                String.format(I18NStringEnum.s256.getI18nValue(), locComId),
                Lists.newArrayList(locComId)),
                null,
                null);
    }

    public StandardData buildLocStandardData(StandardData standardData, String locComNumber, String locComName) {
        ObjectData masterFieldVal = standardData.getMasterFieldVal();
        String warehouseName = masterFieldVal.getString("name");
        String warehouseNumber = masterFieldVal.getString("Number");
        StandardData locStandardData = new StandardData();
        locStandardData.setObjAPIName(standardData.getObjAPIName());
        ObjectData locObjData = ObjectData.convert(masterFieldVal);
        locObjData.put("Number", warehouseComIdJoiner.join(warehouseNumber, locComNumber));
        locObjData.put("name", warehouseComIdJoiner.join(warehouseName, locComName));
        locObjData.put(IS_LOC, true);
        locObjData.put(PARENT_WAREHOUSE, warehouseNumber);
        locStandardData.setMasterFieldVal(locObjData);
        return locStandardData;
    }

    /**
     * 获取可能存在仓位变化的仓库
     * 通过轮询即时库存实现
     * 需要分页不然上层可能永远以为没拉完数据
     * 但是是查了所有出来才分页的
     *
     * @return
     */
    public Map<String, List<String>> queryModifiedWarehouseLocs(TimeFilterArg timeFilterArg, K3CloudApiClient apiClient) {
        String tenantId = timeFilterArg.getTenantId();
        String startDtf = TimeUtils.getDateTime(timeFilterArg.getStartTime());
        String endDtf = TimeUtils.getDateTime(timeFilterArg.getEndTime());
        QueryArg queryArg = new QueryArg();
        //库存组织编码，仓库id，仓库编码，仓库名称，批号（应该为null），仓位id
        queryArg.setFormId(K3CloudForm.STK_Inventory);
        queryArg.setFieldKeys("FStockOrgId.FNumber,FStockId,FStockId.FNumber,FStockName,FLot.FNumber,FStocklocId");
        queryArg.setFilterString(String.format("FUpdateTime >= {ts'%s'} and FUpdateTime < {ts'%s'}", startDtf, endDtf));
        queryArg.appendFilterString("FStocklocId != 0");
        List<String> orgNos = getSyncStockOrgNos(tenantId, apiClient.getDataCenterId());
        queryArg.appendInFilter("FStockOrgId.FNumber", orgNos);
        Result<List<K3Model>> result = apiClient.queryAllNoDup(queryArg);
        if (!result.isSuccess()) {
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s257.getI18nKey(),
                    apiClient.getTenantId(),
                    String.format(I18NStringEnum.s257.getI18nValue(), result.getErrMsg()),
                    Lists.newArrayList(result.getErrMsg())),
                    null,
                    null);
        }
        List<K3Model> list = new ArrayList<>();
        try {
            //为什么拉取全部的库存的仓位呢？因为直接在k3的webapi的参数传进分页参数，可能返回的库存里面包含的仓位都是同一个或者部分仓位。所以现在返回全部的仓位再手动分页去重。
            list= CollectionUtil.sub(result.getData(),timeFilterArg.getOffset(), timeFilterArg.getOffset() + timeFilterArg.getLimit());
        } catch (IndexOutOfBoundsException ignored) {
            log.info("queryModifiedWarehouseLocs error:{},{}",ignored.getMessage(), TraceUtil.get());

        }
        Map<String, List<String>> modifyLoc = list.stream().collect(
                Collectors.groupingBy(k -> k.getString("FStockId.FNumber"),
                        Collectors.mapping(u -> u.getString("FStocklocId"), Collectors.toList())));
        return modifyLoc;
    }

    @Override
    public void beforeRunView(ViewArg viewArg, ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        List<ErpOrganizationObj> syncWareHouseOrgs = getSyncStockOrgs(erpIdArg.getTenantId(), apiClient.getDataCenterId());
        //如果同一家企业的多个组织只有一个组织的仓库需要同步，允许两个仓库有相同的仓库编码，同步仓库时，需要设置只有一个组织机构允许仓库同步
        if (syncWareHouseOrgs.size() == 1) {
            try {
                Integer createOrgId = Integer.valueOf(syncWareHouseOrgs.get(0).getOrgId());
                viewArg.setCreateOrgId(createOrgId);
            } catch (Exception ignored) {
            }
        }
    }


    /**
     * 使用笛卡尔积法，穷举所有可能的仓位。可能生成很大量的数据
     * 暂时弃用
     *
     * @param standardData
     * @param k3CloudApiClient
     * @return
     */
    @Deprecated
    private List<StandardData> getAllPossibleLocs(StandardData standardData,
                                                  K3CloudApiClient k3CloudApiClient) {
        List<ObjectData> flexItems = standardData.getDetailFieldVals().get("StockFlexItem");
        //用于储存仓位的编码、名称列表
        List<Pair<String, String>> locNumberNames = new ArrayList<>();
        for (ObjectData flexItem : flexItems) {
            String flexNumber = flexItem.getString(FLEX_ID_NUMBER);
            List<K3Model> flexValues = getFlexMap(k3CloudApiClient).get(flexNumber);
            locNumberNames = addLocItem(locNumberNames, flexValues);
        }

        List<StandardData> locList = new ArrayList<>();
        for (Pair<String, String> locNumberName : locNumberNames) {
            StandardData locStandardData = buildLocStandardData(standardData, locNumberName.getLeft(), locNumberName.getRight());
            locList.add(locStandardData);
        }
        return locList;
    }

    @Deprecated
    private List<Pair<String, String>> addLocItem(List<Pair<String, String>> currentPairs, List<K3Model> flexValues) {
        //对当前值列表currentPairs分别拼接上增加的值集addItem对应的值列表flexValues,类似于笛卡尔积
        //对flatmap，可查看java.util.stream.Stream.flatMap。
        List<Pair<String, String>> newPairs = flexValues.stream().flatMap(flexValue -> {
            String fFlexValueNumber = flexValue.getString("FFlexValueNumber");
            String fFlexValueName = flexValue.getString("FFlexValueName");
            if (currentPairs.isEmpty()) {
                //如果原来列表为空，直接放到列表里
                return Stream.of(Pair.of(fFlexValueNumber, fFlexValueName));
            } else {
                //如果原来列表不为空，则每对值拼上新值集的值列表
                return currentPairs.stream().map(currentPair ->
                        Pair.of(currentPair.getLeft() + K3Constant.LOC_SEPARATOR + fFlexValueNumber,
                                currentPair.getRight() + K3Constant.LOC_SEPARATOR + fFlexValueName));
            }
        }).collect(Collectors.toList());
        return newPairs;
    }
    /**
     * 获取仓库时的前置动作
     *
     * @param queryArg
     * @param timeFilterArg
     * @param k3CloudApiClient
     */
    @Override
    public void beforeRunBillQuery(QueryArg queryArg, TimeFilterArg timeFilterArg, K3CloudApiClient k3CloudApiClient) {
        //调用组织机构对象接口
        List<String> orgNos = getSyncStockOrgNos(timeFilterArg.getTenantId(), k3CloudApiClient.getDataCenterId());
        // 设置参数
        queryArg.appendInFilter("FUseOrgId.FNumber", orgNos);
        queryArg.setOrderString("FNumber ASC");
    }
}
