package com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
/**
 * <AUTHOR>
 * @Date: 9:58 2020/12/22
 * @Desc:
 */
public interface SpecialObjHandler {

    /**
     * 获取列表数据的后动作。
     */
    void afterQueryListData(TimeFilterArg timeFilterArg, List<StandardData> standardDataList,String dataCenterId);

    /**
     * 根据id获取数据的后动作。
     */
    void afterReSyncDataById(ErpIdArg erpIdArg, List<StandardData> standardDataList,String dataCenterId);

    String getObjApiName();

    /**
     * 创建数据的前动作。
     */
    void beforeCreateErpObjData(String tenantId,StandardData standardData,String dataCenterId);
    /**
     * 创建数据的后动作。
     */
    void afterCreateErpObjData(String tenantId,StandardData standardData, Result<ErpIdResult> result,String dataCenterId);
    /**
     * 更新数据的前动作。
     */
    void beforeUpdateErpObjData(String tenantId,StandardData standardData,String dataCenterId);
    /**
     * 更新数据的后动作。
     */
    void afterUpdateErpObjData(String tenantId,StandardData standardData, Result<ErpIdResult> result,String dataCenterId);


}
