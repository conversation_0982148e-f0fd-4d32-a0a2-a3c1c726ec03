package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * k3c订单新变更单工具类
 */
@Slf4j
public class XOrderUtils {
    public static Map<String, Map<Integer,Integer>> getSalesOrderId2XOrderId(ViewResult viewResult){
        Map<String, Map<Integer,Integer>> data= Maps.newHashMap();
        data.put("order",Maps.newHashMap());
        data.put("detail",Maps.newHashMap());
        data.put("SaleOrderPlan",Maps.newHashMap());
        K3Model k3Model=viewResult.getResult().getResult();
        Integer masterSalesOrderId=k3Model.getInt("PKIDX");
        Integer masterXOrderId=k3Model.getInt("Id");
        data.get("order").put(masterSalesOrderId,masterXOrderId);
        List<K3Model> saleOrderEntry = k3Model.getDetails("SaleOrderEntry");
        for(K3Model entry:saleOrderEntry){
            Integer salesOrderEntryId=entry.getInt("PKIDX");
            Integer xOrderEntryId=entry.getInt("Id");
            data.get("detail").put(salesOrderEntryId,xOrderEntryId);
        }
        List<K3Model> SaleOrderPlan = k3Model.getDetails("SaleOrderPlan");
        for(K3Model entry:SaleOrderPlan){
            if (entry.containsKey("PKIDX")){
                Integer PKIDX=entry.getInt("PKIDX");
                Integer Id=entry.getInt("Id");
                data.get("SaleOrderPlan").put(PKIDX,Id);
            }
        }
        log.info("XOrderUtils.SalesOrderId2XOrderId={}",data);
        return data;
    }

    /**
     * 把带层级结构的字段拍平
     * 比如：输入：{"FMaterialId":{"FNumber":"number200"}} 拍平后：FMaterialId.FNumber
     * @param map
     * @return
     */
    public static String getSaveFieldApiName(Map<String,Object> map) {
        String apiName = null;
        while (!map.isEmpty()) {
            for(String key : map.keySet()) {
                apiName = key;
                Object value = map.get(key);
                if(value instanceof Map) {
                    return apiName + "." + getSaveFieldApiName((Map<String, Object>) value);
                }
                return apiName;
            }
        }
        return apiName;
    }

    /**
     * 获取拍平的字段apiName list
     * @param k3Model
     * @return
     */
    public static List<String> getSaveFieldList(K3Model k3Model) {
        List<String> keyList = new ArrayList<>();
        for(String key : k3Model.keySet()) {
            Object value = k3Model.get(key);
            if(value instanceof Map) {
                Map<String,Object> map = new HashMap<>();
                map.put(key,value);
                String fieldApiName = getSaveFieldApiName(map);
                //对K3C辅助属性字段做特殊处理
                if(StringUtils.containsIgnoreCase(fieldApiName,"FAuxPropId.FAUXPROPID__")) {
                    String fieldApiNameOld = fieldApiName;
                    List<String> itemList = Splitter.on("__").splitToList(fieldApiName);
                    log.info("XOrderUtils.getSaveFieldList,itemList={}",itemList);
                    fieldApiName = "FAuxPropId."+itemList.get(1);
                    log.info("XOrderUtils.getSaveFieldList,fieldApiNameOld={},fieldApiName={}",fieldApiNameOld,fieldApiName);
                }
                keyList.add(fieldApiName);
            } else {
                keyList.add(key);
            }
        }
        return keyList;
    }

    /**
     * 从K3C查询订单明细数据
     * @param salesOrderId 订单ID
     * @param k3Model 订单明细字段模板
     * @param apiClient
     * @return
     */
    public static List<K3Model> querySaleOrderEntry(String salesOrderId, K3Model k3Model, K3CloudApiClient apiClient) {
        List<String> fieldList = getSaveFieldList(k3Model);
        fieldList.addAll(0,Lists.newArrayList("FID,FBillNo,FSaleOrderEntry_FEntryID"));
        fieldList.remove("FPKIDX");
        fieldList.remove("FChangeType");

        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.SAL_SaleOrder);
        queryArg.setFieldKeysByList(fieldList);
        queryArg.setFilterString("FID='"+salesOrderId+"'");
        queryArg.setLimit(2000);
        log.info("XOrderUtils.querySaleOrderEntry,queryArg={}",JSONObject.toJSONString(queryArg));
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        log.info("XOrderUtils.querySaleOrderEntry,result={}",JSONObject.toJSONString(result));
        return result.getData();
    }

    /**
     * 查找销售订单明细数据
     * @param entryList
     * @param entryId
     * @return
     */
    public static K3Model getSaleOrderEntry(List<K3Model> entryList, String entryId) {
        for(K3Model model : entryList) {
            String fEntryID = model.getString("FSaleOrderEntry_FEntryID");
            if(StringUtils.equalsIgnoreCase(fEntryID,entryId)) {
                return model;
            }
        }
        return null;
    }

    /**
     * 组装已删除的订单明细数据
     * @param k3Model
     * @return
     */
    public static K3Model assembleDeleteSaleOrderEntry(K3Model k3Model) {
        k3Model.remove("FID");
        k3Model.remove("FBillNo");
        k3Model.remove("FSaleOrderEntry_FEntryID");

        List<String> keyList = new ArrayList<>();
        keyList.addAll(k3Model.keySet());

        Iterator<String> iterator = keyList.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if(StringUtils.contains(key,".")) {
                Object value = k3Model.get(key);
                Map<String,Object> fieldMap = new HashMap<>();
                assembleField(key,value,fieldMap);
                k3Model.remove(key);
                k3Model.putAll(fieldMap);
            }
        }

        log.info("XOrderUtils.assembleDeleteSaleOrderEntry,k3Model={}",JSONObject.toJSONString(k3Model));
        return k3Model;
    }

    /**
     * 把拍平的字段组装成复合字段
     * 比如：FMaterialId.FNumber 组装成 {"FMaterialId":{"FNumber":"number200"}}
     * @param key
     * @param value
     * @param filedMap
     */
    private static void assembleField(String key,Object value,Map<String,Object> filedMap) {
        List<String> items = new ArrayList<>();
        items.addAll(Splitter.on(".").splitToList(key));
        if(items.size()==0) return;

        String first = items.remove(0);
        if(items.size()>0) {
            key = Joiner.on(".").join(items);
            Map<String,Object> map = new HashMap<>();
            filedMap.put(first,map);
            assembleField(key, value, map);
        } else {
            filedMap.put(key,value);
        }
    }

    public static void main(String[] args) {
//        HashMap<String,Object> hashMap = new HashMap<>();
//
//        HashMap<String,String> map3 = new HashMap<>();
//        map3.put("FNumber3","number300");
//
//        HashMap<String,Object> map2 = new HashMap<>();
//        map2.put("FNumber","number200");
//
//        hashMap.put("FMaterialId",map2);
//
//        String json = JSONObject.toJSONString(hashMap);
//
//        //String saveFieldApiName = getSaveFieldApiName(hashMap);
//        String json2 = "{\"FRowType\":\"Standard\", \"FStockOrgId\":{\"FNumber\":100}, \"FMaterialId\":{\"FNumber\":\"55.01.01.0042\"}, \"FTaxPrice\":0.000000, \"FEntryTaxRate\":13.00, \"FBCCPX\":{\"FNumber\":\"CPX001\"}, \"FChangeType\":\"B\", \"FPKIDX\":118232}";
//        K3Model k3Model = JSONObject.parseObject(json2,K3Model.class);
//        List<String> saveFieldList = getSaveFieldList(k3Model);
//        System.out.println(saveFieldList);
//        Map<String,Object> fieldMap = new HashMap<>();
////        assembleField("test1.test2.test3","hello boy",fieldMap);
////        System.out.println(fieldMap);

        K3Model k3Model = new K3Model();
        k3Model.put("hello","hello boy");
        k3Model.put("test1.test2","test only");
        k3Model.put("hello1.hello2","hello only");
        K3Model k3Model1 = assembleDeleteSaleOrderEntry(k3Model);
        System.out.println(k3Model1);

        String fieldApiName = "FAuxPropId.FAUXPROPID__FF100001.FNumber";
        if(StringUtils.containsIgnoreCase(fieldApiName,"FAuxPropId.FAUXPROPID__")) {
            List<String> itemList = Splitter.on("__").splitToList(fieldApiName);
            fieldApiName = "FAuxPropId."+itemList.get(1);
        }
        System.out.println(fieldApiName);
    }
}
