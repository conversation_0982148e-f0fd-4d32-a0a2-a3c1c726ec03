package com.fxiaoke.open.erpsyncdata.apiproxy.node;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.TriggerPollingMongoManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.context.Push2TempCtx;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsNodeProcessor;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> (^_−)☆
 */
@Service
@Slf4j
public class NodePush2TempProcessor extends AbsNodeProcessor<Push2TempCtx> {
    @Autowired
    private IdFieldKeyManager idFieldKeyManager;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private TriggerPollingMongoManager triggerPollingMongoManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;


    private static final ThreadPoolExecutor triggerQueryTempExecutor = new NamedThreadPoolExecutor("trigger_query_temp", 10, Integer.MAX_VALUE);

    public NodePush2TempProcessor() {
        super(DataNodeNameEnum.EnterTempData);
    }

    @Override
    protected void preReport(Push2TempCtx ctx) {
        //先补录一下streamIds
        List<String> streamIds = syncPloyDetailManager.listEnableStreamIdsBySrcObj(ctx.getTenantId(), ctx.getDcId(), ctx.getSplitObjApiName(), SyncPloyDetailStatusEnum.ENABLE.getStatus());
        ctx.setStreamIds(streamIds);
    }

    @Override
    public Push2TempCtx processMessage(Push2TempCtx ctx) {
        String tenantId = ctx.getTenantId();
        String dcId = ctx.getDcId();
        String splitObjApiName = ctx.getSplitObjApiName();
        String realObjApiName = ctx.getRealObjApiName();
        IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(tenantId, dcId, splitObjApiName, realObjApiName);
        StandardListData standardListData = new StandardListData();
        standardListData.setDataList(ctx.getDataList());
        //注，把SpecialObjHandler的特殊处理省略了。
        erpTempDataManager.batchUpsertErpTempData(tenantId, dcId, realObjApiName, ctx.getOperationType(), standardListData, idFieldKey, !ctx.isDirectSync(),ctx.getLocale());
        if (!ctx.isDirectSync()) {//异步触发轮询临时库
            triggerQueryTempExecutor.submit(() -> triggerPollingTemp(tenantId, splitObjApiName, realObjApiName, ctx.getDataList()));
        }
        return ctx;
    }

    /**
     * 触发轮询临时库
     */
    private void triggerPollingTemp(String tenantId, String visualObjectApiName, String objectApiName, List<StandardData> dataList) {
        TriggerPollingData triggerPollingData = new TriggerPollingData();
        triggerPollingData.setTenantId(tenantId);
        triggerPollingData.setObjApiName(Sets.newHashSet(visualObjectApiName));
        triggerPollingData.setTriggerTime(System.currentTimeMillis());
        triggerPollingMongoManager.triggerPolling(triggerPollingData);
        log.info("receive data success and trigger tenantId:{},objApiName:{},datas:{}", tenantId, objectApiName, dataList);
    }
}
