package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.AviatorUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.RangeConditionFieldMappingData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 价目表同步特殊逻辑
 *
 * <AUTHOR>
 * @date 2021/3/11
 */
@Slf4j
@Component("BD_SAL_PriceList")
public class PriceListSpecialBusinessImpl implements SpecialBusiness {

    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private K3RangerConvertManager rangerConvertManager;


    /**
     * 可售范围拼接上价目表从对象
     *
     * @param erpIdArg
     * @param standardData
     * @param apiClient
     */
    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        log.info("PriceListSpecialBusinessImpl.afterRunView,erpIdArg={},standardData={},erpData={}",erpIdArg,standardData,erpData);
        /**
         * PriceListSpecialBu
         * sinessImpl.afterRunView,erpIdArg=ErpIdArg(tenantId=81243, objAPIName=BD_SAL_PriceList,
         * dataId=828509, dataIdIsNumber=false,
         * includeDetail=true, syncPloyDetailSnapshotId=5fd416b5b1c54793b682e9b52d9cc668,
         * sourceEventType=null),
         * standardData={"objAPIName":n
         * ull,"syncLogId":null,"dataVersion":null,
         * "masterFieldVal":{"erp_id":"828509","erp_num":"XSJMB0160","id":828509,"FName":"\u6D4B\u8BD5\u7C7B\u522B","FNumber":"XSJMB0160","FDescription":" ","FCreateDate":"2023-03-16T17:58:36.443","FEffectiveDate":"2023-03-1
         * 6T00:00:00","FExpiryDate":"2100-01-01T00:00:00","FApproveDate":null},
         * "detailFieldVals":{"BD_SAL_PriceList.SAL_APPLYSALESMAN":[],
         * "BD_SAL_PriceList.SAL_PRICELISTENTRY":[{"DetailId":108185,"FPriceUnitId.Name":"\u4E2A","FPriceUnitId.FNumber":"002","FPriceUnitId.Id":103718,"FPriceBase":1.0,"FToQty":0.0,"FPrice":190.0,"FDownPrice":190.0,"FEntryEffectiveDate":"2023-03-16T00:00:00","FEntryExpiryDate":"2100-01-01T00:00:00","FEntryForbidStatus":"A","FForbiderId.Id":"0","FForbidDate":null,"FMaterialTypeId.Name":"\u539F\u6750\u6599","FMaterialTypeId.FNumber":"CHLB01_SYS","FMaterialTypeIdFCATEGORYID":237,"FMaterialId.Name":"\u57F9\u8BAD\u52A9\u624B\u8BFE\u65F6","FMaterialId.FNumber":"00007","FMaterialIdFMATERIALID":103724,"FRowAuditStatus":"U","FUnitID.Name":"\u4E2A","FUnitID.FNumber":"002","FUnitID.Id":103718,"FFromQty":0.0,"FBomId.Name":null,"FBomId.FNumber":null,"FBomId.Id":0,"FLot.Name":null,"FLot.FNumber":null,"FLotFLOTID":0,"FDefBaseDataO.Name":null,"FDefBaseDataO.FNumber":null,"FDefBaseDataOFStockId":0,"FDefBaseDataT.Name":null,"FDefBaseDataT.FNumber":null,"FDefBaseDataTFStockId":0,"FDefAssistantO.FDataValue":null,"FDefAssistantO.FNumber":null,"FDefAssistantO.Id":" ","FDefAssistantT.FDataValue":null,"FDefAssistantT.FNumber":null,"FDefAssistantT.Id":" ","FDefTextO":" ","FDefTextT":" ","FDefaultPriceO":0.0,"FDefaultPriceT":0.0,"FMaterialGroupId.Name":null,"FMaterialGroupId.FNumber":null,"FMaterialGroupId.Id":0,"FMapId.Name":null,"FMapId.FNumber":null,"FMapId.Id":" "}],
         * "BD_SAL_PriceList.SAL_APPLYCUSTOMER":[{"DetailId":100166,"FCustID.Name":null,"FCustID.FNumber":null,"FCustIDFCUSTID":0,"FCustTypeID.FDataValue":"SSSVIP","FCustTypeID.FNumber":"0000001",
         * "FCustTypeID.Id":"617f91cd9bc165","FIsDefList":false,"FCustGroup":0},
         * {"DetailId":100167,"FCustID.Name":null,"FCustID.FNumber":null,"FCustIDFCUSTID":0,
         * "FCustTypeID.FDataValue":"\u91CD\u8981\u5BA2\u6237","FCustTypeID.FNumber":"ge0z23ob",
         * "FCustTypeID.Id":"5f06c6179054e9","FIsDefList":false,"FCustGroup":0}]}
         */

        ErpTenantConfigurationEntity rangeEntity = tenantConfigurationManager.findOne(erpIdArg.getTenantId(), "ALL", ErpChannelEnum.ERP_K3CLOUD.name(), TenantConfigurationTypeEnum.RANG_EXPRESSION.name());
        if(ObjectUtils.isEmpty(rangeEntity)){
            return;
        }

        List<RangeConditionFieldMappingData> rangeConditionFieldMappingData = JSONArray.parseArray(rangeEntity.getConfiguration(), RangeConditionFieldMappingData.class);
        rangeConditionFieldMappingData = rangeConditionFieldMappingData.stream().filter(item -> item.getSourceErpObjApiName().equals("BD_SAL_PriceList.BillHead")).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(rangeConditionFieldMappingData)){
            return;
        }
        for (RangeConditionFieldMappingData rangeConditionFieldMappingDatum : rangeConditionFieldMappingData) {
            SyncPloyDetailEntity syncPloyDetailEntity = syncPloyDetailManager.getEntryById(erpIdArg.getTenantId(), rangeConditionFieldMappingDatum.getSyncPloyDetailId());

            if(ObjectUtils.isEmpty(syncPloyDetailEntity)){
                return;
            }
            String afterConditionRange= StringUtils.EMPTY;
            for (RangeConditionFieldMappingData.ConditionMapping conditionMapping : rangeConditionFieldMappingDatum.getMatchCondition()) {
                boolean expressionTrue=AviatorUtils.normalConditionValid(conditionMapping.getSourceExpression(),standardData.getMasterFieldVal(),true);
                if(expressionTrue){
                    //走表达式的时候，适用客户的从对象就不需要传递

                    if(CollectionUtils.isNotEmpty(conditionMapping.getRangeFieldMapping())){
                        List<ObjectData> needConvertData = standardData.getDetailFieldVals().get(rangeConditionFieldMappingDatum.getSourceConditionObjectApiName());
                        List<ObjectData> destObjectList= Lists.newArrayList();
                        for (ObjectData needConvertDatum : needConvertData) {
                            //获取对接的相关字段。走一遍字段转换的逻辑
                            ObjectData objectData = rangerConvertManager.convertRangeData(erpIdArg.getTenantId(),syncPloyDetailEntity.getSourceDataCenterId(), needConvertDatum, EventTypeEnum.UPDATE.getType(),
                                    erpIdArg.getTenantId(), syncPloyDetailEntity.getDestObjectApiName(), conditionMapping.getRangeFieldMapping(), TenantTypeEnum.ERP.getType(), TenantTypeEnum.CRM.getType());
                            destObjectList.add(objectData);
                            //根据返回的值，组装条件。金蝶目前转义到纷享的都是AND条件
                        }
                        /**
                         * [{"object_describe_api_name":"PriceBookObj","tenant_id":"84801","account_type":"lA00s9g93"},
                         * {"object_describe_api_name":"PriceBookObj","tenant_id":"84801","account_type":"qS0dx5an2"}]
                         */
                        afterConditionRange= generateCondition(destObjectList,rangeConditionFieldMappingDatum, conditionMapping.getRangeFieldMapping());
                        //清除从对象
                        standardData.getDetailFieldVals().get(rangeConditionFieldMappingDatum.getSourceConditionObjectApiName()).clear();
                    }else{
                        //类似固定的字符串，就不需要填写映射表数据。直接取dest表达式
                        afterConditionRange=conditionMapping.getDestExpression();
                    }
                    standardData.getMasterFieldVal().put(rangeConditionFieldMappingDatum.getSourceRangeFieldApiName(),afterConditionRange);
                }
            }
        }
        log.info("PriceListSpecialBusinessImpl.convert,erpIdArg={},standardData={},erpData={}", erpIdArg, standardData, erpData);
    }


    public String generateCondition(List<ObjectData> convertObjData,RangeConditionFieldMappingData rangeConditionFieldMappingData,List<FieldMappingData> fieldMappingData){

        Map<String, FieldMappingData> fieldDestDataMap
                = fieldMappingData.stream().collect(Collectors.toMap(FieldMappingData::getDestApiName, v -> v, (u, v) -> u));

        Map<String,List<Object>> valuesMap= Maps.newHashMap();
        for (ObjectData convertObjDatum : convertObjData) {
            for (FieldMappingData fieldMappingDatum : fieldMappingData) {
                Object object = convertObjDatum.get(fieldMappingDatum.getDestApiName());
                if(ObjectUtils.isEmpty(object)){
                    continue;
                }
                valuesMap.computeIfAbsent(fieldMappingDatum.getDestApiName(), k -> new ArrayList<>()).add(object);
            }
        }
        //{"account_type":["lA00s9g93","qS0dx5an2"]}
        /**
         * [
         *     {
         *         "filters":[
         *             {
         *                 "field_name":"account_type",
         *                 "operator":"IN",
         *                 "field_values":[
         *                     "1",
         *                     "lA00s9g93"
         *                 ],
         *                 "connector":"AND",
         *                 "type":"select_one"
         *             }
         *         ],
         *         "connector":"OR"
         *     }
         * ]
         */
        RangeConditionFieldMappingData.CrmFilterData crmFilterData=new RangeConditionFieldMappingData.CrmFilterData();

        crmFilterData.setConnector("OR");
        for (String dataKey : valuesMap.keySet()) {
            RangeConditionFieldMappingData.CrmConditionExpression crmConditionExpression=new RangeConditionFieldMappingData.CrmConditionExpression();
            crmConditionExpression.setConnector("AND");
            crmConditionExpression.setOperator("IN");
            crmConditionExpression.setField_name(dataKey);
            crmConditionExpression.setField_values(valuesMap.get(dataKey));
            crmConditionExpression.setType(fieldDestDataMap.get(dataKey).getDestType());
            crmFilterData.addFilterData(crmConditionExpression);
        }
        RangeConditionFieldMappingData.ExpressionFilterData expressionFilterData=new RangeConditionFieldMappingData.ExpressionFilterData();
        if(rangeConditionFieldMappingData.getDestRangeFieldApiName().equals("apply_org_range")){

            expressionFilterData.setType("ORG");
            List<Object> employees=Lists.newArrayList();
            List<Object> departments=Lists.newArrayList();
            for (RangeConditionFieldMappingData.CrmConditionExpression field_value : crmFilterData.getFilters()) {
                if(field_value.getType().equals("department")){
                    departments.addAll(field_value.getField_values());
                }else{
                    employees.addAll(field_value.getField_values());
                }
            }
            Map<String,Object> dataMap=Maps.newHashMap();
            dataMap.put("employees",employees);
            dataMap.put("departments",departments);
            expressionFilterData.setValue(JSONObject.toJSONString(dataMap));
            return JSONObject.toJSONString(expressionFilterData);
        }

        expressionFilterData.setType("CONDITION");
        expressionFilterData.setValue(JSONObject.toJSONString(Lists.newArrayList(crmFilterData)));
        return JSONObject.toJSONString(expressionFilterData);

    }

}
