package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.utils;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateRequestByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.K3UltimateResponseByQuery;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg.K3UltimateQueryBaseArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.K3UltimateApiService;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ConnectParamUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class K3UltimateUtils {
    public static Result<K3UltimateResponseByQuery> queryByNumber(String tenantId,
                                                                  String dataCenterId,
                                                                  String objApiName,
                                                                  String number,
                                                                  Map<String, Object> customQueryArg,
                                                                  K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo) {

        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> query = K3UltimateRequestByQuery.buildBaseRequestByNumber(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.queryMasterById,
                Lists.newArrayList(number),
                customQueryArg,
                1,
                100,
                null);

        Result<K3UltimateResponseByQuery> batchQuery = k3UltimateApiService.batchQuery(tenantId,dataCenterId,objApiName, query, false,ConnectParamUtil.parseK3Ultimate(connectInfo.getConnectParams()));
        log.info("K3UltimateUtils.queryByNumber,query={},batchQuery={}", query,batchQuery);
        return batchQuery;
    }

    public static Result<K3UltimateResponseByQuery> queryById(String tenantId,
                                                                  String dataCenterId,
                                                                  String objApiName,
                                                                  String id,
                                                                  Map<String, Object> customQueryArg,
                                                                  K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo) {

        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> query = K3UltimateRequestByQuery.buildBaseRequestById(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.queryMasterById,
                Lists.newArrayList(id),
                customQueryArg,
                1,
                100,
                null);

        Result<K3UltimateResponseByQuery> batchQuery = k3UltimateApiService.batchQuery(tenantId,dataCenterId,objApiName, query, false,ConnectParamUtil.parseK3Ultimate(connectInfo.getConnectParams()));
        log.info("K3UltimateUtils.queryById,query={},batchQuery={}", query,batchQuery);
        return batchQuery;
    }

    public static Result<K3UltimateResponseByQuery> queryByIdOrNumber(String tenantId,
                                                              String dataCenterId,
                                                              String objApiName,
                                                              String id,
                                                              String number,
                                                              Map<String, Object> customQueryArg,
                                                              K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo) {
        List<String> ids=Lists.newArrayList(),numbers=Lists.newArrayList();
        if(StringUtils.isNotBlank(id)){
            ids.add(id);
        }
        if(StringUtils.isNotBlank(number)){
            numbers.add(number);
        }
        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> query = K3UltimateRequestByQuery.buildBaseRequestByIdOrNumber(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.queryMasterById,
                ids,
                numbers,
                customQueryArg,
                1,
                100,
                null);

        Result<K3UltimateResponseByQuery> batchQuery = k3UltimateApiService.batchQuery(tenantId,dataCenterId,objApiName, query, false,ConnectParamUtil.parseK3Ultimate(connectInfo.getConnectParams()));
        log.info("K3UltimateUtils.queryByIdAndNumber,query={},batchQuery={}", query,batchQuery);
        return batchQuery;
    }

    public static Result<JSONObject> queryXSOrderDataByOrderBillNo(String tenantId,
                                                                   String dataCenterId,
                                                                   String objApiName,
                                                                   String billNo,
                                                                   K3UltimateApiService k3UltimateApiService, ErpConnectInfoEntity connectInfo) {

        K3UltimateQueryBaseArg baseArg = new K3UltimateQueryBaseArg();
        baseArg.setBillno(Lists.newArrayList(billNo));
        baseArg.put("billstatus", "A");
        baseArg.put("validstatus", "A");
        K3UltimateRequestByQuery<K3UltimateQueryBaseArg> queryArg = K3UltimateRequestByQuery.buildBaseRequest(tenantId,
                dataCenterId,
                objApiName,
                ErpObjInterfaceUrlEnum.queryMasterById,
                baseArg,
                1,
                100,
                null);
        //查询生成的订单变更单
        Result<K3UltimateResponseByQuery> batchQuery = k3UltimateApiService.batchQuery(tenantId,dataCenterId,objApiName, queryArg, false, ConnectParamUtil.parseK3Ultimate(connectInfo.getConnectParams()));
        if(!batchQuery.isSuccess()) {
            return Result.copy(batchQuery);
        }

        List<JSONObject> dataList = batchQuery.getData().getData().getRows().stream()
                .filter(row->!StringUtils.endsWithIgnoreCase(row.getString("changebillno"),"_0"))
                .collect(Collectors.toList());

        return Result.newSuccess(dataList.get(0));
    }

    public static ObjectData getCrmObjDetail(String tenantId,
                                      String objApiName,
                                      String dataId,
                                      MetadataControllerService metadataControllerService){
        Integer tenantIdInt = Integer.valueOf(tenantId);
        HeaderObj headerObj = HeaderObj.newInstance(tenantIdInt, -10000);
        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setObjectDataId(dataId);
        controllerDetailArg.setObjectDescribeApiName(objApiName);
        ObjectData data;
        try {
            data = metadataControllerService.detail(headerObj, objApiName, controllerDetailArg).getData().getData();
        }catch (Exception e){
            log.error("K3UltimateUtils.getCrmObjDetail,exception={}",e.getMessage(),e);
            return null;
        }
        return data;
    }

    /**
     * 生成新的旗舰版单据ID
     * @return
     */
    public static String genId() {
        String time = DateFormatUtils.format(new Date(), "HHmmss");
        String timestamp = System.currentTimeMillis() + time;
        return timestamp;
    }

    public static void main(String[] args) {
        String id = genId();
        System.out.println(id);
    }
}
