<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare.open</groupId>
    <artifactId>fs-erp-sync-data</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>erp-i18n</module>
        <module>common-db-proxy</module>
        <module>erp-api-proxy</module>
        <module>erp-preprocess-data</module>
        <module>erp-preprocess-data-api</module>
        <module>erp-probe-data-task</module>
        <module>erp-sync-data-admin</module>
        <module>erp-sync-data-all</module>
        <module>erp-sync-data-custom</module>
        <module>erp-sync-data-web</module>
        <module>fs-aviator</module>
        <module>oa-sync-data-api</module>
        <module>sync-common</module>
        <module>sync-converter</module>
        <module>sync-main</module>
        <module>sync-writer</module>
        <module>fs-erp-oa</module>
        <module>sync-monitor</module>
        <module>erp-sync-data-file</module>
        <module>erp-connector-proxy-api</module>
        <module>erp-connector-proxy</module>
        <module>erp-connector-proxy-sdk</module>
        <module>erp-probe-data-task-jar</module>
        <module>erp-sync-data-file-jar</module>
        <module>erp-sync-data-web-jar</module>
        <module>sync-monitor-jar</module>
        <module>erp-sync-data-whole-war</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <spring-framework.version>4.3.21.RELEASE</spring-framework.version>
        <open-common.version>0.0.1</open-common.version>
        <open-common-result.version>0.0.3</open-common-result.version>
        <com.fxiaoke.syncdata.version>1.0.0-SNAPSHOT</com.fxiaoke.syncdata.version>
        <mongo-java-driver.version>3.10.1</mongo-java-driver.version>
        <fs-fsi-proxy.version>3.0.0-SNAPSHOT</fs-fsi-proxy.version>
        <hutool.version>5.8.5</hutool.version>
        <powermock.version>2.0.2</powermock.version>
        <!--以下两项覆盖父POM版本-->
        <dispatcher-support.version>7.0.1-SNAPSHOT</dispatcher-support.version>
        <mongo-spring-support.version>4.0.3-SNAPSHOT</mongo-spring-support.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- test  start -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>1.10.19</version>
                <scope>test</scope>
            </dependency>
            <!-- test  end -->

            <!-- spring start -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aspects</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring-framework.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- log -->

            <!--<dependency>-->
            <!--<groupId>org.slf4j</groupId>-->
            <!--<artifactId>log4j-over-slf4j</artifactId>-->
            <!--<version>${slf4j.version}</version>-->
            <!--</dependency>-->

            <!-- others common  start -->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>jstl</artifactId>
                <version>1.2</version>
            </dependency>

            <!-- others common  end -->

            <!--mongo start -->
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${mongo-java-driver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb.morphia</groupId>
                <artifactId>morphia</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb.morphia</groupId>
                <artifactId>morphia-logging-slf4j</artifactId>
                <version>1.0.0</version>
            </dependency>
            <!--mongo end -->



            <!-- zk -->

            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>1.6.1</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.fxiaoke</groupId>-->
<!--                <artifactId>logconfig-core</artifactId>-->
<!--                <version>2.0.0-SNAPSHOT</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <artifactId>java-utils</artifactId>-->
<!--                        <groupId>com.fxiaoke.common</groupId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
<!--埋点-->
            <dependency>
                <groupId>com.fxiaoke.cloud</groupId>
                <artifactId>datapersist</artifactId>
                <version>1.0.6-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>config-core</artifactId>
                        <groupId>com.github.colin-lee</groupId>
                    </exclusion>
                </exclusions>
            </dependency>



            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>1.60</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>


            <!--数据同步 依赖平台底层的模块 start-->

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>sync-main</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>sync-converter</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-aviator</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>sync-writer</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>erp-preprocess-data-api</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>sync-common</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>erp-sync-data-admin</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>


            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>common-db-proxy</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>erp-api-proxy</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>erp-preprocess-data</artifactId>
                <version>${com.fxiaoke.syncdata.version}</version>
            </dependency>
            <!--数据同步 依赖平台底层的模块 end-->
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-crm-rest-api</artifactId>
                <version>2.0.8_erpdss-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.github.zhxing</groupId>
                <artifactId>retrofit-spring</artifactId>
                <version>1.3.2-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-other-rest-api</artifactId>
                <version>1.0.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-paas-auth-rest-api</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-organization-adapter-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                <version>2.1.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-cache</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-db</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-crypto</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.api</groupId>
                <artifactId>fs-id-generator</artifactId>
                <version>1.0.0</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>org.apache.rocketmq</groupId>-->
<!--                <artifactId>rocketmq-client</artifactId>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.apache.rocketmq</groupId>-->
<!--                <artifactId>rocketmq-common</artifactId>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.apache.rocketmq</groupId>-->
<!--                <artifactId>rocketmq-remoting</artifactId>-->
<!--            </dependency>-->
            <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.dataformat/jackson-dataformat-xml -->
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>2.15.2</version>
            </dependency>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.8.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Mockito -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <propertyName>jacoco.agent.argLine</propertyName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>${argLine} @{jacoco.agent.argLine}</argLine>
                    <!-- 设置 forked 进程的超时时间，单位为秒 -->
<!--                    <forkedProcessTimeoutInSeconds>30</forkedProcessTimeoutInSeconds>-->
<!--                    <parallelTestsTimeoutInSeconds>120</parallelTestsTimeoutInSeconds>-->
                    <properties>
                        <property>
                            <name>surefire.timeout</name>
                            <value>90</value>
                        </property>
                    </properties>
                    <systemPropertyVariables>
                        <config.mode>localNoUpdate</config.mode> <!-- 指定系统属性 -->
                    </systemPropertyVariables>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>