package com.fxiaoke.open.erpsyncdata.probe.handler;

import com.fxiaoke.open.erpsyncdata.preprocess.service.OnlyRoute2AllGrayService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 19:10 2024/10/22
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "reportData2FsHandler")
public class ReportData2FsHandler extends IJobHandler {
    @Autowired
    private OnlyRoute2AllGrayService onlyRoute2AllGrayService;


    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        onlyRoute2AllGrayService.triggerReportData2FsJob();
        return ReturnT.SUCCESS;
    }

}
