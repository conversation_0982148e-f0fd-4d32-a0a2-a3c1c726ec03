package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.crmmq.consumer.PaasObjectDataMqConsumer;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RetryDataEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.RetrySendMqDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReTrySendMq;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.probe.service.RetryFailDataService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/29 10:43
 * @desc
 */
@Slf4j
@Ignore
public class DataRetryTest extends BaseTest {
    @Autowired
    private RetryFailDataService retryFailDataService;
    @Autowired
    private PaasObjectDataMqConsumer paasObjectDataMqConsumer;
    @Autowired
    private RetrySendMqDao retrySendMqDao;
    @Test
    public void methodInvoke(){
//        retrySendMqDao.countRetryData(null,null,null,null,null);
//        ReTrySendMq reTrySendMq=new ReTrySendMq();
//        reTrySendMq.setStatus(0);
//        reTrySendMq.setMqMsg("{\"actionType\":\"addAction\",\"app\":\"fs-crm-sfa-urgent\",\"body\":[{\"eventId\":\"6656877045117e000107b7b7\",\"context\":{\"appId\":\"CRM\",\"upstreamOwnerId\":null,\"outTenantId\":null,\"tenantId\":\"79337\",\"outUserId\":null,\"userId\":\"1000\"},\"name\":\"盖伦奥特曼\",\"entityId\":\"ProjectRoleObj\",\"triggerType\":\"i\",\"objectId\":\"6656876f45117e000107b77a\"}],\"dataSource\":null,\"entityId\":\"ProjectRoleObj\",\"historicalData\":null,\"name\":\"object_data\",\"op\":\"i\",\"peerName\":\"CEP\",\"profile\":null,\"serverIp\":null,\"sourceTenantId\":null,\"tagChange\":null,\"tenantId\":\"79337\",\"userId\":\"1000\"}");
//        reTrySendMq.setTenantId("88521");
//        reTrySendMq.setRetryDataEnum(RetryDataEnum.PAAS_ORIGIN_DATA_RETRY_MQ.name());
        ObjectId objectId=new ObjectId("6659fd37ffe64d49f8dd07d5");
        List<ReTrySendMq> sendMqs = retrySendMqDao.listByRetryIds(Lists.newArrayList(objectId));

        retryFailDataService.retryDataServiceHandlerData(sendMqs);
    }

    @Test
    public void testAllDubbo(){
        ReTrySendMq reTrySendMq=new ReTrySendMq();
        reTrySendMq.setStatus(0);
        reTrySendMq.setMqMsg("{\"ployDetailSnapshotId\":null,\"sourceEventType\":2,\"sourceTenantType\":1,\"sourceData\":{\"last_modified_time\":*************,\"last_deal_closed_time\":*************,\"last_followed_time\":*************,\"_id\":\"6493164ca8b140000128a2a4\",\"object_describe_api_name\":\"AccountObj\",\"tenant_id\":\"88521\"},\"syncDependForce\":null,\"syncLogId\":null,\"dataVersionList\":null,\"dataVersion\":*************,\"delayDispatcherTime\":null,\"dataReceiveType\":2} ");
        reTrySendMq.setTenantId("88521");
        reTrySendMq.setRetryDataEnum(RetryDataEnum.ERP_POLLING_INTERFACE_MQ.name());
        retryFailDataService.retryDataServiceHandlerData(Lists.newArrayList(reTrySendMq));
        String data="{\"ployDetailSnapshotId\":null,\"sourceEventType\":2,\"sourceTenantType\":1,\"sourceData\":{\"last_modified_time\":*************,\"last_deal_closed_time\":*************,\"last_followed_time\":*************,\"_id\":\"6493164ca8b140000128a2a4\",\"object_describe_api_name\":\"AccountObj\",\"tenant_id\":\"88521\"},\"syncDependForce\":null,\"syncLogId\":null,\"dataVersionList\":null,\"dataVersion\":*************,\"delayDispatcherTime\":null,\"dataReceiveType\":2}";
        SyncDataContextEvent syncDataContextEvent= JSONObject.parseObject(data,new TypeReference<SyncDataContextEvent>(){});
        paasObjectDataMqConsumer.sendAndRetryWhenFailed(Lists.newArrayList(syncDataContextEvent));


    }

    @Test
    public void testHandler(){
        Result<Void> voidResult = retryFailDataService.retryDataServiceHandler();

    }

    @Test
    public void testHandlerData(){

        ObjectId objectId=new ObjectId("666991e0b6f84a70dc573747");
        retryFailDataService.retryDataByIds(Lists.newArrayList(objectId));
//        Result<Void> voidResult = retryFailDataService.retryDataServiceHandler();

    }

}
