<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.probe"/>


    <dubbo:application name="erp-sync-data-task"/>
    <dubbo:registry address="${dubbo.registry.address}"/>


    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:syncdata-task-applicationContext.properties"
          p:configName="erp-sync-data-all"/>

    <bean id="accessLog" class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.LogInterceptor">
    </bean>
    <aop:config>
        <aop:aspect id="accessLogMonitor" ref="accessLog" order="0">
            <aop:around pointcut="
                            execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.dao..*.*(..))
                            or execution(* com.fxiaoke.open.erpsyncdata.*.manager..*.*(..)))" method="around"/>
        </aop:aspect>
    </aop:config>
    <import resource="common-spring.xml"/>
</beans>