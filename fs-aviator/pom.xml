<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>fs-erp-sync-data</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

  <packaging>jar</packaging>
  <artifactId>fs-aviator</artifactId>
  <name>fs-aviator</name>
  <description>A lightweight, high performance expression evaluator for java</description>
  <url>https://github.com/killme2008/aviator</url>
  <inceptionYear>2010</inceptionYear>

  <developers>
    <developer>
      <name>dennis zhuang</name>
      <url>http://fnil.net/</url>
      <timezone>8</timezone>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:killme2008/aviator.git</connection>
    <developerConnection>scm:git:**************:killme2008/aviator.git</developerConnection>
    <url>**************:killme2008/aviator.git</url>
  </scm>
  <licenses>
    <license>
      <name>GNU LESSER GENERAL PUBLIC LICENSE</name>
      <url>http://www.gnu.org/licenses/lgpl.html</url>
    </license>
  </licenses>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.3.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <version>1.9.3</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.springframework/spring-core -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>4.3.16.RELEASE</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.6.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
