package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.AuditLogI18nConstant;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.AuditLog4ErpDss;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * 审计日志服务，日志位置，管理后台->企业日志->审计日志
 * <AUTHOR>
 * @date 2023.03.21
 */
public interface AuditLogService {
    /**
     * 记录审计日志
     * @param tenantId
     * @param logList
     * @return
     */
    Result<Void> record(String tenantId,List<AuditLog4ErpDss> logList);

    /**
     * 记录审计日志
     * @param operationObject 子模块
     * @param bizOperationName 操作行为
     * @param objectName 操作对象
     * @param tenantId 企业EI
     * @param userId 用户ID
     * @param textMessage 日志消息
     * @param level 日志级别
     * @return
     */
    Result<Void> record(AuditLogI18nConstant.SubModuleEnum operationObject,
                        AuditLogI18nConstant.BizOperationEnum bizOperationName,
                        String objectName,
                        String tenantId,
                        String userId,
                        String textMessage,
                        int level);

    /**
     * 记录审计日志
     * @param tenantId 企业EI
     * @param userId 用户ID
     * @param textMessage 日志消息
     * @return
     */
    Result<Void> recordExportSystemFieldLog(ErpFieldTypeEnum fieldType,
                                            String tenantId,
                                            String userId,
                                            String textMessage);

    /**
     * 记录审计日志
     *
     * @param tenantId    企业EI
     * @param userId      用户ID
     * @param textMessage 日志消息
     * @return
     */
    Result<Void> recordDeleteSystemFieldLog(ErpFieldTypeEnum fieldType,
                                            String tenantId,
                                            String userId,
                                            String textMessage);
}
