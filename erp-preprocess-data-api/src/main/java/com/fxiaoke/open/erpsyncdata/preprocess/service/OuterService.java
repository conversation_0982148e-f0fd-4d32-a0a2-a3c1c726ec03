package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg;

import java.util.List;
import java.util.Map;

public interface OuterService {
    /**
     * 查找关联，获取源对象的名称
     * @param tenantId
     * @param tenantType
     * @param objectApiName
     * @param id
     * @return
     */
    Result2<String> getReferName(String tenantId, Integer tenantType, String objectApiName, String id);

    /**
     * 远程获取源部门名称
     * @param tenantId
     * @param tenantType
     * @param detpId
     * @return
     */
    Result2<String> getDeptName(String tenantId, Integer tenantType, String detpId);

    /**
     * 批量获取外部企业
     * @param upstreamEi
     * @param eas
     * @return
     */
    Result2<BatchGetOuterTenantIdByEaData> batchGetOuterTenantIdByEa(Integer upstreamEi, List<String> eas);

    /**
     * 获取主对象apiName，兼容改造需重写
     * @param tenantId 企业id
     * @param tenantType  企业类型
     * @param objectApiName 对象apiName（主、从）
     * @return 主对象时返回空
     */
    Result2<String> getMasterObjectApiName(String tenantId, Integer tenantType, String objectApiName);

    Result2<List<ObjectData>> listObjectDatas(String tenantId, Integer tenantType, String objectApiName, Map<String, List<String>> filterFieldValues, Integer limit, Integer offset);

    /**
     * 转变变量Filter
     * @param filters
     * @param sourceTenantId
     * @param destTenantId
     * @return
     */
    List<List<FilterData>> changeVariableFilter(List<List<FilterData>> filters, String sourceTenantId, String destTenantId);

    /**
     * 获取外部负责人
     * @param upstreamEi
     * @param downstreamEi
     * @return
     */
    Result2<Long> getDownstreamRelationOwnerOuterUid(Integer upstreamEi, Integer downstreamEi);

    /**
     * 获取对象数据，根据useCache设置缓存
     *
     * @deprecated 请使用另外两个同名方法
     */
    @Deprecated
    default Result2<ObjectData> getObjectData(String tenantId, Integer tenantType, String objectApiName, String id, String traceId, Boolean useCache) {
        return getObjectData(tenantId, tenantType, objectApiName, id, useCache);
    }


    /**
     * 获取对象数据，根据useCache设置缓存
     * @param tenantId
     * @param tenantType
     * @param objectApiName
     * @param id
     * @param traceId
     * @param useCache
     * @return
     */
    Result2<ObjectData> getObjectData(String tenantId, Integer tenantType, String objectApiName, String id, Boolean useCache);

    /**
     * 获取对象数据
     * @param tenantId
     * @param tenantType
     * @param objectApiName
     * @param id
     * @return
     */
    Result2<ObjectData> getObjectData(String tenantId, Integer tenantType, String objectApiName, String id);

    /**
     * 获取对象数据
     * @param tenantId
     * @param tenantType
     * @param objectApiName
     * @param name
     * @return
     */
    Result2<ObjectData> getObjectDataByName(String tenantId, Integer tenantType, String objectApiName, String name);

    /**
     * 获取对象详情
     * @param tenantId
     * @param apiName
     * @param id
     * @return
     */
    Result2<ObjectData> getDetailById(String tenantId, String apiName, String id);

    /**
     * 获取负责人id
     * @param tenantId
     * @param tenantType
     * @param objectApiName
     * @param id
     * @return
     */
    Result2<Integer> getOwnerId(Integer tenantId, Integer tenantType, String objectApiName, String id);

    /**
     * 批量用户获取用户额FieldValue
     * @param tenantId
     * @param tenantType
     * @param employeeIds
     * @param fieldName
     * @return
     */
    Result2<Map<String, String>> batchGetEmployeeFieldValue(String tenantId, Integer tenantType, List<String> employeeIds, String fieldName);

    /**
     * 文件转化
     * @param sourceTenantId
     * @param sourceTenantType
     * @param type
     * @param sourceFiles
     * @param destTenantId
     * @param destTenantType
     * @return
     */
    Result2<List<Map<String, String>>> convertFiles(String sourceTenantId, Integer sourceTenantType, String type, List<Map<String, String>> sourceFiles, String destTenantId, Integer destTenantType);

    Result2<GetOuterAccountByFsData> getOuterAccountByFs(Integer upstreamEi, Integer ei, Integer userId);

    Result2<GetFsAccountByOuterData> getFsAccountByOuter(Long outerUid);

    /**
     * 获取关联客户或合作伙伴id
     * @param upstreamEi
     * @param downstreamEi
     * @param objectApiName
     * @return
     */
    Result2<String> getMapperObjectId(Integer upstreamEi, Integer downstreamEi, String objectApiName);

    /**
     * @param functionType 什么时机调用函数,用于确定crm对象的apiName和调用的函数方法
     *                     为null表示不是用的同步前/中/后函数, 可能为连接器api等
     * @param objectName
     */
    Result2<FunctionServiceExecuteReturnData> executeCustomFunction(String tenantId,
                                                                    String erpDataCenterId,
                                                                    ExecuteCustomFunctionArg arg,
                                                                    ErpObjInterfaceUrlEnum interfaceUrl,
                                                                    CustomFunctionTypeEnum functionType,
                                                                    final String ployDetailSnapshotId, String objectName);

    /**
     * 获取源企业产品商品分类列表
     * @param tenentId 源企业id
     * @param code  产品分类code（自增编号与产品关联）
     * @return
     */
    Result2<ProductCategoryData> getSourceProductCategoryValue(String tenentId, String code);

    /**
     * 获取或创建目标企业产品商品分类列表
     * @param tenentId 目标企业id
     * @param categoryName 产品分类名称
     * @param categoryCode 产品分类编号
     * @param orderField  产品分类排序号
     * @return
     */
    Result2<ProductCategoryData> getOrCreateDestProductCategoryValue(String tenentId, String categoryName, String categoryCode, Integer orderField);

    /**
     * 获取企业产品商品分类列表
     * @param tenentId
     * @return
     */
    Result2<Map<String, ProductCategoryData>> listProductCategory(String tenentId);

    /**
     * 获取字段描述数据
     * @param tenantId 企业id
     * @param apiName 对象apiName
     * @param fieldApiName 对象字段apiName
     * @return
     */
    Result2<FieldDescribeData> getFieldDescribe(String tenantId, String apiName, String fieldApiName);

    /**
     * 获取字段描述数据
     * @param tenantId 企业id
     * @param apiName 对象apiName
     * @return
     */
    Result2<String> getObjectNameByApiName(String tenantId, String apiName);

    /**
     * 完成写入后执行钩子
     * @param syncDataContextEvent
     * @return
     * @deprecated 没有使用，将移除
     */
    @Deprecated
    Result2<SyncDataContextEvent> executeCompleteWriteHook(SyncDataContextEvent syncDataContextEvent);
    /**
     * 获取企业类型
     * @return
     */
    Integer getTenantType();

    /**
     * 根据ERP对象编码获取对象ID
     * @param tenantId
     * @param objectApiName
     * @param number
     * @return
     */
    Result2<String> getIdByNumber(String tenantId, String objectApiName, String number);

    /***
     * 批量获取crm数据,且缓存
     */
    Result2<String> batchGetObjectDataAndCache(String tenantId,String objectApiName,List<String> objectDataIds);

    /**
     * 删除缓存
     */
    Result2<Void> removeBatchCache();

    Result2<ObjectData> queryObjectDataByEqFilter(String dcId,String tenantId, Integer tenantType, String objectApiName, List<List<FilterData>>orFilters);
}
