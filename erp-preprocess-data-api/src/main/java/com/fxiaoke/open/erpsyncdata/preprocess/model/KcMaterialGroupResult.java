package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KcMaterialGroupResult extends KcMaterialGroup {
    public static final int RESULT_SUCCESS = 0;
    public static final int RESULT_FAILED = 1;
    /**
     * 错误码
     */
    private int code;
    /**
     * 错误消息
     */
    private String msg;

    public static KcMaterialGroupResult create(KcMaterialGroup kcMaterialGroup,int code,String msg) {
        KcMaterialGroupResult result = new KcMaterialGroupResult();
        result.setId(kcMaterialGroup.getId());
        result.setNumber(kcMaterialGroup.getNumber());
        result.setName(kcMaterialGroup.getName());
        result.setParentId(kcMaterialGroup.getParentId());

        result.setCode(code);
        result.setMsg(msg);

        return result;
    }

    public static KcMaterialGroupResult create(KcMaterialGroup kcMaterialGroup) {
        return create(kcMaterialGroup,0,"");
    }


    public static KcMaterialGroupResult create(String number,String name,int code,String msg) {
        KcMaterialGroupResult result = new KcMaterialGroupResult();
        result.setNumber(number);
        result.setName(name);
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }
}
