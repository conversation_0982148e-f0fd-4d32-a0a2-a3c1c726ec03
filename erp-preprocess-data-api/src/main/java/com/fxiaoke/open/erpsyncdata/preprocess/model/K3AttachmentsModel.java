package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date: 15:09 2023/4/19
 * @Desc:
 */
@Data
public class K3AttachmentsModel extends HashMap<String, Object> implements Serializable {
    public void setServerFileName(String serverFileName) {
        this.put("ServerFileName", serverFileName);
    }
    public String getServerFileName() {
        if(this.get("ServerFileName")==null){
            return null;
        }
        return String.valueOf(this.get("ServerFileName"));
    }
    public String getFileName() {
        if(this.get("FileName")==null){
            return null;
        }
        return String.valueOf(this.get("FileName"));
    }
    public void setFileName(String fileName) {
        this.put("FileName", fileName);
    }
    public void setFileLength(Integer fileLength) {
        this.put("FileLength", fileLength);
    }
    public void setFileBytesLength(Integer fileBytesLength) {
        this.put("FileBytesLength", fileBytesLength);
    }
}
