package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:54 2021/2/25
 * @Desc:
 */
@AllArgsConstructor
@Getter
public enum SAPConnectEnum {

    SAP_RFC("SAP_RFC", "rfc方式"),
    SAP_WEBSERVICE("SAP_WEBSERVICE","webservice方式"),
    SAP_REST("SAP_REST", "rest方式"),
    SAP_OTHERS("SAP_OTHERS", "其他方式"),

    ;

    private String type;
    private String desc;


    /**
     * 任务状态(1.创建，2.开启，3执行中，4.异常，5.中断，6.结束（成功）)
     * @param type
     * @return
     */
    public static SAPConnectEnum getErpHistoryDataTaskStatus(String type) {
        for (SAPConnectEnum theStatus : SAPConnectEnum.values()) {
            if (theStatus.getType().equals(type)) {
                return theStatus;
            }
        }
        return SAP_OTHERS;
    }
}
