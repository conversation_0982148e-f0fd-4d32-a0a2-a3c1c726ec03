package com.fxiaoke.open.erpsyncdata.preprocess.result;

import cn.hutool.core.date.DateRange;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.BIDateRangeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/1/3 19:51
 * 数据大屏筛选
 *
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class ScreenFilterInfoResult  implements Serializable {


    @ApiModelProperty("系统名称")
    private String  screenName;

    @ApiModelProperty("对接crm")
    private Set<DataScreenFilterResult.Option> crmObjectList;

    @ApiModelProperty("对接系统")
    private Set<DataScreenFilterResult.Option> dataCenterList;

    /**
     * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.BIDateRangeEnum
     */
    @ApiModelProperty("筛选时间")
    private BIDateRangeEnum dateRangeEnum;

    @ApiModelProperty("间隔时间")
    private String pollingInterval;


}
