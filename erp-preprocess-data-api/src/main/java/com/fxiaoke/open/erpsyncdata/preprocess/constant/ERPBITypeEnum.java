package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 20:28 2023/1/9
 * @Desc:
 */
@AllArgsConstructor
@Getter
public enum ERPBITypeEnum {
    OUT_SIDE_OBJ_APINAME("bi_erp_data_screen_outSideObjApiName","外部apiname"),
    OUT_SYSTEM("bi_erp_data_screen_dataCenterId","外部系统"),
    CRM_OBJ_APINAME("bi_erp_data_screen_crmObjApiName","crm对象"),//函数apiname也是这个
    ;
    private String name;
    private String desc;

}
