package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.DataDashboardResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/1/3 19:51
 * 数据大屏
 *
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class DataScreenResult implements Serializable {


    @ApiModelProperty("对接的系统数")
    private Integer systemSize;
    @ApiModelProperty("业务模块")
    private Integer businessSize;
    @ApiModelProperty("本月同步")
    private String thisMonthSyncCount;
    @ApiModelProperty("上月同步")
    private String lastSyncCount;
    @ApiModelProperty("今天同步")
    private String todaySyncCount;
    @ApiModelProperty("接口异常次数")
    private Integer interfaceExceptionCount;
    @ApiModelProperty("集成流熔断异常")
    private Integer integrationStreamBreakCount;
    @ApiModelProperty("企业名称")
    private String enterpriseName;
    @ApiModelProperty("企业logo")
    private String enterpriseLogo;
    @ApiModelProperty("系统同步详情")
    private List<ScreenSystemDetailResult> systemSyncDetail;
    @ApiModelProperty("读写CRM接口次数")
    private Map<String,String> operateCrmInterfaceCount;
    @ApiModelProperty("crm同步量")
    private Map<String,List<DataDashboardResult>> businessCrmSyncCount;
    @ApiModelProperty("增量同步")
    private List<ScreenIncrementDataResult> incrementDataResults;
//    @ApiModelProperty("系统同步详情")
//    private List<ScreenSystemDetailResult> systemSyncDetail;
}
