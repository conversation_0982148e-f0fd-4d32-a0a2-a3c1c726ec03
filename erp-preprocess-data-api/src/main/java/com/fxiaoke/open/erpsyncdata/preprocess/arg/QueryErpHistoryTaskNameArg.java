package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date: 10:06 2021/9/24
 * @Desc:
 */
@Getter
@Setter
@ToString
@ApiModel("获取erp历史任务名称参数")
public class QueryErpHistoryTaskNameArg{
    @ApiModelProperty("erp真实对象apiName")
    private String erpRealObjectApiName;
    @ApiModelProperty("erp中间对象apiName")
    private String erpFakeObjectApiName;
    @ApiModelProperty("数据中心id")
    private String dataCenterId;
}
