package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 14:54 2021/2/25
 * @Desc:
 */
@AllArgsConstructor
@Getter
public enum SyncLogStatusEnum {
    /**
     * 通用的节点状态描述
     */
    SYNC_SUCCESS(10000, "调用成功", I18NStringEnum.s867.getI18nValue()),
    SYNC_FAIL(10010, "调用失败", I18NStringEnum.s868.getI18nValue()),
    ;

    private Integer status;
    private String desc;
    private String i18nKey;
}
