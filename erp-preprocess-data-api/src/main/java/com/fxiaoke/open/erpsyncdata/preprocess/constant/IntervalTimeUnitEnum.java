package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 14:54 2021/1/12
 * @Desc:
 */
@Getter
public enum IntervalTimeUnitEnum {
    /**
     * 分钟
     */
    minutes,
    /**
     * 小时
     */
    hours,
    /**
     * 天
     */
    days,

    /**
     * 一次,当是一次时，只在startTime执行
     */
    once,

    ;

    public Integer getPollingInterval(Integer quantity){
        if(quantity==null){
            return Integer.MAX_VALUE;
        }
        switch (this){
            case minutes:return quantity;
            case hours:return quantity*60;
            case days:return quantity*60*24;
            default:return Integer.MAX_VALUE;
        }
    }

}
