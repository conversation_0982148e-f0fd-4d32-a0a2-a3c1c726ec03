package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityField;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class StandardConnectParam extends BaseLangConnectParam {

    private static final long serialVersionUID = -3892271931165465013L;
    /**
     * 对接系统名称
     */
    @ApiModelProperty(value = "对接系统名称")
    private String systemName;

    @ApiModelProperty(value = "对接系统图标Url或者path（因为不想加字段）")
    private String iconUrl;

    /**
     * 基础路径
     */
    @ApiModelProperty(value = "基础路径", example = "http://www.ceshi112.com/erp/syncdata/noAuth/mock")
    private String baseUrl;

    /**
     * 接口路径
     */
    @ApiModelProperty("接口路径,不传为默认")
    private ServicePath servicePath;

    /**
     * 获取header的脚本
     */
    @ApiModelProperty(value = "获取header的脚本", example = "return [:]")
    private String headerScript;
    /**
     * 获取header的函数
     */
    @ApiModelProperty(value = "获取header的函数", example = "return [:]")
    private String headerFunctionName;

    @ApiModelProperty(value = "header", example = "{\"tenant_id\":\"${ei}\"}")
    private Map<String, String> headerMap;

    /**
     * 结果格式
     */
    @ApiModelProperty("结果格式")
    private ResultFormat resultFormat;

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

    /**
     * 服务类型
     */
    private ConnectorHandlerType connectorHandlerType = ConnectorHandlerType.REST_API;

    /**
     * 系统参数，可用于储存授权信息，值统一加密存储，键明文存储
     */
    @SecurityField()
    private SystemParams systemParams;

    /**
     * 连接器apiName，hub的是系统级别，apl类的是企业内的
     */
    private String apiName;

    private ConnectorAuthType authType;

    @Override
    public String getConnectorName() {
        if (ConnectorHandlerType.HUB.equals(connectorHandlerType) || ConnectorHandlerType.APL_CLASS.equals(connectorHandlerType)) {
            return AllConnectorUtil.getI18nName(apiName);
        }
        return null;
    }

    @Override
    public SystemParams getSystemParams() {
        return systemParams;
    }


    @Override
    public void setSystemParams(SystemParams systemParams) {
        systemParams.setAuthType(authType);
        this.systemParams = systemParams;
    }

    /**
     * 接口地址
     */
    @Data
    public static class ServicePath implements Serializable {
        private static final long serialVersionUID = -6091691265483529697L;
        /**
         * 新建单条数据
         */
        @ApiModelProperty("新建单条数据")
        private String create = "create";

        /**
         * 新建单条明细数据
         */
        @ApiModelProperty("新建单条明细数据")
        private String createDetail = "createDetail";

        /**
         * 更新单条数据
         */
        @ApiModelProperty("更新单条数据")
        private String update = "update";


        /**
         * 更新单条明细数据
         */
        @ApiModelProperty("更新单条明细数据")
        private String updateDetail = "updateDetail";

        /**
         * 查询单条数据接口
         */
        @ApiModelProperty("查询单条数据接口")
        private String view = "queryMasterById";

        /**
         * 根据时间查询数据
         */
        @ApiModelProperty("根据时间查询数据接口")
        private String queryByTime = "queryMasterBatch";

        /**
         * 根据时间查询作废数据
         */
        @ApiModelProperty("根据时间查询作废数据")
        private String queryInvalidByTime = "queryInvalid";
    }

    /**
     * 结果格式
     */
    @Data
    public static class ResultFormat implements Serializable {
        private static final long serialVersionUID = -8160729428140174044L;
        /**
         * error code 字段
         */
        @ApiModelProperty(value = "error code 字段", example = "errCode")
        private String codeName;

        /**
         * error msg 字段
         */
        @ApiModelProperty(value = "error msg 字段", example = "errMsg")
        private String msgName;

        /**
         * 数据字段
         */
        @ApiModelProperty(value = "数据字段", example = "data")
        private String dataName;

        /**
         * 成功的code
         */
        @ApiModelProperty(value = "成功的code", example = "s106240000")
        private String successCode = "0";
    }

    public ResultFormat getResultFormatOrDefault() {
        if (resultFormat == null) {
            resultFormat = new ResultFormat();
            resultFormat.setCodeName("errCode");
            resultFormat.setMsgName("errMsg");
            resultFormat.setDataName("data");
        }
        return resultFormat;
    }

    @Override
    public String getBaseUrlRewriteByConnector() {
        if (systemParams != null) {
            //优先从systemParams取
            String baseUrl1 = systemParams.getBaseUrl();
            if (baseUrl1 != null) {
                return baseUrl1;
            }
        }
        return baseUrl;
    }
}
