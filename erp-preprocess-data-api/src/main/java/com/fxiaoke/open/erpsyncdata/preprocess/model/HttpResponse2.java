package com.fxiaoke.open.erpsyncdata.preprocess.model;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Opt;
import lombok.Data;
import okhttp3.Response;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 */
@Data
public class HttpResponse2 {

    private int code;

    private String message;

    private Map<String, List<String>> headers;

    private String body;

    public HttpResponse2(Response response, boolean needBase64Encode) throws IOException {
        this.code = response.code();
        this.message = response.message();
        this.headers = (Opt.ofTry(() -> response.headers().toMultimap()).get());
        if (response.body() != null) {
            if (needBase64Encode) {
                this.body = Base64.encode(response.body().bytes());
            } else {
                this.body = response.body().string();
            }
        }
    }
}
