package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * k3cloud 旗舰版连接参数
 * <AUTHOR>
 * @date 2023.09.05
 */
@Data
@ApiModel
public class K3UltimateConnectParam implements BaseConnectParam {
    private static final long serialVersionUID = 2369962196456072952L;

    @ApiModelProperty(value = "ERP访问路径",example = "http://10.22.0.61:8082/ierp/")
    private String baseUrl;

    @ApiModelProperty("数据中心ID，对应旗舰版的accountId")
    private String dbId;

    @ApiModelProperty("数据中心名称，对应旗舰版的accountName")
    private String dbName;

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty(value = "用户，可以是用户名，手机号，邮箱")
    private String user;

    @ApiModelProperty(value = "用户类型")
    private UserTypeEnum userType;

    @ApiModelProperty(value = "旗舰版认证方式")
    private AuthenticationMethodEnum authenticationMethod;

    @ApiModelProperty(value = "第三方应用系统编码")
    private String appId;

    @ApiModelProperty(value = "AccessToken认证密钥")
    private String appSecret;

    @ApiModelProperty(value = "x-acgw-identity，调用API的身份标识")
    private String xAcgwIdentity;

    @ApiModelProperty(value = "语言，默认值zh_CN，简体中文")
    private String language = "zh_CN";

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

    public static K3UltimateConnectParam newInstance(String baseUrl,
                                                      String dbId,
                                                      String tenantId,
                                                      AuthenticationMethodEnum authenticationMethod,
                                                      String appId,
                                                      String appSecret,
                                                      String user,
                                                      UserTypeEnum userType){
        K3UltimateConnectParam userParam = new K3UltimateConnectParam();
        userParam.setBaseUrl(baseUrl);
        userParam.setDbId(dbId);
        userParam.setTenantId(tenantId);
        userParam.setAuthenticationMethod(authenticationMethod);
        userParam.setAppId(appId);
        userParam.setAppSecret(appSecret);
        userParam.setUser(user);
        userParam.setUserType(userType);
        return userParam;
    }

    /**
     * 用户类型
     */
    public enum  UserTypeEnum {
        UserName,//标识为用户名
        Mobile,//标识为手机，默认为手机
        Email,//标识为email
    }

    /**
     * 认证方式，一期只支持ACCESS_TOKEN认证
     * 参考文档：https://vip.kingdee.com/knowledge/specialDetail/226337046514476288?category=239331354741842688&id=218694224487485696&productLineId=29#1
     */
    public enum  AuthenticationMethodEnum {
        ACCESS_TOKEN,//access token认证
        BASIC,//基本认证
        SIGNATURE,//签名认证
        DIGEST,//摘要认证
        JWT,//JWT认证，全称是JSON WEB TOKEN
    }
}
