package com.fxiaoke.open.erpsyncdata.preprocess.result.base;

import cn.hutool.core.lang.Opt;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fxiaoke.open.erpsyncdata.common.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.*;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SUCCESS;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
@Data
@ApiModel
@Slf4j
@JacksonXmlRootElement(localName = "response")
public class Result<T> extends BaseResult implements Serializable, I18nBase {
    private static final long serialVersionUID = -1407891263555853715L;

    @ApiModelProperty("数据")
    protected T data;

    /**
     * 所有构造方法的最终调用
     */
    private Result(String errCode, String errMsg, T data, String i18nKey,List<String> i18nExtra) {
        this.errCode = errCode;
        //不能预处理多语，因为i18nKey可能是错的。
        this.errMsg = errMsg;
        this.data = data;
        this.i18nKey = i18nKey;
        this.i18nExtra = i18nExtra;
        this.traceMsg = TraceContext.get().getTraceId();
    }

    public Result() {
        this(SUCCESS.getErrCode(), SUCCESS.getErrMsg(), null, SUCCESS.getI18nKey(), null);
    }


    /**
     * 必须有这个方法，传ResultCodeEnum会走到这，不然会走到Result(T data)
     */
    public Result(ResultCodeEnum resultCode) {
        this(resultCode.getErrCode(), resultCode.getErrMsg(), null, resultCode.getI18nKey(), null);
    }

    public Result(ResultCodeEnum resultCode, Object... formats) {
        this(resultCode.getErrCode(), resultCode.getErrMsg(), null, resultCode.getI18nKey(), null);
        if (formats!=null){
            this.errMsg = Opt.ofTry(()-> String.format(resultCode.getErrMsg(), formats)).orElse(resultCode.getErrMsg());
            this.i18nExtra = Lists.newArrayList();
            for(Object format : formats) {
                if(format==null) continue;
                this.i18nExtra.add(format.toString());
            }
        }
    }

    public Result(T data) {
        this(SUCCESS.getErrCode(), SUCCESS.getErrMsg(), data, SUCCESS.getI18nKey(), null);
    }

    public Result(String errCode, String errMsg, T data) {
        this(errCode, errMsg, data, null, null);
    }

    private static <R> Result<R> newInstanceByI18N(String errCode, String errMsg, R data, String i18nKey, List<String> i18nExtra) {
        Result<R> rResult = new Result<>(errCode, errMsg, data, i18nKey, i18nExtra);
        return rResult;
    }

    private static <R> Result<R> newInstanceByI18N(ResultCodeEnum resultCode, R data, String i18nKey, List<String> i18nExtra) {
        return newInstanceByI18N(resultCode.getErrCode(), resultCode.getErrMsg(), data, i18nKey, i18nExtra);
    }

    public static <R> Result<R> newError(String errCode, I18NStringEnum resultCode, String... extra) {
        return newErrorWithData(errCode, resultCode, null, extra);
    }

    public static <R> Result<R> newSystemError(I18NStringEnum resultCode, String... extra) {
        return newErrorWithData(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), resultCode, null, extra);
    }

    public static <R> Result<R> newError(I18NStringEnum resultCode, String... extra) {
        return newErrorWithData(resultCode, null, extra);
    }

    public static <R> Result<R> newErrorWithData(I18NStringEnum resultCode, R data, String... extra) {
        String errCode;
        try {
            errCode = "s30624" + String.format("%04d", Integer.valueOf(resultCode.name().substring(1)));
        } catch (Exception e) {
            log.warn("I18NStringEnum to errCode fail, resultCode:{}", resultCode.name(), e);
            errCode = ResultCodeEnum.SYSTEM_ERROR.getErrCode();
        }

        return newErrorWithData(errCode, resultCode, data, extra);
    }

    public static <R> Result<R> newErrorWithData(String errCode, I18NStringEnum resultCode, R data, String... extra) {
        List<String> extraList = null;
        if (extra != null) {
            extraList = new ArrayList<>(Arrays.asList(extra));
        }

        String msg = resultCode.getI18nValue();
        if (Objects.nonNull(extra)) {
            msg = String.format(msg, extra);
        }

        return newInstanceByI18N(errCode, msg, data, resultCode.getI18nKey(), extraList);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode) {
        return newInstanceByI18N(resultCode.getErrCode(), resultCode.getErrMsg(), null, resultCode.getI18nKey(), null);
    }

    public static <R> Result<R> newError(String resultMsg) {
        return newError(ResultCodeEnum.SYSTEM_ERROR, resultMsg);
    }

    public static <R> Result<R> newErrorByI18N(String resultMsg, String i18nKey, List<String> i18nExtra) {
        return newErrorByI18N(ResultCodeEnum.SYSTEM_ERROR, resultMsg, i18nKey, i18nExtra);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode, String msg) {
        return newErrorByI18N(resultCode.getErrCode(), msg,null,null);
    }

    public static <R> Result<R> newErrorExtra(ResultCodeEnum resultCode, String... extra) {
        List<String> extraList = null;
        if (extra != null) {
            extraList = new ArrayList<>(Arrays.asList(extra));
        }

        String msg = resultCode.getErrMsg();
        if (Objects.nonNull(extra)) {
            msg = String.format(msg, extra);
        }

        return newErrorByI18N(resultCode.getErrCode(), msg, resultCode.getI18nKey(), extraList);
    }

    public static <R> Result<R> newErrorByI18N(ResultCodeEnum resultCode, String msg, String i18nKey, List<String> i18nExtra) {
        return newErrorByI18N(resultCode.getErrCode(), msg, i18nKey, i18nExtra);
    }

    public static <R> Result<R> newErrorWithData(ResultCodeEnum resultCode, R data) {
        return newInstanceByI18N(resultCode, data, resultCode.getI18nKey(), null);
    }

    public static <R> Result<R> newError(String errCode, String errMsg) {
        Result<R> result = new Result<>(errCode,errMsg,null);
        return result;
    }

    public static <R> Result<R> newErrorByI18N(String errCode, String errMsg, String i18nKey, List<String> i18nExtra) {
        Result<R> result = new Result<>(errCode,errMsg,null,i18nKey, null);
        return result;
    }

    public static <R> Result<R> newError(String errCode, String errMsg, String traceMsg) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setI18nKey(null);
        result.setI18nExtra(null);
        result.setTraceMsg(traceMsg);
        return result;
    }

    public static <R> Result<R> newErrorByI18N(String errCode, String errMsg, String traceMsg, String i18nKey, List<String> i18nExtra) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setI18nKey(i18nKey);
        result.setI18nExtra(i18nExtra);
        result.setTraceMsg(traceMsg);
        return result;
    }

    /**
     * 更智能地处理异常！
     * @param e
     * @param <R>
     * @return
     */
    public static <R> Result<R> wrapException(Throwable e) {
        ErpSyncDataException causedByErpException = ExceptionUtil.getCausedBy(e, ErpSyncDataException.class);
        if (causedByErpException != null) {
            return Result.newErrorByI18N(causedByErpException.getErrCode(), causedByErpException.getErrMsg(), causedByErpException.getI18nKey(), causedByErpException.getI18nExtra());
        }
        return Result.newError(ExceptionUtil.getRootCauseMessage(e));
    }

    public static <R> Result<R> newSuccess() {
        return new Result<>();
    }

    public static <R> Result<R> newSuccess(R data) {
        return new Result<>(data);
    }

    public static <R> Result<R> newSuccessByI18N(R data,String i18nKey, List<String> i18nExtra) {
        return newInstanceByI18N(SUCCESS, data,i18nKey,i18nExtra);
    }

    public boolean isSuccess() {
        return super.isSuccess();
    }

    public T safeData() {
        if (isSuccess()) {
            return data;
        }
        //为了不丢失tenantId，尝试从context取，result没有tenantId，但是exception有。而且exception的msg转result会丢失，只能尝试从context取tenantId
        throw new ErpSyncDataException(this.errCode, this.errMsg, this.i18nKey, TraceUtil.tryGetTenantId()).extra(this.i18nExtra);
    }


    /**
     * 复制错误结果，不复制数据
     *
     * @param result
     * @param <R>
     * @return
     */
    public static <R> Result<R> copy(BaseResult result) {
        return newInstanceByI18N(result.getErrCode(), result.getErrMsg(), null,result.getI18nKey(),result.getI18nExtra());
    }

    public <R> Result<R> error() {
        return Result.copy(this);
    }

    /**
     * 复制平台结果
     *
     * @param result
     * @param <R>
     * @return
     */
    public static <R> Result<R> copy(Result2<R> result) {
        if (result.isSuccess()) {
            return newSuccessByI18N(result.getData(),result.getI18nKey(),result.getI18nExtra());
        } else {
            return newInstanceByI18N(result.getErrCode(),
                    result.getErrMsg(),
                    null,
                    result.getI18nKey(),
                    result.getI18nExtra());
        }
    }

    public static Result clearI18NKey(Result result) {
        result.setI18nKey(null);
        result.setI18nExtra(null);
        return result;
    }

    @Override
    public String toString() {
        return "Result{" +
                "data=" + data +
                ", errCode='" + errCode + '\'' +
                ", errMsg='" + errMsg + '\'' +
                ", traceMsg='" + traceMsg + '\'' +
                '}';
    }

    @Override
    public String getI18nValue() {
        return errMsg;
    }
}
