package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4 16:20:19
 */
public interface AddDownstreamEnterprise {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Arg implements Serializable {
        @ApiModelProperty("分组id")
        private String id;

        @ApiModelProperty("添加的下游企业账号")
        private List<String> downstreamAccounts;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Result implements Serializable {
        @ApiModelProperty("成功添加的下游企业")
        private List<String> successIds;
        @ApiModelProperty("失败的下游企业")
        private List<String> failIds;
    }
}
