package com.fxiaoke.open.erpsyncdata.preprocess.result;


import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmResultCodeEnum;
import com.google.common.base.MoreObjects;

import java.io.Serializable;
import java.util.Map;

/**
 * CRM对象通用返回数据
 *
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
public class CrmObjectDataResult implements Serializable {

    private static final long serialVersionUID = -1815417162442579697L;

    /**
     * 返回码，默认值为0
     */
    protected int errorCode = CrmResultCodeEnum.SUCCESS.getErrorCode();

    /**
     * 返回描叙，默认值为success
     */
    protected String errorMessage = CrmResultCodeEnum.SUCCESS.getErrorMessage();

    /**
     * 返回描叙详情
     */
    protected String errorDescription;

    /**
     * 对象数据
     */
    private Map<String, Object> data;

    /**
     * 对象Id
     */
    private String dataId;

    private Object extraData;

    /**
     * traceId方便查询日志
     */
    protected String traceId;

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    /**
     * 默认构造方法
     */
    public CrmObjectDataResult() {}

    public CrmObjectDataResult(CrmResultCodeEnum resultCode) {
        this.errorCode = resultCode.getErrorCode();
        this.errorMessage = resultCode.getErrorMessage();
    }

    public CrmObjectDataResult(String dataId) {
        this.dataId = dataId;
    }

    public CrmObjectDataResult(Map<String, Object> data) {
        this.data = data;
    }

    public CrmObjectDataResult(int errcode, String errmsg) {
        this.errorCode = errcode;
        this.errorMessage = errmsg;
    }

    public CrmObjectDataResult(int errorCode, String errorMessage, String errorDescription) {
        this(errorCode, errorMessage);
        this.errorDescription = errorDescription;
    }

    public CrmObjectDataResult(int errorCode, String errorMessage, String errorDescription, String dataId) {
        this(errorCode, errorMessage, errorDescription);
        this.dataId = dataId;
    }

    public CrmObjectDataResult(int errorCode, String errorMessage, String errorDescription, Map<String, Object> data) {
        this(errorCode, errorMessage, errorDescription);
        this.data = data;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("errorCode", errorCode).add("errorMessage", errorMessage)
            .add("errorDescription", errorDescription).add("data", data).add("dataId", dataId).toString();
    }

    public Object getExtraData() {
        return extraData;
    }

    public void setExtraData(Object extraData) {
        this.extraData = extraData;
    }
}
