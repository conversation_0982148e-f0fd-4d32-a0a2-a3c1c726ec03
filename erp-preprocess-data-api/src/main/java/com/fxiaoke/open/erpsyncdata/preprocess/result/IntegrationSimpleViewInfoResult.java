package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/1/17 16:33
 * @desc
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntegrationSimpleViewInfoResult implements Serializable {

    @ApiModelProperty("集成流id")
    private String id;
    @ApiModelProperty("集成流名称")
    private String integrationStreamName;



}