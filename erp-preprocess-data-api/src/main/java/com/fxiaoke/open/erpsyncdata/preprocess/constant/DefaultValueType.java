package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Collections;

/**
 * 默认值类型
 *
 * <AUTHOR> (^_−)☆
 */
public enum DefaultValueType {
    /**
     * 空字符串
     */
    EMPTY_STR,
    /**
     * null
     */
    NULL,
    /**
     * 空集合
     */
    EMPTY_COLLECTION,
    /**
     * 空map
     */
    EMPTY_MAP,
    /**
     * 0
     */
    ZERO,

    /**
     * 固定值，走原来代码预设的逻辑
     */
    FIX_VALUE,
    ;

    public static DefaultValueType parseFromValue(String value) {
        if (value == null || !value.startsWith("$")) {
            return FIX_VALUE;
        }
        value = value.substring(1);
        for (DefaultValueType specialNullValueEnum : DefaultValueType.values()) {
            if (specialNullValueEnum.name().equalsIgnoreCase(value)) {
                return specialNullValueEnum;
            }
        }
        return FIX_VALUE;
    }


    public Object getSpecialValue() {
        switch (this) {
            case ZERO:
                return 0;
            case EMPTY_COLLECTION:
                return Collections.emptyList();
            case EMPTY_MAP:
                return Collections.emptyMap();
            case EMPTY_STR:
                return StringUtils.EMPTY;
            case NULL:
            default:
                return null;
        }
    }
}
