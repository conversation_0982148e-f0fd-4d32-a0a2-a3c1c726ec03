package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/28 14:11:52
 */
@Getter
public enum ExcelSystemFieldTypeEnum {
    country(I18NStringEnum.s216, ErpFieldTypeEnum.country),
    province(I18NStringEnum.s217, ErpFieldTypeEnum.province),
    city(I18NStringEnum.s218, ErpFieldTypeEnum.city),
    county(I18NStringEnum.s219, ErpFieldTypeEnum.district),
    town(I18NStringEnum.s331, ErpFieldTypeEnum.town),
    employee(I18NStringEnum.s353, ErpFieldTypeEnum.employee),
    department(I18NStringEnum.s221, ErpFieldTypeEnum.department),
    category(I18NStringEnum.s333, ErpFieldTypeEnum.category),
    ;
    private final String sheetName;
    private I18NStringEnum i18nEnum;
    private final ErpFieldTypeEnum erpFieldTypeEnum;

    ExcelSystemFieldTypeEnum(final I18NStringEnum i18nEnum, ErpFieldTypeEnum erpFieldTypeEnum) {
        this.sheetName = i18nEnum.getI18nValue();
        this.i18nEnum = i18nEnum;
        this.erpFieldTypeEnum = erpFieldTypeEnum;
    }


    public static final Map<ErpFieldTypeEnum, I18NStringEnum> erpFieldTypeEnumI18NMap = Arrays.stream(ExcelSystemFieldTypeEnum.values()).collect(java.util.stream.Collectors.toMap(ExcelSystemFieldTypeEnum::getErpFieldTypeEnum, ExcelSystemFieldTypeEnum::getI18nEnum));
    public static I18NStringEnum getEnumI18n(ErpFieldTypeEnum typeEnum) {
        return erpFieldTypeEnumI18NMap.get(typeEnum);
    }

    public static String getDataTypeName(ErpFieldTypeEnum fieldType, String lang, String tenantId, I18NStringManager i18NStringManager) {
        final I18NStringEnum i18n = ExcelSystemFieldTypeEnum.getEnumI18n(fieldType);
        if (Objects.isNull(i18n)) {
            return fieldType.name();
        }
        return i18NStringManager.get(i18n, lang, tenantId);
    }
}
