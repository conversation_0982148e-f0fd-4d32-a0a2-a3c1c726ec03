package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ObjectDataSyncMsg implements Serializable {
    private List<SingleDataSyncMsg> dataSyncResultList;

    @Data
    public static class SingleDataSyncMsg implements Serializable {
        /**
         * 源企业主对象apiName
         */
        private String sourceObjectApiName;
        /**
         * 源数据主键id
         */
        private String sourceDataId;
        /**
         * 源数据的数据事件类型 1、新增 2、修改 3、作废 {@link EventTypeEnum}
         */
        private Integer destEventType;
        /**
         * 目标企业主对象apiName
         */
        private String destObjectApiName;
        /**
         * 目标企数据id
         */
        private String destDataId;
        /**
         * 是否成功，正常终止都算成功
         */
        private Boolean writeDestSucc=false;
        /**
         * 最后执行节点
         */
        private String lastSyncNode;
        /**
         * 同步信息
         */
        private String msg;
        /**
         * 同步数据logId
         */
        private String syncLogId;
        /**
         * 需要返回的目标数据
         */
        private ObjectData needReturnDestObjectData;

    }
}
