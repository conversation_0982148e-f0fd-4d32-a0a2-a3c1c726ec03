package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/29 17:30:20
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class UpdateErpObjectResult implements Serializable {
    @ApiModelProperty("真实对象id")
    private String id;
    @ApiModelProperty("拆分批次")
    private Integer splitSeq;
}
