package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
public class SimpleEmployeeMapping  implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("数据中心id")
    private String dataCenterId;
    @ApiModelProperty("数据id")
    public String id; //
    @ApiModelProperty("fs员工id")
    public Integer fsEmployeeId ;
    @ApiModelProperty("fs员工name")
    public String fsEmployeeName;
    @ApiModelProperty("erp员工id")
    public String erpEmployeeId ;
    @ApiModelProperty("erp员工name")
    public String erpEmployeeName;
}
