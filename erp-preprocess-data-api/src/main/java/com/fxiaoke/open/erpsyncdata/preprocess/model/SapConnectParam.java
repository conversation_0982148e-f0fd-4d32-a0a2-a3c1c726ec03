package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.SAPConnectEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Data
@ApiModel
public class SapConnectParam extends BaseLangConnectParam {

    private static final long serialVersionUID = -3892271931165465013L;
    /**
     * 基础路径
     */
    @ApiModelProperty(value = "基础路径",example = "http://www.ceshi112.com/erp/syncdata/noAuth/mock")
    private String baseUrl;

    /**
     * 接口路径
     */
    @ApiModelProperty("接口路径,不传为默认")
    private ServicePath servicePath;

    /**
     * 获取header的脚本
     */
    @ApiModelProperty(value = "获取header的脚本",example = "return [:]")
    private String headerScript;

    /**
     * 获取header的函数
     */
    @ApiModelProperty(value = "获取header的函数",example = "return [:]")
    private String headerFunctionName;


    @ApiModelProperty(value = "header", example = "{\"tenant_id\":\"${ei}\"}")
    private Map<String, String> headerMap;


    @ApiModelProperty(value = "header连接类型", example = "paramsType functionType")
    private String headerType;

    /**
     * 系统版本
     */
    @ApiModelProperty(value = "系统版本")
    private String systemVersion;
    /**
     * 结果格式
     */
    @ApiModelProperty("结果格式")
    private ResultFormat resultFormat;

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

    /**
     * @see SAPConnectEnum
     *  SAP的连接方式
     *
     */
    private  String connectType;

    /**
     * sap的系统信息
     */
    private SAPRFCParams saprfcParams;

    /**
    /**
     * 接口地址
     */
    @Data
    public static class ServicePath implements Serializable {
        private static final long serialVersionUID = -6091691265483529697L;
        /**
         * 新建单条数据
         */
        @ApiModelProperty("新建单条数据")
        private String create = "create";

        /**
         * 新建单条明细数据
         */
        @ApiModelProperty("新建单条明细数据")
        private String createDetail = "createDetail";

        /**
         * 更新单条数据
         */
        @ApiModelProperty("更新单条数据")
        private String update = "update";


        /**
         * 更新单条明细数据
         */
        @ApiModelProperty("更新单条明细数据")
        private String updateDetail = "updateDetail";

        /**
         * 查询单条数据接口
         */
        @ApiModelProperty("查询单条数据接口")
        private String view = "queryMasterById";

        /**
         * 根据时间查询数据
         */
        @ApiModelProperty("根据时间查询数据接口")
        private String queryByTime = "queryMasterBatch";

        /**
         * 根据时间查询作废数据
         */
        @ApiModelProperty("根据时间查询作废数据")
        private String queryInvalidByTime = "queryInvalid";


        @ApiModelProperty("作废单条数据")
        private String invalid = "invalid";

        @ApiModelProperty("删除单条数据")
        private String delete = "delete";

        @ApiModelProperty("作废单条数据")
        private String invalidDetail = "invalidDetail";
    }

    /**
     * 结果格式
     */
    @Data
    public static class ResultFormat implements Serializable {
        private static final long serialVersionUID = -8160729428140174044L;
        /**
         * error code 字段
         */
        @ApiModelProperty(value = "error code 字段",example = "errCode")
        private String codeName;

        /**
         * error msg 字段
         */
        @ApiModelProperty(value = "error msg 字段",example = "errMsg")
        private String msgName;

        /**
         * 数据字段
         */
        @ApiModelProperty(value = "数据字段",example = "data")
        private String dataName;

        /**
         * 成功的code
         */
        @ApiModelProperty(value = "成功的code",example = "s106240000")
        private String successCode = "0";
    }

    /**
     * SAP的RFC参数格式
     */
    @Data
    public static class SAPRFCParams implements Serializable {



        /**
         * 系统服务器的地址
         */
        @ApiModelProperty(value = "代理服务器的地址")
        private String sapProxyUrl;

        /**
         * sap服务器的端口
         */
        @ApiModelProperty(value = "jco服务器的地址")
        private String jcoHost;



        /**
         * jco的系统编号
         */
        @ApiModelProperty(value = "jco的系统编号")
        private String jcoSystemNumber;


        /**
         * sapclient
         */
        @ApiModelProperty(value = "sapclient")
        private String jcoClient;


        /**
         * jco的用户名
         */
        @ApiModelProperty(value = "jco的用户名")
        private String jcoUser;

        /**
         * jco的账号密码
         */
        @ApiModelProperty(value = "jco的账号密码")
        private String jcoPassWord;

        /**
         *  jco的语言选项
         */
        @ApiModelProperty(value = "jco的语言选项")
        private String jcoLang;

        /**
         *  代理服务器的状态
         */
        @ApiModelProperty(value = "代理服务器的状态")
        private String proxyCode;

        /**
         *  代理服务器的错误信息
         */
        @ApiModelProperty(value = "代理服务器的错误信息")
        private String proxyMessage;

    }

    /**
     * SAP的代理服务器的地址
     */
    @Data
    public static class SAPProxyUrl implements Serializable {

        /**
         * 系统服务器的地址
         */
        @ApiModelProperty(value = "代理服务器的地址")
        private String sapProxyUrl;



    }
}
