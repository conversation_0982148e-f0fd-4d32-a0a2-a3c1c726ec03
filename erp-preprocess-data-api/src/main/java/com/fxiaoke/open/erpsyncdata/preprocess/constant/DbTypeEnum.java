package com.fxiaoke.open.erpsyncdata.preprocess.constant;

/**
 * 支持数据库类型
 * 默认使用ANSI,即标准sql
 *
 * <AUTHOR>
 * @date 2022.12.15
 */
public enum DbTypeEnum {
    ANSI, MYSQL, ORACLE, POSTGRESQL, SQLITE3, H2, SQLSERVER, SQLSERVER2012, PHOENIX;

    /**
     * 是否为指定数据库方言，检查时不分区大小写
     *
     * @param dialectName 方言名
     */
    public boolean match(String dialectName) {
        return dialectName != null && dialectName.toUpperCase().equals(name());
    }

}
