package com.fxiaoke.open.erpsyncdata.preprocess.model;

import cn.hutool.core.codec.Base64;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 简单的httpRequest 可序列化
 * <AUTHOR> (^_−)☆
 */
@Getter
@Setter
@ToString
public class HttpRequest2 implements Serializable {
    /**
     * 包括parameter
     */
    private String url;
    private String method;
    private Map<String, List<String>> headers;
    private String body;
    private String contentType = "application/json";

    private boolean requestBodyNeedBase64Decode = false;
    private boolean responseBodyNeedBase64Encode = false;

    public byte[] getContentBytes() {
        if (requestBodyNeedBase64Decode) {
            return Base64.decode(body);
        } else {
            return body.getBytes();
        }
    }
}
