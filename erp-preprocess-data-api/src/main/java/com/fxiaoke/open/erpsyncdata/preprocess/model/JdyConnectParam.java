package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/11/10 10:49
 * @desc
 */
@Data
@ApiModel
public class JdyConnectParam extends BaseLangConnectParam {


    /**
     * 对接系统名称
     */
    @ApiModelProperty(value = "对接系统名称")
    private String systemName;
    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID")
    private String clientId;
    /**
     * 应用secret
     */
    @ApiModelProperty(value = "应用secret")
    private String clientSecret;
    /**
     * 第三方实例ID
     */
    @ApiModelProperty(value = "第三方实例ID")
    private String instanceId;
    /**
     * appkey
     */
    @ApiModelProperty(value = "应用的KEY")
    private String appKey;
    /**
     * appSecret
     */
    @ApiModelProperty(value = "应用的appSecret")
    private String appSecret;
    /**
     * 接口路径
     */
    @ApiModelProperty("接口路径,不传为默认")
    private ServicePath servicePath;

    /**
     * 获apl类的apiName
     */
    @ApiModelProperty(value = "aplClassApiName", example = "return [:]")
    private String aplClassApiName;


    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;


    /**
     * 接口地址
     */
    @Data
    public static class ServicePath implements Serializable {
        private static final long serialVersionUID = -6091691265483529697L;
        /**
         * 新建单条数据
         */
        @ApiModelProperty("新建单条数据")
        private String create = "create";

        /**
         * 新建单条明细数据
         */
        @ApiModelProperty("新建单条明细数据")
        private String createDetail = "createDetail";

        /**
         * 更新单条数据
         */
        @ApiModelProperty("更新单条数据")
        private String update = "update";


        /**
         * 更新单条明细数据
         */
        @ApiModelProperty("更新单条明细数据")
        private String updateDetail = "updateDetail";

        /**
         * 查询单条数据接口
         */
        @ApiModelProperty("查询单条数据接口")
        private String view = "queryMasterById";

        /**
         * 根据时间查询数据
         */
        @ApiModelProperty("根据时间查询数据接口")
        private String queryByTime = "queryMasterBatch";

        /**
         * 根据时间查询作废数据
         */
        @ApiModelProperty("根据时间查询作废数据")
        private String queryInvalidByTime = "queryInvalid";
    }

    /**
     * 结果格式
     */
    @Data
    public static class ResultFormat implements Serializable {
        private static final long serialVersionUID = -8160729428140174044L;
        /**
         * error code 字段
         */
        @ApiModelProperty(value = "error code 字段", example = "errCode")
        private String codeName;

        /**
         * error msg 字段
         */
        @ApiModelProperty(value = "error msg 字段", example = "errMsg")
        private String msgName;

        /**
         * 数据字段
         */
        @ApiModelProperty(value = "数据字段", example = "data")
        private String dataName;

        /**
         * 成功的code
         */
        @ApiModelProperty(value = "成功的code", example = "s106240000")
        private String successCode = "0";
    }


}
