package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendSuperAdminNoticeArg;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum NoticeType {
    POLLING_ERP_DATA_DUPLICATE("轮询到重复的ERP数据", I18NStringEnum.s861.getI18nKey()),
    GET_BY_ID_INTERFACE_EXCEPTION("客户方getById接口异常", I18NStringEnum.s862.getI18nKey())
    ;
    private final String title;
    private final String i18nKey;

    public SendSuperAdminNoticeArg arg(){
        return new SendSuperAdminNoticeArg(this);
    }
}
