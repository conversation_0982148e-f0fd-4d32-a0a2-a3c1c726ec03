package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 16:29 2020/10/21
 * @Desc:是否开启cpq配置
 */
@Getter
@AllArgsConstructor
public enum IsOpenCpqEnum {
    IsOpen("1", "开启", I18NStringEnum.s863.getI18nKey()),
    IsNotOpen("0", "不开启", I18NStringEnum.s864.getI18nKey());

    private String value;

    private String description;
    private String i18nKey;

    public boolean match(String value){
        return this.value.equals(value);
    }
}
