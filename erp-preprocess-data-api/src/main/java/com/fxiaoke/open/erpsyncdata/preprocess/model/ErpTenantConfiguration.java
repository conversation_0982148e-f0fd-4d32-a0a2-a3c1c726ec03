package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/9
 */
@Data
public class ErpTenantConfiguration implements Serializable {

    private static final long serialVersionUID = 2476976216930848335L;
    /**
     * 主键
     */
    private String id;

    /**
     * 数据中心id
     */
    @NotNull
    private String dataCenterId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 配置类型
     * 也存在动态type
     * TenantConfigurationTypeEnum
     */
    @NotNull
    private String type;

    /**
     * 配置信息
     */
    @NotNull
    private String configuration;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
