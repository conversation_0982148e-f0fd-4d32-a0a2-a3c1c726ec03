package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;

import java.util.List;

public interface SyncPloyDetailSnapshotService {

    Result2<Boolean> isMatchDestCondition(String destTenantId, String destObjectApiName, Integer tenantType);

    /**
     * 获取最新的策略明细快照快照集合
     * @param sourceTenantId 源企业id
     * @param sourceObjectApiName 源对象apiName
     * @return
     */
    Result2<List<SyncPloyDetailSnapshotData2>> listNewestEnableSyncPloyDetailsSnapshots(String sourceTenantId, String sourceObjectApiName, Integer tenantType , List<String> destTenantIds);

    /**
     * 获取最新的集成流快照集合
     * @param sourceTenantId 源企业id
     * @param dest_object_api_name 目标对象apiName
     * @return
     */
    Result2<List<SyncPloyDetailSnapshotData2>> listEnableSyncPloyDetailByDestApiName(String sourceTenantId, String dest_object_api_name, Integer tenantType , List<String> destTenantIds);


    Result2<SyncPloyDetailSnapshotData2> getSyncPloyDetailSnapshotBySnapshotId(String tenantId, String snapshotId);
}
