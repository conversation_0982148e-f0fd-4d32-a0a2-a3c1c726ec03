package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.DataDashboardResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/1/3 19:51
 * 数据大屏筛选
 *
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class DataScreenFilterResult implements Serializable {


    @ApiModelProperty("对接的系统筛选")
    private Set<Option> systemOptional;

    @ApiModelProperty("对接crm业务")
    private Set<Option> crmObjApiOptional;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Option implements Serializable {

        private String value;
        private String label;
    }

}
