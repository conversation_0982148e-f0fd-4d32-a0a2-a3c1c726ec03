package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;

/**
 * 依赖数据特殊处理服务
 * <AUTHOR>
 * @20220817
 */
public interface DependDataSpecialService {
    Result2<SyncDataMappingData> getSyncDataMappingData(String tenantId,
                                                        ObjectData sourceData,
                                                        SyncDataData syncDataData,
                                                        FieldMappingData fieldMappingData);
}
