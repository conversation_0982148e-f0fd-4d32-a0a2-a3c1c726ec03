package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmObjectName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21 16:45:46
 */
public interface GetCrmObjectApiNameByErpObjApiName {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Arg implements Serializable {
        @ApiModelProperty("erp中间对象apiName")
        private String objectApiName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Result implements Serializable {
        @ApiModelProperty("启用的集成流的对应的crm对象apiName")
        private List<CrmObjectName> apiNames;
    }
}
