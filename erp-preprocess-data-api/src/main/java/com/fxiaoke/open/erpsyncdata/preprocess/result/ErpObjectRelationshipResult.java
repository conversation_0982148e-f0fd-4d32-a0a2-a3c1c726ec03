package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SubDetailExtend;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date: 13:43 2020/9/10
 * @Desc:
 */
@ApiModel
@Data
public class ErpObjectRelationshipResult implements Serializable {
    @ApiModelProperty("真实对象")
    private ErpObjectDescResult actualErpObject; //
    @ApiModelProperty("虚拟对象")
    private List<ErpObjectDescResult> fakeErpObject; //
    @ApiModelProperty("主对象apiName")
    private String masterObjectApiName; //
    @ApiModelProperty("批次")
    private Integer splitSeq; //
    /**
     * facebook 有 pageId和formId
     * linkedin 有 formId
     */
    @ApiModelProperty("海外对象的特殊配置")
    private Object extendValue;


    public void allSetFakeErpObject(List<ErpObjectDescResult> allFakeErpObject) {
        if (allFakeErpObject == null) {
            fakeErpObject = new ArrayList<>();
            return;
        }
        for (ErpObjectDescResult erpObjectDescResult : allFakeErpObject) {
            if (ErpObjSplitTypeEnum.NOT_SPLIT.equals(erpObjectDescResult.getSplitType())) {
                //标识主对象apiName
                masterObjectApiName = erpObjectDescResult.getErpObjectApiName();
            }
        }
        //主对象放前面
        allFakeErpObject.sort(Comparator.comparing(v -> v.getSplitType() == ErpObjSplitTypeEnum.NOT_SPLIT ? 0 : 1));
        List<ErpObjectDescResult> results = new ArrayList<>();
        for (ErpObjectDescResult erpObjectDescResult : allFakeErpObject) {
            if (ErpObjSplitTypeEnum.SUB_DETAIL_LOOKUP_DETAIL.equals(erpObjectDescResult.getSplitType())) {
                //孙对象放到明细对象下
                SubDetailExtend subDetailExtend = SubDetailExtend.parse(erpObjectDescResult.getErpObjectExtendValue());
                if (subDetailExtend != null && subDetailExtend.getDetailObjectApiName() != null) {
                    Optional<ErpObjectDescResult> op = allFakeErpObject.stream().filter(v -> v.getErpObjectApiName().equals(subDetailExtend.getDetailObjectApiName())).findFirst();
                    if (op.isPresent()) {
                        ErpObjectDescResult parentDesc = op.get();
                        if (parentDesc.getChildren() == null) {
                            parentDesc.setChildren(new ArrayList<>());
                        }
                        parentDesc.getChildren().add(erpObjectDescResult);
                        continue;
                    }
                }
            }
            results.add(erpObjectDescResult);
        }
        fakeErpObject = results;
    }
}
