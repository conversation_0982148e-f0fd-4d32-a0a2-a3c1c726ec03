package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailTriggerEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/20 11:42:23
 */
@AllArgsConstructor
@NoArgsConstructor()
@Data
public class TriggerConfig implements Serializable {
    /**
     * 触发类型 1-系统默认 2-自定义
     *
     * @see SyncPloyDetailTriggerEnum
     */
    private Integer triggerType;

    /**
     * triggerType = 2 时, 配置触发同步的字段
     * key: 触发对象,crm对象apiName
     * value: 触发字段
     */
    private Map<String, List<String>> triggerObjectFields;
}
