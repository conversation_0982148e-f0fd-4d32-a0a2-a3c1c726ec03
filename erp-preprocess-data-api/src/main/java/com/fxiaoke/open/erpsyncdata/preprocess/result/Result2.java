package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@ApiModel
public class Result2<T>  extends Result<T> implements Serializable {
    private static final long serialVersionUID = 1535964835070227503L;
    @ApiModelProperty("状态码")
    private int intErrCode = -1;

    //保证Hessian 序列化成功
    private Result2() {
    }

    public static <R> Result2<R> newError(ResultCodeEnum resultCode, R data) {
        return newInstance(resultCode.getErrCode(),
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR.getErrCode(),
                resultCode.getErrMsg(),
                data);
    }

    public static <R> Result2<R> newError(ResultCodeEnum resultCode) {
        return newInstance(resultCode.getErrCode(),
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR.getErrCode(),
                resultCode.getErrMsg(),
                null);
    }

    public static <R> Result2<R> newError(int errCode, String errMsg) {
        return newInstance(errCode,
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR.getErrCode(),
                errMsg,
                null);
    }

    public static Result2<List<ObjectData>> newErrorByI18N(int errCode, I18NStringEnum i18NStringEnum) {
        return newErrorByI18N(errCode, i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), null);
    }

    public static <R> Result2<R> newErrorByI18N(int errCode, String errMsg, String i18nKey, List<String> i18nExtra) {
        return newInstanceByI18N(errCode,
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR.getErrCode(),
                errMsg,
                null,
                i18nKey,
                i18nExtra);
    }

    public static <R> Result2<R> newError4CustomFunc(int errCode, String errMsg) {
        return newInstance(errCode,
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.CUSTOM_FUNC_ERROR.getErrCode(),
                errMsg,
                null);
    }

    public static <R> Result2<R> newError4CustomFuncByI18N(int errCode, String errMsg, String i18nKey, List<String> i18nExtra) {
        return newInstanceByI18N(errCode,
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.CUSTOM_FUNC_ERROR.getErrCode(),
                errMsg,
                null,
                i18nKey,
                i18nExtra);
    }

    public static <R> Result2<R> newError(String errCode, String errMsg, String traceMsg) {
        Result2<R> result = new Result2<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setTraceMsg(traceMsg);
        result.setIntErrCode(-1);
        return result;
    }

    public static <R> Result2<R> newErrorByI18N(String errCode, String errMsg, String traceMsg, String i18nKey, List<String> i18nExtra) {
        Result2<R> result = new Result2<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setTraceMsg(traceMsg);
        result.setIntErrCode(-1);
        result.setI18nKey(i18nKey);
        result.setI18nExtra(i18nExtra);
        return result;
    }

    public static <R> Result2<R> newInstance(int intErrCode,String errCode, String errMsg, R data) {
        Result2<R> result = new Result2<>();
        result.setIntErrCode(intErrCode);
        result.setErrMsg(errMsg);
        result.setData(data);
        result.setErrCode(errCode);
        return result;
    }

    public static <R> Result2<R> newInstanceByI18N(int intErrCode,String errCode, String errMsg, R data, String i18nKey, List<String> i18nExtra) {
        Result2<R> result = new Result2<>();
        result.setIntErrCode(intErrCode);
        result.setErrMsg(errMsg);
        result.setData(data);
        result.setErrCode(errCode);
        result.setI18nKey(i18nKey);
        result.setI18nExtra(i18nExtra);
        return result;
    }

    public static <R> Result2<R> newSuccess() {
        return newInstance(ResultCodeEnum.SUCCESS.getErrCode(),
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SUCCESS.getErrCode(),
                ResultCodeEnum.SUCCESS.getErrMsg(),
                null);
    }

    public static <R> Result2<R> newSuccess(R data) {
        return newInstance(ResultCodeEnum.SUCCESS.getErrCode(),
                com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SUCCESS.getErrCode(),
                ResultCodeEnum.SUCCESS.getErrMsg(),
                data);
    }

    @Override
    public boolean isSuccess() {
        return super.isSuccess()||ResultCodeEnum.SUCCESS.getErrCode() == this.intErrCode;
    }
}
