package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@ToString(callSuper = true)
@Setter
@Getter
@AllArgsConstructor
@ApiModel("云星辰对象列表查询参数")
public class ErpListArg implements Serializable {

    private static final long serialVersionUID = 8136395440724348023L;
    @ApiModelProperty("查询云星辰列表的url")
    private String url;
    @ApiModelProperty("需要的字段列名称")
    private List<String> fieldNames;
    @ApiModelProperty("分页参数：页码")
    private Integer page;
    @ApiModelProperty("分页参数：每页数据条数")
    private Integer perPage;
}
