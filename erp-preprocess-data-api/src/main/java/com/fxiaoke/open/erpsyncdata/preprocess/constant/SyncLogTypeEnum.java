package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 全链路日志类型枚举
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/6/2
 */
@AllArgsConstructor
@Getter
public enum SyncLogTypeEnum {
    /**
     * 通用的
     */
    COMMON("COMMON", "通用", I18NStringEnum.s412.getI18nKey(), -1, false),

    /**
     * 读接口
     */
    READ("READ", "读接口调用", I18NStringEnum.s841.getI18nKey(), 1),
    /**
     * APL读接口
     */
    READ_APL("READ_APL", "APL函数", I18NStringEnum.s4009.getI18nKey(), 2),
    /**
     * 临时库处理
     */
    TEMP("TEMP", "临时库处理", I18NStringEnum.s842.getI18nKey(), 3),
    /**
     * crm触发数据，分发框架出来合并后的数据（有可能补齐了，具体逻辑看DispatcherEventListen）
     */
    CRM_TRIGGER("CRM_TRIGGER", "crm触发日志", I18NStringEnum.s4010.getI18nKey(), 4),
    /**
     * 数据过滤节点
     */
    DATA_SYNC_FILTER("DATA_SYNC_FILTER", "数据范围", I18NStringEnum.s843.getI18nKey(), 5),

    /**
     * 同步前函数
     */
    PRE_FUNCTION("PRE_FUNCTION", "同步前函数", I18NStringEnum.s844.getI18nKey(), 6),
    /**
     * 字段映射
     */
    SYNC_DATA("SYNC_DATA", "字段映射", I18NStringEnum.s4013.getI18nKey(), 7),
    /**
     * 查询crm
     */
    CRM_QUERY("CRM_QUERY", "查询crm", I18NStringEnum.s845.getI18nKey(), 8),
    /**
     * 同步中函数
     */
    MID_FUNCTION("MID_FUNCTION", "同步中函数", I18NStringEnum.s846.getI18nKey(), 9),
    /**
     * 写接口
     */
    WRITE("WRITE", "写接口调用", I18NStringEnum.s847.getI18nKey(), 10),
    /**
     * APL读接口
     */
    WRITE_APL("WRITE_APL", "APL函数", I18NStringEnum.s4009.getI18nKey(), 11),
    /**
     * 回写crm
     */
    REVERSE_WRITE("REVERSE_WRITE", "回写", I18NStringEnum.s366.getI18nKey(), 12, false),
    /**
     * 同步后函数
     */
    AFTER_FUNCTION("AFTER_FUNCTION", "同步后函数", I18NStringEnum.s848.getI18nKey(), 13),
    /**
     * 同步后系统处理详情
     */
    AFTER_SYSTEM_PROCESS("AFTER_SYSTEM_PROCESS", "同步后系统处理详情", I18NStringEnum.s849.getI18nKey(), 14, false);
    private String type;

    private String description;

    private String i18nKey;

    private Integer order;

    /**
     * 是否为全链路日志节点
     */
    private boolean fullLog;

    SyncLogTypeEnum(String type, String description, String i18nKey, Integer order) {
        this.type = type;
        this.description = description;
        this.i18nKey = i18nKey;
        this.order = order;
        this.fullLog = true;
    }

    private static final Set<String> functionNode = Stream.of(SyncLogTypeEnum.AFTER_FUNCTION, SyncLogTypeEnum.MID_FUNCTION, SyncLogTypeEnum.PRE_FUNCTION).map(SyncLogTypeEnum::getType).collect(Collectors.toSet());

    public static boolean isFunctionNode(String type) {
        return functionNode.contains(type);
    }

    public String getType() {
        return type;
    }

    public Integer getOrder() {
        return order;
    }

    public String getDescription() {
        return description;
    }

    private static final Map<String, SyncLogTypeEnum> TYPE_MAP = ImmutableMap.of(
            ErpObjInterfaceUrlEnum.queryMasterBatch.name(), SyncLogTypeEnum.READ,
            ErpObjInterfaceUrlEnum.queryMasterById.name(), SyncLogTypeEnum.READ,
            ErpObjInterfaceUrlEnum.push.name(), SyncLogTypeEnum.READ,
            ErpObjInterfaceUrlEnum.crmQuery.name(), SyncLogTypeEnum.READ,
            ErpObjInterfaceUrlEnum.reverseWrite2Crm.name(), SyncLogTypeEnum.REVERSE_WRITE
    );
    public static SyncLogTypeEnum convertInterfaceType(String type) {
        return StringUtils.isEmpty(type) ? SyncLogTypeEnum.WRITE :
                TYPE_MAP.getOrDefault(type, SyncLogTypeEnum.WRITE);
    }

    public static Integer getSize(SyncLogTypeEnum syncLogTypeEnum){
        if(syncLogTypeEnum.equals(SyncLogTypeEnum.READ)){
            return 5;
        }else if(syncLogTypeEnum.equals(SyncLogTypeEnum.TEMP)){
            return 6;
        }else {
            return 7;
        }
    }

    public static List<SyncLogTypeEnum> getTypeByOrder() {
        return Arrays.stream(SyncLogTypeEnum.values()).sorted(Comparator.comparing(SyncLogTypeEnum::getOrder)).collect(Collectors.toList());
    }

}
