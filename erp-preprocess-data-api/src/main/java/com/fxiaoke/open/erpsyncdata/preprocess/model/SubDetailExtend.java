package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 明细子结构扩展
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/3/2
 */
@Data
@Accessors(chain = true)
public class SubDetailExtend {
    /**
     * 明细中间对象的apiName
     */
    private String detailObjectApiName;
    /**
     * 孙对象真实的明细编码,表头编码.孙表编码
     */
    private String realObjectApiName;

    public String toJson() {
        return JSON.toJSONString(this);
    }

    public static SubDetailExtend parse(Object obj) {
        if (obj == null) {
            return null;
        }
        String str = "";
        if (obj instanceof String) {
            str = obj.toString();
        } else {
            str = JSON.toJSONString(obj);
        }
        try{
            return JSON.parseObject(str, SubDetailExtend.class);
        }catch (Exception e){
            //异常是脏数据
            return null;
        }
    }
}
