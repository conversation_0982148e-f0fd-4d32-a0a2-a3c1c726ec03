package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/6 10:58:54
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DownstreamRelationManage implements Serializable {
    @ApiModelProperty(value = "企业Id")
    private String tenantId;
    @ApiModelProperty(value = "企业账号")
    private String enterpriseAccount;
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    /**
     * @see com.fxiaoke.open.erpsyncdata.dbproxy.constant.RelationErpShardStatusEnum
     */
    @ApiModelProperty(value = "代理状态 1-代管中 2-取消代管")
    private Integer status;

    @ApiModelProperty("集成流统计数据")
    private GroupStreamStat groupStreamStat;
}
