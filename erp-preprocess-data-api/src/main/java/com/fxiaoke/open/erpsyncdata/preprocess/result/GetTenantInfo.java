package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/4 16:20:19
 */
public interface GetTenantInfo {

    @Data
    @ApiModel
    class Arg implements Serializable {
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Result implements Serializable {
        @ApiModelProperty(value = "该企业是否为集成管控模板企业")
        private Boolean templateTenant;
        @ApiModelProperty(value = "上游企业id")
        private String upstreamId;
        @ApiModelProperty(value = "上游企业名称")
        private String upstreamName;
    }
}
