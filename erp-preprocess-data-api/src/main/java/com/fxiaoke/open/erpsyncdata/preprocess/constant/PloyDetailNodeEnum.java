package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 集成流节点枚举
 *
 * <AUTHOR>
 * @date 2024/8/7
 */
@AllArgsConstructor
@Getter
public enum PloyDetailNodeEnum {


    /**
     * 源系统
     */
    SOURCE_SYSTEM("SOURCE_SYSTEM", "源系统", I18NStringEnum.s4011.getI18nKey(), 10,true,"fx-icon-obj-app106"),
    /**
     * 临时库，包括：临时库日志
     */
    TEMP_NODE("TEMP_NODE", "临时库", I18NStringEnum.s5105.getI18nKey(), 11,true,"fx-icon-lindaoqidekucun"),
    /**
     * crm触发
     */
    CRM_TRIGGER(SyncLogTypeEnum.CRM_TRIGGER.getType(), SyncLogTypeEnum.CRM_TRIGGER.getDescription(), SyncLogTypeEnum.CRM_TRIGGER.getI18nKey(), 20,true,""),
    /**
     * 读接口
     */
    READ(SyncLogTypeEnum.READ.getType(), SyncLogTypeEnum.READ.getDescription(), SyncLogTypeEnum.READ.getI18nKey(), 30,true,""),
    /**
     * 临时库数据
     */
    TEMP_DATA("TEMP_DATA", "临时库数据", I18NStringEnum.s4016.getI18nKey(), 40,true,"fx-icon-lindaoqidekucun"),
    /**
     * 临时库日志
     */
    TEMP(SyncLogTypeEnum.TEMP.getType(), SyncLogTypeEnum.TEMP.getDescription(), SyncLogTypeEnum.TEMP.getI18nKey(), 50,true,""),

    /**
     * ID映射
     */
    ID_MAPPING("ID_MAPPING", "ID映射", I18NStringEnum.s4012.getI18nKey(), 60,false,"fx-icon-guanlianCRMduixiang"),

    /**
     * 数据范围
     */
    DATA_SYNC_FILTER(SyncLogTypeEnum.DATA_SYNC_FILTER.getType(), SyncLogTypeEnum.DATA_SYNC_FILTER.getDescription(), SyncLogTypeEnum.DATA_SYNC_FILTER.getI18nKey(), 70,true,"fx-icon-filter"),

    /**
     * 同步前函数
     */
    PRE_FUNCTION(SyncLogTypeEnum.PRE_FUNCTION.getType(), SyncLogTypeEnum.PRE_FUNCTION.getDescription(), SyncLogTypeEnum.PRE_FUNCTION.getI18nKey(), 80,true,"fx-icon-obj-app446"),

    /**
     * 根据源数据查询CRM
     */
    CRM_QUERY_BY_SOURCE("CRM_QUERY_BY_SOURCE", SyncLogTypeEnum.CRM_QUERY.getDescription(), SyncLogTypeEnum.CRM_QUERY.getI18nKey(), 90,false,"fx-icon-obj-app46"),
    /**
     * 字段映射
     */
    SYNC_DATA(SyncLogTypeEnum.SYNC_DATA.getType(), SyncLogTypeEnum.SYNC_DATA.getDescription(), SyncLogTypeEnum.SYNC_DATA.getI18nKey(), 100,true,"fx-icon-obj-app447"),
    /**
     * 根据目标数据查询CRM
     */
    CRM_QUERY_BY_DEST("CRM_QUERY_BY_DEST", SyncLogTypeEnum.CRM_QUERY.getDescription(), SyncLogTypeEnum.CRM_QUERY.getI18nKey(), 110,false,"fx-icon-obj-app46"),
    /**
     * 同步中函数
     */
    MID_FUNCTION(SyncLogTypeEnum.MID_FUNCTION.getType(), SyncLogTypeEnum.MID_FUNCTION.getDescription(), SyncLogTypeEnum.MID_FUNCTION.getI18nKey(), 120,true,"fx-icon-obj-app446"),

    /**
     * 目标系统
     */
    DEST_SYSTEM("DEST_SYSTEM", "目标系统", I18NStringEnum.s4014.getI18nKey(), 130,true,"fx-icon-obj-app194"),
    /**
     * 中间表
     */
    DATA_MAPPING("DATA_MAPPING", "中间表", I18NStringEnum.s4017.getI18nKey(), 140,true,""),
    /**
     * 写接口
     */
    WRITE(SyncLogTypeEnum.WRITE.getType(), SyncLogTypeEnum.WRITE.getDescription(), SyncLogTypeEnum.WRITE.getI18nKey(), 150,true,""),
    /**
     * 回写
     */
    REVERSE_WRITE(SyncLogTypeEnum.REVERSE_WRITE.getType(), SyncLogTypeEnum.REVERSE_WRITE.getDescription(), SyncLogTypeEnum.REVERSE_WRITE.getI18nKey(), 160,true,"fx-icon-obj-app200"),
    /**
     * 同步后函数
     */
    AFTER_FUNCTION(SyncLogTypeEnum.AFTER_FUNCTION.getType(), SyncLogTypeEnum.AFTER_FUNCTION.getDescription(), SyncLogTypeEnum.AFTER_FUNCTION.getI18nKey(), 170,true,"fx-icon-obj-app446"),
    /**
     * 条件重试
     */
    CONDITIONAL_RETRY("CONDITIONAL_RETRY", "条件重试", I18NStringEnum.s4015.getI18nKey(), 180,false,"fx-icon-gengxinzhuangtai"),
    /**
     * 通知
     */
    CRM_NOTIC("CRM_NOTIC", "CRM通知", I18NStringEnum.s1223.getI18nKey(), 190,false,"fx-icon-obj-app423"),

    ;
    private String type;

    private String description;

    private String i18nKey;

    private Integer order;
    /**
     * 是否可以查看日志
     */
    private Boolean canQueryLog;

    private String icon;

    public String getType() {
        return type;
    }

    public Integer getOrder() {
        return order;
    }

    public String getDescription() {
        return description;
    }
    public Boolean getCanQueryLog() {
        return canQueryLog;
    }
    public String getIcon() {
        return icon;
    }
    public static PloyDetailNodeEnum getByTypeStr(String codeStr) {
        if (codeStr == null) {
            return null;
        }
        for(PloyDetailNodeEnum item : PloyDetailNodeEnum.values()) {
            if (item.getType().equals(codeStr)) {
                return item;
            }
        }
        return null;
    }
}
