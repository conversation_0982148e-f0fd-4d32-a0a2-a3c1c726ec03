package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 集成平台审计日志专用Model
 * <AUTHOR>
 * @date 2023.03.21
 */
@Data
public class AuditLog4ErpDss implements Serializable {
    /**
     * 业务模块，必填
     */
    private String module;
    /**
     * app类型,如果没有请默认CRM，必填
     */
    private String appId;
    /**
     * 企业ID,必填
     */
    private String corpId;
//    private String outTenantId;
    /**
     * 操作行为（各业务线自定义的行为，例如导出、导入），配合配置文件，必填
     */
    private String bizOperationName;
    /**
     * 子模块,管理日志需要将操作对象和字段拼接
     */
    private String operationObject;
    /**
     * 操作时间
     */
    private Long operationTime;
    /**
     * 操作对象，需要填写正确的apiName，不做特殊处理，若有错误，自行承担,必填
     */
    private String objectName;
//    private String objectId;
    /**
     * 简单的文本信息
     */
    private String textMessage;
    private String userId;
    private String userName;
    private String logId;
    /**
     * 日志级别，1（一般日志），2（安全日志），3（错误日志）
     */
    private int level;
    /**
     * 请求来源方（例如审批流）
     */
    private String peerName;
}
