package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

/**
 * k3cloud物料分组接口请求参数
 * created by wubb @ 2020/11/02
 */
@Data
public class KcMaterialGroup {
    /** 主键id **/
    private int id;

    /** 物料编码 **/
    private String number;

    /** 父分组FID **/
    private int parentId;

    /** 物料名称 **/
    private String name;

    /** 预处理数据，拼装成层级结构 **/
    @JsonIgnore
    @Transient
    private List<KcMaterialGroup> children = new ArrayList<>();
}
