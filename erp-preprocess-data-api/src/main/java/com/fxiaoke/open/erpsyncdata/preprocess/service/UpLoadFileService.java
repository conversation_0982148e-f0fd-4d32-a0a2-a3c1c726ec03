package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.UpLoadFileArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * <AUTHOR>
 * @Date: 19:59 2023/1/30
 * @Desc:
 */
public interface UpLoadFileService {
    Result<String> doFileService(@ContextEi() String tenantId, UpLoadFileArg uploadFileArg);
}
