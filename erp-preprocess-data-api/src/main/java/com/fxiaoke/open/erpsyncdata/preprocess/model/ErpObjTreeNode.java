package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/21
 */
@Data
@Accessors(chain = true)
public class ErpObjTreeNode {
    private String erpObjectApiName;
    private String erpObjectName;

    private List<Field> fields;
    /**
     * 是否已存在的对象
     */
    private boolean exist = false;
    private List<ErpObjTreeNode> children;
    /**
     * 是否已完成，为true时才有数据，为false时应该轮询获取，并且将参数queryCache设置为true。
     */
    private boolean complete = true;

    public static ErpObjTreeNode of(String objApiName, String objName) {
        ErpObjTreeNode erpObjTreeNode = new ErpObjTreeNode();
        erpObjTreeNode.setErpObjectApiName(objApiName);
        erpObjTreeNode.setErpObjectName(objName);
        return erpObjTreeNode;
    }

    public static ErpObjTreeNode notComplete() {
        ErpObjTreeNode erpObjTreeNode = new ErpObjTreeNode();
        erpObjTreeNode.setComplete(false);
        return erpObjTreeNode;
    }

    public List<ErpObjTreeNode> getChildren() {
        if (children == null) {
            this.children = new ArrayList<>();
        }
        return children;
    }

    @Data
    public static class Field {

        private String fieldApiName;

        /**
         * 字段Label
         */
        private String fieldLabel;

        /**
         * 是否必填
         */
        private boolean required;

        /**
         * 字段类型
         */
        private ErpFieldTypeEnum fieldDefineType;

        /**
         * 字段扩展值
         */
        private String fieldExtendValue;
    }
}
