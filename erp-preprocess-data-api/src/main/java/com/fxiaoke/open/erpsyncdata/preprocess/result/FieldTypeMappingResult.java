package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 9:46 2020/9/2
 * @Desc:
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class FieldTypeMappingResult implements Serializable {
    /**
     * @see ErpFieldTypeEnum
     */
    @ApiModelProperty("fs字段类型")
    public String fsFieldType ;
    @ApiModelProperty("fs字段类型描述")
    public String fsFieldTypeLabel;
    /**
     * @see ErpFieldTypeEnum
     */
    @ApiModelProperty("erp字段类型")
    public String erpFieldType;
    @ApiModelProperty("erp字段类型描述")
    public String erpFieldTypeLabel;
    @ApiModelProperty("渠道")
    public ErpChannelEnum channel;
    @ApiModelProperty("字段值映射数据列表")
    public List<SpecialFieldMappingResult> fieldDataMappingList;
}
