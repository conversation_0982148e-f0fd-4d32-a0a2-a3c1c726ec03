package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * 扫描同步过程中相关企业异常的同步对象并告知相关负责人
 */
public interface ScanSyncWarnningService {

    /**
     * 扫描企业的同步中间表中的异常数据数量。如果超过阈值则同步到相关的负责人
     * @param tenantId
     * @dateTime 2022-05-19 11:32
     * <AUTHOR>
     * @version 1.0
     *
     */
    Result<String> scanSyncDataMappingErrorNumber(@ContextEi() String tenantId);

}
