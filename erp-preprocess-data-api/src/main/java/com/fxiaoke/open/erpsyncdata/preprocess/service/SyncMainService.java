package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg.EventData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;

import java.util.List;

public interface SyncMainService {


    /**
     * 数据同步主方法
     * @param eventData
     * @return
     */
    Result2<ObjectDataSyncMsg> syncDataMain(SyncDataContextEvent eventData);


    /**
     * 这里实际上是发送到分发框架，然后做一下数据过滤返回结果。
     * @param eventDatas
     * @param destTenantId
     * @param syncDependForce
     * @return
     */
    Result2<List<EventData>> sendDependEventData(List<EventData> eventDatas, String destTenantId, Boolean syncDependForce);


    @Deprecated
    Result2<Void> changeToWatting(String tenantId, String syncDataId, Integer oldSyncDataStatus, Integer tenantType, List<EventData> eventDataList);



    Result2<String> sendEventData2DispatcherMq(SyncDataContextEvent eventData);
}
