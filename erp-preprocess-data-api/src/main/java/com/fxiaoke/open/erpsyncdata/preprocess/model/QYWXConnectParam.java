package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/26
 * 企微连接参数类
 */
@Data
@ApiModel
public class QYWXConnectParam implements BaseConnectParam {
    private static final long serialVersionUID = 2369962196456072951L;

    @ApiModelProperty(value = "数据中心ID")
    private String dataCenterId;

    @ApiModelProperty(value = "纷享企业ID")
    private String fsEa;

    @ApiModelProperty(value = "外部企业ID")
    private String outEa;

    @ApiModelProperty(value = "外部企业部门ID")
    private String outDepId;


    @ApiModelProperty("push数据的对象名称，企微连接器不需要")
    @Deprecated
    private List<String> pushDataApiNames;
}
