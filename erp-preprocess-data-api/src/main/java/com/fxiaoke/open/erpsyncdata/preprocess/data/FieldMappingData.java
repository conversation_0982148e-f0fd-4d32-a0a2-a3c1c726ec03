package com.fxiaoke.open.erpsyncdata.preprocess.data;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class FieldMappingData implements Serializable {
    private String sourceApiName;
    private String sourceType;
    private String sourceTargetApiName;
    private String sourceQuoteFieldType;
    private String sourceQuoteRealField;
    private String sourceQuoteFieldTargetObjectApiName;
    private String sourceQuoteFieldTargetObjectField;
    private String destApiName;
    private String destType;
    private String destTargetApiName;
    private String destQuoteFieldType;
    //    private Map<Object, Object> optionMapping;
    private List<OptionMappingData> optionMappings;
    private Integer mappingType;
    private String function;
    private String value;
    private String defaultValue;//默认值
    private Integer valueType;//值类型，固定值1，默认值2
    /** 是否不更新该字段，true：不更新，其他：更新 */
    private Boolean notUpdateField;
    /** 是否不校验该字段映射，true：不校验，其他：校验 */
    private Boolean notCheckMappingField;
    /**
     * 直接使用原值 ,, 同时对默认值、固定值 会不执行trim
     */
    private Boolean useSourceValueDirectly;
    /**
     * 什么值判断为null
     * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.NullCandidateEnum
     */
    private List<String> nullCandidateList;

    public Object getDestOptionBySourceOption(Object sourceOption) {
        if (this.optionMappings == null) {
            return null;
        }
        for (OptionMappingData optionMapping : this.getOptionMappings()) {
            //如果源OptionMappingData选项是字符串,把sourceOption变成字符串比较
            if(optionMapping.getSourceOption() instanceof String
                    &&optionMapping.getSourceOption().equals(sourceOption.toString())){
                return optionMapping.getDestOption();
            }
            if (optionMapping.getSourceOption().equals(sourceOption)) {
                return optionMapping.getDestOption();
            }
        }
        return null;
    }
}
