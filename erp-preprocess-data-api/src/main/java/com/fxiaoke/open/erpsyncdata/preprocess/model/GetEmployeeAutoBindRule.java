package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/4 10:35:54
 */
public interface GetEmployeeAutoBindRule {

    @Data
    class Arg {

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @ApiModelProperty("是否开启")
        private Boolean isOpen;
        @ApiModelProperty("开启类型,1:按集成流，2：按绑定规则")
        private String type;
        @ApiModelProperty("crm人员字段中的erpId字段")
        private String erpUserIdField;
        @ApiModelProperty("crm人员字段中的erp主属性字段")
        private String erpUserNameFiled;
    }

}
