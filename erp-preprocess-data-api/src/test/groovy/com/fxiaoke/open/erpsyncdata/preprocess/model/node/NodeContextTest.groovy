package com.fxiaoke.open.erpsyncdata.preprocess.model.node

import com.alibaba.fastjson.JSON
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class NodeContextTest extends Specification {

    def "testFastJson"() {

        def event = new SyncDataContextEvent()
        event.setStreamId("111")
        event.setBasic("1","2")
        def eventString = JSON.toJSONString(event)
        def event2 = JSON.parseObject(eventString, SyncDataContextEvent.class)
        print("string:$eventString,first:$event,end:$event2",)
        expect:
        true
        event.getStreamId() == "111"
        event.getTenantId() == "1"
        event.getDcId() =="2"

        event2.getTenantId() == "1"
        event2.getDcId() == "2"
        event2.getStreamId() == "111"
    }
}
