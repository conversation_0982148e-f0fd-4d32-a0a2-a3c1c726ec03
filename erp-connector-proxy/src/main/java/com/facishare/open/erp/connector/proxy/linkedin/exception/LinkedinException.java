package com.facishare.open.erp.connector.proxy.linkedin.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/5/8 19:53:27
 */
@Getter
public class LinkedinException extends RuntimeException {
    private Integer code;
    private String errorMessage;

    /**
     * RunTimeException的message字段不支持set, 所以这里用一个新的字段
     */
    private String message;

    public LinkedinException(final String message) {
        this.message = message;
    }

    public LinkedinException(final String url, int code, final String errorMessage) {
        if (code == 401) {
            this.message = "LinkedIn token is invalid, please log in again for authorization." + errorMessage;
        } else {
            this.message = "Calling LinkedIn failed, status:" + code + ", url:" + url + ", response: " + errorMessage;
        }
        this.code = code;
        this.errorMessage = errorMessage;
    }

}
