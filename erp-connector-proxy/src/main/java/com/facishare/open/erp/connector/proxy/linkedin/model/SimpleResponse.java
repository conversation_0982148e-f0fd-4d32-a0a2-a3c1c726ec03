package com.facishare.open.erp.connector.proxy.linkedin.model;

import lombok.Data;
import okhttp3.Response;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/5 19:29:26
 */
@Data
public class SimpleResponse {

    private int code;
    private String body;


    public SimpleResponse(Response response) throws IOException {
        code = response.code();
        body = Objects.isNull(response.body()) ? null : response.body().string();
    }

    public boolean isSuccessful() {
        return code >= 200 && code < 300;
    }
}
