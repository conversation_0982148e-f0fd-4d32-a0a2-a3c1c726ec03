package com.fxiaoke.open.oasyncdata.result.base;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 错误码枚举
 * cs标识：s服务端
 * 状态分配：1：成功，2：警告，3：错误
 * 业务分配：06  ：fs-open开平
 * 模块：暂无分配，暂时使用24
 * 状态码：四位
 */
@Getter
@ToString
@AllArgsConstructor
public enum ResultCodeEnum {
    /**
     * 成功
     **/
    SUCCESS("s106240000", "成功",
            I18NStringEnum.s6.getI18nKey()),

    /**
     * 参数不合法
     */
    PARAM_ILLEGAL("s206240000", "参数不合法",
            I18NStringEnum.s482.getI18nKey()),
    /**
     * 检验参数错误
     */
    VALID_ERROR("s206240001", "检验参数错误",
            I18NStringEnum.s483.getI18nKey()),
    /**
     * 获取身份信息失败
     */
    NO_USER("s206240002", "获取身份信息失败,或者不是管理员身份",
            I18NStringEnum.s484.getI18nKey()),
    /**
     * 数据列表不可为空
     */
    LIST_EMPTY("s206240003", "数据列表不可为空",
            I18NStringEnum.s485.getI18nKey()),
    /**
     * json转换异常
     */
    JSON_CONVERT_EXCEPTION("s206240004", "json转换异常",
            I18NStringEnum.s486.getI18nKey()),
    /**
     * 获取锁失败，任务执行中
     */
    GET_LOCK_FAILED("s206240005", "获取锁失败，任务执行中",
            I18NStringEnum.s487.getI18nKey()),
    /**
     * 渠道暂不支持该操作
     */
    UNSUPPORTED_CHANNEL("s206240006","渠道暂不支持该操作",
            I18NStringEnum.s488.getI18nKey()),
    /**
     * 未购买集成平台产品
     */
    UNPURCHASED_PRODUCT("s206240007","未购买集成平台产品",
            I18NStringEnum.s489.getI18nKey()),
    /**
     * 无法获取数据中心id参数
     */
    NOT_RECEIVE_DCID("s206240008","无法获取数据中心id参数",
            I18NStringEnum.s490.getI18nKey()),


    /**
     * 系统错误
     */
    SYSTEM_ERROR("s306240000", "系统错误",
            I18NStringEnum.s392.getI18nKey()),
    /**
     * header脚本执行失败
     */
    HEADER_SCRIPT_RUN_ERROR("s306240001","header脚本执行失败",
            I18NStringEnum.s491.getI18nKey()),
    /**
     * 用户信息异常
     */
    USER_NOT_LOGIN("s306240002", "验证用户登录信息失败",
            I18NStringEnum.s492.getI18nKey()),
    /**
     * 调用外部接口失败
     */
    CALL_OUT_HTTP_FAILED("s306240003","调用OA接口失败，请与OA系统负责人联系，错误信息：%s",
            I18NStringEnum.s1251.getI18nKey()),
    /**
     * 平台测异常
     */
    PLAT_FORM_ERROR("s306240004", "平台侧异常",
            I18NStringEnum.s494.getI18nKey()),
    /**
     * 文件上传失败
     */
    FILE_UPLOAD_FAILED("s30624005","文件上传失败",
            I18NStringEnum.s495.getI18nKey()),
    /**
     * 策略校验不合法
     */
    PLOY_NOT_VALID("s306240006", "策略校验不合法,请查看运行状态",
            I18NStringEnum.s464.getI18nKey()),
    /**
     * 不支持的操作类型
     */
    UNSUPPORTED_MANAGER("s306240007","不支持的操作类型",
            I18NStringEnum.s496.getI18nKey()),
    /**
     * 自定义函数包含新增/修改/作废等操作
     */
    CUSTOM_FUNC_CONTAIN_ILLEGAL_OPERATE_TYPE("s306240008", "自定义函数包含新增/修改/作废等操作",
            I18NStringEnum.s462.getI18nKey()),
    /**
     * 不支持的对象
     */
    UNSUPPORTED_OBJECT("s306240010","不支持的对象",
            I18NStringEnum.s459.getI18nKey()),
    /**
     * 从对象不能单独创建策略
     */
    CAN_NOT_ADD_PLOY_BY_DETAIL("s306240012", "从对象不能单独创建策略",
            I18NStringEnum.s439.getI18nKey()),
    /**
     * 没有该erp对象
     */
    NO_ERP_OBJECT("s306240014","没有该erp对象",
            I18NStringEnum.s497.getI18nKey()),
    /**
     * 多个crm对象中没有主对象
     */
    NO_CRM_MASTER_OBJECT("s306240016","多个crm对象中没有主对象",
            I18NStringEnum.s498.getI18nKey()),
    /**
     * 无权限操作此数据
     */
    NOT_HAVE_DATA_AUTH("s306240018", "无权限操作此数据",
            I18NStringEnum.s437.getI18nKey()),
    /**
     * 策略重复
     */
    PLOY_DETAIL_DUPLICATE("s306240020", "已存在相同策略明细",
            I18NStringEnum.s440.getI18nKey()),
    /**
     * 已启用策略无法删除
     */
    ENABLED_PLOY_CAN_NOT_DELETE("s306240022",  "已启用策略无法删除，请先停用策略",
            I18NStringEnum.s454.getI18nKey()),
    /**
     * 系统繁忙
     */
    SERVER_BUSY("s306240024",  "系统繁忙",
            I18NStringEnum.s431.getI18nKey()),
    /**
     * 参数错误
     */
    PARAM_ERROR("s306240026", "参数错误",
            I18NStringEnum.s433.getI18nKey()),
    /**
     * 错误提示
     */
    ERROR_MSG("s306240027", "提示",
            I18NStringEnum.s499.getI18nKey()),
    /**
     * 对象没有设置字段映射
     */
    NOT_SET_FIELD("s306240028",  "对象没有设置字段映射",
            I18NStringEnum.s442.getI18nKey()),
    /**
     * 不支持的类型映射
     */
    UNSUPPORTED_TYPE_MAPPING("s306240030",  "不支持的字段类型映射",
            I18NStringEnum.s445.getI18nKey()),
    /**
     * 请先停用关联该对象的策略
     */
    OBJECT_REFERENCE_NOT_CLOSE_PLOY_DETAIL("s306240032", "请先停用关联该对象的策略",
            I18NStringEnum.s448.getI18nKey()),
    /**
     * 无此自定义函数的apiName类型
     */
    CUSTOM_FUNC_APINAME_TYPE_NOT_EXIST("s306240034","不存在的函数apiName",
            I18NStringEnum.s461.getI18nKey()),
    /**
     * 策略重复
     */
    PLOY_DUPLICATE("s306240036",  "已存在相同策略",
            I18NStringEnum.s438.getI18nKey()),
    /**
     * 无法删除，请先删除该对象的所有策略
     */
    PLOY_CAN_NOT_DELETE("s306240038",  "已存在策略无法删除，请先删除该对象的所有策略",
            I18NStringEnum.s500.getI18nKey()),
    /**
     * 数据规则执行失败
     */
    DATA_RULE_EXECUTE_FAILED("s306240040",  "数据规则执行失败",
            I18NStringEnum.s443.getI18nKey()),
    /**
     * 数据不存在或已被删除
     */
    DATA_NOT_FOUND("s306240042", "数据不存在或已被删除",
            I18NStringEnum.s435.getI18nKey()),
    /**
     * 定时任务检测正在同步超时
     */
    PLOY_WAS_DISABLED("s306240044", "策略已经被停用",
            I18NStringEnum.s458.getI18nKey()),
    /**
     * 定时任务检测正在同步超时
     */
    SYNC_DATA_TIMEOUT("s306240046",  "同步超时",
            I18NStringEnum.s457.getI18nKey()),
    /**
     * 获取纷享职员信息失败
     */
    GET_FS_EMPLOYEE_FAILED("s306240048", "获取纷享职员信息失败",
            I18NStringEnum.s501.getI18nKey()),
    /**
     * 已存在该企业的绑定信息
     */
    THE_ENTERPRISE_CON_EXIST("s306240050", "已存在该企业的绑定信息",
            I18NStringEnum.s502.getI18nKey()),
    /**
     * 该erp对象的APIName已存在
     */
    THE_ERP_OBJECT_EXIST("s306240052", "该erp对象的APIName已存在",
            I18NStringEnum.s503.getI18nKey()),
    /**
     * 该erp对象的已存在相同的字段APIName
     */
    THE_ERP_OBJECT_FIELD_EXIST("s306240052", "该erp对象的已存在相同的字段APIName",
            I18NStringEnum.s504.getI18nKey()),

    /**
     * 该crm员工已存在映射
     */
    THE_CRM_EMPLOYEE_MAPPING_EXIST("s306240054", "该crm员工已存在映射",
            I18NStringEnum.s505.getI18nKey()),
    /**
     * 该erp员工已存在映射
     */
    THE_ERP_EMPLOYEE_MAPPING_EXIST("s306240056", "该erp员工已存在映射",
            I18NStringEnum.s506.getI18nKey()),
    /**
     * 该crm选项已存在映射
     */
    THE_CRM_FIELD_DATA_MAPPING_EXIST("s306240054", "该crm选项已存在映射",
            I18NStringEnum.s507.getI18nKey()),
    /**
     * 该erp选项已存在映射
     */
    THE_ERP_FIELD_DATA_MAPPING_EXIST("s306240056", "该erp选项已存在映射",
            I18NStringEnum.s508.getI18nKey()),
    /**
     * 该企业的绑定信息不存在
     */
    THE_ENTERPRISE_CON_NOT_EXIST("s306240058", "该企业的绑定信息不存在",
            I18NStringEnum.s509.getI18nKey()),
    /**
     * 数据库异常
     */
    THE_DATABASE_ERROR("s306240060", "数据库异常",
            I18NStringEnum.s510.getI18nKey()),
    /**
     * 该对象不存在erp->crm的策略
     */
    THE_OBJECT_PLOY_NOT_EXIST("s306240062", "该对象不存在erp->crm的有效策略",
            I18NStringEnum.s511.getI18nKey()),
    /**
     * 该对象不存在erp->crm的策略明细
     */
    THE_OBJECT_PLOYDETAIL_NOT_EXIST("s306240064", "该对象不存在erp->crm的启动的策略明细",
            I18NStringEnum.s512.getI18nKey()),
    /**
     * 该对象不存在erp->crm的策略明细快照
     */
    THE_OBJECT_PLOYDETAILSNAPSHOT_NOT_EXIST("s306240066", "该对象不存在erp->crm的策略明细快照，可以尝试重新启动策略明细",
            I18NStringEnum.s513.getI18nKey()),
    /**
     * 未实现方法
     */
    UNIMPLEMENTED_METHOD("s306240065", "未实现方法",
            I18NStringEnum.s514.getI18nKey()),

    /**
     * 该对象存在字段关联
     */
    THE_OBJECT_EXIST_QUOTE("s306240068", "该对象存在字段关联,请先删除关联该对象的字段",
            I18NStringEnum.s515.getI18nKey()),

    /**
     * 不支持明细单独更改
     */
    DETAIL_MODIFY_NO_SUPPORT_NOW("s306240069", "暂不支持单独新增或更新明细",
            I18NStringEnum.s516.getI18nKey()),

    /**
     * 字段转换异常
     */
    FIELD_CONVERT_EXCEPTION("s306240070", "字段转换异常",
            I18NStringEnum.s517.getI18nKey()),
    /**
     * 第三方系统身份校验失败，请检查连接参数
     */
    THIRD_SYSTEM_AUTH_FAILED("s306240071", "第三方系统身份校验失败，请检查连接参数",
            I18NStringEnum.s518.getI18nKey()),
    /**
     * 接口返回结果错误manualSyncErpDataByCrmDataId
     */
    RESULT_ERROR("s306240072", "接口返回结果错误：%s",
            I18NStringEnum.s519.getI18nKey()),

    /**
     * 接口返回结果为空
     */
    RESULT_NULL("s306240073", "接口返回结果为空",
            I18NStringEnum.s520.getI18nKey()),

    /**
     * 该对象存在策略明细
     */
    THE_OBJECT_EXIST_PLOY_DETAIL("s306240074", "该对象存在策略明细，不允许删除",
            I18NStringEnum.s521.getI18nKey()),
    /**
     * ERP组织结构对象查询出错
     */
    LIST_ERP_ORGOBJ_ERROR("s306240075", "ERP组织结构对象获取失败",
            I18NStringEnum.s522.getI18nKey()),

    /**
     * 该对象没有主键字段
     */
    THE_OBJECT_NOT_EXIST_ID_FIELD("s306240076", "该对象没有主键字段，需要添加一个",
            I18NStringEnum.s523.getI18nKey()),
    /**
     * 分配失败
     */
    DISTRIBUTE_ERROR("s306240077", "基础对象分配失败",
            I18NStringEnum.s524.getI18nKey()),

    /**
     * 不支持的库存ID
     */
    UNSUPPORTED_STOCK_ID("s306240079","不支持的库存ID",
            I18NStringEnum.s525.getI18nKey()),
    /**
     * 获取同步物料的组织为空
     */
    STOCK_NOT_FOUND("s306240081","未找到库存",
            I18NStringEnum.s526.getI18nKey()),

    /**
     * 没有需要同步物料的组织
     */
    SYNC_PRODUCT_ORG_NOT_FOUND("s306240080","没有需要同步物料的组织",
            I18NStringEnum.s527.getI18nKey()),

    /**
     * groovy脚本不能import纷享的包
     */
    CHK_SCRIPT_IMPORT_ERROR("s306240082","groovy脚本不能import纷享的包",
            I18NStringEnum.s528.getI18nKey()),

    /**
     * groovy脚本不能import纷享的包
     */
    OA_CALL_SERVICE_ERROR("s306240083","调用OA接口失败",
            I18NStringEnum.s529.getI18nKey()),

    /**
     * 没有需要同步仓库的组织
     */
    SYNC_WAREHOUSE_ORG_NOT_FOUND("s306240084","没有需要同步仓库的组织",
            I18NStringEnum.s530.getI18nKey()),

    /**
     * 该对象还没有同步记录
     */
    THE_OBJECT_HAVE_NOT_SYNC_RECORD("s306240084","该对象还没有同步记录",
            I18NStringEnum.s531.getI18nKey()),

    /**
     * 没有找到推送过来的数据
     */
    PUSH_DATA_HAVE_NOT_FOUND("s306240085","没有找到推送过来的数据",
            I18NStringEnum.s532.getI18nKey()),

    /**
     * 没有从ERP取到最近的数据
     */
    GET_NOTHING_FROM_ERP("s306240086","没有从ERP取到最近的数据",
            I18NStringEnum.s533.getI18nKey()),

    /**
     * 导入文件为空
     */
    IMPORT_FILE_EMPTY("s306240087","导入文件为空",
            I18NStringEnum.s534.getI18nKey()),

    /**
     * 环境错误:灰度环境请使用灰度url,非灰度环境不要使用灰度url
     */
    ERROR_ENVIRONMENT("s306240088", "请强制刷新页面,环境错误:灰度环境请使用灰度url,非灰度环境不要使用灰度url",
            I18NStringEnum.s535.getI18nKey()),

    /**
     * OA账户未绑定
     */
    OA_USER_NOT_BIND("s306240089", "OA用户未绑定",
            I18NStringEnum.s536.getI18nKey()),

    /**
     * 解码错误
     */
    DECRYPT_ERROR("s306240090", "解码错误",
            I18NStringEnum.s537.getI18nKey()),

    /**
     * view接口返回内容过大
     */
    CONTENT_LENGTH_LIMIT_ERROR("s306240092", "接口返回内容过大;%s",
            I18NStringEnum.s538.getI18nKey()),

    /**
     * CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，已删除自动生成的ERP销售订单
     */
    SALES_ORDER_ENTRY_SIZE_NOT_MATCH_AND_DELETE_SUCCESS("s306240093", "CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，已删除自动生成的ERP销售订单",
            I18NStringEnum.s539.getI18nKey()),

    /**
     * CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，自动生成的ERP销售订单删除失败
     */
    SALES_ORDER_ENTRY_SIZE_NOT_MATCH_AND_DELETE_FAILED("s306240094", "CRM的销售订单明细数量和ERP的销售订单明细数量不匹配，自动生成的ERP销售订单删除失败",
            I18NStringEnum.s540.getI18nKey()),

    /**
     * 查询k3cloud库存数据失败
     */
    QUERY_K3CLOUD_STOCK_DATA_FAILED("s306240095", "查询k3cloud库存数据失败",
            I18NStringEnum.s541.getI18nKey()),

    /**
     * 对接信息不存在，该企业没有使用对接平台进行k3cloud对接的信息
     */
    CONNECT_INFO_NOT_EXISTS("s306240096", "连接信息不存在",
            I18NStringEnum.s542.getI18nKey()),

    /**
     * 获取k3cloud库存数据失败
     */
    GET_K3CLOUD_STOCK_DATA_FAILED("s306240097", "获取k3cloud库存数据失败",
            I18NStringEnum.s543.getI18nKey()),

    /**
     * 数据重复
     */
    DATA_REPEAT_ERROR("s306240098", "数据重复;%s",
            I18NStringEnum.s544.getI18nKey()),

    /**
     * OA账套未连接
     */
    OA_UN_CONNECT("s306240090", "OA账套信息未连接",
            I18NStringEnum.s545.getI18nKey()),

    /**
     * 未找到关联该同步记录的接口日志
     */
    SYNC_DATA_NOT_FOUND_INTERFACE_LOG("s306240099","未找到关联该同步记录的接口日志",
            I18NStringEnum.s546.getI18nKey()),

    /**
     * 推送数据id字段为空
     */
    PUSH_DATA_ID_IS_NULL("s306240102", "推送数据id字段为空;数据：%s",
            I18NStringEnum.s547.getI18nKey()),
    /**
     * 该企业已禁止保存接口调用记录，请打开。
     */
    NO_SAVE_INTERFACE_MONITOR("s306240103","该企业已禁止保存接口调用记录，请打开。",
            I18NStringEnum.s548.getI18nKey()),

    /**
     * 任务执行超时，controller接口超时转异步返回使用
     */
    ACTION_EXECUTE_TIME_OUT("s306240104","任务执行时间超过页面等待时间，任务正在后台执行中，结果请稍后关注企信消息。",
            I18NStringEnum.s549.getI18nKey()),

    /**
     * 发送短信校验码错误
     */
    SEND_CODE_ERROR("s306240106","发送短信校验码错误，错误信息：%s",
            I18NStringEnum.s550.getI18nKey()),

    /**
     * 短信校验码已过期
     */
    CODE_IS_INVALID("s306240108","短信校验码已过期",
            I18NStringEnum.s551.getI18nKey()),

    /**
     * 当前用户电话为空
     */
    THE_USER_PHONE_IS_NULL("s306240106","当前用户电话为空",
            I18NStringEnum.s552.getI18nKey()),
    /**
     * 不允许跳过轮询时间的错误
     * 获取K3数据时全部失败，该错误不允许跳过时间
     */
    NOT_ALLOW_SKIP_TIME_ERROR("s306241324","不允许跳过轮询时间的错误",
            I18NStringEnum.s553.getI18nKey()),

    /**
     * 短信校验码校验错误
     */
    CODE_CHECKED_INVALID("s306240110","短信校验码校验错误",
            I18NStringEnum.s554.getI18nKey()),

    /**
     * 待分发数据过多
     */
    MANY_WAITING_DISPATCHER("s306240112","待分发数据过多,等待下一分钟执行",
            I18NStringEnum.s555.getI18nKey()),

    /**
     * 找不到模板数据
     */
    NO_OBJECT_FIELD_TEMPLATE_DATA("s306240113","找不到模板数据",
            I18NStringEnum.s556.getI18nKey()),

    /**
     * SocketTimeoutException
     */
    SOCKETTIMEOUT("s306240114","Socket Timeout",
            I18NStringEnum.s557.getI18nKey()),

    /**
     * 用户名称不存在
     */
    THE_USERNAME_UNKNOWN("s306240115","用户名称不存在，请输入登录金蝶云星空时使用的用户名",
            I18NStringEnum.s558.getI18nKey()),

    /**
     * 初始化帐套失败
     */
    INIT_DATA_CENTER_FAILED("s306240116","初始化帐套失败",
            I18NStringEnum.s559.getI18nKey()),

    /**
     * 帐套已经初始化
     */
    DATA_CENTER_INITED("s306240117","帐套已经初始化",
            I18NStringEnum.s560.getI18nKey()),

    /**
     * 配置集成平台访问权限失败
     */
    CONFIG_ALLOW_VISIT_FAILED("s306240118","配置集成平台访问权限失败",
            I18NStringEnum.s561.getI18nKey()),

    /**
     * 更新连接信息失败
     */
    UPDATE_CONNECT_INFO_FAILED("s306240120","更新连接信息失败",
            I18NStringEnum.s562.getI18nKey()),

    /**
     * 对象预置失败
     */
    PRESET_OBJECTS_FAILED("s306240122","对象预置失败",
            I18NStringEnum.s563.getI18nKey()),

    /**
     * 策略和策略明细预置失败或策略启动失败
     */
    PRESET_PLOY_DETAIL_FAILED("s306240124","策略和策略明细预置失败或策略启动失败",
            I18NStringEnum.s564.getI18nKey()),

    /**
     * 策略明细不存在
     */
    PLOY_DETAIL_NOT_EXIST("s306240125","策略明细不存在",
            I18NStringEnum.s565.getI18nKey()),

    /**
     * 钉钉对象ID换CRM对象ID失败
     */
    GET_CRM_OBJECT_ID_FAILED("s306240126","钉钉对象ID换CRM对象ID失败",
            I18NStringEnum.s566.getI18nKey()),

    /**
     * 存在未结束的初始化映射关系操作
     */
    NOT_END_INIT_MAPPING("s306240128","存在未结束的初始化映射关系操作,请耐心等待结果，%s",
            I18NStringEnum.s567.getI18nKey()),
    /**
     * 推送数据超速
     */
    PUSH_ERP_DATA_LIMIT("s306240129","推送数据超出限制速度",
            I18NStringEnum.s568.getI18nKey()),

    /**
     * 当前账套不是k3c账套
     */
    NOT_K3C_DATA_CENTER("s306240200","当前账套不是k3c账套",
            I18NStringEnum.s569.getI18nKey()),

    /**
     * 通过库存明细的FID字段从K3C获取不到库存数据
     */
    CAN_NOT_GET_STOCK_DATA_BY_FID("s306240202","通过库存明细的FID字段从K3C获取不到库存数据",
            I18NStringEnum.s570.getI18nKey()),

    /**
     * token验证失败
     */
    VERIFY_TOKEN_FAILED("s306240204","token验证失败",
            I18NStringEnum.s571.getI18nKey()),
    /**
     * CRM对象被禁用或者不存在
     */
    CRM_OBJECT_INVALID("s306240205","CRM对象被禁用或者不存在",
            I18NStringEnum.s572.getI18nKey()),
    /**
     * crm对象字段被禁用或不存在
     */
    CRM_OBJECT_FIELD_INVALID("s306240206","crm对象字段被禁用或不存在",
            I18NStringEnum.s573.getI18nKey()),
    /**
     * 存在同名的数据中心
     */
    DUPLICATE_CENTER_NAME("s306240207","连接器名字存在重复",
            I18NStringEnum.s574.getI18nKey()),
    /**
     * 校验函数失败
     */
    VERIFY_STATUS_FAIL("s306240208","校验函数失败",
            I18NStringEnum.s575.getI18nKey()),
    /**
     * 必填字段被不设置映射
     */
    OBJECT_REQUIRED_FIELD_NOT_MAPPING("s306240209","必填字段被不设置映射",
            I18NStringEnum.s576.getI18nKey()),
    /**
     * 集成流不存在
     */
    INTEGRATION_STREAM_NOT_EXIST("s306240210","集成流不存在",
            I18NStringEnum.s227.getI18nKey()),

    /**
     * 获取单条数据熔断
     */
    GET_BY_ID_BREAK("s306240206","获取单条数据熔断",
            I18NStringEnum.s577.getI18nKey()),
    /**
     * 获取单条数据熔断
     */
    DATA_RANGE_VERIFY_FAILED("s306240208","数据范围校验失败",
            I18NStringEnum.s578.getI18nKey()),
    /**
     * 查询不到该字段详情
     */
    QUERY_FIELD_FAIL("s306240209","查询不到该字段详情",
            I18NStringEnum.s579.getI18nKey()),
    /**
     * 自定义函数错误
     */
    CUSTOM_FUNC_ERROR("s306240210","自定义函数错误",
            I18NStringEnum.s580.getI18nKey()),
    /**
     * 没有需要同步客户的组织
     */
    SYNC_CUSTOMER_ORG_NOT_FOUND("s306240212","没有需要同步客户的组织",
            I18NStringEnum.s581.getI18nKey()),
    /**
     * 异步重试中，请稍后查看结果
     */
    ASYNC_RETRY("s306240213","异步重试中，请稍后查看结果",
            I18NStringEnum.s1248.getI18nKey()),
    /**
     * OA配置未开启
     */
    OA_SETTING_NOT_OPEN("s306240214", "OA配置未开启",
            I18NStringEnum.s1249.getI18nKey()),

    OA_SYNC_LOG_NOT_EXIST("s306240215", "oa快照未找到",
            I18NStringEnum.s1250.getI18nKey()),

    OA_PARAMS("s306240216", "oa快照未找到",
            I18NStringEnum.s1250.getI18nKey()),

    OA_NOT_SYNC("s306240217", "无需同步",
            I18NStringEnum.s1216.getI18nKey()),

    OA_TODO_IS_DELETE("s306240218", "crm数据已被删除",
            I18NStringEnum.s4201.getI18nKey()),

    OA_TO_IS_PAAS("s306240220", "待办已处理，不支持重试新增",
            I18NStringEnum.s4202.getI18nKey()),
    ;

    /**
     * 错误码
     */
    private final String errCode;
    /**
     * 错误信息
     */
    private final String errMsg;
    /**
     * i18n 语言 key
     */
    private final String i18nKey;
}
