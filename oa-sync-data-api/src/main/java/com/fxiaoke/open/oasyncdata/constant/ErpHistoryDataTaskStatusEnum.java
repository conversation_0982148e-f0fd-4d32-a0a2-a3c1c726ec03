package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 14:54 2021/2/25
 * @Desc:
 */
@AllArgsConstructor
@Getter
public enum ErpHistoryDataTaskStatusEnum {
    STATUS_OTHER(0, "其他状态", I18NStringEnum.s881.getI18nKey()),
    /**
     * 创建的历史任务为等待执行状态，可执行后转为开启状态，之前没删除是因为兼容不同环境数据
     */
    @Deprecated
    STATUS_CREATE(1, "创建", I18NStringEnum.s363.getI18nKey()),
    STATUS_START(2, "开启", I18NStringEnum.s863.getI18nKey()),
    STATUS_EXECUTING(3, "执行中", I18NStringEnum.s882.getI18nKey()),
    STATUS_ERROR(4, "异常", I18NStringEnum.s883.getI18nKey()),
    STATUS_STOP(5, "中断", I18NStringEnum.s851.getI18nKey()),
    STATUS_END_SUCCESS(6, "结束（成功）", I18NStringEnum.s884.getI18nKey()),
    STATUS_WAITING(7,"等待执行",I18NStringEnum.s885.getI18nKey()),

    ;

    private Integer status;
    private String desc;
    private String i18nKey;

    /**
     * 任务状态(1.创建，2.开启，3执行中，4.异常，5.中断，6.结束（成功）)
     * @param status
     * @return
     */
    public static ErpHistoryDataTaskStatusEnum getErpHistoryDataTaskStatus(Integer status) {
        for (ErpHistoryDataTaskStatusEnum theStatus : ErpHistoryDataTaskStatusEnum.values()) {
            if (theStatus.getStatus().equals(status)) {
                return theStatus;
            }
        }
        return STATUS_OTHER;
    }
}