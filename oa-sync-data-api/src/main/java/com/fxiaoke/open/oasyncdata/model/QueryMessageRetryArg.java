package com.fxiaoke.open.oasyncdata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:
 */
@Data
@ApiModel
public class QueryMessageRetryArg implements Serializable {
    @ApiModelProperty("企业id")
    private String tenantId;
    @ApiModelProperty("数据中心id")
    private String dataCenterId;
    @ApiModelProperty("对象名")
    private String objApiName;

    @ApiModelProperty("同步状态")
    private List<String> status;

    @ApiModelProperty("业务类型 待办crmTodo 提醒crmNotify")
    private String businessTypes;

    @ApiModelProperty("事件类型")
    private  String eventType;

    @ApiModelProperty("开始时间")
    private Long eventTime;

    @ApiModelProperty("每页数量")
    private Integer pageSize=100;

    @ApiModelProperty("页码")
    private Integer page=1;
}
