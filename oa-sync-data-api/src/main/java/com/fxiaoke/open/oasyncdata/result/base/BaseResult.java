package com.fxiaoke.open.oasyncdata.result.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Getter
@Setter
@ToString
public abstract class BaseResult {
    @ApiModelProperty("状态码")
    protected String errCode;
    @ApiModelProperty("状态描述")
    protected String errMsg;
    @ApiModelProperty("国际化词条key")
    protected String i18nKey;
    @ApiModelProperty("国际化需要的附加信息")
    protected List<String> i18nExtra;
    @ApiModelProperty("进程信息")
    protected String traceMsg;
    public boolean isSuccess() {
        return ResultCodeEnum.SUCCESS.getErrCode().equals(this.errCode);
    }
}