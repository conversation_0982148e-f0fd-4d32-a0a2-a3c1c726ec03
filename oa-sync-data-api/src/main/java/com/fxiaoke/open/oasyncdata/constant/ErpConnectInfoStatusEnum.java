package com.fxiaoke.open.oasyncdata.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/20 10:27:09
 */
public enum ErpConnectInfoStatusEnum {
    Normal(1, "正常"),
    Delete(2, "删除"),
    ;
    @Getter
    private int status;
    private String description;

    ErpConnectInfoStatusEnum(int status, String description) {
        this.status = status;
        this.description = description;
    }

    private static Map<Integer, ErpConnectInfoStatusEnum> statusMap = Arrays.stream(ErpConnectInfoStatusEnum.values()).collect(Collectors.toMap(ErpConnectInfoStatusEnum::getStatus, Function.identity()));

    public static ErpConnectInfoStatusEnum valueOf(int status) {
        return statusMap.get(status);
    }
}
