package com.fxiaoke.open.oasyncdata.service;


import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.model.OASettingVO;
import com.fxiaoke.open.oasyncdata.model.QueryOAEmployeeMappingListArg;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.QueryResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;

import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2021/3/18
 */
public interface OASettingService {
    Result<OASettingVO> getSettingInfo(String tenantId, OATenantEnum oaTenantEnum,String dataCenterId);

    Result<List<OASettingVO>> listSettingInfo(String tenantId, OATenantEnum oaTenantEnum,String dataCenterId);

    <T> Result<T> getGenericInfo(String tenantId, OATenantEnum oaTenantEnum,String dataCenterId);


    Result<String> upsertSettingInfo(String tenantId, OASettingVO info,String dataCenterId);


    Result<String> deleteSettingInfo(String tenantId, OATenantEnum oaTenantEnum,String dataCenterId);

    Result<String> deleteSettingInfoById(String tenantId, String id);
}
