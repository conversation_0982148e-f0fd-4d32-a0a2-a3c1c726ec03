package com.fxiaoke.open.oasyncdata.result;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/17 17:04
 * @Version 1.0
 */

public class QueryInventoryResult implements Serializable {
    @Getter
    @Setter
    @ToString
    public static class CombineInventoryResult implements Serializable {
        private static final long serialVersionUID = 42L;
        private Integer rowcount;
        private boolean success;
        private String message;
        private List<InventoryResult> data;

    }
}
