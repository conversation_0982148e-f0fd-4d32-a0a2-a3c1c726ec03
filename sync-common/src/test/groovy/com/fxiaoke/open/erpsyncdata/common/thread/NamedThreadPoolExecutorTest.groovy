package com.fxiaoke.open.erpsyncdata.common.thread

import com.github.trace.TraceContext
import spock.lang.Specification

import java.util.concurrent.RejectedExecutionHandler
import java.util.concurrent.ThreadPoolExecutor

/**
 *
 * <AUTHOR> (^_−)☆
 */
class NamedThreadPoolExecutorTest extends Specification {


    def "测试线程池"() {
        NamedThreadPoolExecutor executor = new NamedThreadPoolExecutor("test",3,3,100, new RejectedExecutionHandler(){
            @Override
            void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                println("thread pool full")
            }
        })

        TraceContext context = TraceContext.get();
        context.setTraceId("test-111")
        for (i in 0..<500) {
            executor.submit {
                println(Thread.currentThread().getName()+" "+TraceContext.get().getTraceId())
            }
        }
        expect:
        true
    }
}
