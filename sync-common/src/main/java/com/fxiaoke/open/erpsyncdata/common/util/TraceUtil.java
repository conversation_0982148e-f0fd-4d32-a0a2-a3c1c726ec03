package com.fxiaoke.open.erpsyncdata.common.util;

import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;

/**
 * trace 工具类
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/9
 */
@Slf4j
public class TraceUtil {

    public static String get(){
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        return traceId;
    }

    /**
     * 尝试从context拿TenantId。
     */
    public static String tryGetTenantId() {
        String ei = TraceContext.get().getEi();
        if (ei == null) {
            return "0";
        }
        return ei;
    }
}
