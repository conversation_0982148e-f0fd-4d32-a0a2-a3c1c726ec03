package com.fxiaoke.open.erpsyncdata.common.constant;

public enum ValueTypeEnum {
    FIXEDVALUE(1, "fixed_value"),
    DEFAULTVALUE(2, "default_value"),
    ;
    private Integer type;
    private String name;


    ValueTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return  name;
    }

    public static String getNameByType(Integer type) {
        for (ValueTypeEnum tenantType : ValueTypeEnum.values()) {
            if (tenantType.getType() == type) {
                return tenantType.getName();
            }
        }
        return null;
    }
}
