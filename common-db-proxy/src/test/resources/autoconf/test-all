[http-support]
#代理,本地测试无代理
httpProxy=
httpProxy2=localhost:31112
httpProxy2Hosts = test.cn

[redis]
pool.maxActive=10
redis.servers=**********:30001,**********:30001,**********:30001
pool.minIdle=2
pool.maxWaitMillis=3000
pool.maxIdle=5
#redis.password=F0BC3B214612D729F06B283051F262FE92797B3849C1B007903C60C37D163F66
pool.testOnReturn=false
pool.testOnBorrow=false
circuit-breaker=state=continue
redis.masterName=test2
redis.sentinel=true
pool.testWhileIdle=true
redis.dbIndex=1
startup=true
pool.minIdle=1