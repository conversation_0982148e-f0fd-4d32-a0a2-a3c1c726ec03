package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/5 10:25:13
 */
public interface TestExcelClass {

    @Data
    static class TestClass {
        private String name1;
        private String name2;

        TestClass(final String name1, final String name2) {
            this.name1 = name1;
            this.name2 = name2;
        }
    }

    @Data
    static class TestClass2 {
        @ExcelProperty("金蝶云星空1")
        private String name1;
        @ExcelProperty("金蝶云星空2")
        private String name2;

        TestClass2(final String name1, final String name2) {
            this.name1 = name1;
            this.name2 = name2;
        }
    }
}
