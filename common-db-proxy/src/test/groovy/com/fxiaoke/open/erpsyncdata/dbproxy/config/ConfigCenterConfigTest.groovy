package com.fxiaoke.open.erpsyncdata.dbproxy.config

import com.alibaba.fastjson.JSON
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3CreateConfig
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/8/14 17:19:16
 */
class ConfigCenterConfigTest extends Specification {

    def "测试json"() {
        when:
        def mock = Mock(TenantConfigurationManager) {
            findOne(*_) >> [configuration: JSON.toJSONString([test: K3CreateConfig.DefaultUpdateConfig])]
        }
        def config = new ConfigCenterConfig(
                tenantConfigurationManager: mock
        )

        def config1 = config.getK3CreateConfig("", "")
        then:
        config1.values().stream().findAny().ifPresent { println it.getClass()}
        println config1
    }
}
