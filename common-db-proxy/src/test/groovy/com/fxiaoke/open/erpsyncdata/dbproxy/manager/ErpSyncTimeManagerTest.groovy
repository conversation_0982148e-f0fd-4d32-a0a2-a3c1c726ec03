package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PollingIntervalDto
import com.fxiaoke.open.erpsyncdata.preprocess.constant.IntervalTimeUnitEnum
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto
import spock.lang.Specification
import spock.lang.Unroll

import java.util.stream.Collectors

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2022-11-14
 */
class ErpSyncTimeManagerTest extends Specification {
    private ErpSyncTimeManager erpSyncTimeManager

    void setup() {
        ErpSyncTimeDao erpSyncTimeDao = Stub()
        erpSyncTimeDao.setGlobalTenant(_) >> erpSyncTimeDao
        erpSyncTimeDao.listByTenantIdAndObjectApiName(*_) >> {
            String tenantId, String obj ->
                ErpSyncTimeEntity entity = new ErpSyncTimeEntity()
                //2022-11-18 10:30:37
                entity.setLastSyncTime(1668738637000)
                entity.setOperationType(2)
                return [entity]
        }
        erpSyncTimeManager = new ErpSyncTimeManager(
                erpSyncTimeDao: erpSyncTimeDao
        )
    }

    def "test trans day cron"(int interval, String cron) {
        given:
        PollingIntervalApiDto apiDto = new PollingIntervalApiDto()
        apiDto.setTimeUnit(IntervalTimeUnitEnum.days)
        apiDto.setIntervalQuantity(interval)
        expect:
        cron == erpSyncTimeManager.transAndUpdateDayCron("83952", "srcObj", new PollingIntervalDto(), apiDto, "")
        println(cron)
        where:
        interval | cron
        360      | "30 10 18 11 *"
        30       | "30 10 18 1/1 *"
        29       | "30 10 1/29 * *"
        2        | "30 10 1/2 * *"
        1        | "30 10 * * *"

    }

    @Unroll
    def "#name-getMinQueryMongoTime"() {
        when:
        def collect = data.entrySet().stream().map({
            def entity = new ErpSyncTimeEntity()
            entity.setOperationType(it.key)
            entity.setLastQueryMongoTime(it.value)
            return entity
        }).collect(Collectors.toList())
        def mock = Mock(ErpSyncTimeDao) {
            listByTenantIdAndObjectApiName(*_) >> collect
        }
        mock.setGlobalTenant(*_) >> mock
        ErpSyncTimeManager manager = new ErpSyncTimeManager(
                erpSyncTimeDao: mock
        )
        def time = manager.getMinQueryMongoTime("83952", "srcObj")

        then:
        time == result

        where:
//        备注 | 数据库的sync_time数据 || 结果
        name                       | data                                || result
        "没有数据"                 | [:]                                 || null
        "只有一个1"                | [1: 123]                            || 123
        "只有一个2"                | [2: 123]                            || 123
        "只有一个3"                | [203: 123]                          || 123

        "4,5,6->不处理"            | [4: 123, 5: 456, 6: 789]            || null
        "1,7->1为准"               | [1: 1234, 7: 456]                   || 1234
        "203,7->203为准"           | [203: 1234, 7: 456]                 || 1234

        "1,2->2为准"               | [1: 123, 2: 456]                    || 456
        "2,3->小为准"              | [3: 123, 2: 456]                    || 123
        "1,3->小为准"              | [3: 123, 1: 456]                    || 123
        "1,2,3->2,3小为准"         | [1: 123, 3: 456, 2: 789]            || 456

        "201,1->小为准"            | [1: 123, 201: 456]                  || 123
        "201,202->202为准"         | [201: 123, 202: 456]                || 456
        "201,1,2->2为准"           | [1: 123, 201: 456, 2: 789]          || 789
        "201,1,202->202为准"       | [1: 123, 201: 456, 202: 789]        || 789
        "201,1,2,3->2,3小为准"     | [1: 123, 201: 456, 2: 789, 3: 123]  || 123
        "1,202,203->202,203小为准" | [1: 123, 202: 456, 203: 789]        || 456
        "1,201,3,203->小为准"      | [1: 123, 201: 456, 3: 789, 203: 12] || 12
    }
}
