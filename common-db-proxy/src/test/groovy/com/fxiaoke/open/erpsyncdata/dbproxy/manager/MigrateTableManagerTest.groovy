package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.BaseDbTest
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Ignore

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023-06-16
 */
@Ignore
class MigrateTableManagerTest extends BaseDbTest {

    MigrateTableManager migrateTableManager
    String tenantId = "1234"

    void setup() {
        ErpTableDao erpTableDao = Mock(ErpTableDao)
        erpTableDao.setTenantId(*_) >> erpTableDao
        erpTableDao.superQuerySql(*_) >> []
        migrateTableManager = new MigrateTableManager(
//                erpTableDao: erpTableDao,
        )
        // TODO: Mock

    }

    def "initTenantTableTest"() {
        def table = migrateTableManager.initTenantTable(tenantId)
        println(table)
    }
}
