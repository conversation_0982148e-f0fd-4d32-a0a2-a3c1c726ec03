package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.facishare.qixin.api.model.message.content.TextInfo
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SimpleSyncDataArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SyncStatusMessageArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
import com.fxiaoke.paasauthrestapi.result.RoleUserResult
import com.fxiaoke.paasauthrestapi.service.PaasAuthService
import org.bson.Document
import spock.lang.Specification

import java.util.concurrent.TimeUnit

/**
 * <AUTHOR> 
 * @date 2024/10/23 09:55:45
 */
class DataSyncNotifyManagerTest extends Specification {
    NotificationService notificationService = Mock()
    SyncPloyDetailManager syncPloyDetailManager = Mock()
    SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager = Mock()
    PaasAuthService paasAuthService = Mock()
    ObjectDataServiceV3 objectDataServiceV3 = Mock()
    ErpFieldMappingManager erpFieldMappingManager = Mock()
    ErpTempDataDao erpTempDataDao = Mock()
    FieldDbManager fieldDbManager = Mock()
    ErpConnectInfoManager erpConnectInfoManager = Mock()
    ErpObjManager erpObjManager = Mock()
    ObjectDescribeService objectDescribeService = Mock()
    I18NStringManager i18NStringManager = Mock()

    DataSyncNotifyManager dataSyncNotifyManager = new DataSyncNotifyManager(
            notificationService: notificationService,
            syncPloyDetailManager: syncPloyDetailManager
            ,
            syncPloyDetailSnapshotManager: syncPloyDetailSnapshotManager
            ,
            paasAuthService: paasAuthService
            ,
            objectDataServiceV3: objectDataServiceV3
            ,
            erpFieldMappingManager: erpFieldMappingManager
            ,
            erpTempDataDao: erpTempDataDao
            ,
            fieldDbManager: fieldDbManager
            ,
            erpConnectInfoManager: erpConnectInfoManager
            ,
            erpObjManager: erpObjManager
            ,
            objectDescribeService: objectDescribeService
            ,
            i18NStringManager: i18NStringManager)

    void setup() {
        dataSyncNotifyManager.afterPropertiesSet()
    }

    def "test push Data To Notify"() {
        given:
        syncPloyDetailSnapshotManager.getEntryBySnapshotId(*_) >> new SyncPloyDetailSnapshotEntity(sourceObjectApiName: "soan", syncPloyDetailData: new SyncPloyDetailData(sourceTenantType: TenantTypeEnum.ERP.getType(), integrationStreamNodes: new IntegrationStreamNodesData(notifyComplementNode: new IntegrationStreamNodesData.NotifyComplementNode(notifyEmployees: [1000], notifyRoles: ['roles'], dataRelatedOwner: ['owners'], notifyStatus: [SyncStatusEnum.SUCCESS.getStatus(), SyncStatusEnum.FAILED.getStatus()], notifyConditionAviator: "1==1"))))

        paasAuthService.roleUser(*_) >> new com.fxiaoke.paasauthrestapi.common.result.Result<RoleUserResult>()
        objectDataServiceV3.getById(*_) >> new com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result>()
        erpFieldMappingManager.listByErpIds(*_) >> [new ErpFieldDataMappingEntity("id", "88521", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, ErpFieldTypeEnum.text, "fsDataId", "fsDataName", "erpDataId", "erpDataName", "fieldDataExtendValue", 1l, 1l)]
        erpTempDataDao.getErpObjDataById(*_) >> new Document("key", "value")
        erpTempDataDao.getErpObjDataByNum(*_) >> new Document("key", "value")
        fieldDbManager.hasUsedIdQuery(*_) >> true
        fieldDbManager.getRelation(*_) >> new ErpObjectRelationshipEntity("id", "88521", "dataCenterId", ErpChannelEnum.ERP_K3CLOUD, 0, "erpRealObjectApiname", "erpSplitObjectApiname", ErpObjSplitTypeEnum.NOT_SPLIT, 1l, 1l)

        when:
        dataSyncNotifyManager.pushDataToNotify("88521", [new SyncDataEntity(sourceObjectApiName: "soan", status: SyncDataStatusEnum.WRITE_SUCCESS.status, sourceData: new ObjectData(name: "name", owners: ['123', '456']))])

        then:
        !dataSyncNotifyManager.localDispatcherUtil.dataQueueMap.isEmpty()
    }

    def "test generate Tenant Id Info"() {
        given:
        syncPloyDetailManager.getEntityByTenantIdAndObjApiName(*_) >> new SyncPloyDetailEntity(sourceTenantType: TenantTypeEnum.ERP.getType(), sourceObjectApiName: "soan", sourceDataCenterId: 'sdc', destDataCenterId: 'ddc', destObjectApiName: 'doan')
        erpConnectInfoManager.getByIdAndTenantId(*_) >> new ErpConnectInfoEntity("id", "88521", ErpChannelEnum.ERP_K3CLOUD, "dataCenterName", "enterpriseName", "connectParams", 1l, 1l, 0, 0)
        erpObjManager.getErpObjName(*_) >> "getErpObjNameResponse"
        objectDescribeService.list(*_) >> new com.fxiaoke.crmrestapi.common.result.Result<ListObjectDescribeResult>()

        when:
        SyncStatusMessageArg result = dataSyncNotifyManager.generateTenantIdInfo('{"left":"88521","middle":"san","right":"dan"}')

        def interval = result.endTime - result.startTime
        result.startTime = 0
        result.endTime = 0

        then:
        interval == TimeUnit.MINUTES.toMillis(5)
        result == new SyncStatusMessageArg(connInfoName: 'dataCenterName', tenantId: '88521', syncDirection: 'dataCenterName->CRM', srcObjectName: 'getErpObjNameResponse', startTime: 0, endTime: 0)
    }

    def "test send Sync Message To User"() {
        when:
        dataSyncNotifyManager.sendSyncMessageToUser([new SimpleSyncDataArg(receivers: 1000, status: SyncDataStatusEnum.WRITE_SUCCESS.status)], new SyncStatusMessageArg())

        then:
        notificationService.sendErpSyncDataAppMultiNotice(*_) >> new Result<Void>(null, null, null)
        notificationService.generateDataSyncResult(*_) >> new Result<List<TextInfo>>(null, null, [new TextInfo()])
    }
}
