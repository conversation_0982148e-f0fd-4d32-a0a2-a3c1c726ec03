package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.MongoTestUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.ErpTempMongoStore
import com.github.mongo.support.DatastoreExt
import groovy.util.logging.Slf4j
import org.bson.types.ObjectId
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Unroll
class ErpTempDataDaoSpecTest extends Specification {
    @Shared
    private ErpTempDataDao dao
    @Shared
    private ErpTempMongoStore tempStore


    @Shared
    private DatastoreExt store = MongoTestUtil.createStore()

    void setup() {
        if (tempStore == null) {
            def configCenterConfig = Mock(ConfigCenterConfig)
            configCenterConfig.readTempSecondaryPreference() >> { return new HashSet<>(["*"]) }
            tempStore = new ErpTempMongoStore(
                    configCenterConfig: configCenterConfig,
                    store: this.store
            )
            dao = new ErpTempDataDao(
                    mongoStore: tempStore,
                    syncLogManager: Mock(SyncLogManager)
            )
        }
    }


    def "fillNewLastSyncTime should update ErpTempData with newLastSyncTime from MongoDB"() {
        given:
        def tenantId = "1"
        def dcId = "dcid"
        def obj = "obj1"
        // 模拟输入数据
        ObjectId id1 = new ObjectId()
        ObjectId id2 = new ObjectId()


        def now = System.currentTimeMillis()
        ErpTempData data1 = new ErpTempData(id: id1, dataId: "d1", dataNumber: "d1", tenantId: tenantId, dcId: dcId, objApiName: obj, createTime: now,lastSyncTime: now)
        ErpTempData data2 = new ErpTempData(id: id2, dataId: "d2", dataNumber: "d2", tenantId: tenantId, dcId: dcId, objApiName: obj, createTime: now,lastSyncTime: now)
        List<ErpTempData> erpTempDataList = [data1, data2]
        //插入数据
        dao.batchUpsertErpTempData(tenantId, erpTempDataList, false)

        when:
        // 执行方法
        dao.fillNewLastSyncTime(tenantId, dcId, obj, erpTempDataList, false)
        log.info("result:$erpTempDataList")

        then:
        // 验证 ErpTempData 对象的 newLastSyncTime 属性是否被正确更新
        data1.newLastSyncTime != null
        data2.newLastSyncTime != null
        //id会更换
        data1.id != id1
    }

}
