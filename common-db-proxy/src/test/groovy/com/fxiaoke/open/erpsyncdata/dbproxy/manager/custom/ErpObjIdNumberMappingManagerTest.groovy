package com.fxiaoke.open.erpsyncdata.dbproxy.manager.custom

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.custom.ErpObjIdNumberMappingDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjIdNumberMappingEntity
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Stepwise
import spock.lang.Unroll

@Unroll
@Stepwise
class ErpObjIdNumberMappingManagerTest extends Specification {

    @Shared
    ErpObjIdNumberMappingManager manager
    @Shared
    String tenantId
    @Shared
    List<ErpObjIdNumberMappingEntity> list = []

    def setupSpec() {
        ErpObjIdNumberMappingDao dao = Mock(ErpObjIdNumberMappingDao) {
            // 模拟链式调用
            setTenantId(_) >> { args ->
                tenantId = args[0]
                def mock = Mock(ErpObjIdNumberMappingDao) {
                    insert(_) >> { a ->
                        def e = a[0] as ErpObjIdNumberMappingEntity
                        list.add(e)
                        return 1
                    }
                    updateDataNumber(*_) >> { aa ->
                        def a = aa as List<String>
                        println(list)
                        println(a)
                        for (ErpObjIdNumberMappingEntity e : list) {
                            if (e.getTenantId().equals(a[0]) && e.getDcId().equals(a[1])
                                    && e.getObjApiName().equals(a[2]) && e.getDataId().equals(a[3])) {
                                e.setDataNumber(a[4])
                                return 1
                            }
                        }
                        return 0
                    }
                    queryByDataId(*_) >> { a ->
                        for (ErpObjIdNumberMappingEntity e : list) {
                            if (e.getTenantId().equals(a[0]) && e.getDcId().equals(a[1])
                                    && e.getObjApiName().equals(a[2]) && e.getDataId().equals(a[3])) {
                                return e
                            }
                        }
                        return null
                    }
                    queryByDataNumber(*_) >> { a ->
                        def res = []
                        list.each { ee ->
                            def e = ee as ErpObjIdNumberMappingEntity
                            if (e.getTenantId().equals(a[0]) && e.getDcId().equals(a[1])
                                    && e.getObjApiName().equals(a[2]) && e.getDataNumber().equals(a[3])) {
                                res.add(res)
                            }
                        }
                        return res
                    }
                }
                return mock
            }
        }
        manager = new ErpObjIdNumberMappingManager(erpObjIdNumberMappingDao: dao)
    }

    def "test insert"() {
        when:
        int insert = manager.insert("84801",
                "dcId1000",
                "objApiName1000",
                "1000",
                "ch1000",
                "data name 1000")

        then:
        noExceptionThrown()
        list.size() == insert
    }

    def "test updateDataNumber"() {
        when:
        println(list)
        int update = manager.updateDataNumber("84801", "dcId1000", "objApiName1000","1000", "ch1001")
        println(update)

        then:
        update == 1
        def res = list.get(0) as ErpObjIdNumberMappingEntity
        "ch1001".equals(res.getDataNumber())

    }

    def "test queryByDataId"() {
        when:
        ErpObjIdNumberMappingEntity erpObjIdNumberMappingEntity = manager.queryByDataId("84801",
                "dcId1000","objApiName1000","1000")

        println(erpObjIdNumberMappingEntity);
        then:
        tenantId != null
        erpObjIdNumberMappingEntity != null
    }

    def "test queryByDataNumber - #name"() {
        when:
        List<ErpObjIdNumberMappingEntity> erpObjIdNumberMappingEntityList = manager.queryByDataNumber("84801",
                "dcId1000",
                "objApiName1000",
                dataNumber)
        then:
        erpObjIdNumberMappingEntityList != null
        erpObjIdNumberMappingEntityList.size() == result

        where:
        name    | dataNumber    || result
        "成功"    | "ch1001"     || 1
        "失败"    | "ch1000"     || 0
    }
}
