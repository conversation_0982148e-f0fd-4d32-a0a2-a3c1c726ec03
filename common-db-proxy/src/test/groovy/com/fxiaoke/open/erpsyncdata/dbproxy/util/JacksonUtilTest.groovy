package com.fxiaoke.open.erpsyncdata.dbproxy.util

import cn.hutool.core.lang.Dict
import com.fxiaoke.open.erpsyncdata.BaseSpockTest

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/6/2
 */
class JacksonUtilTest extends BaseSpockTest {

    def "大数字测试"() {
        String testStr = "{\n" +
                "    \"errCode\": \"s106240000\",\n" +
                "    \"errMsg\": \"success\",\n" +
                "    \"data\": {\n" +
                "        \"objAPIName\": \"lrp_product_item_join\",\n" +
                "        \"masterFieldVal\": {\n" +
                "            \"companynode\": \"100619\",\n" +
                "            \"child_id\": 993433391825506559,\n" +
                "            \"org_id\": 914030175340028452,\n" +
                "            \"item_type\": 2,\n" +
                "            \"parent_id\": 993434190689515107,\n" +
                "            \"companyname\": \"昆明华银康医学检验实验室有限公司\",\n" +
                "            \"update_at\": \"2023-05-31 13:25:53\",\n" +
                "            \"id\": 993433391825492644\n" +
                "        },\n" +
                "        \"detailFieldVals\": {}\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}"
        def dict = JacksonUtil.fromJson(testStr, Dict.class)
        println dict
        def str2 = JacksonUtil.toJson(dict)
        println(str2)
        expect:
        true
    }
}
