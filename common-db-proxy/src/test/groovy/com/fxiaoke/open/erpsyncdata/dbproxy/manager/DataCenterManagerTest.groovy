package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2024/10/17 21:00:13
 */
class DataCenterManagerTest extends Specification {

    def "GetDataCenterBySnapshotId"() {
        def mock = Mock(AdminSyncPloyDetailSnapshotDao) {
            getById(*_) >> new SyncPloyDetailSnapshotEntity(sourceTenantId: "123", syncPloyDetailData: new SyncPloyDetailData(sourceObjectApiName: "S", destObjectApiName: "D", sourceTenantType: type), syncPloyDetailId: "123")
        }
        mock.setTenantId(_) >> mock
        when:
        DataCenterManager dataCenterManager = new DataCenterManager(
                syncPloyDetailSnapshotDao: mock,
                erpObjManager: Mock(ErpObjManager) {
                    getRelation(*_) >> {
                        new ErpObjectRelationshipEntity(dataCenterId: it[1] == 'S' ? "ERP" : "CRM")
                    }
                }
        )
        def id = dataCenterManager.getDataCenterBySnapshotId('1', '12')


        then:
        id == result

        where:
        type           || result
        TenantType.ERP || "ERP"
        TenantType.CRM || "CRM"
    }

    def "GetResultBySnapshotId"() {
        when:
        def mock = Mock(AdminSyncPloyDetailSnapshotDao) {
            getById(*_) >> new SyncPloyDetailSnapshotEntity(sourceTenantId: "123", syncPloyDetailData: new SyncPloyDetailData(sourceObjectApiName: "S", destObjectApiName: "D", sourceTenantType: type), syncPloyDetailId: "123")
        }
        mock.setTenantId(_) >> mock
        DataCenterManager dataCenterManager = new DataCenterManager(
                syncPloyDetailSnapshotDao: mock,
                erpObjManager: Mock(ErpObjManager) {
                    getRelation(*_) >> {
                        new ErpObjectRelationshipEntity(dataCenterId: it[1] == 'S' ? "ERP" : "CRM")
                    }
                }
        )
        def id = dataCenterManager.getResultBySnapshotId('1', '12')

        then:
        result == id

        where:
        type           || result
        TenantType.ERP || [streamId: "123", dataCenterId: 'ERP']
        TenantType.CRM || [streamId: "123", dataCenterId: 'CRM']
    }

    def "GetDataCenterBySyncDataId"() {
        when:
        def mock = Mock(SyncDataFixDao) {
            getSimple(*_) >> new SyncDataEntity(syncPloyDetailSnapshotId: "123")
        }
        mock.setTenantId(_) >> mock
        DataCenterManager dataCenterManager = new DataCenterManager(
                adminSyncDataDao: mock,
                dataCenterManager: Mock(DataCenterManager) {
                    getDataCenterBySnapshotId(*_) >> {
                        assert it[1] == "123"
                        return "dcId"
                    }
                }
        )
        def id = dataCenterManager.getDataCenterBySyncDataId('1', '12')

        then:
        id == "dcId"
    }

    def "GetDataCenterSeq"() {
        when:
        DataCenterManager dataCenterManager = new DataCenterManager(
                erpConnectInfoManager: Mock(ErpConnectInfoManager) {
                    getByIdAndTenantId(*_) >> new ErpConnectInfoEntity(channel: ErpChannelEnum.ERP_K3CLOUD, connectParams: '{}', number: 123)
                })
        def seq = dataCenterManager.getDataCenterSeq('1', '2')

        then:
        seq == 123
    }

    def "OnlyOneErpDataCenter"() {
        when:
        DataCenterManager dataCenterManager = new DataCenterManager(
                erpConnectInfoManager: Mock(ErpConnectInfoManager) {
                    listByTenantId(*_) >> [new ErpConnectInfoEntity(channel: ErpChannelEnum.ERP_K3CLOUD), new ErpConnectInfoEntity(channel: ErpChannelEnum.ERP_DB_PROXY), new ErpConnectInfoEntity(channel: ErpChannelEnum.CRM), new ErpConnectInfoEntity(channel: ErpChannelEnum.ERP_JDY)]
                })
        def seq = dataCenterManager.onlyOneErpDataCenter('1')

        then:
        !seq
    }
}
