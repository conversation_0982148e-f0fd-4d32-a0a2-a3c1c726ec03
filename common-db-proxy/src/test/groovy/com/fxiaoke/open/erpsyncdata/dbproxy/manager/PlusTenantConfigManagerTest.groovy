package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import cn.hutool.core.convert.Convert
import cn.hutool.core.lang.Opt
import com.alicp.jetcache.CacheManager
import com.alicp.jetcache.CacheResultCode
import com.alicp.jetcache.MultiLevelCache
import com.alicp.jetcache.support.Kryo5ValueDecoder
import com.alicp.jetcache.support.Kryo5ValueEncoder
import com.fasterxml.jackson.core.type.TypeReference
import com.fxiaoke.open.erpsyncdata.common.jetcache.BaseJetCacheTest
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.JetCacheName
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemInfo
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Type

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.*

/**
 * 这里只测试了查询，更新需要在继承测试
 * <AUTHOR> (^_−)☆
 */
// TODO: Spring
//@ContextConfiguration(classes = [TestConfiguration.class])
@Ignore
class PlusTenantConfigManagerTest extends BaseJetCacheTest {
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager
    @Autowired
    private CacheManager cacheManager

    void setup() {
    }

    void cleanup() {
        removeConfig()
    }
    static ErpTenantConfigurationEntity currentRealConfig = null

    static class TestConfiguration extends Specification {
        TenantConfigurationManager tenantConfigurationManager() {
            //mock
            def mock = Mock(TenantConfigurationManager)
            mock.findNoCache(*_) >> { currentRealConfig }
            return mock
        }

        @Bean
        PlusTenantConfigManager plusTenantConfigManager() {
            PlusTenantConfigManager plusTenantConfigManager = new PlusTenantConfigManager(tenantConfigurationManager())
            return plusTenantConfigManager
        }
    }

    def "manager not null"() {
        expect:
        plusTenantConfigManager != null
        plusTenantConfigManager.getTenantConfigurationManager() != null
    }

    @Unroll
    def "测试普通类型缓存 #type"(TenantConfigurationTypeEnum type, Type resultType, def defaultVaL, String configVal, def expectVal) {
        //使用global来测试
        given:
        def cacheKey = "0.0.${type.name()}" as String
        //先测试空缓存和空默认值
        def nullObj = plusTenantConfigManager.getGlobalTypeConfig(type, resultType, null)
        def multiCache = (MultiLevelCache) cacheManager.getCache(JetCacheName.CONVERT_CONFIG)
        def removeNull = multiCache.remove(cacheKey)
        def cacheEventsNull = getRemoveCacheEvents()

        //设置当前应该读到的值
        setConfig(configVal)

        //第一次读取，读缓存，写缓存
        def convert = plusTenantConfigManager.getGlobalTypeConfig(type, resultType, defaultVaL)
        def cacheEvents = getRemoveCacheEvents()

        //第二次读取，从本地缓存读
        def convert2 = plusTenantConfigManager.getGlobalTypeConfig(type, resultType, defaultVaL)
        def cacheEvents2 = getRemoveCacheEvents()

        //模拟本地缓存超时,实际上是直接移除本地缓存
        def localCache = multiCache.caches()[0]
        def remove = localCache.remove(cacheKey)

        //第三次读取，从远程缓存读，并写入本地缓存
        def convert3 = plusTenantConfigManager.getGlobalTypeConfig(type, resultType, defaultVaL)
        def cacheEvents3 = getRemoveCacheEvents()

        expect:
        nullObj == null
        remove
        removeNull
        convert == expectVal
        convert2 == expectVal
        convert3 == expectVal
        cacheEvents.size() == 4
        cacheEvents2.size() == 1
        cacheEvents3.size() == 4


        //先从本地缓存获取为空
        testGet(cacheEventsNull.get(0), false, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //再从远程缓存获取为空
        testGet(cacheEventsNull.get(1), true, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //添加到本地缓存
        testPut(cacheEventsNull.get(2), false, cacheKey, null)
        //添加到远程缓存
        testPut(cacheEventsNull.get(3), true, cacheKey, null)
        testRemove(cacheEventsNull.get(4), false, cacheKey)
        testRemove(cacheEventsNull.get(5), true, cacheKey)

        //先从本地缓存获取为空
        testGet(cacheEvents.get(0), false, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //再从远程缓存获取为空
        testGet(cacheEvents.get(1), true, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //添加到本地缓存
        testPut(cacheEvents.get(2), false, cacheKey, expectVal)
        //添加到远程缓存
        testPut(cacheEvents.get(3), true, cacheKey, expectVal)

        //第二次从本地缓存读到数据
        testGet(cacheEvents2.get(0), false, cacheKey, CacheResultCode.SUCCESS, convert2)

        //第三次从本地缓存移除数据，从远程缓存读到数据
        testRemove(cacheEvents3.get(0), false, cacheKey)
        //先从本地缓存获取为空
        testGet(cacheEvents3.get(1), false, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //从远程缓存读到数据
        testGet(cacheEvents3.get(2), true, cacheKey, CacheResultCode.SUCCESS, convert2)
        //添加到本地缓存
        testPut(cacheEvents3.get(3), false, cacheKey, expectVal)

        where:
        [type, resultType, defaultVaL, configVal] << [
                [
                        SCAN_NO_TRIGGER_TEMP_NOTIFY_COUNT,
                        Integer.class,
                        1,
                        null
                ],

                [
                        SCAN_NO_TRIGGER_TEMP_LIMIT_COUNT,
                        Integer.class,
                        1000,
                        "2000"
                ],

                [
                        KEY_GET_BY_ID_BREAK_EXPIRE_TIME,
                        Long.class,
                        600L,
                        null
                ],

                [
                        DEFAULT_LIMIT_QUERY_TEMP_COEFFICIENT,
                        Double.class,
                        0.5,
                        null
                ],

                [
                        saleOrderNeedHandleCpq,
                        Boolean.class,
                        false,
                        null
                ],

                [
                        NeedSendDispatcherMqEnv,
                        String.class,
                        "",
                        null
                ]
        ]
        expectVal = setExpectValue(resultType, defaultVaL, configVal)
    }

    @Unroll
    def "测试对象类型缓存 #type"(String testKey, TenantConfigurationTypeEnum type, TypeReference resultType, String configVal, def expectVal) {
        //使用global来测试
        given:
        //模拟本地缓存超时,实际上是直接移除本地缓存
        def cacheKey = "0.0.${type.name()}" as String

        //先测试空的场景
        def nullObj = plusTenantConfigManager.getGlobalObjConfig(type, resultType)
        def multiCache = (MultiLevelCache) cacheManager.getCache(JetCacheName.CONVERT_CONFIG)
        //移除缓存，这个bu
        def remove1 = multiCache.remove(cacheKey)
        def cacheEventsNull = getRemoveCacheEvents()

        //设置当前应该读到的值
        setConfig(configVal)
        //第一次读取，读缓存，写缓存
        def convert = plusTenantConfigManager.getGlobalObjConfig(type, resultType)
        def cacheEvents = getRemoveCacheEvents()

        //第二次读取，从本地缓存读
        def convert2 = plusTenantConfigManager.getGlobalObjConfig(type, resultType)
        def cacheEvents2 = getRemoveCacheEvents()

        def localCache = multiCache.caches()[0]
        def remove = localCache.remove(cacheKey)

        //第三次读取，从远程缓存读，并写入本地缓存
        def convert3 = plusTenantConfigManager.getGlobalObjConfig(type, resultType)
        def cacheEvents3 = getRemoveCacheEvents()

        //测试Kryo5序列化是否正常
        if (convert != null) {
            def encodeVal = Kryo5ValueEncoder.INSTANCE.apply(convert)
            def decodeVal = Kryo5ValueDecoder.INSTANCE.apply(encodeVal)
        }

        expect:
        nullObj == null
        remove1
        remove
        //第1、2次读取的是同一个引用，第3次读取的是远程缓存转换的
        convert.is(convert2)
        !convert2.is(convert3)
        testObj(convert, expectVal, testKey)
        testObj(convert3, expectVal, testKey)
        cacheEventsNull.size() == 6
        cacheEvents.size() == 4
        cacheEvents2.size() == 1
        cacheEvents3.size() == 4


        //先从本地缓存获取为空
        testGet(cacheEventsNull.get(0), false, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //再从远程缓存获取为空
        testGet(cacheEventsNull.get(1), true, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //添加到本地缓存
        testPut(cacheEventsNull.get(2), false, cacheKey, null)
        //添加到远程缓存
        testPut(cacheEventsNull.get(3), true, cacheKey, null)
        testRemove(cacheEventsNull.get(4), false, cacheKey)
        testRemove(cacheEventsNull.get(5), true, cacheKey)

        //先从本地缓存获取为空
        testGet(cacheEvents.get(0), false, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //再从远程缓存获取为空
        testGet(cacheEvents.get(1), true, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //添加到本地缓存
        testPut(cacheEvents.get(2), false, cacheKey, expectVal)
        //添加到远程缓存
        testPut(cacheEvents.get(3), true, cacheKey, expectVal)

        //第二次从本地缓存读到数据
        testGet(cacheEvents2.get(0), false, cacheKey, CacheResultCode.SUCCESS, convert2)

        //第三次从本地缓存移除数据，从远程缓存读到数据
        testRemove(cacheEvents3.get(0), false, cacheKey)
        //先从本地缓存获取为空
        testGet(cacheEvents3.get(1), false, cacheKey, CacheResultCode.NOT_EXISTS, null)
        //从远程缓存读到数据
        testGet(cacheEvents3.get(2), true, cacheKey, CacheResultCode.SUCCESS, convert2)
        //添加到本地缓存
        testPut(cacheEvents3.get(3), false, cacheKey, expectVal)

        where:
        [testKey, type, resultType, configVal] << [
                [
                        KNOWN_SYSTEM_INFO.name(),
                        KNOWN_SYSTEM_INFO,
                        new TypeReference<List<SystemInfo>>() {},
                        '[ { "name": "金蝶云苍穹" }, { "name": "金蝶KIS" }, { "name": "金蝶EAS" }, { "name": "金蝶K3 WISE" }, { "name": "金蝶管易云" }, { "name": "金蝶精斗云" }, { "name": "SAP-S/4 HANA" }, { "name": "SAP-ECC" }, { "name": "SAP Business One" }, { "name": "用友U8Cloud" }, { "name": "用友YonBIP" }, { "name": "用友EAI" }, { "name": "用友YonSuite" }, { "name": "DB-Oracle" }, { "name": "DB-MySql" }, { "name": "DB-PostgreSQL" }, { "name": "DB-SqlServer" }, { "name": "管家婆" }, { "name": "易订货" }, { "name": "NC" }, { "name": "畅捷通" }, { "name": "聚水潭" }, { "name": "销售易" }, { "name": "尘峰SCRM" }, { "name": "Salesforce" }, { "name": "泛微" }, { "name": "问卷星" }, { "name": "金数据" }, { "name": "自研" } ]'
                ],
                [
                        CONNECTOR_HUB_INFO.name(),
                        CONNECTOR_HUB_INFO,
                        new TypeReference<HubInfo>() {},
                        '{ "name": "default", "baseUrl": "http://**************:43454/hub", "outerConnectors": [ { "apiName": "Salesforce", "defaultName": "Salesforce", "moduleCode": "salesforce_data_sync_app", "systemName": "Salesforce", "iconUrl": "https://c1.sfdcstatic.com/content/dam/sfdc-docs/www/logos/logo-salesforce.svg" } ] }'
                ]
        ]
        expectVal = setExpectValue(resultType, configVal)
    }

    private static Object setExpectValue(Type resultType, def defaultVal, String configVal) {
        if (configVal == null) {
            return defaultVal
        }
        return Convert.convert(resultType, configVal)
    }

    //验证对象
    private static boolean testObj(Object realVal, Object expectVal, String testKey) {
        if (!testObjDetail(testKey, realVal)) {
            return false
        }
        return JacksonUtil.toJson(realVal) == JacksonUtil.toJson(expectVal)
    }

    //设置更精细的验证
    private static boolean testObjDetail(String testKey, Object realVal) {
        if (testKey == KNOWN_SYSTEM_INFO.name()) {
            return Opt.ofTry {
                return ((realVal as List).get(0) as SystemInfo).getName() == "金蝶云苍穹"
            }.orElse(false)
        }
        if (testKey == CONNECTOR_HUB_INFO.name()) {
            return Opt.ofTry {
                return (realVal as HubInfo).getName() == "default"
            }.orElse(false)
        }
        return true
    }

    private static Object setExpectValue(TypeReference resultType, String configVal) {
        if (configVal == null) {
            return null
        }
        return JacksonUtil.fromJson(configVal, resultType)
    }


    private static void setConfig(String configVal) {
        if (configVal != null) {
            def entity = new ErpTenantConfigurationEntity()
            entity.setTenantId("0")
            entity.setDataCenterId("0")
            entity.setConfiguration(configVal)
            currentRealConfig = entity
        }
    }


    private static void removeConfig() {
        currentRealConfig = null
    }


}
