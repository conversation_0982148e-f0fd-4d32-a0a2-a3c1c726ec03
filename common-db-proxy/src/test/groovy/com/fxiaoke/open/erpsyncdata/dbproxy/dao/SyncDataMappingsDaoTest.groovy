package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import cn.hutool.core.util.IdUtil
import cn.hutool.db.sql.SqlExecutor
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncObjectAndTenantMappingData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.MigrateTableManager
import com.fxiaoke.open.erpsyncdata.dbproxy.model.UpdateMapping
import org.apache.ibatis.session.SqlSession
import spock.lang.*

import java.sql.Connection

/**
 * 这个类使用mybatis连接了数据库，但是不使用spring
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/6/1
 */
//按顺序测试
@Stepwise
@Unroll
class SyncDataMappingsDaoTest extends Specification {
    @Shared
    private static SyncDataMappingsDao mapper
    @Shared
    private static boolean initTable = false

    void setup() {
        mapper = PgTestUtil.getMapper(SyncDataMappingsDao.class)
        if (!initTable) {
            initTable = true
            initTable()
        }
    }

    //测试参数
    static String tenantId = "88783";
    static String srcObj = "srcObj";
    static String destObj = "destObj";
    static SyncDataMappingsEntity entity1 = newEntity()
    static String srcId1 = entity1.getSourceDataId()
    static String destId1 = entity1.getDestDataId()
    static def id1 = entity1.getId()
    static def entity2 = newEntity()
    static def id2 = entity2.getId()
    static def srcId2 = entity2.getSourceDataId()
    static def destId2 = entity2.getDestDataId()
    static String syncDataId1 = IdUtil.nanoId()
    static String syncDataId2 = IdUtil.nanoId()
    static def dn1 = "dn12"
    static def dn2 = "dn22"
    static int syncStatus = 2
    static Long version = 10086
    static Long updateTime = System.currentTimeMillis()
    static def remark = "r1"
    static def srcDetailObj = "srcDetailObj"
    static def destDetailObj = "destDetailObj"


    def initTable() {
        SqlSession session = PgTestUtil.getSession()
        def sqls = MigrateTableManager.getSql(tenantId, true)
        Connection connection = session.getConnection()
        for (String sql : sqls) {
            if (!sql.startsWith("--")) {
                SqlExecutor.execute(connection, sql)
            }
        }
    }

    def "清空数据"() {
        expect:
        mapper.truncateTable(tenantId)
    }

    def "insert1"() {
        expect:
        mapper.insert(entity1) == 1
    }

    def "insert2"() {
        expect:
        //插入,忽略错误
        insert2 == mapper.insertIgnore(input)
        where:
        input   | insert2
        entity1 | 0
        entity2 | 1
    }

    def "批量插入"() {
        when:
        def batch = []
        for (i in 0..<10) {
            batch.add(newEntity())
        }
        def batchInsert = mapper.batchInsert(tenantId, batch)
        then:
        batchInsert == 10
    }

    def "getByUninKey"() {
        when:
        //查询
        SyncDataMappingsEntity byUninKey = mapper.getByUninKey(tenantId, srcObj, srcId1, destObj);
        println("byUninKey:$byUninKey")
        then:
        byUninKey != null
        byUninKey.getId() == id1


        when:
        //查询
        SyncDataMappingsEntity byUninKeyByDestId = mapper.getByUninKeyByDestId(tenantId, srcObj, destId1, destObj);
        println("byUninKeyByDestId:$byUninKeyByDestId")
        then:
        byUninKeyByDestId != null
        byUninKeyByDestId.getId() == id1


        when:
        //查询
        SyncDataMappingsEntity byUninKeyReverse = mapper.getByUninKeyReverse(tenantId, srcObj, srcId1, destObj);
        println("byUninKeyReverse:$byUninKeyReverse")
        then:
        byUninKeyReverse == null
    }

    def "bulkUpdateDestBySourceArgs"() {
        when:
        destId1 = IdUtil.nanoId()
        destId2 = IdUtil.nanoId()
        remark = "remark3"
        syncStatus++
        List<UpdateMapping.BySourceArg> bySourceArgs = [
                UpdateMapping.BySourceArg.builder()
                        .sourceObjectApiName(srcObj)
                        .destObjectApiName(destObj)
                        .sourceDataId(srcId1)
                        .lastSyncStatus(syncStatus)
                        .destDataId(destId1)
                        .destDataName(dn1)
                        .remark(remark)
                        .syncDataId(syncDataId1)
                        .build(),
                UpdateMapping.BySourceArg.builder()
                        .sourceObjectApiName(srcObj)
                        .destObjectApiName(destObj)
                        .sourceDataId(srcId2)
                        .lastSyncStatus(syncStatus)
                        .destDataId(destId2)
                        .destDataName(dn2)
                        .remark(remark)
                        .syncDataId(syncDataId2)
                        .build(),
        ]
        //不测试false的了
        def update = mapper.bulkUpdateDestBySourceArgs(tenantId, true, bySourceArgs)
        entity1 = mapper.getById(tenantId, this.id1)
        entity2 = mapper.getById(tenantId, id2)
        then:
        update == 2
        entity1.lastSyncStatus == syncStatus
        entity1.destDataName == dn1
        entity1.remark == remark
        //使用数据库的方法更新的时间
        entity1.updateTime > updateTime
        entity1.destDataId == destId1
        entity1.isCreated == true
        entity2.lastSyncStatus == syncStatus
        entity2.destDataName == dn2
        entity2.remark == remark
        //使用数据库的方法更新的时间
        entity2.updateTime > updateTime
        entity2.destDataId == destId2
        entity2.isCreated == true

    }

    def "mergeDestDataId"() {
        when:
        def entity3 = newEntity()
        mapper.insert(entity3)
        def oldId = entity3.getDestDataId()
        def newId = IdUtil.nanoId()
        def id = entity3.getId()
        def mergeDestDataId = mapper.mergeDestDataId(tenantId, [id], srcObj, destObj, oldId, newId, System.currentTimeMillis())
        entity3 = mapper.getById(tenantId, id)
        then:
        mergeDestDataId == 1
        entity3.getDestDataId() == newId
    }


    def "mergeSourceDataId"() {
        when:
        def entity3 = newEntity()
        mapper.insert(entity3)
        def oldId = entity3.getSourceDataId()
        def newId = IdUtil.nanoId()
        def id = entity3.getId()
        def mergeDestDataId = mapper.mergeSourceDataId(tenantId, [id], srcObj, destObj, oldId, newId, System.currentTimeMillis())
        entity3 = mapper.getById(tenantId, id)
        then:
        mergeDestDataId == 1
        entity3.getSourceDataId() == newId
    }

    def "listBySourceInfo"() {
        expect:
        mapper.listBySourceInfo(tenantId, srcObj, [srcId1]) != null
    }


    def "listByDestInfo"() {
        expect:
        mapper.listByDestInfo(tenantId, srcObj, [destId1]) != null
    }

    def "findDetailByMasterId"() {
        SyncDataMappingsEntity entity = newDetailEntity(srcId1)
        mapper.insert(entity)
        def find = mapper.findDetailByMasterId(tenantId, entity.getSourceObjectApiName(), entity.getDestObjectApiName(), srcId1)
        expect:
        find != null
    }


    def "countByObjectApiNames"() {
        def t1 = System.currentTimeMillis()
        def entity = newEntity()
        def detailEntity = newDetailEntity(entity.getSourceDataId())
        mapper.insert(entity)
        mapper.insert(detailEntity)
        def objMappings = [
                new SyncObjectAndTenantMappingData(
                        sourceTenantId: tenantId,
                        destTenantId: tenantId,
                        sourceObjectApiName: srcObj,
                        destObjectApiName: destObj
                ),
                new SyncObjectAndTenantMappingData(
                        sourceTenantId: tenantId,
                        destTenantId: tenantId,
                        sourceObjectApiName: srcDetailObj,
                        destObjectApiName: destDetailObj
                )
        ]

        def objMappings2 = [
                new SyncObjectAndTenantMappingData(
                        sourceTenantId: tenantId,
                        destTenantId: tenantId,
                        sourceObjectApiName: srcDetailObj,
                        destObjectApiName: destDetailObj
                )
        ]
        def t2 = System.currentTimeMillis()
        def count = mapper.countByObjectApiNames(tenantId, objMappings, 2, null, null, null,
                null, null, t1, t2)
        def count2 = mapper.countByObjectApiNamesLimit1000(tenantId, objMappings, 2, null, null, null,
                null, null, t1, t2)
        def count3 = mapper.countByDetailObjectApiNames(tenantId, entity.getSourceDataId(), objMappings2, 2,
                null, null, null, null, null, t1, t2)
        def list = mapper.listByObjectApiNames(tenantId, objMappings, 2, null, null, null,
                null, null, 0, 1000, t1, t2)
        def details = mapper.listByMasterDataId(tenantId, objMappings2, entity.getSourceDataId())
        def details2 = mapper.listByDetailObjectApiNames(tenantId, entity.getSourceDataId(), objMappings2, 2,
                null, null, null, null, null, 0, 1000, t1, t2)
        expect:
        list.size() == 2
        count == 2
        count2 == 2
        count3 == 1
        details.size() == 1
        details2.size() == 1
    }

    def "querySyncFailed"() {
        def objMapping = new SyncObjectAndTenantMappingData(
                sourceTenantId: tenantId,
                destTenantId: tenantId,
                sourceObjectApiName: srcObj,
                destObjectApiName: destObj
        )
        def objMappings = [
                objMapping,
                new SyncObjectAndTenantMappingData(
                        sourceTenantId: tenantId,
                        destTenantId: tenantId,
                        sourceObjectApiName: srcDetailObj,
                        destObjectApiName: destDetailObj
                )
        ]
        def failed = mapper.countSyncFailed(tenantId, objMappings)
        def limit = mapper.countBySyncFailedAndLimit(tenantId, objMapping, 0, 1001)
        expect:
        //这没造数据
        failed.size() >= 0
        limit >= 0
    }

    def "queryBySourceDataIdList"() {
        def list = mapper.queryBySourceDataIdList(tenantId, srcObj, [srcId1, srcId2], destObj)
        def list2 = mapper.queryByDestDataIdList(tenantId, srcObj, [destId1, destId2], destObj)
        def list3 = mapper.listCreatedBySourceDataIds(tenantId, srcObj, destObj, [srcId1, srcId2])
        def list4 = mapper.listCreatedByDestDataIds(tenantId, srcObj, destObj, [destId1, destId2])
        expect:
        list.size() == 2
        list2.size() == 2
        list3.size() == 2
        list4.size() == 2
    }

    def "删除，但是使用错误参数"() {
        def del = mapper.deleteSyncDataMappings(tenantId, "fakeDel1", "fakeDel2")
        def del2 = mapper.deleteMappingsByDestId(tenantId, "fakeid", srcObj, destObj)
        def del3 = mapper.deleteMappingsBySourceId(tenantId, "fakeid", srcObj, destObj)
        expect:
        del == 0
        del2 == 0
        del3 == 0
    }

    def "countIsCreatedMapping"() {
        def count = mapper.countIsCreatedMapping(tenantId, srcObj, destObj, true)
        expect:
        count > 0
    }


    private static SyncDataMappingsEntity newEntity() {
        SyncDataMappingsEntity entity = new SyncDataMappingsEntity();
        entity.setTenantId(tenantId);
        entity.setSourceObjectApiName(srcObj);
        entity.setDestObjectApiName(destObj);
        String id = IdUtil.nanoId();
        entity.setId(id);
        entity.setSourceDataId("s" + id);
        entity.setSourceDataName("sn" + id);
        entity.setDestDataId("d" + id);
        entity.setDestDataName("dn" + id);
        entity.setIsDeleted(false);
        entity.setIsCreated(false);
        entity.setLastSyncStatus(1)
        entity.setCreateTime(System.currentTimeMillis())
        return entity;
    }


    private static SyncDataMappingsEntity newDetailEntity(String masterId) {
        def entity = newEntity()
        entity.setSourceObjectApiName(srcDetailObj)
        entity.setDestObjectApiName(destDetailObj)
        entity.setMasterDataId(masterId)
        return entity
    }
}
