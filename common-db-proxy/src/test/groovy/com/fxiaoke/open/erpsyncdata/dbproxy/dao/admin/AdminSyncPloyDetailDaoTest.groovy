package com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin

import com.alibaba.fastjson.JSON
import com.fxiaoke.api.IdGenerator
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/6/7 17:15:24
 */
// TODO: Ignore Spring
@Ignore
@ContextConfiguration(["classpath:spring-test.xml"])
class AdminSyncPloyDetailDaoTest extends Specification {

    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao

    static {
        System.setProperty("process.profile", "fstest")
    }

    def "沙盒少拷贝测试"() {
        expect:
        final String tenantId = "84801"
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId)
        System.out.println(JSON.toJSONString(syncPloyDetailEntities))
        final SyncPloyDetailEntity entity = syncPloyDetailEntities.stream()
                .filter({ syncPloyDetailEntity -> StringUtils.isNotEmpty(syncPloyDetailEntity.getIntegrationStreamName()) && syncPloyDetailEntity.getIntegrationStreamName().contains("回写") })
                .findFirst().orElse(null)
        System.out.println(JSON.toJSONString(entity))
        System.out.println("+++++++++++++"+JSON.toJSONString(entity.getIntegrationStreamNodes()))

        def s = "88598"
        def v = entity
        v.setId(IdGenerator.get())
        v.setTenantId(s)
//        v.setSourceDataCenterId(dcIdMap.get(v.getSourceDataCenterId()))
//        v.setDestDataCenterId(dcIdMap.get(v.getDestDataCenterId()))
        v.setSourceTenantIds(ListStringData.newListStringData(s))
        v.setDestTenantIds(ListStringData.newListStringData(s))
        v.setStatus(SyncPloyDetailStatusEnum.DISABLE.getStatus())
        long now = System.currentTimeMillis()
        v.setCreateTime(now)
        v.setUpdateTime(now)

        println JSON.toJSONString(v)
        println "-----------------"+v.getIntegrationStreamNodes()
    }

    def "测试update"() {
        expect:
        final String tenantId = "88521"
        def id = "3a95f4ad0e204ae08220f9c4edac3d2c"
        def entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, id)
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).update(entity)
    }
}
