package com.fxiaoke.open.erpsyncdata.dbproxy.manager


import spock.lang.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/2/17 10:25:35
 */
@Ignore
class SpeedWriteMongoTest extends Specification {
//
//    @Unroll
//    def "#name"() {
//        given:
//        def config = Mock(ConfigCenterConfig) {
//            isDropWhenOverSaveLogLimitPloy() >> false
//            getAllSaveSyncLogLimit((String) _) >> ["123": limit, "ALL": allLimit]
//        }
//        long overLimitSize = 0
//        SpeedLimiterProcess speedWriteMongo = new SpeedLimiterProcess<Integer, Integer>(
//                config,
//                TenantConfigurationTypeEnum.WRITE_SYNC_DATA_LIMIT, "SpeedWriteMongoTest") {
//            @Override
//            protected List<Integer> normalFunc(final String tenantId, final List<Integer> dataList) {
//                return dataList
//            }
//
//            @Override
//            protected List<Integer> overFunc(final String tenantId, final List<Integer> overDataList) {
//                return overDataList.stream().map({ it + 1 }).collect(Collectors.toList())
//            }
//
//            @Override
//            public long getOverLimitSize(final String tenantId, final int size) {
//                overLimitSize = super.getOverLimitSize(tenantId, size)
//                return overLimitSize
//            }
//        }
//
//        when:
//        // 过期时间为5秒
//        SpeedLimiterProcess.setExpireSeconds(5)
//        SpeedLimiterProcess.globalCountMap.get("SpeedWriteMongoTest").get("123").set(count)
//        SpeedLimiterProcess.globalCountMap.get("SpeedWriteMongoTest").get("0").set(allCount)
//
//        if (sleep > 0) {
//            Thread.sleep(sleep * 1000)
//        }
//
//        def list = JSON.parseArray(JSON.toJSONString(arg), Integer.class)
//        def function = speedWriteMongo.process("123", list)
//
//        then:
//        arg == list
//        overLimitSize == size
//        function == result
//
//        // TODO: 结果错误
//        where:
////        标题 | 企业限额 | 全局限额 | 企业已使用量 | 全局已使用量 | 等待时间 | 入参 | 超出的数量 || 返回值
//        name         | limit | allLimit | count | allCount | sleep | arg             | size || result
//        "不超"       | 1000  | 1000     | 1     | 5        | 0     | [1, 2, 3, 4, 5] | -990 || arg
//        "企业全超"   | 3     | _        | 5     | 100      | 0     | [1, 2, 3, 4, 5] | 7    || [2, 3, 4, 5, 6]
//        "企业部分超" | 7     | _        | 5     | 100      | 0     | [1, 2, 3, 4, 5] | 3    || [1, 2, 4, 5, 6]
//        "全局全超"   | 20    | 50       | 10    | 55       | 0     | [1, 2, 3, 4, 5] | 10   || [2, 3, 4, 5, 6]
//        "全局部分超" | 20    | 50       | 10    | 48       | 0     | [1, 2, 3, 4, 5] | 3    || [1, 2, 4, 5, 6]
//
//        "全超过期"   | 20    | 50       | 10    | 55       | 5     | [1, 2, 3, 4, 5] | -45  || [1, 2, 3, 4, 5]
//        "部分超过期" | 20    | 50       | 10    | 48       | 5     | [1, 2, 3, 4, 5] | -45  || [1, 2, 3, 4, 5]
//    }
//
//    def "测试参数不变"() {
//        setup:
//        def config = Mock(ConfigCenterConfig) {
//            isDropWhenOverSaveLogLimitPloy() >> false
//        }
//        SpeedLimiterProcess speedWriteMongo = new SpeedLimiterProcess<TestInvariable, String>(
//                config,
//                TenantConfigurationTypeEnum.WRITE_SYNC_DATA_LIMIT, "") {
//            @Override
//            protected List<String> normalFunc(final String tenantId, final List<TestInvariable> dataList) {
//                return [dataList.get(0).field]
//            }
//
//            @Override
//            protected List<String> overFunc(final String tenantId, final List<TestInvariable> overDataList) {
//                List<Runnable> rollback = new ArrayList<>();
//                try {
//                    overDataList.stream().forEach({ syncLog ->
//                        final String dataBody = syncLog.getField()
//                        syncLog.setField("写入量超过限额，为保证系统稳定，不记录具体详情")
//                        rollback.add({
//                            syncLog.setField(dataBody)
//                        })
//                    })
//                } finally {
//                    rollback.forEach({ it.run() })
//                }
//                return [overDataList.get(0).getField() + "123"]
//            }
//
//            @Override
//            public long getOverLimitSize(final String tenantId, final int size) {
//                return 1
//            }
//        }
//
//        when:
//        def arg = [new TestInvariable("123"), new TestInvariable("456")]
//        def list = JSON.parseArray(JSON.toJSONString(arg), TestInvariable.class)
//        def function = speedWriteMongo.process("123", list)
//
//        then:
//        ListUtils.isEqualList(arg, list)
//        ListUtils.isEqualList(function, ["123", "456123"])
//    }
//
//    @Data
//    public static class TestInvariable {
//        String field
//
//        TestInvariable(final String field) {
//            this.field = field
//        }
//
//        boolean equals(final o) {
//            if (this.is(o)) return true
//            if (o == null || getClass() != o.class) return false
//
//            TestInvariable that = (TestInvariable) o
//
//            if (field != that.field) return false
//
//            return true
//        }
//
//        int hashCode() {
//            return (field != null ? field.hashCode() : 0)
//        }
//    }
}
