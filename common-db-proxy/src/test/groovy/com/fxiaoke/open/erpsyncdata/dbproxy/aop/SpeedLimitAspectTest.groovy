package com.fxiaoke.open.erpsyncdata.dbproxy.aop

import com.alibaba.fastjson.JSON
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDaoImpl
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.InterfaceMonitorDataDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncLogDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.InterfaceMonitorMongoStore
import com.mongodb.client.MongoCollection;
import com.mongodb.bulk.BulkWriteResult
import org.springframework.aop.aspectj.annotation.AspectJProxyFactory
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2023/2/28 17:45:18
 */
class SpeedLimitAspectTest extends Specification {

//    private SyncDataFixDaoImpl syncDataFixDao
//
//    private InterfaceMonitorDataDao interfaceMonitorDataDao
//
//    private SyncLogDao syncLogDao
//    ConfigCenterConfig config
//    def cache = []
//
//    def setup() {
////        interfaceMonitorDataDao = Mock(InterfaceMonitorDataDao) {
////            batchUpsertInterfaceMonitorData(*_) >> { args ->
////                def dataList = args[1] as Collection<InterfaceMonitorData>
////                def res = []
////                dataList.each {
////                    println(args[0]+ ": "+ it.getId())
////                    res.add(it.getId())
////                }
////                return res
////            }
////        }
//        def store = Mock(InterfaceMonitorMongoStore) {
//            getOrCreateCollection(_) >> {
//                def collection = Mock(MongoCollection) {
//                    bulkWrite(*_) >> {
//                        def res = Mock(BulkWriteResult) {
//                            wasAcknowledged() >> true
//                        }
//                        return res
//                    }
//                }
//                return collection
//            }
//        }
//        interfaceMonitorDataDao = new InterfaceMonitorDataDao(mongoStore: store)
//        syncDataFixDao = Mock(SyncDataFixDaoImpl) {
//            insertCache(*_) >> { t ->
//                SyncDataEntity e = t[0] as SyncDataEntity
//                println(e)
//                def id = e.getTenantId()
//                println(id)
//                cache.add(id)
//            }
//            insertSyncData(*_) >> { args ->
//                cache.add(args[0])
//            }
//            removeCacheAndInsertDb(*_) >> { id ->
//                println(id)
//                cache.remove(id[0])
//            }
//        }
//
//    }
//
//    @Unroll
//    def "interfaceMonitorDataDao-#name"() {
//        given:
//        config = Mock(ConfigCenterConfig) {
//            isDropWhenOverSaveLogLimitPloy() >> false
//            getAllSaveSyncLogLimit((String) _) >> ["84801": limit, "ALL": 1000]
//        }
//        def ids = JSON.parseArray("[{\"arg\":\"{\\\"FormId\\\":\\\"BD_Customer\\\",\\\"FieldKeys\\\":\\\"FNumber\\\",\\\"FilterString\\\":\\\"((FModifyDate> {ts'2023-02-28 04:00:09'} and FModifyDate<= {ts'2023-02-28 19:06:04'} ) or (FApproveDate> {ts'2023-02-28 04:00:09'} and FApproveDate<= {ts'2023-02-28 19:06:04'} )) and FUseOrgId.FNumber in ( '000' ) \\\",\\\"OrderString\\\":\\\"FNumber asc\\\",\\\"TopRowCount\\\":null,\\\"StartRow\\\":0,\\\"Limit\\\":100,\\\"SubSystemId\\\":null}\",\"callTime\":1677582364294,\"costTime\":139,\"createTime\":1677582364433,\"dcId\":\"780777150699143168\",\"expireTime\":1677651982423,\"id\":{\"counter\":5311165,\"date\":1677582364000,\"machineIdentifier\":13958299,\"processIdentifier\":27631,\"time\":1677582364000,\"timeSecond\":1677582364,\"timestamp\":1677582364},\"logId\":\"J-E.84801.0.BD_Customer.1WrOSfoi7q8\",\"objApiName\":\"BD_Customer\",\"result\":\"{\\\"data\\\":[],\\\"errCode\\\":\\\"s106240000\\\",\\\"errMsg\\\":\\\"成功\\\",\\\"traceMsg\\\":\\\"J-E.84801.-10000-erp190603-BD_Customer.BillHead\\\"}\",\"returnTime\":1677582364433,\"status\":1,\"tenantId\":\"84801\",\"timeFilterArg\":{\"endTime\":1677582364052,\"includeDetail\":true,\"limit\":100,\"offset\":0,\"startTime\":1677528009101},\"traceId\":\"J-E.84801.-10000-erp190603-BD_Customer.BillHead\",\"type\":\"queryMasterBatch\"}]", InterfaceMonitorData.class)
//        Class c = InterfaceMonitorDataDao.class
//        println(c.getMethod("batchUpsertInterfaceMonitorData", String.class, Collection.class).getParameters())
//        when:
//        def proxy = getAspect(interfaceMonitorDataDao)
//        proxy.batchUpsertInterfaceMonitorData("84801", ids)
//        // TODO: 覆盖超额的情况下
//        then:
//        noExceptionThrown()
//        where:
//        name    | limit
//        "不超额"   | 1000
//        "超额"    | 0
//    }
//
//    def "syncDataFixDao"() {
//        given:
//        def ids = JSON.parseObject("{\"createTime\":*************,\"destData\":{\"object_describe_api_name\":\"PriceBookObj\",\"tenant_id\":\"84801\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"active_status\":\"1\",\"name\":\"新价目表01\",\"remark\":\"新价目表01-编辑明细-移除明细新增明细-编辑去掉客户---编辑加客户\",\"apply_account_range\":\"{\\\"type\\\":\\\"FIXED\\\",\\\"value\\\":\\\"FIXED\\\"}\",\"_id\":\"63dcc1503d59930001740b3f\"},\"destDataId\":\"63dcc1503d59930001740b3f\",\"destEventType\":2,\"destObjectApiName\":\"PriceBookObj\",\"destTenantId\":\"84801\",\"destTenantType\":1,\"id\":\"63dccb82f2ab6a70dc82e083\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"84801\",\"FDescription\":\"新价目表01-编辑明细-移除明细新增明细-编辑去掉客户---编辑加客户\",\"object_describe_api_name\":\"BD_SAL_PriceList.BillHead\",\"name\":\"XSJMB0145\",\"_id\":\"816007\",\"FName\":\"新价目表01\"},\"sourceDataId\":\"816007\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_SAL_PriceList.BillHead\",\"sourceTenantId\":\"84801\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.84801.0.BD_SAL_PriceList.1VMaCPlXjWg.0.0\",\"syncPloyDetailSnapshotId\":\"ad41e489f81d44e48982efd17061190f\",\"tenantId\":\"84801\",\"updateTime\":1677238417169}", SyncDataEntity.class)
//        def proxy = getAspect(syncDataFixDao)
//        when:
//        proxy.insertCache(ids)
//        proxy.removeCacheAndInsertDb("84801")
//        then:
//        cache.size() == 0
//    }
//
//    def "syncDataFixDao-insertSyncData"() {
//        given:
//        def ids = JSON.parseObject("{\"createTime\":*************,\"destData\":{\"object_describe_api_name\":\"PriceBookObj\",\"tenant_id\":\"84801\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"active_status\":\"1\",\"name\":\"新价目表01\",\"remark\":\"新价目表01-编辑明细-移除明细新增明细-编辑去掉客户---编辑加客户\",\"apply_account_range\":\"{\\\"type\\\":\\\"FIXED\\\",\\\"value\\\":\\\"FIXED\\\"}\",\"_id\":\"63dcc1503d59930001740b3f\"},\"destDataId\":\"63dcc1503d59930001740b3f\",\"destEventType\":2,\"destObjectApiName\":\"PriceBookObj\",\"destTenantId\":\"84801\",\"destTenantType\":1,\"id\":\"63dccb82f2ab6a70dc82e083\",\"isDeleted\":false,\"operatorId\":\"-10000\",\"remark\":\"success\",\"sourceData\":{\"tenant_id\":\"84801\",\"FDescription\":\"新价目表01-编辑明细-移除明细新增明细-编辑去掉客户---编辑加客户\",\"object_describe_api_name\":\"BD_SAL_PriceList.BillHead\",\"name\":\"XSJMB0145\",\"_id\":\"816007\",\"FName\":\"新价目表01\"},\"sourceDataId\":\"816007\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"BD_SAL_PriceList.BillHead\",\"sourceTenantId\":\"84801\",\"sourceTenantType\":2,\"status\":6,\"syncLogId\":\"J-E.84801.0.BD_SAL_PriceList.1VMaCPlXjWg.0.0\",\"syncPloyDetailSnapshotId\":\"ad41e489f81d44e48982efd17061190f\",\"tenantId\":\"84801\",\"updateTime\":1677238417169}", SyncDataEntity.class)
//        def proxy = getAspect(syncDataFixDao)
//        when:
//        // TODO: 同样的，SpeedLimitAspect的53行获取不到parameters，不能直接Mock触发的DAO
//        proxy.insertSyncData("84801", null)
//        then:
//        thrown(Exception)
//    }
//
//    private <T> T getAspect(T o) {
//        def aop = new SpeedLimitAspect(configCenterConfig: config)
//        println("get " + o)
//
//        // 使用AspectJProxyFactory来创建被增强的代理对象
//        AspectJProxyFactory factory = new AspectJProxyFactory(o);
//        factory.setProxyTargetClass(true) // 使用CGLib代理
//        factory.addAspect(aop); // 添加切面到代理工厂中
//        return factory.getProxy(); // 获取代理对象
//    }
}
