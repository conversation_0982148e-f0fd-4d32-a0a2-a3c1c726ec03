package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import cn.hutool.core.date.TimeInterval
import com.fxiaoke.open.erpsyncdata.dbproxy.CommonSpringTest
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Ignore
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/26
 */
// TODO: Spring
@Ignore
@Slf4j
class ErpObjManagerTest extends Specification {

    ErpObjManager erpObjManager
    ErpObjectDao erpObjectDao
    ErpObjectRelationshipDao erpObjectRelationshipDao;

    def ti = new TimeInterval()
    def tenantId = "84141"
    def tenantId2 = "83952"
    def dcId = "786671266423242752"
    def o1 = "BD_PaymentCondition.BillHead"
    def o2 = "BD_PaymentCondition.PayRecCondtionEntry"

    def setup() {
        erpObjectDao = Mock(ErpObjManager)
        erpObjectRelationshipDao = Mock(ErpObjectRelationshipDao)
        erpObjManager = new ErpObjManager()

    }

    @Ignore
    def "测试获取对象、缓存"() {
        // TODO: 测试不通过
        def tenantId = "84141"
        def dcId = "786671266423242752"
        def o1 = "BD_PaymentCondition.BillHead"
        def o2 = "BD_PaymentCondition.PayRecCondtionEntry"
        erpObjectDao.getByObjApiName(tenantId, null, o1)
        ti.restart()
        def obj1 = erpObjManager.getErpObj(tenantId, o1)
        log.info("cost {}", ti.intervalRestart())
        def obj2 = erpObjManager.getErpObj(tenantId, o2)
        def cost2 = ti.intervalRestart()
        log.info("cost {}", cost2)
        def obj11 = erpObjManager.getErpObj(tenantId, o1)
        def cost11 = ti.intervalRestart()
        log.info("cost {}", cost11)

        def dobj1 = erpObjManager.getErpObj(tenantId, dcId, o1)
        log.info("cost {}", ti.intervalRestart())
        def dobj2 = erpObjManager.getErpObj(tenantId, dcId, o2)
        def dcost2 = ti.intervalRestart()
        log.info("cost {}", dcost2)
        def dobj11 = erpObjManager.getErpObj(tenantId, dcId, o1)
        def dcost11 = ti.intervalRestart()
        log.info("cost {}", dcost11)

        erpObjectDao
        expect:
        obj1.getErpObjectApiName() == o1
        obj2.getErpObjectApiName() == o2
        obj1 == obj11
        cost2 > 10
        cost11 < 20

        dobj1.getErpObjectApiName() == o1
        dobj2.getErpObjectApiName() == o2
        dobj1 == dobj11
        dcost2 > 10
        dcost11 < 10
    }


    def "测试获取对象relation、缓存"() {
        erpObjectDao.getByObjApiName(tenantId, null, o1)
        ti.restart()
        def obj1 = erpObjManager.getRelation(tenantId, o1)
        log.info("cost1 {}", ti.intervalRestart())
        def obj2 = erpObjManager.getRelation(tenantId, o2)
        def cost2 = ti.intervalRestart()
        log.info("cost2 {}", cost2)
        def obj11 = erpObjManager.getRelation(tenantId, o1)
        def cost11 = ti.intervalRestart()
        log.info("cost3 {}", cost11)

        erpObjectDao
        expect:
        obj1.getErpSplitObjectApiname() == o1
        obj2.getErpSplitObjectApiname() == o2
        obj1 == obj11
        cost2 > 10
        cost11 < 10
    }

    def "测试缓存销毁"() {
        erpObjectRelationshipDao.listByTenantId(tenantId)
        ti.restart()
        def map1 = erpObjManager.getMainSeqMap(tenantId)
        def t1 = ti.intervalRestart()
        log.info("cost1,{},{}", t1, map1)

        def map2 = erpObjManager.getMainSeqMap(tenantId2)
        def t2 = ti.intervalRestart()
        log.info("cost2,{},{}", t2, map2)


        def map11 = erpObjManager.getMainSeqMap(tenantId)
        def t11 = ti.intervalRestart()
        log.info("cost11,{},{}", t11, map11)

        def m1 = erpObjManager.getRealMainSplitObjApiNamesMap(tenantId, dcId)
        def mt1 = ti.intervalRestart()
        log.info("mcost1,{},{}", mt1, m1)


        def m2 = erpObjManager.getRealMainSplitObjApiNamesMap(tenantId, dcId)
        def mt2 = ti.intervalRestart()
        log.info("mcost1,{},{}", mt2, m2)
        erpObjectRelationshipDao.invalidCacheErpObj(tenantId, dcId)

        ti.restart()
        def map12 = erpObjManager.getMainSeqMap(tenantId)
        def t12 = ti.intervalRestart()
        log.info("cost4,{},{}", t12, map12)

        def m12 = erpObjManager.getRealMainSplitObjApiNamesMap(tenantId, dcId)
        def mt12 = ti.intervalRestart()
        log.info("mcost12,{},{}", mt12, m12)
        erpObjectRelationshipDao.invalidCacheErpObj(tenantId, dcId)


        expect:
        t1 > 100
        t2 > 100
        map1 != map2
        map11 == map1
        t11 < 10
        mt1 > 10
        mt2 < 10
        m2 == m1
        t12 > 100
        mt12 > 10

    }
}
