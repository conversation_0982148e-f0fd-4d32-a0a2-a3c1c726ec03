package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.facishare.converter.EIEAConverter
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload
import com.facishare.fsi.proxy.service.NFileStorageService
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.easyexcel.CustomCellWriteHandler
import com.fxiaoke.open.erpsyncdata.dbproxy.model.BuildExcelFileResult
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ExcelSheetArg
import com.fxiaoke.open.erpsyncdata.dbproxy.vo.SyncDataNotifyExcelVo
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.collect.Lists
import org.apache.commons.io.FileUtils
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime

import static com.fxiaoke.open.erpsyncdata.dbproxy.manager.TestExcelClass.TestClass
import static com.fxiaoke.open.erpsyncdata.dbproxy.manager.TestExcelClass.TestClass2

/**
 * <AUTHOR>
 * @date 2023/5/4 20:25:58
 */
@Unroll
class DBFileManagerTest extends Specification {

    I18NStringManager i18NStringManager = new I18NStringManager()
    String tenantId = "12345"
    DBFileManager manager
    boolean flag = true

    def setup() {
        def eieaConverter = Mock(EIEAConverter) {
            enterpriseIdToAccount(*_) >> "1000"
        }
        def handler = new CustomCellWriteHandler(i18NStringManager, tenantId, "zh-CN")
        def nFileStorageService = Mock(NFileStorageService) {
            nTempFileUpload(*_) >> {
                if (flag) {
                    // 上传成功，返回一个文件名
                    return new NTempFileUpload.Result("temp")
                }
                return null
            }
        }
        manager = new DBFileManager(eieaConverter: eieaConverter, i18NStringManager: Mock(I18NStringManager), storageService: nFileStorageService)
    }


    def "测试生成的excel"() {
        when:
        def stream = manager.createExcelStream("", null, "测试.xlsx", [
                new ExcelSheetArg(sheetName: "测试1", dataList: [new TestClass("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass.class),
                new ExcelSheetArg(sheetName: "测试2", dataList: [new TestClass2("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass2.class),
                new ExcelSheetArg(sheetName: "测试3", dataList: [new TestClass2("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass2.class, headers: [["测试标题1"], ["测试标题2"]]),
                new ExcelSheetArg(sheetName: "测试4", dataList: [new TestClass2("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass2.class, headers: [["测试标题1", "测试标题2-1"], ["测试标题1", "测试标题2-2"]]),
        ])

        then:
        FileUtils.writeByteArrayToFile(new File("/Users/<USER>/workspaces/java/fs-erp-sync-data-git/common-db-proxy/src/test/resources/测试.xlsx"), stream.toByteArray())
    }

    def "测试构建excel表格，并上传到临时文件 - 上传#name"() {
        given:
        BuildExcelFileResult.Arg<SyncDataNotifyExcelVo> syncDataNotifyExcelVoArg = new BuildExcelFileResult.Arg();
        syncDataNotifyExcelVoArg.setTenantId(tenantId);
        syncDataNotifyExcelVoArg.setFileName(i18NStringManager.getByEi2(I18NStringEnum.s626.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s626.getI18nValue(), tenantId, LocalDateTime.now().toString()),
                Lists.newArrayList(tenantId, LocalDateTime.now().toString())));
        syncDataNotifyExcelVoArg.setDataList(["test0", "test1", "test2"]);
        syncDataNotifyExcelVoArg.setSheetNames(Lists.newArrayList(i18NStringManager.getByEi(I18NStringEnum.s625, tenantId)));
        flag = success

        when:
        Exception ex
        Result<BuildExcelFileResult.Result> excelResult
        try {
            excelResult = manager.buildExcelFileResult(i18NStringManager, null, tenantId, syncDataNotifyExcelVoArg)
        } catch (Exception e) {
            ex = e
        }

        then:
        if (success)
            excelResult.isSuccess()
        else
            ex instanceof ErpSyncDataException


        where:
        name   | success
        "成功" | true
        "失败" | false
    }

    def "test writeAndUploadExcel"() {
        expect:
        manager.writeAndUploadExcel(tenantId, "测试.xlsx", [
                new ExcelSheetArg(sheetName: "测试1", dataList: [new TestClass("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass.class),
                new ExcelSheetArg(sheetName: "测试2", dataList: [new TestClass2("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass2.class),
                new ExcelSheetArg(sheetName: "测试3", dataList: [new TestClass2("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass2.class, headers: [["测试标题1"], ["测试标题2"]]),
                new ExcelSheetArg(sheetName: "测试4", dataList: [new TestClass2("test1", "测试1"), new TestClass("test2", "测试2")], clazz: TestClass2.class, headers: [["测试标题1", "测试标题2-1"], ["测试标题1", "测试标题2-2"]]),
        ], null).isSuccess()
    }
}
