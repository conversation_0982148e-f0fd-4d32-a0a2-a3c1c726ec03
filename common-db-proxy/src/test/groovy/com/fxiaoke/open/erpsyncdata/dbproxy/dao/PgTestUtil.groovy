package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import ch.qos.logback.classic.Level
import cn.hutool.core.io.FileUtil
import cn.hutool.db.sql.SqlExecutor
import com.fxiaoke.open.erpsyncdata.common.util.LogUtil
import org.apache.ibatis.session.SqlSession
import org.apache.ibatis.session.SqlSessionFactory
import org.apache.ibatis.session.SqlSessionFactoryBuilder

/**
 *
 * 注意事项：
 * <br/>
 * - pg执行可以先offset 再limit，但是h2单测必须符合规范：先limit，再offset
 * <br/>
 * - 建索引不支持  concurrently if not exists。
 * <br/>
 * - 需要在src/test/resources/mybatisTestConfiguration/mybatis-config.xml 中，增加测试的mapper
 * <AUTHOR> (^_−)☆
 */
class PgTestUtil {
    private static SqlSession session
    private static SqlSessionFactory factory
    static {
        factory = new SqlSessionFactoryBuilder()
                .build(FileUtil.getInputStream("mybatisTestConfiguration/mybatis-config.xml"))
        session = factory.openSession(true)
        LogUtil.changeLevel("parseSql", Level.ALL)
        SqlExecutor.execute(session.getConnection(), "CREATE ALIAS trans_sync_status FOR \"com.fxiaoke.open.erpsyncdata.dbproxy.dao.PgTestUtil.transSyncStatus\"")
    }

    // Java方法实现
    static int transSyncStatus(int status) {
        if (status == 6) {
            return 1;
        } else if (status == 1 || status == 3 || status == 5) {
            return 2;
        } else {
            return 3;
        }
    }


    static <T> T getMapper(Class<T> aClass) {
        return factory.getConfiguration().getMapper(aClass, session)
    }

    static SqlSession getSession() {
        return session
    }


}
