package com.fxiaoke.open.erpsyncdata;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fxiaoke.open.erpsyncdata.common.interceptor.TenantShardingTableInterceptor;
import io.vavr.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import spock.lang.Ignore;
import spock.lang.Specification;

import java.util.function.Consumer;

/**
 * 由于在common里面引入了不应该引入的service，这个Test基类已无法使用
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
// TODO: Spring
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(locations = {"classpath*:spring/spring-db-test.xml"})
@Ignore
@Slf4j
public abstract class BaseDbTest extends Specification {
//    @BeforeClass
//    public static void before() {
//        System.setProperty("process.profile", "fstest");
//        System.setProperty("process.name", "fs-erp-sync-data");
//    }

    /**
     * 调用该方法可以打印专表sql
     * @param tenantId
     */
    protected void statSql(String tenantId){
        TenantShardingTableInterceptor.switchStatistics(tenantId, true);
        TenantShardingTableInterceptor.setStatConsumer(new StatConsumer());
    }


    private static class StatConsumer implements Consumer<Tuple3<String, String, Object>> {

        @Override
        public void accept(Tuple3<String, String, Object> tuple3) {
            String param = JSON.toJSONString(tuple3._3, SerializerFeature.WriteMapNullValue);
            String sql = tuple3._2;
            sql = StrUtil.removeAllLineBreaks(sql);
            String tenantId = tuple3._1;
            log.info("stat-{},{},{}", tenantId, sql, param);
        }
    }
}
