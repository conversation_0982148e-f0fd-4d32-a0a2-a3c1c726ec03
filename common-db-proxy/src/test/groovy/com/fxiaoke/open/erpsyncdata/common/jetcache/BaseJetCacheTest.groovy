package com.fxiaoke.open.erpsyncdata.common.jetcache

import cn.hutool.core.collection.CollUtil
import com.alicp.jetcache.CacheResultCode
import com.alicp.jetcache.event.CacheEvent
import com.alicp.jetcache.event.CacheGetEvent
import com.alicp.jetcache.event.CachePutEvent
import com.alicp.jetcache.event.CacheRemoveEvent
import com.alicp.jetcache.external.AbstractExternalCache
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.common.config.AutoConfConfig
import com.fxiaoke.open.erpsyncdata.common.config.MockRedissonConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.config.JetCacheConfig
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.util.function.Predicate
import java.util.function.Supplier

/**
 * 会启动spring，但仅加载指定的bean
 * <AUTHOR> (^_−)☆
 */
// TODO: Spring
//@ContextConfiguration(
//        classes = [AutoConfConfig.class, MockRedissonConfig.class, JetCacheConfig.class, TestBaseJetCacheConfig.class])
class BaseJetCacheTest extends Specification {//BaseSpockTest {
    private static ThreadLocal<List<CacheEvent>> latestCacheEventsHolder = ThreadLocal.withInitial(
            new Supplier<List<CacheEvent>>() {
                @Override
                List<CacheEvent> get() {
                    return new ArrayList<>()
                }
            })

    protected static List<CacheEvent> getRemoveCacheEvents() {
        def localHold = latestCacheEventsHolder.get()
        def list = CollUtil.newArrayList(localHold)
        localHold.clear()
        return list
    }

    static class TestBaseJetCacheConfig extends Specification {
        @Bean
        CustomCacheMonitor checkCacheMonitor() {
            return new CustomCacheMonitor() {
                @Override
                void afterOperation(CacheEvent event) {
                    latestCacheEventsHolder.get().add(event)
                }
            }
        }
    }

    protected boolean testEvent(CacheEvent cacheEvent, boolean expectRemote, Predicate<? extends CacheEvent> predicate) {
        def cache = cacheEvent.getCache()
        def realRemote = cache instanceof AbstractExternalCache
        if (realRemote && !expectRemote) {
            return false
        }
        return predicate.test(cacheEvent);
    }


    protected boolean testGet(CacheEvent cacheEvent, boolean expectRemote, String key, CacheResultCode resultCode, Object value) {
        return testEvent(cacheEvent, expectRemote, {
            if (it instanceof CacheGetEvent) {

                def result = it.getResult()
                return (Objects.equals(key, it.getKey())
                        && resultCode == result.getResultCode()
                        && Objects.equals(result.getValue(), value))
            }
            return false;
        })
    }


    protected boolean testRemove(CacheEvent cacheEvent, boolean expectRemote, String key) {
        return testEvent(cacheEvent, expectRemote, {
            if (it instanceof CacheRemoveEvent) {
                def result = it.getResult()
                return (Objects.equals(key, it.getKey())
                        && result.isSuccess())
            }
            return false;
        })
    }


    protected boolean testPut(CacheEvent cacheEvent, boolean expectRemote, String key, Object value) {
        return testEvent(cacheEvent, expectRemote, {
            if (it instanceof CachePutEvent) {
                def result = it.getResult()
                return (Objects.equals(key, it.getKey())
                        && result.isSuccess()
                        && Objects.equals(it.getValue(), value))
            }
            return false;
        })
    }
}
