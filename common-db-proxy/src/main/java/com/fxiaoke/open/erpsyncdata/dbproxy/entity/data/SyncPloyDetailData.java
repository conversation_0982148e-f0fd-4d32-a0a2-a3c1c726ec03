package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import cn.hutool.core.util.ObjectUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.BaseData;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SyncPloyDetailData extends BaseData implements Serializable {
    private String id;
    private String syncPloyId;
    @TenantID
    private String tenantId;
    /**
     * {@link  SyncPloyDetailStatusEnum}
     */
    private Integer status;
    private Integer sourceTenantType;
    private String sourceObjectApiName;
    private Integer destTenantType;
    private String destObjectApiName;
    private FieldMappingsData fieldMappings;
    private DetailObjectMappingsData detailObjectMappings = new DetailObjectMappingsData();
    private SyncRulesData syncRules;
    private SyncConditionsData syncConditions;
    private List<SyncConditionsData> detailObjectSyncConditions = Lists.newArrayList();
    private IntegrationStreamNodesData integrationStreamNodes;
    private String beforeFuncApiName;
    private String duringFuncApiName;
    private String afterFuncApiName;
    /** 源数据中心 */
    private String sourceDataCenterId;
    /** 目标数据中心 */
    private String destDataCenterId;

    @Override
    public String toString() {
        return "SyncPloyDetailData";
    }
}
