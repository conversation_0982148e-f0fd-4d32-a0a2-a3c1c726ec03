package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/7 21:10:41
 */
@Data
public class TimeMonitor implements Serializable {
    @ApiModelProperty("监控时间")
    private Long time;
    @ApiModelProperty("总调用次数")
    private Long total;
    @ApiModelProperty("对象调用次数")
    private List<ObjectMonitor> objectMonitors;

    @Data
    public static class ObjectMonitor implements Serializable {
        @ApiModelProperty("对象apiName")
        private String apiName;
        @ApiModelProperty("对象名称")
        private String name;
        @ApiModelProperty("调用次数")
        private Long count;
    }

}
