package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.factory;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DoubleWrite;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.AspectSpelUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.proxy.Enhancer;
import net.sf.cglib.proxy.MethodInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:38:42
 */
@Setter
@Slf4j
public class GrayAndDoubleWriteFactory<T> implements FactoryBean<T>, InitializingBean {

    private final Class<T> type;

    private T oldBean;

    private T newBean;

    public GrayAndDoubleWriteFactory(Class<T> type) {
        this.type = type;
    }

    protected static final String GRAY_AND_DOUBLE_WRITE_KEY_PREFIX = "GAD_";

    protected boolean ignoreDoubleWriteError = true;

    protected String grayKey;

    /**
     * 灰度
     */
    protected TenantGrayRule grayRule = TenantGrayRule.DEFAULT;

    /**
     * 双写新库
     */
    protected TenantGrayRule doubleWriteNewRule = TenantGrayRule.DEFAULT;

    /**
     * 双写旧库
     */
    protected TenantGrayRule doubleWriteOldRule = TenantGrayRule.DEFAULT;

    @Override
    public T getObject() throws Exception {
        return (T) Enhancer.create(type, (MethodInterceptor) (o, method, args, methodProxy) -> {
            if (method.getDeclaringClass().equals(Object.class) || StringUtils.startsWith(method.getName(), "$")) {
                return method.invoke(newBean, args);
            }

            String tenantId = getTenantId(method, args);
            T runningBean;
            T backupBean;
            TenantGrayRule doubleWriteRule;
            if (isGray(tenantId)) {
                runningBean = newBean;
                backupBean = oldBean;
                doubleWriteRule = doubleWriteOldRule;
            } else {
                runningBean = oldBean;
                backupBean = newBean;
                doubleWriteRule = doubleWriteNewRule;
            }

            final Object invoke = method.invoke(runningBean, args);
            if (Objects.isNull(method.getAnnotation(DoubleWrite.class))) {
                return invoke;
            }

            if (needDoubleWrite(tenantId, doubleWriteRule)) {
                try {
                    method.invoke(backupBean, args);
                } catch (Exception e) {
                    log.error("双写旧数据失败, class:{} method:{} args:{}", type.getSimpleName(), method.getName(), args, e);
                    if (!ignoreDoubleWriteError) {
                        throw new RuntimeException(e);
                    }
                }
            }

            return invoke;
        });
    }

    private boolean needDoubleWrite(final String tenantId, TenantGrayRule doubleWriteRule) {
        return doubleWriteRule.isAllow(tenantId);
    }

    private boolean isGray(final String tenantId) {
        // 先不管从normal转为gray的情况,要的话需要迁移后, 单独配置
        return grayRule.isAllow(tenantId);
    }

    private static String getTenantId(final Method method, final Object[] args) {
        final Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            final Parameter parameter = parameters[i];
            final TenantID annotation = parameter.getAnnotation(TenantID.class);
            if (Objects.nonNull(annotation)) {
                if (StringUtils.isEmpty(annotation.value())) {
                    return (String) args[i];
                }
                final StandardEvaluationContext context = AspectSpelUtil.getStandardEvaluationContext(AspectSpelUtil.getParameters(method), args, null);
                return AspectSpelUtil.getSpelValue(annotation.value(), context);
            }
        }

        return Arrays.stream(args)
                .filter(arg -> arg instanceof String)
                .map(arg -> (String) arg)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No enterprise ID specified, method:" + method.getName()));
    }

    @Override
    public Class<T> getObjectType() {
        return type;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        final String key = GRAY_AND_DOUBLE_WRITE_KEY_PREFIX + grayKey;
        ConfigFactory.getConfig("erp-sync-data-all", "GrayAndDoubleWrite", config -> {
            grayRule = new TenantGrayRule(config.get(key, ""));
            doubleWriteNewRule = new TenantGrayRule(config.get(key + "_write_new", ""));
            doubleWriteOldRule = new TenantGrayRule(config.get(key + "_write_old", ""));
            ignoreDoubleWriteError = config.getBool("ignoreDoubleWriteError", true);
        });
    }

    public static class TenantGrayRule {
        private boolean white;

        private boolean all;
        private Set<String> tenantIds;

        public static TenantGrayRule DEFAULT = new TenantGrayRule("");

        public TenantGrayRule(String config) {
            white = false;

            if (StringUtils.isEmpty(config)) {
                // 默认全部不灰度
                all = true;
                tenantIds = new HashSet<>();
                return;
            }

            final String s = StringUtils.substringBefore(config, ":");
            if (StringUtils.isEmpty(s)) {
                // 默认全部不灰度
                all = true;
                tenantIds = new HashSet<>();
                return;
            }

            if (Objects.equals(s, "white") || Objects.equals(s, "allow")) {
                white = true;
            }

            final String after = StringUtils.substringAfter(config, ":");
            if (StringUtils.isEmpty(after)) {
                white = !white;
                all = true;
                tenantIds = new HashSet<>();
                return;
            }

            final List<String> strings = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(after);
            tenantIds = Sets.newHashSet(strings);
            all = tenantIds.contains("*");
        }

        public boolean isAllow(String tenantId) {
            if (all) {
                return white;
            }

            return tenantIds.contains(tenantId) == white;
        }
    }
}
