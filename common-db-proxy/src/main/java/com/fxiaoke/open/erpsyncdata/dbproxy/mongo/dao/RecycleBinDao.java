package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.RecycleType;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.SafeEnumCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.BaseLogMongoStore;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.RecycleBinData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.RecycleBinData.Fields;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.github.trace.TraceContext;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/26
 */
@Slf4j
@Repository
public class RecycleBinDao extends BaseLogMongoStore<RecycleBinData> {

    protected RecycleBinDao() {
        super(CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromCodecs(SafeEnumCodec.of(RecycleType.class)),
                CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build())));
    }

    @Override
    public String getCollName(String tenantId) {
        return "recycle_bin_data";
    }

    public List<IndexModel> buildIndexes(String tenantId) {
        List<IndexModel> indexModels = new ArrayList<>();
        indexModels.add(new IndexModel(Indexes.ascending(Fields.tenantId, Fields.recycleType, Fields.id), new IndexOptions().background(true)));
        indexModels.add(new IndexModel(Indexes.ascending(Fields.createTime),
                new IndexOptions().expireAfter(2L, TimeUnit.DAYS).background(true)));
        return indexModels;
    }

    private RecycleBinData buildData(String tenantId, RecycleType type, Object data) {
        return buildData(tenantId, type, data, null);
    }

    private RecycleBinData buildData(String tenantId, RecycleType type, Object data, String actionId) {
        Map<String, Object> beanMap = JSON.parseObject(JSON.toJSONString(data, JSONWriter.Feature.WriteEnumUsingToString, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.WriteNulls), Map.class);
        Document dataDoc = new Document(beanMap);
        return buildData(tenantId, type, actionId, dataDoc, null);
    }

    private RecycleBinData buildDataJson(String tenantId, RecycleType type, Object data, String actionId) {
        String json = JSON.toJSONString(data, JSONWriter.Feature.WriteEnumUsingToString, JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.WriteNulls);
        return buildData(tenantId, type, actionId, null, json);
    }

    private static RecycleBinData buildData(String tenantId, RecycleType type, String actionId, Document dataDoc, String recycleJson) {
        actionId = StringUtils.defaultIfBlank(actionId, TraceUtil.get());
        String operatorId = StrUtil.nullToDefault(TraceContext.get().getEmployeeId(), "-10000");
        RecycleBinData recycleBinData = RecycleBinData.builder()
                .tenantId(tenantId)
                .operatorId(operatorId)
                .recycleType(type)
                .actionId(actionId)
                .recycleData(dataDoc)
                .recycleJson(recycleJson)
                .createTime(new Date()).build();
        return recycleBinData;
    }

    public void insertIgnore(String tenantId, RecycleType type, Object data) {
        try {
            insert(tenantId, type, data);
        } catch (Exception e) {
            log.warn("insert exception", e);
        }
    }

    public void insert(String tenantId, RecycleType type, Object data) {
        MongoCollection<RecycleBinData> coll = getOrCreateCollection(tenantId);
        RecycleBinData recycleBinData = buildData(tenantId, type, data);
        coll.insertOne(recycleBinData);
    }

    public void batchInsert(String tenantId, RecycleType type, List<?> data) {
        List<RecycleBinData> recycleBinDataList = CollUtil.map(data, v -> buildData(tenantId, type, v), true);
        MongoCollection<RecycleBinData> coll = getOrCreateCollection(tenantId);
        coll.insertMany(recycleBinDataList);
    }

    public void batchInsertJson(String tenantId, RecycleType type, List<?> data, String actionId) {
        List<RecycleBinData> recycleBinDataList = CollUtil.map(data, v -> buildDataJson(tenantId, type, v, actionId), true);
        MongoCollection<RecycleBinData> coll = getOrCreateCollection(tenantId);
        coll.insertMany(recycleBinDataList);
    }

    /**
     * @param beforeId 为null时不增加条件
     */
    public List<RecycleBinData> listRecent(String tenantId, RecycleType type, String beforeId, int limit) {
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.recycleType, type)
        );
        if (beforeId != null) {
            ObjectId objectId = new ObjectId(beforeId);
            filters = Filters.and(filters, Filters.lt("_id", objectId));
        }
        Bson sorts = Sorts.descending("_id");
        List<RecycleBinData> result = new ArrayList<>();
        getOrCreateCollection(tenantId).find(filters).sort(sorts).limit(limit).into(result);
        return result;
    }
}
