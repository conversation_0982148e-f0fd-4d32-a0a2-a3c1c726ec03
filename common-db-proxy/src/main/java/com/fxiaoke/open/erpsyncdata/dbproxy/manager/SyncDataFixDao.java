package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DeleteWithLogicBySourceData;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 过渡期间
 * 放到这里是因为如果放到dao包下会被mybatis扫到创建bean
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/4/28
 */
public interface SyncDataFixDao{

    //copy from TenantBaseMapper

    /**
     * @param tenantId
     * @return this
     * @deprecated pg访问历史遗留，实际上无作用，别调用
     */
    @Deprecated
    default SyncDataFixDao setTenantId(String tenantId) {
        return this;
    }

    /**
     * 忽略结果
     *
     * @param t
     */
    void insertCache(SyncDataEntity t);

    void removeCacheAndInsertDb(String tenantId);

    Map<String, SyncDataEntity> getTenantSyncDataCache(String tenantId);


    List<SyncDataEntity> listByIds( String tenantId,
                                    Collection<String> ids);
    List<SyncDataEntity> listSimpleByIds( String tenantId,
                                    Collection<String> ids);
    
    default List<SyncDataEntity> listByIdsCachePri( String tenantId,
                                    Collection<String> ids){
        return listByIds(tenantId, ids);
    }

    List<SyncDataEntity> listBySourceData( String tenantId,
                                           String sourceTenantId,
                                           List<String> sourceObjectApiName,
                                           List<String> sourceDataIds,
                                           String destTenantId,
                                           List<String> destObjectApiName,
                                           Long startLogTime,
                                           Long endLogTime);



    int updateDestDataById(String tenantId, String id, ObjectData destData);

    int updateSourceDataById(String tenantId, String id, ObjectData sourceData,Integer destEventType);

    int updateRemarkAndStatus(String tenantId, String id, String remark);

    int updateWaitingStatus(String tenantId, String id, Integer status,String remark);

    List<SyncDataEntity> listByStatusListAndEndUpdateTime( String tenantId,  List<Integer> statusList,  Long startUpdateTime,  Long endUpdateTime,  Integer offset,  Integer limit);


    int deleteSingleSyncData( String tenantId,  Long updateTime,  String id);

    Integer deleteWithLogicBySourceDatas( String tenantId,  List<DeleteWithLogicBySourceData> deleteWithLogicBySourceDataList);

    int deleteSyncDatas( String tenantId,  String sourceObjApiName,  String destObjApiName);


    //copy from SyncDataDao

    /**
     * 从线程变量取出数据
     * @param tenantId
     * @param id
     * @return
     */
    SyncDataEntity getFromThreadLocal(String tenantId,  String id);

    SyncDataEntity getById( String tenantId,  String id,String... returnFields);
    SyncDataEntity getByIdFromDb( String tenantId,  String id, String... returnFields);
    SyncDataEntity getSimple(String tenantId, String id);
    SyncDataEntity getSimpleFromDb(String tenantId, String id);


    int updateStatusAndDestDataIdBySuccess(String tenantId, String id, String destDataId, Integer newStatus,
                                           String remark);

    int updateDestEventTypeAndDestDataIdAndStatus( String tenantId,  String id,  Integer destEventType,  String destDataId,  Integer oldStatus,
                                                   Integer newStatus,  Long updateTime);

    int updateStatus(String tenantId, String id, Integer newStatus, String remark, String errorCode);

    int updateStatusByIds(String tenantId, List<String> ids, Integer newStatus, String remark, String errorCode);

    void updateNodeMsg(String tenantId,String sourceObjApiName,String lastNodeName,String lastNodeStatus, String dataVersion,Boolean reverseWrite2CrmFailed,
                       String reverseWrite2CrmFailedRemark,Boolean afterFuncFailed,String afterFuncFailedRemark);

    void updateNodeMsgBySyncDataId(String tenantId,String syncDataId,String lastNodeName,String lastNodeStatus, String dataVersion);

    int updateDestData( String tenantId, String id,  String destDataId,  ObjectData destData,  Integer oldStatus,
                        Integer newStatus,  Long updateTime);

    int updateNeedReturnData(String tenantId, String id, ObjectData needReturnData,  Long updateTime);

    long countByTenantId( String tenantId);

}
