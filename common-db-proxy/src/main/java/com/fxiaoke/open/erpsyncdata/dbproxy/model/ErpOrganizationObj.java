package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ErpOrganizationObj extends ObjectData {
    private static final long serialVersionUID = 8525184133615083949L;

    public static ErpOrganizationObj convert(Map map){
        if (MapUtils.isEmpty(map)){
            return null;
        }
        ErpOrganizationObj objectData = new ErpOrganizationObj();
        objectData.putAll(map);
        return objectData;
    }

    public String getOrgId() {
        return this.getString("org_id");
    }
    public void putOrgId(String value) {
        this.put("org_id", value);
    }

    public String getOrgNumber() {
        return this.getString("org_number");
    }

    public void putOrgNumber(String value) {
        this.put("org_number", value);
    }

    public Boolean getNeedSyncWarehouse() {
        return this.getBoolean("need_sync_warehouse");
    }

    public void putNeedSyncWarehouse(Boolean value) {
        this.put("need_sync_warehouse", value);
    }

    public Boolean getNeedSyncCustomer() {
        return this.getBoolean("need_sync_customer");
    }

    public void putNeedSyncCustomer(Boolean value) {
        this.put("need_sync_customer", value);
    }

    public Boolean getNeedSyncProduct() {
        return this.getBoolean("need_sync_product");
    }

    public void putNeedSyncProduct(String value) {
        this.put("need_sync_product", value);
    }


}
