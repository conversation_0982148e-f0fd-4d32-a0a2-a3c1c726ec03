package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/22 19:44:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity(value = "integration_task", noClassnameStored = true)
@Indexes({@Index(fields = {@Field("tenantId"), @Field("taskId")}, options = @IndexOptions(background = true)),
        @Index(fields = {@Field("taskId"), @Field("status")}, options = @IndexOptions(background = true))})
public class IntegrationTaskEntity implements Serializable {

    @Id
    @Property("id")
    private ObjectId id;

    @Property("task_id")
    private String taskId;

    @TenantID
    @Property("tenant_id")
    private String tenantId;
    @Property("data_center_id")
    private String dataCenterId;
    @Property("policy_id")
    private String policyId;
    /**
     * 任务类型
     * @see Type
     */
    @Property("type")
    private String type;
    /**
     * 0.未处理 1.处理中 2.处理成功 3.处理失败
     */
    @Property("status")
    private Integer status;
    /**
     * 错误信息
     */
    @Property("message")
    private String message;

    /**
     * 任务创建时间
     */
    @Property("create_time")
    private Long createTime;


    /**
     * 修改时间
     */
    @Property("update_time")
    private Long updateTime;

    /**
     * 任务完成通知企业Id
     */
    @Property("notify_tenant_id")
    private String notifyTenantId;

    /**
     * 任务完成通知员工Id
     */
    @Property("notify_employee_ids")
    private List<Integer> notifyEmployeeIds;

    public enum Type {
        /**
         * 启用集成流
         */
        enableIntegration
    }
}
