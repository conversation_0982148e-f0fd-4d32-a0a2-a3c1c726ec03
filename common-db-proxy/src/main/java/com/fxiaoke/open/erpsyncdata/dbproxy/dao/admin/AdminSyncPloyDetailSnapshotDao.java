package com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin;


import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.ibatis.BaseMapper;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ManagedTenantReplace
public interface AdminSyncPloyDetailSnapshotDao extends BaseMapper<SyncPloyDetailSnapshotEntity>, ITenant<AdminSyncPloyDetailSnapshotDao> {
    /**
     * 根据源企业id、源对象apiName、策略状态获取最新的策略明细快照集合
     *
     * @param status 策略状态 {@link SyncPloyDetailStatusEnum}
     */
    List<SyncPloyDetailSnapshotEntity> listNewestBySourceTenantIdAndSrouceObjectApiName(@Param("sourceTenantId") String sourceTenantId, @Param("sourceObjectApiName") String sourceObjectApiName,
                                                                                        @Param("status") Integer status);

    List<SyncPloyDetailSnapshotEntity> listNewestByDestTenantIdAndDestObjectApiName(@Param("destTenantId") String destTenantId, @Param("destObjectApiName") String destObjectApiName,
                                                                                    @Param("status") Integer status);

    int batchUpdateStatusBySourceTenantIdAndDestTenantIdReverse(@Param("sourceTenantId") String sourceTenantId, @Param("destTenantId") String destTenantId, @Param("status") Integer status);

    List<SyncPloyDetailSnapshotEntity> listBySourceTenantIdAndObjectApiName(@Param("sourceTenantId") String sourceTenantId, @Param("sourceObjectApiName") String sourceObjectApiName);

    int updateStatusByPloyDetailId(@Param("syncPloyDetailId") String syncPloyDetailId, @Param("status") Integer status);

    int updateStatusById(@Param("id") String id, @Param("status") Integer status);

    int updateSyncPloyDataDetailById(@Param("id") String id, @Param("syncPloyDetailData") SyncPloyDetailData syncPloyDetailData);

    SyncPloyDetailSnapshotEntity getById(@Param("sourceTenantId") String tenantId, @Param("id") String id);

    List<SyncPloyDetailSnapshotEntity> listEnableSnapshotsBySyncPloyDetailsId(@Param("sourceTenantId") String tenantId, @Param("syncPloyDetailsId") String syncPloyDetailsId, @Param("status") Integer status);

    List<SyncPloyDetailSnapshotEntity> listEnableSnapshotOffsetOne(@Param("sourceTenantId") String tenantId, @Param("syncPloyDetailsId") String syncPloyDetailsId, @Param("status") Integer status);

    int invalidAllByTenantId(@Param("sourceTenantId")String sourceTenantId);

    int deleteByTenantId(String tenantId);

    List<SyncPloyDetailSnapshotEntity> listByTenantId(@Param("destTenantId") String tenantId);
}
