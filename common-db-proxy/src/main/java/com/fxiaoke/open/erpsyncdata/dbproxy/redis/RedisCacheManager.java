package com.fxiaoke.open.erpsyncdata.dbproxy.redis;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/7
 */
@Service
public class RedisCacheManager {
    @Autowired
    private RedisDataSource redisDataSource;


    public boolean setCache(String key,String value, Long secondsToExpire) {
        String className = "Unknown";
        try {
            //直接从异常堆栈取调用方类
            className = new Throwable().getStackTrace()[1].getClassName();
        } catch (Throwable ignore) {
        }
        SetParams setParams = SetParams.setParams().ex(secondsToExpire);
        return setCache(key, value, setParams, className);
    }

    public boolean setCache(String key, String value, SetParams setParams, String monitorName) {
        String set = redisDataSource.get(monitorName).set(key, value, setParams);
        return "OK".equals(set);
    }

    public boolean setCache(String key,String value, Long secondsToExpire, String redisVisitorName) {
        SetParams setParams = SetParams.setParams().ex(secondsToExpire);
        return setCache(key, value, setParams, redisVisitorName);
    }

    /**
     * redis key 统一放到{@link CommonConstant}
     * @param key
     * @return
     */
    public String getCache(String key, String redisVisitorName) {
        return redisDataSource.get(redisVisitorName).get(key);
    }

    public String getCache(String key) {
        String className = "Unknown";
        try {
            //直接从异常堆栈取调用方类
            className = new Throwable().getStackTrace()[1].getClassName();
        } catch (Throwable ignore) {
        }
        return getCache(key, className);
    }

    /**
     * 获取并删除
     * @param key
     * @return
     */
    public String getDelCache(String key, String redisVisitorName) {
        String s = redisDataSource.get(redisVisitorName).get(key);
        if (StringUtils.isNotBlank(s)){
            redisDataSource.get(redisVisitorName).del(key);
        }
        return s;
    }

    public Long delCache(String key, String redisVisitorName) {
        return redisDataSource.get(redisVisitorName).del(key);
    }
}
