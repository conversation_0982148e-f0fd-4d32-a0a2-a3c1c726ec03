package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/10/15
 */
@Component
@Slf4j
public class InterfaceMonitorManager {
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private CHSyncLogManager chSyncLogManager;

    private static final ThreadLocal<InterfaceMonitorData> WAITING_DATA = new ThreadLocal<>();
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 可用于保存erp接口数据接口监控
     */
    public void saveErpInterfaceMonitor(String tenantId, String dcId, String objApiName, String type, Object arg, String result, Integer status, Long callTime, Long returnTime, String remark, String traceId, Long costTime, TimeFilterArg timeFilterArg) {
        try {
            if (!ConfigCenter.INTERFACE_MONITOR_WRITE_MONGO) {
                return;
            }
            if (ConfigCenter.NOT_SAVE_INTERFACE_MONITOR_TENANTS.contains(tenantId)) {
                return;
            }
            InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitorData(tenantId, dcId, objApiName, type, JacksonUtil.toJson(arg), result, status,
                    callTime, returnTime, remark, traceId, costTime);
            interfaceMonitorData.setTimeFilterArg(timeFilterArg);
            interfaceMonitorData.setLogId(LogIdUtil.get());
            if (ConfigCenter.ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS.contains(tenantId)) {
                //等待后续判断之后再处理
                WAITING_DATA.set(interfaceMonitorData);
                return;
            }
            saveInterfaceLogAndSyncLog(interfaceMonitorData);
        } catch (Exception e) {
            log.error("save erp interface log error,", e);
        }
    }

    /**
     * 可用于保存erp接口数据接口监控
     */
    public void saveBatchWriteCrmInterfaceMonitor(String tenantId, String objApiName, String type, Map<String, Object> arg, Object result, Integer status, Long callTime, Long returnTime, String url, List<SyncDataContextEvent> doWriteMqDataList, List<ObjectData> dataList) {
        try {
            if (!ConfigCenter.INTERFACE_MONITOR_WRITE_MONGO) {
                return;
            }
            if (ConfigCenter.NOT_SAVE_INTERFACE_MONITOR_TENANTS.contains(tenantId)) {
                return;
            }
            if (status == 1 && ConfigCenter.ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS.contains(tenantId)) {
                // 成功且仅报错错误日志监控,不记录
                return;
            }
            // 精简参数
            arg = simplifyArg(tenantId, arg, dataList);

            InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitorData(tenantId, null, objApiName, type, JacksonUtil.toJson(arg), JacksonUtil.toJson(result), status,
                    callTime, returnTime, url, TraceUtil.get(), returnTime - callTime);
            interfaceMonitorData.setLogId(LogIdUtil.get());

            List<String> objectIds = chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(tenantId, Lists.newArrayList(interfaceMonitorData));
            //接口日志只录入objectId/有可能受到限速。objectIds为空
            if (CollectionUtils.isNotEmpty(objectIds)) {
                Integer syncStatus = status == 1 ? SyncLogStatusEnum.SYNC_SUCCESS.getStatus() : SyncLogStatusEnum.SYNC_FAIL.getStatus();
                final String string = JacksonUtil.toJson(objectIds);
                final SyncLogTypeEnum syncLogTypeEnum = SyncLogTypeEnum.convertInterfaceType(type);
                List<SyncLog> syncLogs = doWriteMqDataList.stream()
                        .map(contextEvent -> SyncLog.initSyncLog(contextEvent.getSyncLogId(), objApiName, contextEvent.getSourceData().getApiName(),contextEvent.getStreamId(), syncLogTypeEnum, string, syncStatus))
                        .collect(Collectors.toList());
                chSyncLogManager.batchInsert(tenantId, syncLogs);
            }
        } catch (Exception e) {
            log.warn("save batch write crm interface monitor error,", e);
        }
    }

    private Map<String, Object> simplifyArg(String tenantId, Map<String, Object> arg, List<ObjectData> dataList) {
        // 大于设置的精简预置,精简参数
        if (CollectionUtils.isEmpty(dataList) || ConfigCenter.ACCURATE_CALCULATION_BATCH_WRITE_BYTE && RamUsageEstimateUtil.sizeOfObjectIgnoreException(dataList) <= ConfigCenter.LIST_CONTENT_LOG_LENGTH_LIMIT) {
            return arg;
        }

        if (!ConfigCenter.ACCURATE_CALCULATION_BATCH_WRITE_BYTE && dataList.size() <= ConfigCenter.NEED_SIMPLIFY_BYTE_LIST_SIZE) {
            return arg;
        }

        // 超出,显示第一条,后面的只显示id
        List<ObjectData> data = Lists.newArrayList(dataList.get(0));
        dataList.subList(1, dataList.size()).stream()
                .map(ObjectData::getId)
                .forEach(id -> {
                    final ObjectData objectData = new ObjectData();
                    objectData.putId(id);
                    data.add(objectData);
                });
        arg = new HashMap<>(arg);
        arg.put("msg", i18NStringManager.getByEi(I18NStringEnum.s4504, tenantId));
        arg.put("objectDataList", data);
        return arg;
    }

    @SneakyThrows
    public void saveInterfaceLogAndSyncLog(InterfaceMonitorData interfaceMonitorData) {
        String tenantId = interfaceMonitorData.getTenantId();
        Integer status = interfaceMonitorData.getStatus();
        String type = interfaceMonitorData.getType();

        List<String> objectIds = chInterfaceMonitorManager.batchUpsertInterfaceMonitorData(tenantId, Lists.newArrayList(interfaceMonitorData));
        //接口日志只录入objectId/有可能受到限速。objectIds为空
        Integer syncStatus = status == 1 ? SyncLogStatusEnum.SYNC_SUCCESS.getStatus() : SyncLogStatusEnum.SYNC_FAIL.getStatus();
        if (CollectionUtils.isNotEmpty(objectIds)) {
            syncLogManager.saveLog(tenantId, SyncLogTypeEnum.convertInterfaceType(type), syncStatus, objectIds);
        }
    }

    public void saveAndRemoveWaitingData(boolean save) {
        try {
            InterfaceMonitorData interfaceMonitorData = WAITING_DATA.get();
            if (interfaceMonitorData != null) {
                if (save) {
                    saveInterfaceLogAndSyncLog(interfaceMonitorData);
                }
                WAITING_DATA.remove();
            }
        } catch (Exception e) {
            log.error("save erp interface log error saveOrRemoveWaitingData,", e);
        }


    }
}
