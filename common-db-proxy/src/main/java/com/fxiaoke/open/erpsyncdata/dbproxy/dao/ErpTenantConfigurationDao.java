package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.notifier.support.NotifierClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.github.mybatis.local.TenantThreadLocal;
import com.github.mybatis.mapper.ITenant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:25 2021/2/25
 * @Desc:
 *
 * 22.8.8  hardy.
 * 访问db的类改名为ErpTenantConfigurationDaoAccess了。
 * tenantconfig表，在修改数据时，需要通知其它的pod.
 *
 */
@Slf4j
@Repository
public class ErpTenantConfigurationDao implements ErpBaseDao<ErpTenantConfigurationEntity>, ITenant<ErpTenantConfigurationDao>{

    @Autowired
    private ErpTenantConfigurationDaoAccess erpTenantConfigurationDaoAccess;

    @Autowired
    protected RelationErpShardDao relationErpShardDao;

    /**如果有某个ei的配置项变化了，使用fast-notifier通过该room通知其他的pod更新内存。
     * */
    public static final String ErpTenantConfigurationChangeNotifyRoom = "erpTenantConfigurationChangeNotifyRoom";

    /**
     * 获取企业配置，暂时直接在这里做缓存
     * 如果有设置接口展示实时数据需求，不可调用该接口。
     *
     * @param tenantId
     * @param dataCenterId
     * @param channel      可以传空
     * @param type
     * @return
     */
    public ErpTenantConfigurationEntity findOne(String tenantId,
                                         String dataCenterId,
                                         String channel,
                                         String type) {
        return erpTenantConfigurationDaoAccess.findOne(tenantId, dataCenterId, channel, type);
    };

    public ErpTenantConfigurationEntity findOneNoCache(String tenantId,
                                         String dataCenterId,
                                         String channel,
                                         String type) {
        return erpTenantConfigurationDaoAccess.findOneNoCache(tenantId, dataCenterId, channel, type);
    };

    public int updateConfig(String tenantId, String id, String updatedConfiguration, Long updatedUpdateTime, TenantConfigurationTypeEnum type){

        TenantConfigurationTypeEnum.checkFormat(tenantId, type, updatedConfiguration);
        int ret = erpTenantConfigurationDaoAccess.updateConfig(id, updatedConfiguration, updatedUpdateTime);
        notifyOtherPod(tenantId);
        return ret;

    };

    public int deleteByTenantId(String tenantId) {
        int ret = erpTenantConfigurationDaoAccess.deleteByTenantId(tenantId);
        notifyOtherPod(tenantId);
        return ret;
    };

    @Override
    public int insert(ErpTenantConfigurationEntity record) {
        TenantConfigurationTypeEnum.checkFormat(record.getTenantId(),TenantConfigurationTypeEnum.valueOf(record.getType()), record.getConfiguration());
        int ret =  erpTenantConfigurationDaoAccess.insert(record);
        notifyOtherPod(record.getTenantId());
        return ret;
    }


    @Override
    public int insertIgnore(ErpTenantConfigurationEntity record) {
        TenantConfigurationTypeEnum.checkFormat(record.getTenantId(),TenantConfigurationTypeEnum.valueOf(record.getType()), record.getConfiguration());
        int ret =  erpTenantConfigurationDaoAccess.insertIgnore(record);
        notifyOtherPod(record.getTenantId());
        return ret;
    }

    @Override
    public int batchInsert(List<ErpTenantConfigurationEntity> record) {
        int ret = erpTenantConfigurationDaoAccess.batchInsert(record);
        for(ErpTenantConfigurationEntity  entity: record) {
            TenantConfigurationTypeEnum.checkFormat(entity.getTenantId(), TenantConfigurationTypeEnum.valueOf(entity.getType()), entity.getConfiguration());
            notifyOtherPod(entity.getTenantId());
        }
        return ret;
    }

    @Override
    public List<ErpTenantConfigurationEntity> queryList(ErpTenantConfigurationEntity record) {
        return erpTenantConfigurationDaoAccess.queryList(record);
    }

    @Override
    public int updateById(ErpTenantConfigurationEntity record) {
        TenantConfigurationTypeEnum.checkFormat(record.getTenantId(), TenantConfigurationTypeEnum.valueOf(record.getType()), record.getConfiguration());
        int ret = erpTenantConfigurationDaoAccess.updateById(record);
        notifyOtherPod(record.getTenantId());
        return ret;
    }

    @Override
    public int deleteById(String Id) {
        int ret = erpTenantConfigurationDaoAccess.deleteById(Id);
        notifyOtherPod(TenantThreadLocal.get());
        return ret;
    }

    public int deleteById(String tenantId, String id) {
        int ret = erpTenantConfigurationDaoAccess.deleteByTenantIdAndId(tenantId,id);
        notifyOtherPod(tenantId);
        return ret;
    }

    public int deleteByDataCenterId(String tenantId, String dataCenterId) {
        int ret = erpTenantConfigurationDaoAccess.deleteByDataCenterId(tenantId, dataCenterId);
        if (ret > 0) {
            notifyOtherPod(tenantId);
        }
        return ret;
    }

    public int batchDeleteByDataCenterId(String tenantId, List<String> dataCenterIds) {
        int ret = erpTenantConfigurationDaoAccess.batchDeleteByDataCenterId(tenantId, dataCenterIds);
        if (ret > 0) {
            notifyOtherPod(tenantId);
        }
        return ret;
    }

    @Override
    public int deleteByEiAndId(String ei, String Id) {
        return erpTenantConfigurationDaoAccess.deleteByEiAndId(ei,Id);
    }

    @Override
    public ErpTenantConfigurationEntity findById(String id) {
        return erpTenantConfigurationDaoAccess.findById(id);
    }

    public List<ErpTenantConfigurationEntity> findOnePage(Integer offset,  Integer limit) {
        return erpTenantConfigurationDaoAccess.findOnePage(offset,limit);
    }

    /**通知所有环境其它pod重新从db加载该ei的配置到内存*/
    private void notifyOtherPod(String tenantId) {
        log.info("notify ei:{} tenant config change ", tenantId);
        NotifierClient.send(ErpTenantConfigurationChangeNotifyRoom,tenantId);

        notifyDownstreamChange(tenantId);
    }

    /**
     * 模板企业变更通知下游企业变更
     */
    private void notifyDownstreamChange(String tenantId) {
        final List<String> downstreamIds = relationErpShardDao.getAllDownstreamIdsByTemplateId(tenantId);
        downstreamIds.forEach(this::notifyOtherPod);
    }
}