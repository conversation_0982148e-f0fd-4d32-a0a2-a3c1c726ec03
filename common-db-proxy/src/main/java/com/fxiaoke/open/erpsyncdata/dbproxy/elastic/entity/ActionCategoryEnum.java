package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/7 10:46:15
 */
public enum ActionCategoryEnum {
    Query(1, "查询"),
    CREATE(2, "新增"),
    UPDATE(3, "编辑"),
    INVALID(4, "作废"),
    RECOVER(5, "恢复/反禁用"),
    PUSH(6, "推送"),
    /**
     * 只有函数有
     */
    EXECUTE(7, "执行"),
    DELETE(8, "删除"),
    ;
    @Getter
    private Integer type;
    @Getter
    private String detail;

    ActionCategoryEnum(final Integer type, final String detail) {
        this.type = type;
        this.detail = detail;
    }

    private static final Map<Integer, ActionCategoryEnum> map = Arrays.stream(ActionCategoryEnum.values()).collect(Collectors.toMap(ActionCategoryEnum::getType, Function.identity()));

    public ActionCategoryEnum valueOf(Integer action) {
        return map.get(action);
    }
}
