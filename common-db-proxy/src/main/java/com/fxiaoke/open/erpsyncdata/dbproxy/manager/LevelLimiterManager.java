package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;

/**
 * 阶梯限速工具类
 * 用于实现阶梯式的限速控制，包括加速和降速
 * 每分钟只能执行一次速度调整
 */
@Slf4j
@Component
public class LevelLimiterManager {
    @Autowired
    private RedisDataSource redisDataSource;
    private final Clock clock;

    // 常量定义
    private static final String REDIS_KEY_PREFIX = "ERPDSS:LEVELLIMIT:";
    private static final int INITIAL_SPEED = 100; // 初始速度 100条/分钟
    private static final double FAILURE_RATE_THRESHOLD = 0.1; // 失败率阈值 10%
    private static final int MIN_SPEED = 0; // 最小速度
    private static final int REDIS_KEY_EXPIRE_SECONDS = 24*60*60; // Redis key过期时间 1天
    private static final int SPEED_ADJUST_INTERVAL_SECONDS = 60; // 速度调整间隔 60秒
    private static final int NO_SYNC_RESET_SECONDS = 1800; // 30分钟无数据同步 则当前速度重置为初始速度
    private static final int MIN_REQUESTS_FOR_SPEED_ADJUSTMENT = 10;

    // Hash字段名常量
    private static final String FIELD_CURRENT_SPEED = "current_speed";
    private static final String FIELD_TARGET_SPEED = "target_speed";
    private static final String FIELD_TOTAL = "total";
    private static final String FIELD_FAIL = "fail";
    private static final String FIELD_LAST_SYNC = "last_sync";
    private static final String FIELD_LAST_ADJUST = "last_adjust";

    // 本地记录使用过限速功能的tenant
    private final Set<String> tenantLevelLimitSet = new HashSet<>();
    private int tenantLevelLimitCount = 0;

    // 无参构造函数
    public LevelLimiterManager() {
        this.clock = Clock.systemDefaultZone();
    }

    /**
     * 获取当前时间戳（秒）
     */
    private int getCurrentTimeSeconds() {
        return (int)(clock.millis() / 1000);
    }

    public int calculateAndUpdateSpeed(String key, int targetSpeed) {
        tenantLevelLimitSet.add(key);
        SpeedState state = getCurrentState(key);
        log.debug("Current speed: {}", state.getCurrentSpeed());
        String infoKey = buildRedisKey(key, "info");
        Map<String, Integer> currentInfo = getRedisInfo(infoKey);
        log.debug("trace currentInfo start, key:{}, currentInfo:{}", key, currentInfo);

        if(currentInfo.isEmpty()) {
            // 使用Integer值
            currentInfo.put(FIELD_TARGET_SPEED, targetSpeed);
            currentInfo.put(FIELD_CURRENT_SPEED, INITIAL_SPEED);
            currentInfo.put(FIELD_TOTAL, 0);
            currentInfo.put(FIELD_FAIL, 0);
            currentInfo.put(FIELD_LAST_SYNC, getCurrentTimeSeconds());
            currentInfo.put(FIELD_LAST_ADJUST, 0);
            updateRedisInfo(infoKey, currentInfo, REDIS_KEY_EXPIRE_SECONDS);
        } else if(targetSpeed != currentInfo.get(FIELD_TARGET_SPEED)) {
            log.info("trace level limiter set initspeed, calculateSpeed, key:{},targetSpeed:{}, oldTargetSpeed:{},currentInfo:{}", key, targetSpeed, currentInfo.get(FIELD_TARGET_SPEED), currentInfo);
            currentInfo.put(FIELD_TARGET_SPEED, targetSpeed);
            currentInfo.put(FIELD_LAST_SYNC, getCurrentTimeSeconds());
            currentInfo.put(FIELD_LAST_ADJUST, 0);
            updateRedisInfo(infoKey, currentInfo, REDIS_KEY_EXPIRE_SECONDS);
        }

        // 如果30分钟无数据同步，重置为初始速度
        if (shouldResetDueToNoSync(key, currentInfo)) {
            updateCurrentSpeed(key, INITIAL_SPEED, currentInfo);
            log.debug("Speed reset to initial value {} due to no sync for {} seconds for key:{}",  INITIAL_SPEED, NO_SYNC_RESET_SECONDS, key);
            return INITIAL_SPEED;
        }

        // 1分钟内只能调整1次速度
        if (!canAdjustSpeed(key, currentInfo)) {
            log.info("trace level limiter, can not adjust speed, key:{}, curSpeed:{}", key, state.getCurrentSpeed());
            return state.getCurrentSpeed();
        }
        
        // 计算新的速度
        int newSpeed = calculateSpeed(key, state.getCurrentSpeed(), state.getTotalRequests(), state.getFailCount(), currentInfo.get(FIELD_TARGET_SPEED));
        log.debug("trace level limiter, calculateSpeed, key:{}, curspeed:{}, newSpeed:{}", key, state.getCurrentSpeed(), newSpeed);
        
        // 更新速度和最后调整时间到redis
        updateCurrentSpeed(key, newSpeed, currentInfo);
        return newSpeed;
    }

    public void recordRequest(String key, boolean isFail) {
        if (!tenantLevelLimitSet.contains(key)) {
            return;
        }
        tenantLevelLimitCount++;
        if (tenantLevelLimitCount >= 1000) {
            tenantLevelLimitCount = 0;
            tenantLevelLimitSet.remove(key);
        }

        String infoKey = buildRedisKey(key, "info");
        Map<String, Integer> currentInfo = getRedisInfo(infoKey);
        // 更新计数
        if (isFail) {
            int failCount = currentInfo.getOrDefault(FIELD_FAIL, 0);
            currentInfo.put(FIELD_FAIL, failCount + 1);
        } else {
            int totalCount = currentInfo.getOrDefault(FIELD_TOTAL, 0);
            currentInfo.put(FIELD_TOTAL, totalCount + 1);
        }
        
        // 更新最后同步时间
        currentInfo.put(FIELD_LAST_SYNC, getCurrentTimeSeconds());            
        
        // 一次性更新所有信息
        updateRedisInfo(infoKey, currentInfo, REDIS_KEY_EXPIRE_SECONDS);
    }

    // 速度计算相关方法
    private int calculateSpeed(String key, int currentSpeed, int totalRequests, int failCount, int targetSpeed) {
        // 计算失败率
        totalRequests = Math.max(totalRequests, 1);
        double failRate = (double) failCount / totalRequests;
        
        // 如果失败率超过阈值，速度减半
        if (failRate > FAILURE_RATE_THRESHOLD) {
            int newSpeed = currentSpeed / 2;
            return Math.max(newSpeed, INITIAL_SPEED);
        }
        
        // 如果失败率在可接受范围内，速度翻倍
        int newSpeed = currentSpeed * 2;

        log.debug("trace calculateSpeed,  currentSpeed:{},  targetSpeed:{}, newSpeed:{}", currentSpeed, targetSpeed, newSpeed);
        // 确保不超过目标速度
        return Math.min(newSpeed, targetSpeed);
    }

    // 状态管理相关方法
    private SpeedState getCurrentState(String key) {
        try {
            String infoKey = buildRedisKey(key, "info");
            Map<String, Integer> info = getRedisInfo(infoKey);
            
            int total = info.getOrDefault(FIELD_TOTAL, 0);
            int fail = info.getOrDefault(FIELD_FAIL, 0);
            int currentSpeed = info.getOrDefault(FIELD_CURRENT_SPEED, INITIAL_SPEED);
            
            return new SpeedState(key, currentSpeed, total, fail);
        } catch (Exception e) {
            log.error("Failed to get current state for key:{}", key, e);
            return new SpeedState(key, getCurrentSpeed(key), 0, 0);
        }
    }

    private int getCurrentSpeed(String key) {
        String infoKey = buildRedisKey(key, "info");
        Map<String, Integer> info = getRedisInfo(infoKey);
        return info.getOrDefault(FIELD_CURRENT_SPEED, INITIAL_SPEED);
    }

    private void updateCurrentSpeed(String key, int newSpeed, Map<String, Integer> currentInfo) {
        try {
            String infoKey = buildRedisKey(key, "info");
            currentInfo.put(FIELD_CURRENT_SPEED, newSpeed);
            currentInfo.put(FIELD_LAST_ADJUST, getCurrentTimeSeconds());
            //total和fail要清0， 否则会影响下一次调速。  
            currentInfo.put(FIELD_FAIL, 0);
            currentInfo.put(FIELD_TOTAL, 0);
            updateRedisInfo(infoKey, currentInfo, REDIS_KEY_EXPIRE_SECONDS);
            log.debug("trace updateSpeed for key:{}, newSpeed:{}", key, newSpeed);
        } catch (Exception e) {
            log.error("Failed to update speed for key:{}", key, e);
        }
    }

    private boolean canAdjustSpeed(String key, Map<String, Integer> info) {
        Integer lastAdjust = info.get(FIELD_LAST_ADJUST);
        if (lastAdjust == null) {
            log.debug("trace canAdjustSpeed true, skipped for key:{}, lastAdjustTime is null", key);
            return true;
        }
        
        int currentTime = getCurrentTimeSeconds();
        int timeDiff = currentTime - lastAdjust;
        if (timeDiff >= SPEED_ADJUST_INTERVAL_SECONDS) {
            log.debug("trace canAdjustSpeed true for key:{}, currentTime(s):{}, lastAdjust(s):{}, timeDiff(s):{}",
                    key, currentTime, lastAdjust, timeDiff);
            return true;
        }
        log.debug("trace canAdjustSpeed false for key:{}, currentTime(s):{}, lastAdjust(s):{}, timeDiff(s):{}",
                key, currentTime, lastAdjust, timeDiff);
        return false;
    }

    private boolean shouldResetDueToNoSync(String key, Map<String, Integer> info) {        
        Integer lastSync = info.get(FIELD_LAST_SYNC);
        if (lastSync == null) {
            return true;
        }        
        int currentTime = getCurrentTimeSeconds();
        return (currentTime - lastSync) > NO_SYNC_RESET_SECONDS;
    }

    /**
     * 获取Redis中的所有信息并转换为Integer类型
     * @param key Redis key
     * @return 所有字段的Map，值为Integer类型
     */
    private Map<String, Integer> getRedisInfo(String key) {
        try {
            Map<String, String> stringInfo = redisDataSource.get(this.getClass().getSimpleName()).hgetAll(key);
            Map<String, Integer> intInfo = new LinkedHashMap<>();
            
            if (stringInfo != null) {
                for (Map.Entry<String, String> entry : stringInfo.entrySet()) {
                    try {
                        intInfo.put(entry.getKey(), Integer.parseInt(entry.getValue()));
                    } catch (NumberFormatException e) {
                        log.error("Failed to parse value to Integer for key:{}, field:{}, value:{}",  key, entry.getKey(), entry.getValue(), e);
                        // 使用默认值0
                        intInfo.put(entry.getKey(), 0);
                    }
                }
            }
            
            return intInfo;
        } catch (Exception e) {
            log.error("Failed to get Redis info for key:{}", key, e);
            return new LinkedHashMap<>();
        }
    }

    /**
     * 更新Redis中的所有信息，将Integer类型转换为String
     * @param key Redis key
     * @param intInfo 要更新的所有字段，值为Integer类型
     * @param expireSeconds 过期时间
     */
    private void updateRedisInfo(String key, Map<String, Integer> intInfo, int expireSeconds) {
        try {
            Map<String, String> stringInfo = new HashMap<>();

            for (Map.Entry<String, Integer> entry : intInfo.entrySet()) {
                stringInfo.put(entry.getKey(), Integer.toString(entry.getValue()));
            }
            redisDataSource.get(this.getClass().getSimpleName()).hmset(key, stringInfo);
            redisDataSource.get(this.getClass().getSimpleName()).expire(key, expireSeconds);
        } catch (Exception e) {
            log.error("Failed to update Redis info for key:{}", key, e);
        }
        
    }

    private String buildRedisKey(String key, String type) {
        return REDIS_KEY_PREFIX + key + ":" + type;
    }
}

/**
 * 速度状态类
 */
class SpeedState {
    private final String key;
    private final int currentSpeed;
    private final int totalRequests;
    private final int failCount;

    public SpeedState(String key, int currentSpeed, int totalRequests, int failCount) {
        this.key = key;
        this.currentSpeed = currentSpeed;
        this.totalRequests = totalRequests;
        this.failCount = failCount;
    }

    public String getKey() {
        return key;
    }

    public int getCurrentSpeed() {
        return currentSpeed;
    }

    public int getTotalRequests() {
        return totalRequests;
    }

    public int getFailCount() {
        return failCount;
    }
} 