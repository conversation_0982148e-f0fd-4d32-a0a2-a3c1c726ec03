package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
public class StreamSimpleInfo implements Serializable {

    @ExcelIgnore
    private static final long serialVersionUID = 624678879289351340L;

    @ApiModelProperty("集成流id")
    @ExcelProperty("集成流id")
    private String streamId;
    private String tenantId;
    @ApiModelProperty("ea")
    @ExcelProperty("ea")
    private String enterpriseAccount;
    @ApiModelProperty("集成流名称")
    @ExcelProperty("集成流名称")
    private String integrationStreamName;
    /**
     * {@link com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum}
     */
    @ApiModelProperty("状态;1.启用 2.停用")
    @ExcelProperty("状态")
    private Integer status = -1;
    @ExcelIgnore
    private String erpDcId;
    /**
     * {@link com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType}
     */
    @ApiModelProperty("源企业类型;1.纷享 2.外部对接")
    @ExcelProperty("源企业类型")
    private Integer sourceTenantType;
    @ApiModelProperty("CRM对象")
    @ExcelProperty("CRM对象")
    private String crmObjApiName;
    @ApiModelProperty("外部对象")
    @ExcelProperty("外部对象")
    private String erpObjApiName;
    /**
     * 连接器key
     */
    @ApiModelProperty("连接器类型")
    @ExcelProperty("连接器类型")
    private String connectorKey;
    @ApiModelProperty("集成流最后修改时间")
    @ExcelProperty("集成流最后修改时间")
    private Long streamLastUpdateTime;
    @ExcelIgnore
    @ApiModelProperty("最后同步时间")
    private Date mongoLastSyncTime;
}
