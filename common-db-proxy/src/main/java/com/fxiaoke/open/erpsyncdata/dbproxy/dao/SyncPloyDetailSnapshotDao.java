package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.ibatis.BaseMapper;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@ManagedTenantReplace
public interface SyncPloyDetailSnapshotDao extends BaseMapper<SyncPloyDetailSnapshotEntity> , ITenant<SyncPloyDetailSnapshotDao> {
    /**
     * 根据主键查找记录
     */
    SyncPloyDetailSnapshotEntity get(@Param("sourceTenantId") String sourceTenantId, @Param("id") String id);

    @LogLevel(LogLevelEnum.TRACE)
    int countBySourceTenantId(@Param("sourceTenantId") String sourceTenantId, @Param("status") Integer status);

    /**
     * 根据源企业id、源对象apiName、策略状态获取最新的策略明细快照集合
     *
     * @param status 策略状态 {@link SyncPloyDetailStatusEnum}
     */
    List<SyncPloyDetailSnapshotEntity> listNewestBySourceTenantIdAndSrouceObjectApiName(@Param("sourceTenantId") String sourceTenantId, @Param("sourceObjectApiName") String sourceObjectApiName,
        @Param("status") Integer status);

    List<SyncPloyDetailSnapshotEntity> listBySourceTenantIdAndObjectApiNames(@Param("sourceTenantId") String sourceTenantId, @Param("sourceObjectApiNames") List<String> sourceObjectApiNames,
                                                                                        @Param("status") Integer status);

    List<SyncPloyDetailSnapshotEntity> listNewestByDestTenantIdAndDestObjectApiName(@Param("destTenantId") String destTenantId, @Param("destObjectApiName") String destObjectApiName,
        @Param("status") Integer status);

    int batchUpdateStatusBySourceTenantIdAndDestTenantIdReverse(@Param("sourceTenantId") String sourceTenantId, @Param("destTenantId") String destTenantId, @Param("status") Integer status);

    List<SyncPloyDetailSnapshotEntity> listBySourceTenantIdAndObjectApiName(@Param("sourceTenantId") String sourceTenantId, @Param("sourceObjectApiName") String sourceObjectApiName);

    int updateStatusByPloyDetailId(@Param("syncPloyDetailId") String syncPloyDetailId, @Param("status") Integer status);

    int updateStatusByPloyDetailIds(@Param("syncPloyDetailIds") List<String> syncPloyDetailIds, @Param("status") Integer status);

    int deleteByPloyDetailIds(@Param("sourceTenantId") String sourceTenantId, @Param("syncPloyDetailIds") List<String> syncPloyDetailIds);

    int updateSyncPloyDataDetailById(@Param("id") String id, @Param("syncPloyDetailData") SyncPloyDetailData syncPloyDetailData);

    List<SyncPloyDetailSnapshotEntity> listEnableSnapshotsBySyncPloyDetailsId(@Param("sourceTenantId") String sourceTenantId, @Param("syncPloyDetailsId") String syncPloyDetailsId, @Param("status") Integer status);
    @LogLevel(LogLevelEnum.TRACE)
    List<String> listEnableSnapshotTenantId();

}
