package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DoubleWrite;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.SpeedLimiter;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.TenantLimitableMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.InterfaceMonitorMongoStore;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.lt;

/**
 * <AUTHOR>
 * @Date: 15:32 2021/7/23
 * @Desc:
 */
@Slf4j
@Repository
public class InterfaceMonitorDataDao implements TenantLimitableMongoDao {
    @Autowired
    private InterfaceMonitorMongoStore mongoStore;
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Override
    public String getCollPrefix() {
        return "interface_monitor";
    }

    @Override
    public CollStat getCollStat(String tenantId) {
        CollStat collStat = new CollStat();
        long count=chInterfaceMonitorManager.countByTenantId(tenantId);
        collStat.setCount(count);
        return collStat;
    }

    @Override
    public DateTime findMinDate(String tenantId) {
        Long first = chInterfaceMonitorManager.findMinDate(tenantId);
        if (first != null) {
            return DateUtil.date(first);
        }
        return DateUtil.date();
    }

    @Override
    public Long deleteBetween(String tenantId, Date beginDate, Date endDate) {
        return chInterfaceMonitorManager.deleteBetween(tenantId, beginDate, endDate);
    }

    public InterfaceMonitorData buildInterfaceMonitorDataFromDoc(Document document) {
        InterfaceMonitorData interfaceMonitorData = new InterfaceMonitorData();
        interfaceMonitorData.setId(document.getObjectId("_id"));
        interfaceMonitorData.setTenantId(document.getString("tenant_id"));
        interfaceMonitorData.setDcId(document.getString("dc_id"));
        interfaceMonitorData.setObjApiName(document.getString("obj_api_name"));
        interfaceMonitorData.setType(document.getString("type"));
        interfaceMonitorData.setArg(document.getString("arg"));
        interfaceMonitorData.setResult(document.getString("result"));
        interfaceMonitorData.setStatus(document.getInteger("status"));
        interfaceMonitorData.setCallTime(document.getLong("call_time"));
        interfaceMonitorData.setReturnTime(document.getLong("return_time"));
        interfaceMonitorData.setRemark(document.getString("remark"));
        interfaceMonitorData.setTraceId(document.getString("trace_id"));
        interfaceMonitorData.setLogId(document.getString("log_id"));
        if (ObjectUtils.isEmpty(document.getString("log_id"))) {
            interfaceMonitorData.setLogId(document.getString("trace_id"));//兼容下旧数据
        }
        interfaceMonitorData.setCostTime(document.getLong("cost_time"));
        interfaceMonitorData.setCreateTime(document.getLong("create_time"));
        interfaceMonitorData.setExpireTime(document.getDate("expire_time"));
        interfaceMonitorData.setSyncDataId(document.getString("sync_data_id"));
        TimeFilterArg timeFilterArg = new TimeFilterArg();
        timeFilterArg.setStartTime(document.getEmbedded(ListUtil.of("time_filter_arg", "startTime"), 0L));
        timeFilterArg.setEndTime(document.getEmbedded(ListUtil.of("time_filter_arg", "endTime"), 0L));
        timeFilterArg.setLimit(document.getEmbedded(ListUtil.of("time_filter_arg", "limit"), timeFilterArg.getLimit()));
        timeFilterArg.setOffset(document.getEmbedded(ListUtil.of("time_filter_arg", "offset"), timeFilterArg.getOffset()));
        interfaceMonitorData.setTimeFilterArg(timeFilterArg);
        return interfaceMonitorData;
    }



    public List<InterfaceMonitorData> listInterfaceMonitorDataLimit1000(String tenantId,String lastId) {
        List<Bson> filters = Lists.newArrayList();
        if (StringUtils.isNotBlank(lastId)) {
            filters.add(Filters.lt("_id", new ObjectId(lastId)));
        }
        List<Document> documents = mongoStore.listInterfaceMonitorDocument(tenantId, filters, Sorts.orderBy(Sorts.descending("_id")), 0, 1000);
        List<InterfaceMonitorData> list = Lists.newArrayList();
        for (Document document : documents) {
            InterfaceMonitorData interfaceMonitorData = this.buildInterfaceMonitorDataFromDoc(document);
            list.add(interfaceMonitorData);
        }
        return list;
    }
}
