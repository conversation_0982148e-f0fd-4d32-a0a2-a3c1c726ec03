package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.PloyBreakNoticeConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * getById熔断管理器，支持熔断和解除熔断
 *
 * <AUTHOR>
 * @date 2022-03-09
 */

@Component
@Slf4j
public class GetByIdBreakManager {
    private static final String KEY_GET_BY_ID_BREAK = "key_get_by_id_break";

    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;

    public void increaseFailedCount(String tenantId, String dataCenterId, ErpChannelEnum channel, String objectApiName, String errMsg) {
        String key = KEY_GET_BY_ID_BREAK + "_" + tenantId + "_" + dataCenterId + "_" + objectApiName;
        String jsonValue = redisDataSource.get(this.getClass().getSimpleName()).get(key);
        FailedData failedData = null;

        long expireTime = getExpireTime(tenantId, dataCenterId, channel);
        if (StringUtils.isEmpty(jsonValue)) {
            failedData = new FailedData();
            failedData.setTimestamp(System.currentTimeMillis());
            failedData.setFailedCount(1);
            redisDataSource.get(this.getClass().getSimpleName()).set(key, JSONObject.toJSONString(failedData));
            //设置超时时间
            redisDataSource.get(this.getClass().getSimpleName()).expire(key, expireTime);
        } else {
            failedData = JSONObject.parseObject(jsonValue, FailedData.class);
            failedData.setFailedCount(failedData.getFailedCount() + 1);
            redisDataSource.get(this.getClass().getSimpleName()).set(key, JSONObject.toJSONString(failedData));
            Long offset = System.currentTimeMillis()-failedData.getTimestamp();
            Long newExpiredTime = expireTime * 1000 - offset;
            if(expireTime * 1000 - offset>1000) {
                redisDataSource.get(this.getClass().getSimpleName()).expire(key,newExpiredTime / 1000);
            } else {
                redisDataSource.get(this.getClass().getSimpleName()).del(key);
                failedData.failedCount=0;
            }
        }

        long breakCount = getBreakCount(tenantId, dataCenterId, channel);
        if (failedData.failedCount > breakCount) {
            redisDataSource.get(this.getClass().getSimpleName()).persist(key);//取消超时设置

            GetEnterpriseDataArg getEnterpriseDataArg = new GetEnterpriseDataArg();
            getEnterpriseDataArg.setEnterpriseId(Integer.valueOf(tenantId));
            GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(getEnterpriseDataArg);

            boolean isGrayTenant = ErpSyncDataBackStageEnvironmentEnum.GRAY.equals(tenantEnvManager.getTenantAllModelEnv(tenantId));

            String url = result.getEnterpriseData().getDomain() + "/erp/{syncdata}/out/break/getbyid/remove?tenantId=" + tenantId + "&dcId=" + dataCenterId + "&objApiName=" + objectApiName;
            url = url.replace("{syncdata}",isGrayTenant ? "syncdatagray":"syncdata");
            List<ErpObjectRelationshipEntity> byRealObjectApiName = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByRealObjectApiName(tenantId, dataCenterId, objectApiName);
            Map<String, String> infoMap = convertBreakInfo(tenantId, dataCenterId, byRealObjectApiName.get(0).getErpSplitObjectApiname());
            String finalMessage=i18NStringManager.getByEi2(I18NStringEnum.s822.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s822.getI18nValue(), infoMap.get("enterpriseName"),infoMap.get("dataCenterName"),infoMap.get("erpComplexName"),failedData.getFailedCount(),"\n",url,"\n"),
                    Lists.newArrayList(infoMap.get("enterpriseName"),infoMap.get("dataCenterName"),infoMap.get("erpComplexName"),failedData.getFailedCount()+"","\n",url,"\n"));
            SendAdminNoticeArg arg = SendAdminNoticeArg.builder()
                    .msg(finalMessage)
                    .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s821,tenantId))
                    .tenantId(tenantId)
                    .dcId(dataCenterId)
                    .sendSuperAdminIfNoSendTenantAdmin(false)
                    .needFillPreDbName(false)
                    .build();
            arg = arg.addTraceInfo();
            notificationService.sendTenantAdminNotice(arg,
                    AlarmRuleType.GENERAL,
                    AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                    AlarmType.GET_BY_ID_API_BREAK,
                    AlarmLevel.IMPORTANT);
            notificationService.sendSuperAdminNoticeToManageTool(
                    NoticeType.GET_BY_ID_INTERFACE_EXCEPTION.arg()
                    .setTenantId(tenantId)
                    .setDcId(dataCenterId)
                    .setMsg(finalMessage));
        }
    }

    /**
     * 转换对应的熔断通知需要的信息
     * @param
     * @param
     * @return
     */
    private Map<String,String> convertBreakInfo(String tenantId, String dcId, String erpObjectApiName){
        Map<String,String> resultMap= Maps.newHashMap();
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoManager.listByTenantId(tenantId);
        String enterpriseName=String.format("%s(%s)",erpConnectInfoEntities.get(0).getEnterpriseName(),erpConnectInfoEntities.get(0).getTenantId());
        ErpConnectInfoEntity erpConnectInfoEntity=erpConnectInfoEntities.stream().filter(item ->item.getId().equals(dcId)).findFirst().orElseGet(() ->new ErpConnectInfoEntity());
        String dataCenterName=erpConnectInfoEntity.getDataCenterName();
        String erpObjName = erpObjManager.getErpObjName(tenantId, dcId, erpObjectApiName);
        String erpComplexName=String.format("%s(%s)",erpObjName,erpObjectApiName);
        resultMap.put("enterpriseName",enterpriseName);
        resultMap.put("dataCenterName",dataCenterName);
        resultMap.put("erpComplexName",erpComplexName);
        return resultMap;
    }


    public FailedData getFailedData(String tenantId, String dataCenterId, String objectApiName) {
        String key = KEY_GET_BY_ID_BREAK + "_" + tenantId + "_" + dataCenterId + "_" + objectApiName;
        String jsonValue = redisDataSource.get(this.getClass().getSimpleName()).get(key);
        FailedData breakData = null;
        if (StringUtils.isNotEmpty(jsonValue)) {
            breakData = JSONObject.parseObject(jsonValue, FailedData.class);
        }
        return breakData;
    }

    private long getExpireTime(String tenantId, String dataCenterId, ErpChannelEnum channel) {
        long expireTime = configCenterConfig.getGetByIdBreakExpireTime(tenantId, dataCenterId, channel.name());
        if (expireTime == 0) {
            expireTime = configCenterConfig.getGetByIdBreakExpireTime();
        }
        return expireTime;
    }

    private long getBreakCount(String tenantId, String dataCenterId, ErpChannelEnum channel) {
        PloyBreakNoticeConfig ployBreakNoticeConfig = tenantConfigurationManager.getPloyBreakNoticeConfig(tenantId, dataCenterId);
        long breakCount=0;
        if(ObjectUtils.isNotEmpty(ployBreakNoticeConfig)&&ployBreakNoticeConfig.getGetByIdsFailCount()!=null){
            breakCount=ployBreakNoticeConfig.getGetByIdsFailCount();
        }
         breakCount = configCenterConfig.getGetByIdBreakCount(tenantId, dataCenterId, channel.name());
        if (breakCount == 0) {
            breakCount = configCenterConfig.getGetByIdBreakCount();
        }
        return breakCount;
    }

    /**没有实现getbyid接口*/
    private boolean notOfferGetByID(String tenantId, String dataCenterId, String objAPIName) {
        Set<String> tenantAndObjAPISet = tenantConfigurationManager.getNotOfferGetbyidTenantAndObjAPISet(tenantId,dataCenterId);
        log.info("trace notOfferGetByID tenantId:{}, objAPIName{}",tenantId, objAPIName);
        if(tenantAndObjAPISet.contains(objAPIName)) {
            /**配置了 没实现getbyid接口的企业名单，不往下调用了，直接返回空数据。
             * 注意这里返回的是 成功，但是数据为空的 结果。这种返回结果比较诡异，可能会导致有些处理报错。
             * **/
            return true;
        }
        return false;
    }

    public boolean isBreak(String tenantId, String dataCenterId, ErpChannelEnum channel, String objectApiName) {
        FailedData failedData = getFailedData(tenantId, dataCenterId, objectApiName);
        if (failedData != null && failedData.getFailedCount() > getBreakCount(tenantId,dataCenterId,channel)) {
            return true;
        }
        return false;
    }

    public boolean isTenantConfigBreak(String tenantId, String dataCenterId, String objectApiName) {
        if(notOfferGetByID(tenantId, dataCenterId, objectApiName)) {
            return true;
        }
        return false;
    }

    public void removeBreak(String tenantId, String dataCenterId, String ployDetailId, String objectApiName) {
        String key = KEY_GET_BY_ID_BREAK + "_" + tenantId + "_" + dataCenterId + "_" + objectApiName;
        redisDataSource.get(this.getClass().getSimpleName()).del(key);
        if(StringUtils.isNotEmpty(ployDetailId)) {
            //告警恢复后，更新告警状态为已恢复
            dataIntegrationNotificationManager.updateAlarmStatus(tenantId, ployDetailId,AlarmType.GET_BY_ID_API_BREAK,true);
        }
    }

    @Data
    public static class FailedData implements Serializable {
        /**
         * 首次记录失败数据的时间
         */
        private Long timestamp;
        /**
         * 失败次数
         */
        private int failedCount;
    }
}
