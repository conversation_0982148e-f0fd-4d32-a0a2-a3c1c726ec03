package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityField;
import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityObj;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpConnectStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * erp企业连接信息
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SecurityObj
@Table(name = "erp_connect_info")
public class ErpConnectInfoEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 渠道
     */
    @Column(name = "channel")
    private ErpChannelEnum channel;

    /**
     * 数据中心名称
     */
    @Column(name = "data_center_name")
    private String dataCenterName;


    /**
     * 企业名称
     */
    @Column(name = "enterprise_name")
    private String enterpriseName;

    /**
     * 连接参数
     */
    @Column(name = "connect_params")
    @ToString.Exclude
    @SecurityField
    private String connectParams;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;
    /**
     * 编号，渠道内序号，百位为渠道id，个位十位为渠道内序号,从0开始,如K3第一个渠道，为0.
     * jar连接器，为100w+
     *
     * @see ErpChannelEnum
     */
    @Column(name = "number")
    private Integer number;
    /**
     * 状态,添加逻辑删除状态
     *
     * @see ErpConnectStatusEnum
     */
    @Column(name = "status")
    private Integer status = 1;
}