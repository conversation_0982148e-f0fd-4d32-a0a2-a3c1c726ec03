package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 17:18 2020/11/9
 * @Desc:
 */
@Data
@ApiModel
public class Order implements Serializable {
    @ApiModelProperty("字段名")
    private String fieldName;
    @ApiModelProperty("如果是true，按照升序排列，如果是false，则按照倒序排列,默认是false")
    private boolean asc=false;
}
