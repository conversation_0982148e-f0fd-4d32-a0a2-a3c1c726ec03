package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/27 20:17:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Embedded
public class CheckResultEntity implements Serializable {
    @Property("lack_object")
    private List<String> lackObject = new ArrayList<>();
    @Embedded("lack_field")
    private List<Field> lackField = new ArrayList<>();
    @Embedded("lack_option")
    private List<Option> lackOption = new ArrayList<>();
    @Property("lack_func")
    private List<String> lackFunc = new ArrayList<>();
    @Property("lack_role")
    private List<String> lackRole = new ArrayList<>();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Embedded
    public static class Field implements Serializable {
        @Property("object_api_name")
        private String objectApiName;
        @Property("field_api_name")
        private String fieldApiName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Embedded
    public static class Option implements Serializable {
        @Property("object_api_name")
        private String objectApiName;
        @Property("field_api_name")
        private String fieldApiName;
        @Property("field_option")
        private Object fieldOption;
    }
}
