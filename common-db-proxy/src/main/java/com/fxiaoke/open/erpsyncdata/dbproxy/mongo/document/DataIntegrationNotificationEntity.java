package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.List;

/**
 * 数据集成通知entity
 * <AUTHOR>
 * @date 2023.08.07
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataIntegrationNotificationEntity {
    @BsonId
    private ObjectId id;
    private String tenantId;

    //数据中心ID
    private String dataCenterId;

    //集成流ID
    private List<String> ployDetailIdList;

    //通知类型，已废弃，暂时保留
    private NotificationType notificationType;

    //告警规则类型
    private AlarmRuleType alarmRuleType;

    //告警规则名称
    private String alarmRuleName;

    //告警类型
    private AlarmType alarmType;

    //告警级别
    private AlarmLevel alarmLevel;

    //通知时间
    private Long time;

    //通知消息
    private String msg;

    //通知人列表
    private List<Integer> userIdList;

    //通知方式
    private List<String> notifyType;

    //标记告警恢复状态
    private boolean recover;

    //trace id
    private String traceId;


    private Date createTime;
    private Date updateTime;
}
