package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.util.ObjectUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TemplateOptionDoc;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/26
 */
@Slf4j
@Component
@DependsOn("erpSyncDataMongoStore")
public class TemplateOptionDao {

    @Qualifier("erpSyncDataMongoStore")
    @Autowired
    private DatastoreExt store;

    private static final String dbName = "erp_sync_data2";


    @PostConstruct
    void init() {
        createIndex();
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()));
    }

    private MongoCollection<TemplateOptionDoc> getColl() {
        MongoCollection<TemplateOptionDoc> coll = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection("template_option", TemplateOptionDoc.class);
        return coll;
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<TemplateOptionDoc> coll = getColl();
        coll.createIndex(Indexes.ascending("type", "value"), new IndexOptions().unique(true).background(true));
    }

    public void upsertIgnore(TemplateOptionDoc newDoc) {
        try {
            upsert(newDoc);
        } catch (Exception e) {
            log.info("upsert template option error,", e);
        }
    }

    public void upsert(TemplateOptionDoc newDoc) {
        if (!ObjectUtil.isAllNotEmpty(newDoc.getType(), newDoc.getValue(), newDoc.getLabel())) {
            throw new ErpSyncDataException(I18NStringEnum.s3633, null);
        }
        Bson filters = Filters.and(
                Filters.eq(TemplateOptionDoc.Fields.type, newDoc.getType()),
                Filters.eq(TemplateOptionDoc.Fields.value, newDoc.getValue())
        );
        Bson updates = Updates.combine(Updates.set(TemplateOptionDoc.Fields.label, newDoc.getLabel()),
                Updates.set(TemplateOptionDoc.Fields.extend, newDoc.getExtend()),
                Updates.set(TemplateOptionDoc.Fields.order, newDoc.getOrder()));
        UpdateOptions updateOptions = new UpdateOptions();
        updateOptions.upsert(true);
        UpdateResult updateResult = getColl().updateOne(filters, updates, updateOptions);
    }


    public void delete(String type, String value) {
        Bson filters = Filters.and(
                Filters.eq(TemplateOptionDoc.Fields.type, type),
                Filters.eq(TemplateOptionDoc.Fields.value, value)
        );
        DeleteResult deleteResult = getColl().deleteOne(filters);
    }

    public List<TemplateOptionDoc> findByType(String type) {
        Bson filters = Filters.and(Filters.eq(TemplateOptionDoc.Fields.type, type));
        List<TemplateOptionDoc> result = new ArrayList<>();
        getColl().find(filters).into(result);
        return result;
    }

    public List<TemplateOptionDoc> findAll() {
        List<TemplateOptionDoc> result = new ArrayList<>();
        getColl().find().into(result);
        return result;
    }
}
