package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataMonitorScreenDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.DownstreamPloyDetailCircuitBreakerAspect;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.AlertAggregationType;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.AlertAggregationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncFailedStatDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.REDIS_KEY_EXCEPTION_PLOY_DETAIL;

/**
 * <AUTHOR>
 * @Date: 9:58 2021/5/24
 * @Desc:
 */
@Component
@Slf4j
public class SyncPloyManager {
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private SyncFailedStatDao syncFailedStatDao;
    @Autowired
    private AlertAggregationDao alertAggregationDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ErpObjManager erpObjManager;

    /**
     * 熔断使用，停止某个企业的策略
     * sourceObjectApiName 或者 ployDetailSnapshotId传其一，有限使用snapId
     *
     * @param tenantId
     * @param ployDetailSnapshotId
     * @param msg
     * @return
     */
    public boolean disablePloyDetail(String tenantId, String ployDetailSnapshotId, String msg) {
        SyncPloyDetailSnapshotEntity snapshot = syncPloyDetailSnapshotManager.getAdminEntryBySnapshotId(tenantId, ployDetailSnapshotId);
        SyncPloyDetailData syncPloyDetailData = snapshot.getSyncPloyDetailData();
        String erpDcId = syncPloyDetailData.getSourceTenantType().equals(TenantType.ERP)
                ? syncPloyDetailData.getSourceDataCenterId() : syncPloyDetailData.getDestDataCenterId();
        String ployDetailId = snapshot.getSyncPloyDetailId();
        String sourceObjectApiName = snapshot.getSourceObjectApiName();
        boolean success = disablePloyDetailByStreamId(tenantId, erpDcId, ployDetailId, sourceObjectApiName, msg, true);
        deletePloyDetailAlertData(tenantId,erpDcId,ployDetailId);
        return success;
    }

    public void deletePloyDetailAlertData(String tenantId,String dcId,String ployDetailId) {
        long timestamp = System.currentTimeMillis();
        //更新上次熔断时间
        syncFailedStatDao.updateLastBreakTime(tenantId, dcId, ployDetailId, timestamp);
        //集成流熔断，删除告警聚合数据
        alertAggregationDao.delete(tenantId,
                dcId,
                ployDetailId,
                AlertAggregationType.SYNC_DATA_FAILED_ALERT);
        alertAggregationDao.delete(tenantId,
                dcId,
                ployDetailId,
                AlertAggregationType.POLLING_ERP_ALERT);
    }


    /**
     * 集成流失败上报
     */
    public void addErpErrorMonitor(String tenantId, final String erpDcId, final String ployDetailId, final String sourceObjectApiName) {
//        if (!configCenterConfig.isMonitorTenant(tenantId)) {
//            return;
//        }
//        final ObjectInvokeEntity objectInvokeEntity = ObjectInvokeEntity.initIntegrationStreamEntity(tenantId, ObjectInvokeStatus.blow);
//        objectInvokeEntity.setPloyDetailId(ployDetailId);
//        objectInvokeEntity.setObjectApiName(sourceObjectApiName);
//        objectInvokeEntity.setDcId(erpDcId);
//
//        MonitorUtil.send(objectInvokeEntity, MonitorType.INVOKE_MONITOR);
    }

    /**
     * 停用集成流
     */
    public boolean disablePloyDetailByStreamId(String tenantId, String ployDetailId, String sourceObjectApiName) {
        final long stopTime = System.currentTimeMillis();
        int result = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateStatusById(tenantId, ployDetailId, SyncPloyDetailStatusEnum.DISABLE.getStatus(), SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if (result < 0) {
            return false;
        }

        int i1 = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .updateStatusByPloyDetailId(ployDetailId, SyncPloyDetailStatusEnum.DISABLE.getStatus());

        syncPloyDetailSnapshotManager.setPloyDetailLastSyncTime(tenantId, ployDetailId, sourceObjectApiName, stopTime);

        return i1 > 0 && result > 0;
    }

    /**
     * 根据集成流id停用集成流
     * <p>
     * 1+N集成管控aop切了这个接口,有变更需要同步{@link DownstreamPloyDetailCircuitBreakerAspect#ployDetailCircuitBreaker}
     */
    public boolean disablePloyDetailByStreamId(String tenantId, String erpDcId, String ployDetailId, String sourceObjectApiName, String msg,boolean needMark) {
        //msg去掉换行
        msg = StrUtil.removeSuffix(msg,"\n");
        msg = StrUtil.blankToDefault(msg,i18NStringManager.getByEi(I18NStringEnum.s823, tenantId));
        boolean success = disablePloyDetailByStreamId(tenantId, ployDetailId, sourceObjectApiName);
        Integer i3 = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateValid(ployDetailId, false);
        log.info("update ploy detail status,{},result:{},{}", ployDetailId, success, i3);
        if (success) {
            long secondsToExpire = TimeUnit.DAYS.toSeconds(10L);
            if (needMark){
                //标记熔断。同步量的才需要标记
                String breakSetTopic = String.format(CommonConstant.REDIS_KEY_PLOY_BREAK_SET, tenantId);
                redisDataSource.saddAndExpire(breakSetTopic,ployDetailId,secondsToExpire,this.getClass().getSimpleName());
                //     上报集成流熔断监控
                try {
                    addErpErrorMonitor(tenantId, erpDcId, ployDetailId, sourceObjectApiName);
                } catch (Exception e) {
                    log.error("上报熔断监控失败, logId:{} tenantId:{} erpDcId:{} ployDetailId:{} sourceObjectApiName:{}", LogIdUtil.get(), tenantId, erpDcId, ployDetailId, sourceObjectApiName, e);
                }
            }
            //记录熔断信息
            String key = String.format(REDIS_KEY_EXCEPTION_PLOY_DETAIL, tenantId, ployDetailId);
            boolean setCache = redisCacheManager.setCache(key, msg, secondsToExpire, this.getClass().getSimpleName());
            UserOperatorLogManager.asyncSavaLog(UserOperatorLog.create(tenantId, erpDcId, "INTEGRATION_STREAM",
                    ployDetailId, -10000, "STOP", i18NStringManager.getByEi(I18NStringEnum.s824,tenantId) + msg, null));
        }
        return success;
    }

    public void disableAllStream(String tenantId,String msg){
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantId(tenantId);
        syncPloyDetailEntities.removeIf(v->!Objects.equals(v.getStatus(),SyncPloyDetailStatusEnum.ENABLE.getStatus()));
        for (SyncPloyDetailEntity syncPloyDetailData : syncPloyDetailEntities) {
            String erpDcId = syncPloyDetailData.getSourceTenantType().equals(TenantType.ERP)
                    ? syncPloyDetailData.getSourceDataCenterId() : syncPloyDetailData.getDestDataCenterId();
            disablePloyDetailByStreamId(tenantId,erpDcId,syncPloyDetailData.getId(),syncPloyDetailData.getSourceObjectApiName(),msg, false);
        }
    }


    public void fillScreenByPloyDetailId(String tenantId, SyncDataContextEvent syncDataContextEvent) {
        if (syncDataContextEvent.getSourceTenantType().equals(TenantTypeEnum.ERP.getType())) {
            String realObjApiName = erpObjManager.getRealObjApiName(tenantId, syncDataContextEvent.getMainObjApiName());
            syncDataContextEvent.setOutSideObjApiName(realObjApiName);
            syncDataContextEvent.setCrmObjApiName(syncDataContextEvent.getDestObjectApiName());
        } else {
            String realObjApiName = erpObjManager.getRealObjApiName(tenantId, syncDataContextEvent.getDestObjectApiName());
            syncDataContextEvent.setOutSideObjApiName(realObjApiName);
            syncDataContextEvent.setCrmObjApiName(syncDataContextEvent.getMainObjApiName());
        }
    }

}
