package com.fxiaoke.open.erpsyncdata.dbproxy.annotation;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.ReduceResultEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TransactionTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/12/11 11:54:38
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface SplitArgReduceResult {

    /**
     * 集合的参数名称
     */
    String value();

    /**
     * 按多少数量拆分批次
     */
    int num() default 0;

    /**
     * 到多少就不在查询了
     * 暂时只支持number类型的返回值
     */
    int limit() default 0;

    /**
     * 返回值的合并方法
     */
    ReduceResultEnum reduce() default ReduceResultEnum.Void;

    /**
     * 事务类型
     * 只有配置了对应事务类型的才进行事务处理
     */
    TransactionTypeEnum transactionType() default TransactionTypeEnum.NONE;
}
