package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.CompareResultDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.CheckMessageListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.ErpReSyncDataCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.JsonStringCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec.ReSyncDataNodeMsgCodec;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpReSyncData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReSyncDataNodeMsg;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.CharMatcher;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date: 15:47 2021/7/15
 * @Desc:
 */

@Slf4j
@Component
public class MongoStore {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    private DatastoreExt store;
    private String DATABASE = "erp_sync_data2";
    private String logDbName;
    private final static String erpReSyncDataCollectionPrefix = "erp_resync_data_";
    private final static String reSyncDataNodeMsgCollectionPrefix = "re_sync_data_node_msg_";
    private final static String erpComparePrefix = "erp_compare_data";

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry erpReSyncDataCodecRegistry = CodecRegistries.fromRegistries(
                CodecRegistries.fromCodecs(new ErpReSyncDataCodec(),new JsonStringCodec<>(CheckMessageListData.class)),
                MongoClientSettings.getDefaultCodecRegistry());
        private static final CodecRegistry codecCompareRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(CompareResultEntity.class)
                        .automatic(true).build()));
        private static final CodecRegistry reSyncDataNodeMsgCodecRegistry = CodecRegistries.fromRegistries(
                CodecRegistries.fromCodecs(new ReSyncDataNodeMsgCodec(),new JsonStringCodec<>(SyncDataEntity.class)),
                MongoClientSettings.getDefaultCodecRegistry());
    }

    @Autowired
    public MongoStore(DatastoreExt erpSyncDataMongoStore) {
        this.store = erpSyncDataMongoStore;
        this.logDbName = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
        //已有的集合，储存下来。不再在代码动态创建索引。代码仅用于创建新集合时创建索引，旧集合使用统一刷索引的接口
        store.getMongo().getDatabase(DATABASE)
                .listCollectionNames().iterator().forEachRemaining(v -> {
            if (v.startsWith(erpReSyncDataCollectionPrefix)) {
                erpReSyncDataCollectionCache.add(v);
            }else if (v.startsWith(erpComparePrefix)) {
                erpCompareCache.add(v);
            }else if (v.startsWith(reSyncDataNodeMsgCollectionPrefix)) {
                reSyncDataNodeMsgCollectionCache.add(v);
            }
        });
    }

    @Getter
    private final Set<String> erpReSyncDataCollectionCache = Sets.newConcurrentHashSet();
    @Getter
    private final Set<String> erpCompareCache = Sets.newConcurrentHashSet();
    @Getter
    private final Set<String> reSyncDataNodeMsgCollectionCache = Sets.newConcurrentHashSet();


    /**
     * 创建erpReSyncData集合
     * 用于依赖数据重试、socketTimeOut重试
     * @param tenantId
     */
    public synchronized MongoCollection<ErpReSyncData> getOrCreateErpReSyncDataCollectionAndIndex(String tenantId) {
        Pair<String, Long> name2ExpireTime = getErpReSyncDataCollectionName(tenantId);
        String erpReSyncDataCollectionName = name2ExpireTime.getLeft();
        MongoCollection<ErpReSyncData> collectionList = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.erpReSyncDataCodecRegistry)
                .getCollection(erpReSyncDataCollectionName, ErpReSyncData.class);
        if (!erpReSyncDataCollectionCache.add(erpReSyncDataCollectionName)) {
            return collectionList;
        }
        List<String> exists = Lists.newArrayList();
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间
        Long expireAfter = name2ExpireTime.getRight();
        if (!exists.remove(key)) {
            Bson index_expire_time = Indexes.ascending("expireTime");
            toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(expireAfter, TimeUnit.DAYS)));
        }
        key="index_ei_obj_id";
        if (!exists.remove(key)) {
            Bson index_ei_obj_id = Indexes.compoundIndex(Indexes.ascending("dataId"),
                    Indexes.ascending("objApiName"), Indexes.ascending("tenantId"));
            toBeCreate.add(new IndexModel(index_ei_obj_id, new IndexOptions().name(key).background(true)));
        }
        key="index_ei_type_status_utime";
        if (!exists.remove(key)) {
            Bson index_ei_type_status_utime = Indexes.compoundIndex(Indexes.ascending("tenantId"), Indexes.ascending("type"),
                    Indexes.ascending("status"), Indexes.ascending("updateTime"));
            toBeCreate.add(new IndexModel(index_ei_type_status_utime, new IndexOptions().name(key).background(true)));
        }
        if (!toBeCreate.isEmpty()) {
            List<String> created = collectionList.createIndexes(toBeCreate);
            log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        }
        //首次创建企业专用erpReSyncData表
        if (!erpReSyncDataCollectionName.equals(erpReSyncDataCollectionPrefix + "common")) {
            //从公共表复制数据
            MongoCollection<ErpReSyncData> tenantCollection = store.getMongo().getDatabase(DATABASE)
                    .withCodecRegistry(SingleCodecHolder.erpReSyncDataCodecRegistry)
                    .getCollection(erpReSyncDataCollectionName, ErpReSyncData.class);
            MongoCollection<ErpReSyncData> commonCollection = store.getMongo().getDatabase(DATABASE)
                    .withCodecRegistry(SingleCodecHolder.erpReSyncDataCodecRegistry)
                    .getCollection(erpReSyncDataCollectionPrefix + "common", ErpReSyncData.class);
            List<Bson> filters = Lists.newArrayList();
            filters.add(Filters.eq("tenantId", tenantId));
            MongoCursor<ErpReSyncData> iterator = commonCollection.find(Filters.and(filters)).iterator();
            List<ErpReSyncData> list = Lists.newArrayList();
            while (iterator.hasNext()) {
                list.add(iterator.next());
                if (list.size() == 1000) {
                    tenantCollection.insertMany(list);
                    list.clear();
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                tenantCollection.insertMany(list);
            }
            commonCollection.deleteMany(Filters.and(filters));
        }
        return collectionList;
    }

    /**
     * 创建event比对的集合
     *
     * @param tenantId
     */
    public synchronized MongoCollection<CompareResultDoc> getOrCreateCompare(String tenantId) {


        MongoCollection<CompareResultDoc> collectionList = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.codecCompareRegistry)
                .getCollection(erpComparePrefix, CompareResultDoc.class);
        if (!erpReSyncDataCollectionCache.add(erpComparePrefix)) {
            return collectionList;
        }
        List<String> exists = Lists.newArrayList();
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间

        if (!exists.remove(key)) {
            Bson index_expire_time = Indexes.ascending("tenantId");
            toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(7L, TimeUnit.DAYS)));
        }
        key="index_ei_obj_apiName";
        if (!exists.remove(key)) {
            Bson index_ei_obj_id = Indexes.compoundIndex(Indexes.ascending("tenantId"),
                    Indexes.ascending("sourceObjApiName"), Indexes.ascending("destObjectApiName"),Indexes.ascending("sourceDataId"));
            toBeCreate.add(new IndexModel(index_ei_obj_id, new IndexOptions().name(key).background(true)));
        }
        List<String> created = collectionList.createIndexes(toBeCreate);
        return collectionList;
    }


    private Pair<String, Long> getErpReSyncDataCollectionName(String tenantId) {
        Map<String, Long> erpReSyncDataConfig = tenantConfigurationManager.getErpReSyncDataCollectionConfig();
        if (StringUtils.isNotBlank(tenantId) && erpReSyncDataConfig.containsKey(tenantId)) {
            String collectionName = erpReSyncDataCollectionPrefix + tenantId;
            Long expireAfter = erpReSyncDataConfig.get(tenantId);
            if (expireAfter == null) {
                expireAfter = 7L;
            }
            return Pair.of(collectionName, expireAfter);
        } else {
            String collectionName = erpReSyncDataCollectionPrefix + "common";
            Long expireAfter = 7L;
            return Pair.of(collectionName, expireAfter);
        }
    }

    /**
     * 创建ReSyncDataNodeMsg集合
     * 用于重试节点数据重试
     * @param tenantId
     */
    public synchronized MongoCollection<ReSyncDataNodeMsg> getOrCreateReSyncDataNodeMsgCollectionAndIndex(String tenantId) {
        Pair<String, Long> name2ExpireTime = getReSyncDataNodeMsgCollectionName(tenantId);
        String reSyncDataNodeMsgCollectionName = name2ExpireTime.getLeft();
        MongoCollection<ReSyncDataNodeMsg> collectionList = store.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.reSyncDataNodeMsgCodecRegistry)
                .getCollection(reSyncDataNodeMsgCollectionName, ReSyncDataNodeMsg.class);
        if (!reSyncDataNodeMsgCollectionCache.add(reSyncDataNodeMsgCollectionName)) {
            return collectionList;
        }
        List<String> exists = Lists.newArrayList();
        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key = "index_expire_time";//过期时间，新的集合有效,如果要旧的集合有效，需要使用修改ttl索引的过期时间
        Long expireAfter = name2ExpireTime.getRight();
        if (!exists.remove(key)) {
            Bson index_expire_time = Indexes.ascending("createTime");
            toBeCreate.add(new IndexModel(index_expire_time, new IndexOptions().name(key).expireAfter(expireAfter, TimeUnit.DAYS)));
        }
        key="index_ei_next_time";
        if (!exists.remove(key)) {
            Bson index_next_time_ei = Indexes.compoundIndex(Indexes.ascending("tenantId"), Indexes.ascending("nextReSyncTime"));
            toBeCreate.add(new IndexModel(index_next_time_ei, new IndexOptions().name(key).background(true)));
        }
        key="index_streamId_ei";
        if (!exists.remove(key)) {
            Bson index_ei_type_status_time = Indexes.compoundIndex(Indexes.ascending("streamId"), Indexes.ascending("tenantId"));
            toBeCreate.add(new IndexModel(index_ei_type_status_time, new IndexOptions().name(key).background(true)));
        }
        key="index_uniqueKey";
        if (!exists.remove(key)) {
            Bson index_uniqueKey = Indexes.compoundIndex(Indexes.ascending("uniqueKey"));
            toBeCreate.add(new IndexModel(index_uniqueKey, new IndexOptions().name(key).background(true)));
        }

        if (!toBeCreate.isEmpty()) {
            List<String> created = collectionList.createIndexes(toBeCreate);
            log.info("created indices: {}, wanted: {}, created: {}", created, toBeCreate.size(), created.size());
        }
        //首次创建企业专用ReSyncDataNodeMsg表
        if (!reSyncDataNodeMsgCollectionName.equals(reSyncDataNodeMsgCollectionPrefix + "common")) {
            //从公共表复制数据
            MongoCollection<ErpReSyncData> tenantCollection = store.getMongo().getDatabase(DATABASE)
                    .withCodecRegistry(SingleCodecHolder.erpReSyncDataCodecRegistry)
                    .getCollection(reSyncDataNodeMsgCollectionName, ErpReSyncData.class);
            MongoCollection<ErpReSyncData> commonCollection = store.getMongo().getDatabase(DATABASE)
                    .withCodecRegistry(SingleCodecHolder.erpReSyncDataCodecRegistry)
                    .getCollection(erpReSyncDataCollectionPrefix + "common", ErpReSyncData.class);
            List<Bson> filters = Lists.newArrayList();
            filters.add(Filters.eq("tenantId", tenantId));
            MongoCursor<ErpReSyncData> iterator = commonCollection.find(Filters.and(filters)).iterator();
            List<ErpReSyncData> list = Lists.newArrayList();
            while (iterator.hasNext()) {
                list.add(iterator.next());
                if (list.size() == 1000) {
                    tenantCollection.insertMany(list);
                    list.clear();
                }
            }
            if (CollectionUtils.isNotEmpty(list)) {
                tenantCollection.insertMany(list);
            }
            commonCollection.deleteMany(Filters.and(filters));
        }
        return collectionList;
    }

    private Pair<String, Long> getReSyncDataNodeMsgCollectionName(String tenantId) {
        Map<String, Long> erpReSyncDataConfig = tenantConfigurationManager.getReSyncDataNodeMsgCollectionConfig();
        if (StringUtils.isNotBlank(tenantId) && erpReSyncDataConfig.containsKey(tenantId)) {
            String collectionName = reSyncDataNodeMsgCollectionPrefix + tenantId;
            Long expireAfter = erpReSyncDataConfig.get(tenantId);
            if (expireAfter == null) {
                expireAfter = 2L;
            }
            return Pair.of(collectionName, expireAfter);
        } else {
            String collectionName = reSyncDataNodeMsgCollectionPrefix + "common";
            Long expireAfter = 2L;
            return Pair.of(collectionName, expireAfter);
        }
    }
}
