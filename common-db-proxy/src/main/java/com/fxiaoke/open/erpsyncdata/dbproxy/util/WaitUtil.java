package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import lombok.SneakyThrows;

import java.util.concurrent.Callable;
import java.util.function.Predicate;

/**
 * 等待工具类
 *
 * <AUTHOR> (^_−)☆
 */
public class WaitUtil {


    @SneakyThrows
    public static <T> T fetchResourceWithDelays(Callable<T> resourceFunction, Iterable<? extends Number> delaysMillis) {
        return fetchResourceWithDelays(resourceFunction, t -> t != null, delaysMillis);
    }


    @SneakyThrows
    public static <T> T fetchResourceWithDelays(Callable<T> resourceFunction, Predicate<T> predicate, Iterable<? extends Number> delaysMillis) {
        T resource = null;
        for (Number delay : delaysMillis) {
            // 等待指定毫秒
            ThreadUtil.safeSleep(delay);

            // 尝试获取资源
            resource = resourceFunction.call();
            if (BooleanUtil.isTrue(predicate.test(resource))) {
                return resource;
            }
        }
        return resource;
    }
}
