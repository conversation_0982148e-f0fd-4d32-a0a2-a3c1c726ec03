package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * oa字段信息
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "oa_object_field")
public class OAObjFieldEntity implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 对象名称
     */
    @Column(name = "obj_api_name")
    private String objApiName;


    /**
     * 标签
     */
    @Column(name = "label")
    private String label;

    /**
     * 字段ApiName
     */
    @Column(name = "field_api_name")
    private String fieldApiName;

    /**
     * 占位符
     */
    @Column(name = "replace_name")
    private String replaceName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Long updateTime;

}