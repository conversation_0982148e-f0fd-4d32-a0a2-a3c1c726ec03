package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022-10-31
 */
public class UpdateMapping {
    /**
     * <AUTHOR> (^_−)☆
     * @date 2022-10-28
     */
    @Data()
    @Builder
    public static class BySyncDataIdArg {
        private final String syncDataId;
        private final Integer lastSyncStatus;
        /**
         * 为null时不更新
         */
        private final String destDataName;
        private final String remark;
    }

    /**
     * 创建成功时更新的参数
     * <AUTHOR> (^_−)☆
     * @date 2022-10-28
     */
    @Data()
    @Builder
    public static class BySourceArg{
        private final String sourceObjectApiName;
        private final String destObjectApiName;
        private final String sourceDataId;
        private final Integer lastSyncStatus;
        /**
         * 为null时不更新destDataId和destDataName
         */
        private final String destDataId;
        private final String destDataName;
        private final String remark;
        private final String syncDataId;
    }

}
