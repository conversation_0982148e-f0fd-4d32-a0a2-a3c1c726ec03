package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateEventConfigModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

/**
 * token表
 * <AUTHOR>
 * @date 2023-09-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("erpObjApiName")
@Table(name = "erp_k3_ultimate_token")
public class ErpK3UltimateTokenEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 企业ei
     */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * erp对象apiName
     */
    private String erpObjApiName;

    /**
     * token版本，默认为1.0,后期可能会扩展到2.0，3.0
     */
    private String version;

    /**
     * 时间戳
     */
    private String timestamps;

    /**
     * 根据tenantId，dataCenterId，erpObjApiName，version，timestamps动态生成的token
     */
    private String token;

    /**
     * 订阅事件json配置
     */
    private String eventConfig;

    @Transient
    private List<K3UltimateEventConfigModel> eventConfigList;

   /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}