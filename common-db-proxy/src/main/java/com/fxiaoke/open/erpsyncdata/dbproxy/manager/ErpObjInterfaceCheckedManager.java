package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjInterfaceCheckedDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjInterfaceCheckedEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ErpObjInterfaceCheckedManager {
    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ErpObjInterfaceCheckedDao erpObjInterfaceCheckedDao;

    public int insertOrUpdate(ErpObjInterfaceCheckedEntity entity) {
        ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                .findData(entity.getTenantId(),
                        entity.getDataCenterId(),
                        entity.getObjApiName(),
                        entity.getInterfaceUrl());
        if (checkedEntity != null) {
            checkedEntity.setCheckedInterfaceType(entity.getCheckedInterfaceType());
            checkedEntity.setUpdateTime(System.currentTimeMillis());
            int count = erpObjInterfaceCheckedDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                    .updateById(checkedEntity);
            log.info("ErpObjInterfaceCheckedManager.insertOrUpdate,updateById,count={}", count);
            return count;
        }

        entity.setId(idGenerator.get());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        int count = erpObjInterfaceCheckedDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                .insert(entity);
        log.info("ErpObjInterfaceCheckedManager.insertOrUpdate,insert,count={}", count);
        return count;
    }

    public ErpObjInterfaceCheckedEntity findOne(String tenantId,
                                                String dataCenterId,
                                                String objApiName,
                                                ErpObjInterfaceUrlEnum interfaceUrl) {
        return erpObjInterfaceCheckedDao.findData(tenantId,
                dataCenterId,
                objApiName,
                interfaceUrl);
    }
}
