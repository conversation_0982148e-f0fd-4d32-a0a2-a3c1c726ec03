package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * ERP对象ID和编码映射表
 * <AUTHOR>
 * @date 20230313
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ObjApiName("objApiName")
@Table(name = "erp_obj_id_number_mapping")
public class ErpObjIdNumberMappingEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 3739310944368869L;
    /**
     * 主键ID
     */
    private String id;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 对象apiName
     */
    private String objApiName;
    /**
     * 数据ID
     */
    private String dataId;
    /**
     * 物料编码
     */
    private String dataNumber;
    /**
     * 物料名称
     */
    private String dataName;
    /**
     * 数据中心ID
     */
    private String dcId;
}
