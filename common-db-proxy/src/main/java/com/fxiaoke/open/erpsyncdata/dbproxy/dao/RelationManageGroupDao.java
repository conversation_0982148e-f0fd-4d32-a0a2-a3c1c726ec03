package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/21 12:01:52
 */
@Repository
public interface RelationManageGroupDao extends BaseTenantDao<RelationManageGroupEntity, RelationManageGroupDao> {

    RelationManageGroupEntity getById(@Param("tenantId") String tenantId, @Param("id") String id);

    /**
     * 根据上游企业,获取代管企业信息
     * @return 去重后的templateId和dcId,其他字段为空
     */
    List<RelationManageGroupEntity> queryAllByTenantId(@Param("tenantId") String tenantId);

    /**
     * 根据模板企业id,获取代管企业分组信息
     */
    List<RelationManageGroupEntity> queryAllByTemplateId(@Param("tenantId") String tenantId);


    /**
     * 获取所有模板企业id
     */
    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    List<String> getAllTemplateIds();

    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    boolean isTemplateId(@Param("tenantId") String tenantId);

    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    String getUpStreamIdByTemplateId(@Param("tenantId") String tenantId);

    RelationManageGroupEntity queryByTemplateIdAndDcId(@Param("tenantId") String templateId, @Param("dcId") String dcId);

    RelationManageGroupEntity queryByTenantIdAndDcId(@Param("tenantId") String templateId, @Param("dcId") String dcId);

    int deleteByTenantIdAndId(@Param("tenantId") String tenantId, @Param("id") String id);

    int refreshUpdateTime(@Param("templateId") String templateId, @Param("id") String id);
}
