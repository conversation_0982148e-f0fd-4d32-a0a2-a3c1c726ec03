package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ManagedTenantReplace;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.github.mybatis.mapper.ITenant;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/2/29 19:46:27
 * 对下游代管企业,在查询时,需要获取模板企业id,查询模板企业的信息
 * 连接器/对象/对象字段/对象api/集成流/集成流快照/push token
 */
@Slf4j
@Aspect
/**
 * 指定order为最低优先级,确保在JetCache后执行
 */
@Order
@Component
public class MybatisReplaceEnterpriseAspect extends AbstractReplaceEnterpriseAspect {

    @Autowired
    private RelationErpShardDao relationErpShardDao;

    @Autowired
    private RelationManageGroupDao relationManageGroupDao;

    @Autowired
    @Lazy
    private ErpSyncTimeDao erpSyncTimeDao;

    // 写操作方法名前缀
    private final Set<String> writeMethodPrefix = Sets.newHashSet("insert", "update", "delete", "batchUpdate", "invalid", "batchInsert", "batchDelete", "superInsert", "superUpdate", "truncate", "create", "drop", "stopTask", "bulkUpdate", "merge");

    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao..*.*(..)) ")
    public Object pgReplaceManageData(ProceedingJoinPoint jp) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        if (!methodNeedReplace(methodSignature, jp.getTarget().getClass())){
            return jp.proceed();
        }

        // 特殊支持的接口
        final Method method = methodSignature.getMethod();
        final SpecialMethodReplaceService specialMethodReplaceService = specialMethodResultReplaceMap.get(method);
        if (Objects.nonNull(specialMethodReplaceService)) {
            return handlerSpecialMethodResultReplace(jp, specialMethodReplaceService);
        }

        // 入参的tenantId为一个企业
        final String tenantId = getTenantIdByParameter(jp);
        if (Objects.nonNull(tenantId)) {
            if (managedEnterprise(tenantId)) {
                return replaceTenantId(jp, tenantId);
            }
            return jp.proceed();
        }

        // 入参的tenantId为多个企业
        final List<String> tenantIds = getTenantIdsByParameter(jp);
        if (CollectionUtils.isNotEmpty(tenantIds) && tenantIds.stream().anyMatch(this::managedEnterprise)) {
            return replaceTenantIdList(jp, tenantIds);
        }

        return jp.proceed();
    }

    private Map<Pair<Method, Class<?>>, Boolean> needReplaceMethodCache = new ConcurrentHashMap<>();

    private boolean methodNeedReplace(MethodSignature methodSignature, Class<?> clazz) {
        final Method method = methodSignature.getMethod();
        return needReplaceMethodCache.computeIfAbsent(Pair.of(method, clazz), key -> {
            final String methodName = method.getName();
            if (method.getDeclaringClass().equals(Object.class) || writeMethodPrefix.stream().anyMatch(methodName::startsWith)) {
                return false;
            }

            // 调用父类接口时,使用getDeclaringType会拿到父类 所以不能直接用methodSignature.getDeclaringType()
            final Class<?> declaringType = Arrays.stream(clazz.getInterfaces())
                    .filter(type -> !type.getSimpleName().contains("Base"))
                    .findFirst()
                    .orElse(methodSignature.getDeclaringType());
            final ManagedTenantReplace annotation = AnnotationUtils.findAnnotation(declaringType, ManagedTenantReplace.class);
            if (Objects.isNull(annotation)) {
                return false;
            }

            return true;
        });
    }

    /**
     * 需要返回代理对象
     * @see ErpSyncTimeDao#listSyncExtentByTenantId(String)
     * @see MybatisReplaceEnterpriseAspect#replaceListSyncExtent
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao.setTenantId(String)) && args(tenantId)")
    public Object returnAopProxy(ProceedingJoinPoint jp, String tenantId) throws Throwable {
        return setTenantIdReturnProxy.handler(jp);
    }

    /**
     * 接口内联了快照表,需要替换为模板企业的快照表
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao.listSyncExtentByTenantId(String)) && args(tenantId)")
    public Object replaceListSyncExtent(ProceedingJoinPoint jp, String tenantId) throws Throwable {
        if (Objects.isNull(tenantId) || !managedEnterprise(tenantId)) {
            return jp.proceed();
        }

        final String templateId = getTemplateId(tenantId);
        if (Objects.isNull(templateId)) {
            return jp.proceed();
        }

        final List<ErpSyncExtentDTO> proceed = erpSyncTimeDao.listSyncExtent(tenantId, templateId);

        return replaceResultTenantId(proceed, tenantId);
    }

    /**
     * ErpConnectInfoEntity 只返回加入的模板数据
     */
    @Override
    protected Object replaceResultTenantId(Object proceed, String downstreamId) {
        if (Objects.isNull(proceed) || isPrimitive(proceed)) {
            return proceed;
        }

        if (!(proceed instanceof List) || !CollectionUtils.isNotEmpty((List<?>) proceed) || !((List<?>) proceed).stream().allMatch(ErpConnectInfoEntity.class::isInstance)) {
            return super.replaceResultTenantId(proceed, downstreamId);
        }

        final List<RelationErpShardDto> relationErpShardDtos = relationErpShardDao.queryByDownstreamId(downstreamId);
        final String templateId = relationErpShardDtos.get(0).getTemplateId();
        final Set<String> dcIds = relationErpShardDtos.stream()
                .filter(dto -> Objects.equals(templateId, dto.getTemplateId()))
                .map(RelationErpShardDto::getDcId)
                .collect(Collectors.toSet());

        final Object returnValue = ((List<?>) proceed).stream()
                .map(x -> (ErpConnectInfoEntity) x)
                .filter(entity -> Objects.equals(entity.getChannel(), ErpChannelEnum.CRM) || dcIds.contains(entity.getId()))
                .collect(Collectors.toList());
        return super.replaceResultTenantId(returnValue, downstreamId);
    }

    // 👇🏻特殊方法处理


    private Object handlerSpecialMethodResultReplace(ProceedingJoinPoint jp, SpecialMethodReplaceService specialMethodReplaceService) throws Throwable {
        if (!specialMethodReplaceService.isAccept(jp.getArgs())) {
            return jp.proceed();
        }

        return specialMethodReplaceService.handler(jp);
    }

    /**
     * 特殊接口支持
     */
    public interface SpecialMethodReplaceService {
        Method method();

        boolean isAccept(Object[] args);

        Object handler(ProceedingJoinPoint jp) throws Throwable;
    }

    /**
     * 处理没有入参且返回值为TenantIdList的
     * 假如返回值中有模板企业,则将对应的下游企业id加到返回值中
     */
    public abstract class NoArgTenantIdListResultReplace implements SpecialMethodReplaceService {
        @Override
        public boolean isAccept(Object[] args) {
            return Objects.isNull(args) || args.length == 0;
        }

        @Override
        public Object handler(ProceedingJoinPoint jp) throws Throwable {
            final Object proceed = jp.proceed();
            handleResult(proceed);
            return proceed;
        }

        public void handleResult(Object result) {
            if (Objects.isNull(result) || !(result instanceof List) || CollectionUtils.isEmpty((List<?>) result) || !(((List<?>) result).stream().allMatch(String.class::isInstance))) {
                return;
            }

            final Set<String> allTemplateIds = Sets.newHashSet(relationManageGroupDao.getAllTemplateIds());
            if (CollectionUtils.isEmpty(allTemplateIds)) {
                return;
            }

            final List<String> list = (List<String>) result;
            List<String> manageTenantIds = list.stream()
                    .filter(allTemplateIds::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(manageTenantIds)) {
                return;
            }

            // 加上所有下游企业id
            final Set<String> set = Sets.newHashSet(list);
            manageTenantIds.stream()
                    .map(relationErpShardDao::getAllDownstreamIdsByTemplateId)
                    .flatMap(Collection::stream)
                    .distinct()
                    .filter(id -> !set.contains(id))
                    .forEach(list::add);
        }
    }

    public final SpecialMethodReplaceService listEnableSnapshotReplaceService = new NoArgTenantIdListResultReplace() {

        private final Method method = Arrays.stream(SyncPloyDetailSnapshotDao.class.getMethods())
                .filter(method1 -> method1.getName().equals("listEnableSnapshotTenantId"))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("SyncPloyDetailSnapshotDao not find listEnableSnapshotTenantId method"));

        @Override
        public Method method() {
            return method;
        }
    };

    public final SpecialMethodReplaceService queryListReplaceService = new SpecialMethodReplaceService() {
        private final Gson gson = new Gson();

        private final Method method = Arrays.stream(ErpBaseDao.class.getMethods())
                .filter(method1 -> method1.getName().equals("queryList"))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("ErpBaseDao not found queryList method"));

        @Override
        public Method method() {
            return method;
        }

        @Override
        public boolean isAccept(Object[] args) {
            if (args.length != 1 && !isPrimitive(args[0])) {
                return false;
            }

            final ReplaceData<Object> replaceData = getReplaceData(args[0]);
            final String tenantId = replaceData.getFindTenantIdFunc().apply(args[0]);

            return Objects.nonNull(tenantId) && managedEnterprise(tenantId) && Objects.nonNull(getTemplateId(tenantId));
        }

        @Override
        public Object handler(ProceedingJoinPoint jp) throws Throwable {
            final Object param = jp.getArgs()[0];
            // com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity.getRequired 导致不能使用FastJson
            final Object arg = JSON.parseObject(gson.toJson(param), param.getClass());
            final ReplaceData<Object> replaceData = getReplaceData(arg);
            final String tenantId = replaceData.getFindTenantIdFunc().apply(arg);

            final String templateId = getTemplateId(tenantId);

            replaceData.getReplaceTenantIdFunc().accept(arg, templateId);
            final Object proceed = jp.proceed(new Object[]{arg});

            return replaceResultTenantId(proceed, tenantId);
        }
    };

    /**
     * {@link ITenant#setTenantId}
     * {@link BaseTenantDao#setGlobalTenant}
     * {@link BaseTenantMapper#setGlobalTenant}
     * 方法返回的this是没有aop代理的对象,所以这里需要返回aop代理对象
     */
    public abstract static class SetTenantIdReturnProxy implements SpecialMethodReplaceService {
        @Override
        public boolean isAccept(Object[] args) {
            return args.length == 1 && args[0] instanceof String;
        }

        /**
         * 1.返回this,防止不进aop
         * 2.现在使用的模板数据都没有分库,所以不需要替换企业id
         */
        @Override
        public Object handler(ProceedingJoinPoint jp) throws Throwable {
            jp.proceed();
            return jp.getThis();
        }
    }

    public final SpecialMethodReplaceService setTenantIdReturnProxy = new SetTenantIdReturnProxy() {
        private final Method method = Arrays.stream(ITenant.class.getMethods())
                .filter(method1 -> method1.getName().equals("setTenantId"))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("ITenant not found setTenantId method"));

        @Override
        public Method method() {
            return method;
        }
    };

    public final SpecialMethodReplaceService setGlobalTenantReturnProxy = new SetTenantIdReturnProxy() {
        private final Method method = Arrays.stream(BaseTenantDao.class.getMethods())
                .filter(method1 -> method1.getName().equals("setGlobalTenant"))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("BaseTenantDao not found setGlobalTenant method"));

        @Override
        public Method method() {
            return method;
        }
    };

    public final SpecialMethodReplaceService setGlobalTenantReturnProxy2 = new SetTenantIdReturnProxy() {
        private final Method method = Arrays.stream(BaseTenantMapper.class.getMethods())
                .filter(method1 -> method1.getName().equals("setGlobalTenant"))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("BaseTenantMapper not found setGlobalTenant method"));

        @Override
        public Method method() {
            return method;
        }
    };

    private final Map<Method, SpecialMethodReplaceService> specialMethodResultReplaceMap = Stream.of(setTenantIdReturnProxy, setGlobalTenantReturnProxy, setGlobalTenantReturnProxy2, listEnableSnapshotReplaceService, queryListReplaceService).collect(Collectors.toMap(SpecialMethodReplaceService::method, Function.identity()));
}
