package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.common.IpUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

import java.util.Date;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/26
 */
@Entity(value = "time_point", noClassnameStored = true)
@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimePoint {

    /**
     * id
     */
    @Id
    private ObjectId id;
    /**
     * 企业id
     */
    private String tenantId;
    /**
     * 源对象名称
     */
    private String objApiName;
    /**
     * 源数据Id或同步数据Id
     */
    private String dataId;
    /**
     * 事件名称
     */
    private String pointName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态：1：已落库，2：计算中，3：已上传
     */
    private int status;

    /**
     * 线程名称，同一时间段同一线程只会跑一条数据
     */
    private String threadName;

    /**
     * 发送方ip地址
     */
    private String sender;

    /**
     * traceId
     */
    private String traceId;

    public static TimePoint create(String tenantId, String pointName, String objApiName, String dataId) {
        Date createTime = new Date();
        TimePoint timePoint = TimePoint.builder()
                .tenantId(tenantId)
                .dataId(dataId)
                .pointName(pointName)
                .objApiName(objApiName)
                .createTime(createTime)
                .status(1)
                .sender(IpUtil.getSiteLocalIp())
                .threadName(Thread.currentThread().getName())
                .traceId(TraceUtil.get())
                .build();
        return timePoint;
    }
}
