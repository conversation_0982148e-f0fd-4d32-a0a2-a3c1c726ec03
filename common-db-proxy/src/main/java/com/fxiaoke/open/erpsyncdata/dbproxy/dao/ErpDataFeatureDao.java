package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDataFeatureEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;

/**
 * 
 * <AUTHOR> (^_−)☆
 * @date 2021/5/20
 */
public interface ErpDataFeatureDao extends ErpBaseDao<ErpDataFeatureEntity>, ITenant<ErpDataFeatureDao> {
    /**
     * 获取特征值
     * @param tenantId
     * @param dataId
     * @return
     */
    ErpDataFeatureEntity getOne(@Param("tenantId")String tenantId, @Param("dataId")String dataId);

}