package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.*;
import lombok.experimental.Accessors;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 19:01 2023/1/12
 * @Desc:
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class NeedReportFsEiMsgDoc {

    /**
     * 根据type不一样
     */
    private String uniqueKey;

    /**
     * id
     */
    @BsonId
    private ObjectId id;
    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 连接器id
     */
    private String dcId;
    /**
     * 渠道
     */
    private String channel;

    /**
     * 连接信息创建时间
     */
    private Long connectCreateTime;
    /**
     * 类型
     */
    private String type;

    /**
     * 需要检查
     */
    private Boolean needCheck;

    /**
     * 是否完成
     */
    private Boolean isFinish;


    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 数据创建时间
     */
    private Date createTime;


    public static NeedReportFsEiMsgDoc create(String tenantId, String dcId,String channel,Long connectCreateTime, String type) {
        Date createTime = new Date();
        StringBuffer uniqueKey = new StringBuffer();

        uniqueKey.append(type).append("_").append(tenantId).append("_").append(dcId);


        NeedReportFsEiMsgDoc timePoint = NeedReportFsEiMsgDoc.builder()
                .uniqueKey(uniqueKey.toString())
                .tenantId(tenantId)
                .dcId(dcId)
                .channel(channel)
                .connectCreateTime(connectCreateTime)
                .type(type)
                .needCheck(true)
                .isFinish(false)
                .createTime(createTime)
                .updateTime(createTime)
                .build();

        return timePoint;
    }
}
