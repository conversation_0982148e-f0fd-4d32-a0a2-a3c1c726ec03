package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3SourceBillInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataWebEnvironmentEnum;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
public class ConfigCenter {
    /**
     * 不开启专表企业
     */
    public static Set<String> DISABLE_DYNAMIC_TENANTS;
    /**
     * 每次轮询拉取erp数据最大数量
     */
    public static Integer ERP_LIST_MAX_NUM = 10000;

    /**
     * 数据同步执行超时时间,单位：分钟,默认10分钟
     */
    public static Integer END_UPDATE_TIME_OUT_MIN = 10;

    /**
     * 超级管理员
     */
    public static Set<String> SUPER_ADMINS = new HashSet<>();

    /**
     * key:formId value：pair<源单类型字段，源单编号字段>
     */
    public static Map<String, List<K3SourceBillInfo>> K3_SOURCE_BILL_INFO_MAP;

    /**
     * 手动执行策略url    manualExecutePloys
     */
    public static String MANUAL_EXECUTE_PLOYS_URL;
    /**
     * erp数据同步appId
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";
    /**
     * (重要）erp数据同步appId
     */
    public static String PRIMARY_ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * 导入接口超时时间
     */
    public static Long IMPORT_TIME_OUT = 2000L;
    /**
     * 导入接口超时时间
     */
    public static Long EXPORT_TIME_OUT = 5000L;
    /**
     * 导出数据维护文件包含的条数
     */
    public static Integer EXPORT_DATA_NUMBER = 50000;
    /**
     * 导出数据维护查询映射表的数量
     */
    public static Integer EXPORT_DATA_QUERY_NUMBER = 100;
    /**
     * 用来储存K3预设描述的企业id
     */
    public static String STORE_DEFAULT_K3_OBJ_TENANT_ID = "01";
    /**
     * inner标准接口调用的url前缀
     */
    public static String CRM_OBJECT_INNER_URL_PREFIX;
    /**
     * 元数据接口url前缀
     */
    public static String CRM_METADATA_URL_PREFIX;

    /**
     * 通用不带验证跳转
     */
    public static Set<String> SSO_ENTERPRISE_EI = Sets.newHashSet();

    /**
     * 编码ID对象表
     */
    public static Map<String, String> APINAME_FID_FNUMBER_MAPPING = new HashMap<>();

    /**
     * 环境key,{@link ErpSyncDataWebEnvironmentEnum}里面的environment
     */
    public static String ServiceEnvironment;

    /**
     * U8需要审核的单据apiName
     */
    public static Set<String> U8_VERIFY_APINAME_SET;

    /**
     * U8的masterId的格式
     */
    public static Map<String, String> U8_MASTERID_STRUCTURE_MAP = new HashMap<>();

    /**
     * 不需要过滤的字段
     */
    public static Set<String> NOT_NEED_FILTER_FIELD = Sets.newHashSet();

    /**
     * 需要转换成null的K3DataId
     */
    public static Set<String> TO_NULL_K3_DATA_ID = new HashSet<>();
    /**
     * 获取客户端和服务器建立连接的timeout
     */
    public static Integer CONNECT_TIME_OUT = 20000;

    /**
     * OA重试时间范围,默认2天
     */
    public static Long OA_RE_TIME = 172800000L;

    /**
     * erpTempData的mongo数据过期时间 单位：小时
     */
    public static Long ERP_TEMP_DATA_MONGO_EXPIRE_TIME = 12 + 24 * 30L;



    /**
     * 用户操作日志的mongo数据过期时间 单位：日
     */
    public static Long USER_OPERATOR_LOG_MONGO_EXPIRE_TIME = 180L;

    /**
     * 单条数据Content-Length大小限制
     */
    public static Long CONTENT_LENGTH_LIMIT = (1024 * 1024 * 2L);

    /**
     * 列表 Content-Length大小限制 默认限制10M
     */
    public static Long LIST_CONTENT_LENGTH_LIMIT = (1024 * 1024 * 10L);

    /**
     * CH存储不能超1M,单个字段按500K算
     */
    public static Long LIST_CONTENT_LOG_LENGTH_LIMIT = 500 * 1024L;

    // CH存储不能超1M,按arg 500K算,假如5K/条,最多100条
    public static int NEED_SIMPLIFY_BYTE_LIST_SIZE = 100;

    public static boolean ACCURATE_CALCULATION_BATCH_WRITE_BYTE = false;


    /**
     * 接口日志是否写mongo库
     */
    public static Boolean INTERFACE_MONITOR_WRITE_MONGO = true;

    /**
     * 不保存日志监控的企业
     */
    public static Set<String> NOT_SAVE_INTERFACE_MONITOR_TENANTS = ImmutableSet.of();
    /**
     * 仅报错错误日志监控的企业
     */
    public static Set<String> ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS = ImmutableSet.of();

    /**
     * 超过16M过滤的对象名单
     */
    public static Set<String> OVER_OBJ_NAMES = Sets.newHashSet();

    /**
     * 历史数据任务同步中超时时间(毫秒)
     */
    public static Integer HISTORY_DATA_TASK_OVERTIME = 30 * 60 * 1000;


    /**
     * 灰度OA走MQ
     */
    public static Set<String> OA_MQ_GRAY_TENANT_ID = Sets.newHashSet();

    /**
     * 为方便112测试多企业场景，支持配置线程池核心线程数
     */
    public static Integer THREAD_POOL_CORE_SIZE = 10;

    /**
     * 拆分参数默认数量
     */
    public static Integer SPLIT_ARG_DEFAULT_NUM = 100;

    /**
     * SAML协议idp唯一标识
     */
    public static String IDP_ENTITY_ID = "https://www.ceshi112.com/erp/syncdata/open/saml/metadata";

    /**
     * 锐捷免登
     */
    public static String RUI_JIE_SSO = "https://erpdss.my.ceshi112.com/saml2/sp/sso/login";

    /**
     * 配置在纷享的SP唯一标识
     */
    public static String SP_ENTITY_ID = "spn:434e38be64b7f51a674bce6dcad2422e";
    /**
     * 域名管理
     */
    public static String ERP_DOMAIN_URL = "https://${variables_endpoint.http_domain}";
    /**
     * 域名管理,默认与ERP_DOMAIN_URL相同
     */
    public static String ERP_OPEN_DOMAIN_URL = ERP_DOMAIN_URL;

    /**
     * CSM客户对象标识
     */
    public static String CSM_CUSTOMER_API_NAME = "object_3UIfd__c";
    /**
     * 实施项目（新）对象标识
     */
    public static String IMPLEMENT_PROJECT_API_NAME = "object_R7poC__c";

    /**
     * FS企业账号
     */
    public static String FS_ENTERPRISE_ID = "78052";

    /**
     * CSM客户企业账号字段apiname
     */
    public static String CS_CUSTOMER_ENTERPRISE_API_NAME = "field_1O0qO__c";

    /**
     * 实施项目(新)企业账号字段apiname
     */
    public static String IMPLEMENT_PROJECT_ENTERPRISE_ID = "field_WOqvv__c";

    /**
     * 推送数据触发通知的数量
     */
    public static Integer PUSH_DATA_SIZE_NOTIFY = 1000;

    /**
     * 通过BillQuery接口查某条，查询条数限制
     */
    public static int BILL_QUERY_SIZE_LIMIT = 50000;

    /**
     * 重试依赖数据天数（重试超过时间就删除掉）
     */
    public static Integer DO_RE_SYNC_DATA_DAY = 2;

    /**
     * 初始化映射，每limit条睡眠时间
     */
    public static Long INIT_MAPPING_SLEEP_MS_PER_LIMIT = 1000 * 3L;

    /**
     * fs的客户对象的企业Id字段
     */
    public static String FS_ACCOUNT_TENANT_ID = "UDInt1__c";

    /**
     * fs的客户对象的客户级别字段
     */
    @Deprecated
    public static String FS_ACCOUNT_ENTERPRISE_LEVEL = "UDSSel31__c";
    /**
     * fs的客户对象的字段 产研-服务等级（多选）
     */
    public static String FS_ACCOUNT_ENTERPRISE_LEVEL_NEW = "field_e5798__c";
    /**
     * fs的客户对象的客户级别value->label
     */
    public static Map<String, String> LEVEL_VALUE_TO_LABEL;

    /**
     * 分配路由的url前缀
     */
    public static String DB_POR_URL;
    /**
     * 是否消费沙盒/更改集事件
     * 注意将erpsync_sandbox_consumer配置为特殊的consumer，不然会让正式环境的mq不处理
     */
    public static boolean ENABLE_CONSUME_SANDBOX_EVENT = true;

    /**
     * 后端all与web对应关系
     */
    public static Map<String, String> ALL_ENV_TO_WEB_ENV = Maps.newHashMap();
    /**
     * 后端web与前端应用对应关系
     */
    public static Map<String, String> WEB_ENV_TO_WEB_APP_ENV = Maps.newHashMap();

    /**
     * 默认pg路由
     * 一定要配置
     */
    public static String DEFAULT_CONFIG_ROUTE_SOURCE_ID;

    /**
     * 沙盒默认pg路由
     * 一定要配置
     */
    public static String DEFAULT_SANDBOX_CONFIG_ROUTE_SOURCE_ID;

    /**
     * 默认vip企业pg路由
     */
    public static String DEFAULT_VIP_CONFIG_ROUTE_SOURCE_ID;

    /**
     * 纷享vip及以上级别标识
     */
    public static Set<String> VIP_TENANT_LEVEL_VALUE;

    /**
     * k3存在作废字段FCancelStatus的对象
     */
    public static Set<String> K3_EXIST_CANCEL_OBJ = new HashSet<>();

    /**
     * 开启扫描临时库的环境
     */
    public static Set<String> ENABLE_SCAN_TEMP_ENVS = new HashSet<>();
    /**
     * 扫描临时库数据的分钟
     */
    public static int MIN_OF_DAY_2_SCAN_TEMP = 120;


    /**
     * apibus变量
     */
    public static String APIBUS_NCRM = "";
    /**
     * apibus_global
     */
    public static String APIBUS_GLOBAL = "http://${variables_endpoint.svc_apibus_global}";
    /**
     * 同一条数据在TIME_OUT_SYNC_DATA_TIME内不再次超时重试
     */
    public static Long TIME_OUT_SYNC_DATA_TIME = 30 * 60L;
;
    /**
     * 默认连续次数失败告警阈值
     */
    public static int POLLING_ALERT_THRESHOLDS = 10;
    /**
     * 默认异常增量熔断阈值
     */
    public static int FAILED_INCREMENT_BREAK_THRESHOLDS = 1000;
    /**
     * 默认异常增量告警阈值
     */
    public static int FAILED_INCREMENT_ALERT_THRESHOLDS = 500;

    /**
     * tenant config 表缓存到内存中，多长时间过期。单位ms.
     */
    public static Long TENANT_CONFIG_MAP_CACHE_EXPIRE_TIME = 3 * 60 * 1000L;

    /**
     * 灰度触发环境
     */
    public static Set<String> TRIGGER_GRAY = Sets.newHashSet();


    /**
     * 存在重复数据的页数量阈值
     */
    public static Integer NUMBER_OF_PAGES_WITH_DUPLICATE_DATA = 2;

    /**
     * k3渠道字段值由null转""字段类型
     */
    public static Set<String> K3_NULL_TO_EMPTY_STRING = Sets.newHashSet();
    /**
     * 触发task,轮询临时库数据
     */
    public static String TASK_BASE_URL = "http://localhost/task";

    /**
     * web环境对应的url
     */
    public static Map<String, String> WEB_ENV_URL = Maps.newHashMap();

    /**
     * 加密秘钥
     */
    public static String SECRET_KEY = "[B@5c909414";

    /**
     * 通知时间延迟，默认5min
     */
    public static Integer NOTIFY_DELAY_TIME_SECONDS = 60 * 5;

    /**
     * 文件预览路径
     */
    public static String dispatcherLockServicePrefix="erpSyncData-recorder-lock";

    public static String RECORD_AUDIT_LOG_URL = null;

    /**
     * 客群消息通知时间延迟，默认1min
     */
    public static Integer CUSTOM_NOTIFY_DELAY_TIME_SECONDS = 60 * 2;

    /**
     * 隐藏产品分类入口日期,2023.03.22
     */
    public static Long HIDE_CATEGORY_ENTRANCE_DATE = 1679414400000L;

    /**
     * sfa 需要主从一起更新的crm对象
     */
    public static Set<String> sfaFillUpObjects = new HashSet<>();
    /**
     * 临时配置：true 拦截加密。false 不拦截.，需要调用reversePassword的接口更新成明文
     */
    public static boolean ENCRYPT_INTERCEPTOR = true;

    /**
     * 乐享appId
     */
    public static String lexiangAppId;
    /**
     * 乐享appSecret
     */
    public static String lexiangAppSecret;

    /**
     * cdn base url地址
     */
    public static String CDN_BASE_URL = "https://a9.fspage.com/FSR/weex/erpdss";
    /**
     * cdn domain
     */
    public static String CDN_DOMAIN = "https://a9.fspage.com";
    /**
     * amis sdk url
     */
    public static String AMIS_SDK_URL = "https://unpkg.zhihu.com/amis@6.11.0/sdk";
    /**
     * 知识库企业
     */
    public static String fsSearchKnowledgeEa = "fs";
    public static Integer fsSearchKnowledgeEi = 1;
    /**
     * 知识库场景
     */
    public static String erpdssSearchKnowledgeScene = "ERP集成平台";   // ignoreI18n    cms配置文件
    /**
     * 错误信息干扰项
     */
    public static List<String> KnowledgeStopWords = Lists.newArrayList("调用ERP接口报错:", "调用ERP接口报错:");   // ignoreI18n    cms配置文件

    /**
     * 轮询ERP告警聚合时间，默认2小时
     */
    public static long pollingErpAlertAggregationTime = TimeUnit.MINUTES.toMillis(120L);

    /**
     * 同步数据失败告警聚合时间，默认2小时
     */
    public static long syncDataFailedAlertAggregationTime = TimeUnit.MINUTES.toMillis(120L);

    /**
     * 发送集成平台消息到CRM消息中心
     */
    public static boolean sendErpdssMsg2Crm = false;

    /**
     * 好丽友环境企业
     */
    public static Set<String> HAOLIYOU_TENANTS = Sets.newHashSet();

    /**
     * vip环境企业
     */
    public static Set<String> VIP_ENVIROMENT_TENANT = Sets.newHashSet();

    /**
     * gray环境企业
     */
    public static Set<String> GRAY_TENANTS = Sets.newHashSet();

    /**
     * jacoco环境企业
     */
    public static Set<String> JACOCO_TENANTS = Sets.newHashSet();

    /**
     * 轮询临时库重试次数
     */
    public static int maxPollingTempRetryTime = 1;
    /**
     * 轮询临时库重试间隔
     */
    public static int retryPollingTempIntervalSeconds = 10 * 60;


    /**
     * 需要更改集复制的全局配置类型
     */
    public static List<String> globalTenantListConfigType = Lists.newArrayList("SERIALIZE_NULL_TENANTS", "checkQuotaDisableTenants");

    /**
     * 没有知识库服务
     */
    public static boolean notKnowledgeService = false;

    /**
     * 默认hub地址
     */
    public static String DEFAULT_HUB_BASE_URL = "http://**********:8099/hub";

    public static IConfig config;

    /**
     * 调用云星辰项目的内部地址
     */
    public static String JDY_INNER_URL="";
    /**
     * 下游熔断通知间隔时间
     */
    public static int RelationPloyBreakerExpireSeconds = 3600;
    /**
     * 下游企业需要替换为模板企业的TenantConfig Key
     */
    public static Set<String> replaceDownstreamConfigTypeSet = Sets.newHashSet("SYNC_LOG_EXPIRE_TIME_TENANT_CONFIG", "LOG_STORAGE_RULE", "NOT_OFFER_GETBYID_INTERFACE_TENANTS");

    /**
     * 上游企业告警聚合时间
     */
    public static Integer UPSTREAM_ALERT_AGG_DELAY_TIME_SECONDS = 60;

    /**
     * 调用bi服务
     */
    public static String BI_SERVICE_URL="";
    /**
     * 调用bi报表服务
     */
    public static String BI_FUNCTION_URL="";


    /**
     * 区分需要重试的异常
     */
    public static List<String> needRetryException=Lists.newArrayList();
    /**
     * 订单新变更单修改灰度企业
     */
    public static Set<String> XORDER_BILL_QUERY_TENANTS = new HashSet<>();

    public static String feishuConnectorInternalUrl = "http://172.17.4.230:61598/feishu/internal";//线上飞书WEB的URL
    public static String qywxConnectorInternalUrl = "http://172.17.4.230:48826/qyweixin/inner";//线上企微WEB的URL
    public static String OUTOA_BASE_INNER_URL = "http://10.112.5.251:27929/inner/outerOa/connect";//OA基座

    /**
     * 聚合框架 多个企业放在一个表中的企业列表,支持*
     */
    public static Set<String> mergeDispatcherTenantIds = Sets.newHashSet();

    /**
     * 聚合框架 聚合表数量
     */
    public static Integer mergeDispatcherCollectionCount = 10;

    /**
     * 预分配PG库的企业
     */
    public static Set<String> pgShardTenantIds = Sets.newHashSet();

    /**
     * 预分配的PG库资源
     * 按list排序,想增大权重可以配多个相同的resourceId
     */
    public static List<String> pgShardResourceIds = Lists.newArrayList();

    /**
     * 切面日志名称，重启服务后生效
     */
    public static String AOP_LOG_NAME = "AOP";
    //切面日志忽略的方法。立刻生效。我只是搬运到这里。hardy加的。
    public static Set<String> AOP_LOG_IGNORE_METHOD = ImmutableSet.of();

    public static Map<String, String> globalConfig = new HashMap<>();

    public static Boolean isCostTimes = Boolean.FALSE;

    /**
     * K3默认保存参数,value: 1.数组 2.布尔 3.字符串
     * @see TenantConfigurationTypeEnum#ERP_K3_OBJ_SAVE_ARG_SETTING
     *
     */
    public static Map<String,Map<String, Integer>> k3PresetSaveConfigKey = ImmutableMap.of();

    /**
     * 自定义函数中展示集成对象APItab的权限
     */
    public static Set<String> functionErpdssTabPermissions;


    /**
     * 此时间后，不再支持TLSv1.1
     */
    public static Long notSupportTlsV11Time = 1723824000000L;

    public static boolean disableParseObjCache = false;

    public static String svc_fs_paas_auth;

    /**
     * 100M基准单位大小限制
     */
    public static Long EXPORT_CONTENT_LENGTH_LIMIT = 1024 * 1024 * 100L;


    static {
        ConfigFactory.getInstance().getConfig("erp-sync-data-all", config -> {
            disableParseObjCache = config.getBool("disableParseObjCache", false);
            svc_fs_paas_auth = config.get("svc_fs_paas_auth", svc_fs_paas_auth);
            AOP_LOG_IGNORE_METHOD = ImmutableSet.copyOf(Splitter.on(",").trimResults().split(config.get("AOP_LOG_IGNORE_METHOD", "setTenantId, checkStreamExist, getRealObjApiName, isCutDownField, sendOneEvent, getHandlerByName, getRelation, saveLog, queryIntegrationStreamSnaps, getMappingFirstBySource, initLogId, listNewestEnableSyncPloyDetailsSnapshots, existByTwoWay, executeCustomFunction, sendEventData2DispatcherMq, getSyncDataMapping, updateErpTempDataByIds, getReferName, getDataByMasterId, removeBatchCache, changeVariableFilter")));
            AOP_LOG_NAME = config.get("AOP_LOG_NAME",AOP_LOG_NAME);
            notSupportTlsV11Time = config.getLong("notSupportTlsV11Time",notSupportTlsV11Time);
            DEFAULT_HUB_BASE_URL = config.get("DEFAULT_HUB_BASE_URL", DEFAULT_HUB_BASE_URL);
            VIP_ENVIROMENT_TENANT = ImmutableSet.copyOf(Splitter.on(ConfigCenterManager.tenantIdSeparator).split(config.get("VIP_ENVIROMENT_TENANT", "")));
            HAOLIYOU_TENANTS = ImmutableSet.copyOf(Splitter.on(ConfigCenterManager.tenantIdSeparator).split(config.get("HAOLIYOU_TENANTS", "")));
            JACOCO_TENANTS = ImmutableSet.copyOf(Splitter.on(ConfigCenterManager.tenantIdSeparator).split(config.get("JACOCO_TENANTS", "")));
            GRAY_TENANTS = ImmutableSet.copyOf(Splitter.on(ConfigCenterManager.tenantIdSeparator).split(config.get("GRAY_TENANTS", "")));
            CDN_BASE_URL = config.get("CDN_BASE_URL",CDN_BASE_URL);
            CDN_DOMAIN = config.get("CDN_DOMAIN", CDN_DOMAIN);
            AMIS_SDK_URL = config.get("AMIS_SDK_URL",AMIS_SDK_URL);
            DISABLE_DYNAMIC_TENANTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("DISABLE_DYNAMIC_TENANTS", "")));
            HISTORY_DATA_TASK_OVERTIME = config.getInt("HISTORY_DATA_TASK_OVERTIME", HISTORY_DATA_TASK_OVERTIME);
            SECRET_KEY = config.get("SECRET_KEY", SECRET_KEY);
            WEB_ENV_URL = JSON.parseObject(config.get("WEB_ENV_URL", "{}"), Map.class);
            TASK_BASE_URL = config.get("TASK_BASE_URL", TASK_BASE_URL);
            TRIGGER_GRAY = ImmutableSet.copyOf(Splitter.on(",").split(config.get("TRIGGER_GRAY", "GRAY")));
            SECRET_KEY = config.get("SECRET_KEY", SECRET_KEY);
            FAILED_INCREMENT_ALERT_THRESHOLDS = config.getInt("FAILED_INCREMENT_ALERT_THRESHOLDS", FAILED_INCREMENT_ALERT_THRESHOLDS);
            FAILED_INCREMENT_BREAK_THRESHOLDS = config.getInt("FAILED_INCREMENT_BREAK_THRESHOLDS", FAILED_INCREMENT_BREAK_THRESHOLDS);
            POLLING_ALERT_THRESHOLDS = config.getInt("POLLING_ALERT_THRESHOLDS", POLLING_ALERT_THRESHOLDS);
            K3_NULL_TO_EMPTY_STRING = ImmutableSet.copyOf(Splitter.on(",").split(config.get("K3_NULL_TO_EMPTY_STRING", "text,long_text,string")));
            NUMBER_OF_PAGES_WITH_DUPLICATE_DATA = config.getInt("NUMBER_OF_PAGES_WITH_DUPLICATE_DATA", NUMBER_OF_PAGES_WITH_DUPLICATE_DATA);
            TIME_OUT_SYNC_DATA_TIME = config.getLong("TIME_OUT_SYNC_DATA_TIME", TIME_OUT_SYNC_DATA_TIME);
            MIN_OF_DAY_2_SCAN_TEMP = config.getInt("MIN_OF_DAY_2_SCAN_TEMP", MIN_OF_DAY_2_SCAN_TEMP);
            ENABLE_SCAN_TEMP_ENVS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("ENABLE_SCAN_TEMP_ENVS", "GRAY")));
            K3_EXIST_CANCEL_OBJ = ImmutableSet.copyOf(Splitter.on(",").split(config.get("K3_EXIST_CANCEL_OBJ", "SAL_OUTSTOCK,STK_TransferDirect,SC_RECEIVESETTLEBILL,SAL_SaleOrder,SAL_DELIVERYNOTICE,BD_BatchMainFile,PRD_MO,AR_receivable,SAL_RETURNSTOCK,AR_REFUNDBILL,AR_RECEIVEBILL,IV_SALESOC,IV_SALESIC")));
            DEFAULT_SANDBOX_CONFIG_ROUTE_SOURCE_ID = config.get("DEFAULT_SANDBOX_CONFIG_ROUTE_SOURCE_ID", DEFAULT_SANDBOX_CONFIG_ROUTE_SOURCE_ID);
            DEFAULT_CONFIG_ROUTE_SOURCE_ID = config.get("DEFAULT_CONFIG_ROUTE_SOURCE_ID", DEFAULT_CONFIG_ROUTE_SOURCE_ID);
            DEFAULT_VIP_CONFIG_ROUTE_SOURCE_ID = config.get("DEFAULT_VIP_CONFIG_ROUTE_SOURCE_ID", DEFAULT_VIP_CONFIG_ROUTE_SOURCE_ID);
            VIP_TENANT_LEVEL_VALUE = ImmutableSet.copyOf(Splitter.on(",").split(config.get("VIP_TENANT_LEVEL_VALUE", "")));
            ENABLE_CONSUME_SANDBOX_EVENT = config.getBool("ENABLE_CONSUME_SANDBOX_EVENT", ENABLE_CONSUME_SANDBOX_EVENT);
            ALL_ENV_TO_WEB_ENV = JSON.parseObject(config.get("ALL_ENV_TO_WEB_ENV", "{\"vip\":\"vip\",\"jacoco\":\"jacoco\",\"gray\":\"gray\",\"normal\":\"normal\",\"haoliyou\":\"haoliyou\"}"), Map.class);
            WEB_ENV_TO_WEB_APP_ENV = JSON.parseObject(config.get("WEB_ENV_TO_WEB_APP_ENV", "{\"vip\":\"vip\",\"jacoco\":\"jacoco\",\"gray\":\"gray\",\"normal\":\"normal\",\"haoliyou\":\"vip\"}"), Map.class);
            DB_POR_URL = config.get("DB_POR_URL", "");
            INIT_MAPPING_SLEEP_MS_PER_LIMIT = config.getLong("INIT_MAPPING_SLEEP_MS_PER_LIMIT", INIT_MAPPING_SLEEP_MS_PER_LIMIT);
            DO_RE_SYNC_DATA_DAY = config.getInt("DO_RE_SYNC_DATA_DAY", DO_RE_SYNC_DATA_DAY);
            BILL_QUERY_SIZE_LIMIT = config.getInt("BILL_QUERY_SIZE_LIMIT", BILL_QUERY_SIZE_LIMIT);
            PUSH_DATA_SIZE_NOTIFY = config.getInt("PUSH_DATA_SIZE_NOTIFY", PUSH_DATA_SIZE_NOTIFY);
            INTERFACE_MONITOR_WRITE_MONGO = config.getBool("INTERFACE_MONITOR_WRITE_MONGO", INTERFACE_MONITOR_WRITE_MONGO);
            NOT_SAVE_INTERFACE_MONITOR_TENANTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOT_SAVE_INTERFACE_MONITOR_TENANTS", "")));
            ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS", "")));
            CONTENT_LENGTH_LIMIT = config.getLong("CONTENT_LENGTH_LIMIT", CONTENT_LENGTH_LIMIT);
            LIST_CONTENT_LENGTH_LIMIT = config.getLong("LIST_CONTENT_LENGTH_LIMIT", LIST_CONTENT_LENGTH_LIMIT);
            LIST_CONTENT_LOG_LENGTH_LIMIT = config.getLong("LIST_CONTENT_LOG_LENGTH_LIMIT", LIST_CONTENT_LENGTH_LIMIT);
            ACCURATE_CALCULATION_BATCH_WRITE_BYTE = config.getBool("ACCURATE_CALCULATION_BATCH_WRITE_BYTE", ACCURATE_CALCULATION_BATCH_WRITE_BYTE);
            NEED_SIMPLIFY_BYTE_LIST_SIZE = config.getInt("NEED_SIMPLIFY_BYTE_LIST_SIZE", NEED_SIMPLIFY_BYTE_LIST_SIZE);
            USER_OPERATOR_LOG_MONGO_EXPIRE_TIME = config.getLong("USER_OPERATOR_LOG_MONGO_EXPIRE_TIME", USER_OPERATOR_LOG_MONGO_EXPIRE_TIME);
            ERP_TEMP_DATA_MONGO_EXPIRE_TIME = config.getLong("ERP_TEMP_DATA_MONGO_EXPIRE_TIME", ERP_TEMP_DATA_MONGO_EXPIRE_TIME);
            ERP_LIST_MAX_NUM = config.getInt("ERP_LIST_MAX_NUM", ERP_LIST_MAX_NUM);
            END_UPDATE_TIME_OUT_MIN = config.getInt("END_UPDATE_TIME_OUT_MIN", END_UPDATE_TIME_OUT_MIN);
            SUPER_ADMINS = ImmutableSet.copyOf(Splitter.on(";").split(config.get("SUPER_ADMINS", "")));
            List<K3SourceBillInfo> k3SourceBillInfo = JacksonUtil.fromJson(config.get("K3_SOURCE_BILL_INFO", "[]"),
                    new TypeReference<List<K3SourceBillInfo>>() {
                    });
            K3_SOURCE_BILL_INFO_MAP = k3SourceBillInfo.stream().collect(Collectors.groupingBy(K3SourceBillInfo::getFormId));
            MANUAL_EXECUTE_PLOYS_URL = config.get("dss.task.manual.trigger.url");
            ERP_SYNC_DATA_APP_ID = config.get("ERP_SYNC_DATA_APP_ID", ERP_SYNC_DATA_APP_ID);
            //没有时取普通应用的值
            PRIMARY_ERP_SYNC_DATA_APP_ID = config.get("PRIMARY_ERP_SYNC_DATA_APP_ID", PRIMARY_ERP_SYNC_DATA_APP_ID);
            NOTICE_MAX_SIZE = config.getInt("NOTICE_MAX_SIZE", NOTICE_MAX_SIZE);
            //ASYNC_IMPORT_MSG = config.get("ASYNC_IMPORT_MSG", ASYNC_IMPORT_MSG);
            IMPORT_TIME_OUT = config.getLong("IMPORT_TIME_OUT", IMPORT_TIME_OUT);
            EXPORT_TIME_OUT = config.getLong("EXPORT_TIME_OUT", EXPORT_TIME_OUT);
            OA_RE_TIME = config.getLong("OA_RE_TIME", OA_RE_TIME);
            STORE_DEFAULT_K3_OBJ_TENANT_ID = config.get("STORE_DEFAULT_K3_OBJ_TENANT_ID", STORE_DEFAULT_K3_OBJ_TENANT_ID);
            CRM_OBJECT_INNER_URL_PREFIX = config.get("crm.object.inner.url.prefix", "");
            CRM_METADATA_URL_PREFIX = config.get("crm.metadata.url.prefix", "");
            String mappingField = config.get("APINAME_FID_FNUMBER_MAPPING", "{}");
            APINAME_FID_FNUMBER_MAPPING = JacksonUtil.fromJson(mappingField, Map.class);
            EXPORT_DATA_NUMBER = config.getInt("EXPORT_DATA_NUMBER", EXPORT_DATA_NUMBER);
            EXPORT_DATA_QUERY_NUMBER = config.getInt("EXPORT_DATA_QUERY_NUMBER", EXPORT_DATA_QUERY_NUMBER);
            ServiceEnvironment = config.get("ServiceEnvironment", "normal");
            U8_VERIFY_APINAME_SET = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("U8_VERIFY_APINAME_SET", ""))
            );
            String u8MasteridStructureMap = config.get("U8_MASTERID_STRUCTURE_MAP", "{}");
            U8_MASTERID_STRUCTURE_MAP = JacksonUtil.fromJson(u8MasteridStructureMap, Map.class);
            SSO_ENTERPRISE_EI = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("SSO_ENTERPRISE_EI", "40010021")));
            NOT_NEED_FILTER_FIELD = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("NOT_NEED_FILTER_FIELD", "ID")));
            TO_NULL_K3_DATA_ID = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("TO_NULL_K3_DATA_ID", "0;-;--; ;")));
            OVER_OBJ_NAMES = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OVER_OBJ_NAMES", "")));
            OA_MQ_GRAY_TENANT_ID = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_MQ_GRAY_TENANT_ID", "")));
            THREAD_POOL_CORE_SIZE = config.getInt("THREAD_POOL_CORE_SIZE", THREAD_POOL_CORE_SIZE);
            SPLIT_ARG_DEFAULT_NUM = config.getInt("SPLIT_ARG_DEFAULT_NUM", SPLIT_ARG_DEFAULT_NUM);
            IDP_ENTITY_ID = config.get("IDP_ENTITY_ID", IDP_ENTITY_ID);
            RUI_JIE_SSO = config.get("RUI_JIE_SSO", RUI_JIE_SSO);
            SP_ENTITY_ID = config.get("SP_ENTITY_ID", SP_ENTITY_ID);
            ERP_DOMAIN_URL = config.get("ERP_DOMAIN_URL", ERP_DOMAIN_URL);
            ERP_OPEN_DOMAIN_URL = config.get("ERP_OPEN_DOMAIN_URL", ERP_DOMAIN_URL);
            CSM_CUSTOMER_API_NAME = config.get("CSM_CUSTOMER_API_NAME", CSM_CUSTOMER_API_NAME);
            FS_ENTERPRISE_ID = config.get("FS_ENTERPRISE_ID", FS_ENTERPRISE_ID);
            CS_CUSTOMER_ENTERPRISE_API_NAME = config.get("CS_CUSTOMER_ENTERPRISE_API_NAME", CS_CUSTOMER_ENTERPRISE_API_NAME);
            FS_ACCOUNT_TENANT_ID = config.get("FS_ACCOUNT_TENANT_ID", FS_ACCOUNT_TENANT_ID);
            FS_ACCOUNT_ENTERPRISE_LEVEL = config.get("FS_ACCOUNT_ENTERPRISE_LEVEL", FS_ACCOUNT_ENTERPRISE_LEVEL);
            LEVEL_VALUE_TO_LABEL = JacksonUtil.fromJson(config.get("LEVEL_VALUE_TO_LABEL", "{\"1\":\"SVIP\",\"2\":\"VIP\",\"3\":\"重要\",\"4\":\"标准\",\"5\":\"小微\",\"other\":\"其他\"}"), Map.class);   // ignoreI18n    cms配置文件
            APIBUS_NCRM = config.get("APIBUS_NCRM", APIBUS_NCRM);
            APIBUS_GLOBAL = config.get("APIBUS_GLOBAL", APIBUS_GLOBAL);
            TENANT_CONFIG_MAP_CACHE_EXPIRE_TIME = config.getLong("TENANT_CONFIG_MAP_CACHE_EXPIRE_TIME", 3 * 60 * 1000L);
            NOTIFY_DELAY_TIME_SECONDS = config.getInt("NOTIFY_DELAY_TIME_SECONDS", 60 * 5);
            RECORD_AUDIT_LOG_URL = config.get("record_audit_log_url");
            CUSTOM_NOTIFY_DELAY_TIME_SECONDS = config.getInt("CUSTOM_NOTIFY_DELAY_TIME_SECONDS", 60 * 1);
            HIDE_CATEGORY_ENTRANCE_DATE = config.getLong("hide_category_entrance_date", 1679414400000L);
            ENCRYPT_INTERCEPTOR = config.getBool("ENCRYPT_INTERCEPTOR", true);
            lexiangAppId = config.get("lexiangAppId");
            lexiangAppSecret = config.get("lexiangAppSecret");
            fsSearchKnowledgeEa = config.get("fsSearchKnowledgeEa", "fs");
            fsSearchKnowledgeEi = config.getInt("fsSearchKnowledgeEi", 1);
            erpdssSearchKnowledgeScene = config.get("erpdssSearchKnowledgeScene", "ERP集成平台");   // ignoreI18n    cms配置文件
            KnowledgeStopWords = JSON.parseArray(config.get("KnowledgeStopWords", "[\"调用ERP接口报错:\", \"调用ERP接口报错:\"]"), String.class);   // ignoreI18n    cms配置文件
            pollingErpAlertAggregationTime = config.getLong("pollingErpAlertAggregationTime", 120 * 60 * 1000L);
            syncDataFailedAlertAggregationTime = config.getLong("syncDataFailedAlertAggregationTime", 120 * 60 * 1000L);
            sendErpdssMsg2Crm = config.getBool("sendErpdssMsg2Crm", false);
            // 配置文件不加 ${} , 防止被当成占位符导致报错
            final String headerOption = config.get("headerValueOption", "{\"推送token\":\"erpdss-token\",\"企业Id\": \"tenantId\"}");   // ignoreI18n    cms配置文件
            final Map<String, String> headerOptionMap = JSON.parseObject(headerOption, new com.alibaba.fastjson.TypeReference<Map<String, String>>() {
            });
            maxPollingTempRetryTime = config.getInt("maxPollingTempRetryTime", 1);
            retryPollingTempIntervalSeconds = config.getInt("retryPollingTempIntervalSeconds", 10 * 60);
            globalTenantListConfigType = JSON.parseArray(config.get("globalTenantListConfigType", "[\"SERIALIZE_NULL_TENANTS\", \"checkQuotaDisableTenants\"]"), String.class);

            RelationPloyBreakerExpireSeconds = config.getInt("RelationPloyBreakerExpireSeconds", 3600);
            replaceDownstreamConfigTypeSet = Sets.newHashSet(JSON.parseArray(config.get("replaceDownstreamConfigTypeSet", "[\"SERIALIZE_NULL_TENANTS\", \"checkQuotaDisableTenants\", \"NOT_OFFER_GETBYID_INTERFACE_TENANTS\"]"), String.class));
            UPSTREAM_ALERT_AGG_DELAY_TIME_SECONDS = config.getInt("UPSTREAM_ALERT_AGG_DELAY_TIME_SECONDS", 60);

            mergeDispatcherTenantIds = Sets.newHashSet(Splitter.on(",").split(config.get("mergeDispatcherTenantIds", "")));
            mergeDispatcherCollectionCount = config.getInt("mergeDispatcherCollectionCount", 10);

            JDY_INNER_URL = config.get("JDY_INNER_URL","");
            notKnowledgeService = config.getBool("notKnowledgeService", false);
            BI_SERVICE_URL = config.get("BI_SERVICE_URL","");
            BI_FUNCTION_URL = config.get("BI_FUNCTION_URL","");

            needRetryException = ImmutableList.copyOf(Splitter.on(";").split(config.get("needRetryException", "com.alibaba.druid.pool.DataSourceNotAvailableException;com.alibaba.druid.pool.GetConnectionTimeoutException;org.springframework.dao.DataAccessResourceFailureException")));

            k3PresetSaveConfigKey = JSON.parseObject(config.get("k3PresetSaveConfigKey", "{\"2\":{\"NeedUpDateFields\":1,\"IsDeleteEntry\":2,\"SubSystemId\":3,\"IsVerifyBaseDataField\":2,\"IsEntryBatchFill\":2,\"ValidateFlag\":2,\"NumberSearch\":2,\"IsAutoAdjustField\":2,\"InterationFlags\":3,\"IgnoreInterationFlag\":2},\"3\":{\"CreateOrgId\":3,\"SelectedPostId\":3,\"NetworkCtrl\":2,\"IgnoreInterationFlag\":2},\"4\":{\"CreateOrgId\":3,\"InterationFlags\":3,\"NetworkCtrl\":2,\"IsVerifyProcInst\":2,\"IgnoreInterationFlag\":2},\"5\":{\"CreateOrgId\":3,\"InterationFlags\":3,\"NetworkCtrl\":2,\"IsVerifyProcInst\":2,\"IgnoreInterationFlag\":2},\"6\":{\"CreateOrgId\":3,\"NetworkCtrl\":2}}"), new com.alibaba.fastjson.TypeReference<Map<String,Map<String, Integer>>>() {
            });

            XORDER_BILL_QUERY_TENANTS = ImmutableSet.copyOf(Splitter.on(ConfigCenterManager.tenantIdSeparator).split(config.get("XORDER_BILL_QUERY_TENANTS", "")));

            feishuConnectorInternalUrl = config.get("feishuConnectorInternalUrl",feishuConnectorInternalUrl);
            qywxConnectorInternalUrl = config.get("qywxConnectorInternalUrl",qywxConnectorInternalUrl);
            OUTOA_BASE_INNER_URL = config.get("OUTOA_BASE_INNER_URL",OUTOA_BASE_INNER_URL);
            pgShardTenantIds = Sets.newHashSet(Splitter.on(",").split(config.get("pgShardTenantIds", "")));
            final String shardResourceIds = config.get("pgShardResourceIds", "");
            pgShardResourceIds = StringUtils.isBlank(shardResourceIds) ? Lists.newArrayList() : Splitter.on(",").splitToList(shardResourceIds);

            isCostTimes = config.getBool("isCostTimes", Boolean.FALSE);

            functionErpdssTabPermissions = ImmutableSet.copyOf(Splitter.on(",").trimResults().split(config.get("functionErpdssTabPermissions", "erpdss/policy,erpdss/templatecenter,erpdss/setting")));
            EXPORT_CONTENT_LENGTH_LIMIT = config.getLong("EXPORT_CONTENT_LENGTH_LIMIT", EXPORT_CONTENT_LENGTH_LIMIT);
            ConfigCenter.config = config;
        }, true);

        ConfigFactory.getInstance().getConfig("variables_sfa_object", config -> {
            sfaFillUpObjects = Sets.newHashSet(Splitter.on(",").split(config.get("slave_must_with_master_object", "")));
        });

        ConfigFactory.getInstance().getConfig("variables_erp_global", config -> {
            globalConfig = config.getAll();
        });
    }



    public static String getOauthAppId(final String channel) {
        return config.get(channel + "AppId");
    }

    public static String getOauthAppSecret(final String channel) {
        return config.get(channel + "AppSecret");
    }

    public static String getOauthRedirectUri(final String channel) {
        return config.get(channel + "RedirectUri");
    }

    public static String getOauthLoginDialogUrl(final String channel) {
        return config.get(channel + "LoginDialogUrl");
    }

    /**
     *
     * @param type
     * @return
     */
    public static Integer getDefaultSaveSyncLogLimit(String type) {
        return config.getInt(type + "_default", 30_000);
    }

    public static Integer getAllSaveSyncLogLimit(String type) {
        return config.getInt(type + "_all", 300_000);
    }

    public static boolean isAllowMergeDispatcherCollection(String tenantId) {
        return mergeDispatcherTenantIds.contains("*") || mergeDispatcherTenantIds.contains(tenantId);
    }

    public static boolean isPgShardTenant(String tenantId) {
        return pgShardTenantIds.contains("*") || pgShardTenantIds.contains(tenantId);
    }

    public static String getPgShardResourceId(String tenantId) {
        if (CollectionUtils.isEmpty(pgShardResourceIds)) {
            return DEFAULT_CONFIG_ROUTE_SOURCE_ID;
        }

        int ei;
        if (StringUtils.isNumeric(tenantId)) {
            ei = Integer.parseInt(tenantId);
        } else {
            ei = tenantId.hashCode();
        }

        return pgShardResourceIds.get(Math.abs(ei % pgShardResourceIds.size()));
    }
}
