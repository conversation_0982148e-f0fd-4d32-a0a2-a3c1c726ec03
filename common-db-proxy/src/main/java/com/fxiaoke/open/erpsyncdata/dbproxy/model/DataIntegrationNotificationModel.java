package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataIntegrationNotificationModel implements Serializable {
    /**
     * 总条数
     */
    private long totalCount;

    @Transient
    @JsonIgnore
    @JSONField(serialize = false)
    private List<DataIntegrationNotificationEntity> entityList;

    private List<Entry> dataList;

    @Data
    public static class Entry implements Serializable {
        private String id;
        private String tenantId;

        //数据中心ID
        private String dataCenterId;
        private String dataCenterName;

        //集成流ID
        private List<PloyDetailModel> ployDetailList;

        //通知类型
        private NotificationType notificationType;

        //告警规则类型
        private AlarmRuleType alarmRuleType;

        //告警类型
        private AlarmType alarmType;

        //告警级别
        private AlarmLevel alarmLevel;

        //通知时间
        private Long time;

        //通知消息
        private String msg;

        //通知人列表
        private List<UserModel> userList;
        //通知方式
        private List<String> notifyType;


        @JSONField(serialize = false)
        @JsonIgnore
        private Date createTime;
        @JSONField(serialize = false)
        @JsonIgnore
        private Date updateTime;
    }
}
