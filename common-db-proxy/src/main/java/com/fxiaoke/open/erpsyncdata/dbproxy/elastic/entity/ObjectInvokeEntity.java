package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncLogBaseInfo;
import lombok.Data;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/6 11:06:52
 */
@Data
@ToString(callSuper = true)
public class ObjectInvokeEntity extends IdEntity {
    /**
     * 企业
     */
    @TenantID
    private String tenantId;
    /**
     * 连接器id
     *
     * @see ErpConnectInfoEntity#getId()
     */
    private String dcId;
    /**
     * 对象APIName
     */
    private String objectApiName;
    /**
     * 对象APIName
     * 集成流中用于失败的函数apiName
     */
    private String dataId;
    // /**
    //  * 执行时的日志id
    //  */
    // private String logId;
    /**
     * @see ActionEnum#action
     */
    private Integer action;
    /**
     * 调用类型
     *
     * @see InvokeTypeEnum
     */
    private Integer invokeType;
    // /**
    //  * 集成流策略快照id,写操作时有值
    //  */
    // private String snapshotId;
    /**
     * 集成流策略id,写操作时有值
     */
    private String ployDetailId;
    /**
     * 源数据的数据事件类型
     *
     * @see com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum
     */
    private Integer sourceEventType;

    /**
     * 调用结果状态
     *
     * @see ObjectInvokeStatus
     */
    private Integer status;
    /**
     * 返回的错误码
     */
    private String errorCode;
    /**
     * 返回的错误信息
     */
    private String errorMessage;
    /**
     * 查询数量
     */
    private Integer count;
    /**
     * 耗时
     */
    private Integer cost;
    /**
     * 调用时间
     */
    private Long createTime;
    /**
     * 返回值大小，可能为空
     */
    private Long resultObjSize;

    public static ObjectInvokeEntity initIntegrationStreamEntity(String tenantId, ObjectInvokeStatus status) {
        final ObjectInvokeEntity objectInvokeEntity = new ObjectInvokeEntity();
        objectInvokeEntity.setCost(0);
        objectInvokeEntity.setCreateTime(System.currentTimeMillis());
        // objectInvokeEntity.setLogId(LogIdUtil.get());
        objectInvokeEntity.setAction(ActionEnum.EXECUTE.getAction());
        objectInvokeEntity.setInvokeType(InvokeTypeEnum.INTEGRATION_STREAM.getType());
        objectInvokeEntity.setCount(1);
        objectInvokeEntity.setTenantId(tenantId);
        objectInvokeEntity.setStatus(status.getStatus());

        setPloyDetailIdByThreadLocal(objectInvokeEntity);

        return objectInvokeEntity;
    }

    public static void setPloyDetailIdByThreadLocal(final ObjectInvokeEntity objectInvokeEntity) {
        final SyncLogBaseInfo baseLogNoCreate = LogIdUtil.getBaseLogNoCreate();
        if (Objects.nonNull(baseLogNoCreate)) {
            objectInvokeEntity.setPloyDetailId(baseLogNoCreate.getStreamId());
        }
    }
}
