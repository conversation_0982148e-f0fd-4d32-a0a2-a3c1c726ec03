package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.dispatcher.common.BaseEvent;
import com.fxiaoke.dispatcher.common.MessageField;
import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 10:25 2021/6/21
 * @Desc:
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DispatcherEventData extends BaseEvent implements RAMEstimable {
    /**
     * 对象名称，比如 AccountObj
     */
    @MessageField(name = "api_name")
    private String apiName;
    /**
     * 对象ID
     */
    @MessageField(name = "data_id")
    private String dataId;

    /**
     * 事件类型
     */
    @MessageField(name = "event_type", replace = true)
    private Integer eventType;

    /**
     * 事件类型
     */
    @MessageField(name = "event_type_list", addToSet = true)
    private List<Integer> eventTypeList;

    /**
     * 策略快照
     */
    @MessageField(name = "snapshot_id")
    private String ployDetailSnapshotId;

    /**
     * 企业类型
     */
    @MessageField(name = "tenant_type")
    private Integer tenantType;

    /**
     * crm有变化的字段Map的json
     */
    @MessageField(name = "updateJson", addToList = true)
    protected List<String> updateJson;

    /**
     * erp的sourceData的json
     */
    @MessageField(name = "erp_source_data",replace = true)
    protected String erpSourceData;


    /**
     * 主+从，所有对象的数量。当前仅ERP的有值
     */
    @MessageField(name = "all_obj_count")
    private Integer allObjCount;

    /**
     * 数据分发的logID
     *
     * @param other
     * @return
     */
    @MessageField(name = "sync_log_id")
    private String syncLogId;

    /**
     * 数据版本
     *
     * @param other
     * @return
     */
    @MessageField(name = "data_version", addToList = true)
    private List<Long> dataVersion;

    /**
     * 数据来源类型
     */
    @MessageField(name = "data_receive_type")
    private Integer dataReceiveType;
    @MessageField(name = "source_context_user_id")
    private String sourceContextUserId;//用户id
    /**
     * 多语
     */
    @MessageField(name = "locale", replace = true)
    private String locale;

    /**
     * 强制同步的集成流id
     */
    @MessageField(name = "force_sync_snapshot_id", addToSet = true)
    protected List<String> forceSyncSnapshotIds;

    @MessageField(name = "remark", addToSet = true)
    private List<String> remark;

    /**
     * 同一批数据，相同id聚合
     *
     * @param other
     * @return
     */
    @Override
    public BaseEvent merge(BaseEvent other) {
        DispatcherEventData data = (DispatcherEventData) other;
        this.incrCommitTimes();
        if(this.getRemark()== null){
            this.setRemark(Lists.newArrayList());
        }
        if (this.getDataVersion() == null) {
            this.setDataVersion(Lists.newArrayList());
        }
        if (data.getDataVersion() != null) {
            this.getDataVersion().addAll(data.getDataVersion());
        }
        if (this.getEventTypeList() == null) {
            this.setEventTypeList(Lists.newArrayList());
        }
        if (data.getEventTypeList() != null) {
            this.getEventTypeList().addAll(data.getEventTypeList());
        }
        if(data.getSourceContextUserId()!=null){
            this.setSourceContextUserId(data.getSourceContextUserId());
        }
        if (StringUtils.isNotBlank(data.getLocale())) {
            this.setLocale(data.getLocale());
        }
        if(CollectionUtils.isNotEmpty(data.getRemark())){
            this.getRemark().addAll(data.getRemark());
        }
//        //聚合事件类型,目前只支持：删除/作废/新增/更新
//        if (new Integer(EventTypeEnum.DELETE_DIRECT.getType()).equals(data.getEventType())
//                || new Integer(EventTypeEnum.DELETE_DIRECT.getType()).equals(this.getEventType())) {//其一事件为删除
//            this.setEventType(EventTypeEnum.DELETE_DIRECT.getType());
//        } else if (new Integer(EventTypeEnum.RECOVER.getType()).equals(data.getEventType())
//                || new Integer(EventTypeEnum.RECOVER.getType()).equals(this.getEventType())) {//其一事件为恢复，同一批应该不可能作废后马上恢复
//            this.setEventType(EventTypeEnum.RECOVER.getType());
//        } else if (new Integer(EventTypeEnum.INVALID.getType()).equals(data.getEventType())
//                || new Integer(EventTypeEnum.INVALID.getType()).equals(this.getEventType())) {//其一事件为作废
//            this.setEventType(EventTypeEnum.INVALID.getType());
//        } else if (new Integer(EventTypeEnum.UPDATE.getType()).equals(data.getEventType())
//                || new Integer(EventTypeEnum.UPDATE.getType()).equals(this.getEventType())) {//其一事件为更新，更新优先级大于新增
//            this.setEventType(EventTypeEnum.UPDATE.getType());
//        } else if (new Integer(EventTypeEnum.DEPEND.getType()).equals(data.getEventType())
//                || new Integer(EventTypeEnum.DEPEND.getType()).equals(this.getEventType())) {//其一事件为依赖
//            this.setEventType(EventTypeEnum.DEPEND.getType());
//        } else {
//            this.setEventType(EventTypeEnum.ADD.getType());
//        }
//        if (!this.getEventTypeList().contains(this.getEventType())) {
//            this.getEventTypeList().add(this.getEventType());
//        }
        if (CollectionUtils.isNotEmpty(data.getUpdateJson())) {//聚合sourceData
            if (CollectionUtils.isNotEmpty(this.getUpdateJson())) {
                JSONObject thisUpdateFields = JSONObject.parseObject(this.getUpdateJson().get(0), JSONObject.class);
                JSONObject otherUpdateFields = JSONObject.parseObject(data.getUpdateJson().get(0), JSONObject.class);
                if (other.getModifiedTime() > this.getModifiedTime()) {
                    for (String thisKey : thisUpdateFields.keySet()) {
                        otherUpdateFields.putIfAbsent(thisKey, thisUpdateFields.get(thisKey));
                    }
                    this.setUpdateJson(Lists.newArrayList(JacksonUtil.toJson(otherUpdateFields)));
                } else {
                    for (String otherKey : otherUpdateFields.keySet()) {
                        thisUpdateFields.putIfAbsent(otherKey, otherUpdateFields.get(otherKey));
                    }
                    this.setUpdateJson(Lists.newArrayList(JacksonUtil.toJson(thisUpdateFields)));
                }
            } else {
                this.setUpdateJson(data.getUpdateJson());
            }
        } else {//erp的不add2List
            if (StringUtils.isNotBlank(data.getErpSourceData())) {
                if (StringUtils.isNotBlank(this.getErpSourceData())) {
                    if (other.getModifiedTime() > this.getModifiedTime()) {
                        this.setErpSourceData(data.getErpSourceData());
                    }
                } else {
                    this.setErpSourceData(data.getErpSourceData());
                }
            }
        }
        if (other.getModifiedTime() > this.getModifiedTime()) {
            this.setModifiedTime(other.getModifiedTime());
        }
        //数据类型历史的优先
//        if(data.getDataReceiveType()== DataReceiveTypeEnum.FROM_ERP_HISTORY_TASK.getType()){
//            this.setDataReceiveType(data.getDataReceiveType());
//        }
        if (CollectionUtils.isNotEmpty(data.getForceSyncSnapshotIds())) {
            if (CollectionUtils.isEmpty(this.getForceSyncSnapshotIds())) {
                this.setForceSyncSnapshotIds(data.getForceSyncSnapshotIds());
            } else {
                final List<String> forceSyncSnapshotIds = Stream.of(data.getForceSyncSnapshotIds(), this.getForceSyncSnapshotIds())
                        .flatMap(Collection::stream)
                        .distinct()
                        .collect(Collectors.toList());
                this.setForceSyncSnapshotIds(forceSyncSnapshotIds);
            }
        }
        return this;
    }

    @Override
    public BaseEvent parse(Document doc) {
        super.parse(doc);
        this.setApiName(doc.getString("api_name"));
        this.setDataId(doc.getString("data_id"));
        this.setEventType(doc.getInteger("event_type"));
        this.setPloyDetailSnapshotId(doc.getString("snapshot_id"));
        this.setTenantType(doc.getInteger("tenant_type"));
        this.setUpdateJson(doc.getList("updateJson", String.class));
        this.setErpSourceData(doc.getString("erp_source_data"));
        this.setEventTypeList(doc.getList("event_type_list", Integer.class));
        this.setSyncLogId(doc.getString("sync_log_id"));
        this.setDataReceiveType(doc.getInteger("data_receive_type"));
        this.setSourceContextUserId(doc.getString("source_context_user_id"));
        this.setDataVersion(doc.getList("data_version", Long.class));
        this.setLocale(doc.getString("locale"));
        this.setForceSyncSnapshotIds(doc.getList("force_sync_snapshot_id", String.class));
        this.setRemark(doc.getList("remark", String.class));
        this.setAllObjCount(doc.getInteger("all_obj_count"));
        return this;
    }

    @Override
    public long ramBytesUsed(int depth) {
        return RamUsageEstimateUtil.sizeOfObject(updateJson, depth);
    }
}
