package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc: http调用url（inner标准接口）工具
 */
public class HttpUrlUtils {

    //
    public static final String SOURCE_HEADER = "OpenAPI-V2.0";
    public static final String ERP_SYNC_DATA = "fs-erp-sync-data";


    /**
     * 生成请求头
     *
     * @param enterpriseId
     * @param currentEmployeeId
     * @param sourceHeader
     * @return
     */
    public static Map<String, String> buildHeaderMap(int enterpriseId, int currentEmployeeId, String sourceHeader) {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-type", "application/json;charset=utf-8");
        headerMap.put("x-fs-userInfo", String.valueOf(currentEmployeeId));
        headerMap.put("x-fs-peer-name", sourceHeader);
        headerMap.put("x-fs-ei", String.valueOf(enterpriseId));
        return headerMap;
    }
    public static Map<String, String> buildHeaderMap(String enterpriseId, int currentEmployeeId, String sourceHeader) {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("Content-type", "application/json;charset=utf-8");
        headerMap.put("x-fs-userInfo", String.valueOf(currentEmployeeId));
        headerMap.put("x-fs-peer-name", sourceHeader);
        headerMap.put("x-fs-ei", enterpriseId);
        return headerMap;
    }

    /**
     * 生成创建对象的Url
     *
     * @param apiName
     * @return
     */
    public static String buildCreateObjectDataUrl(String apiName, Map<String,Object> extParamMap) {
        StringBuilder sb = new StringBuilder(ConfigCenter.CRM_OBJECT_INNER_URL_PREFIX);
        sb.append("/").append(apiName);
        sb.append("/action/Add");
        return sb.toString();
    }

    /**
     * 生成更新对象的Url
     *
     * @param apiName
     * @return
     */
    public static String buildUpdateObjectDataUrl(String apiName) {
        StringBuilder sb = new StringBuilder(ConfigCenter.CRM_OBJECT_INNER_URL_PREFIX);
        sb.append("/").append(apiName);
        sb.append("/action/Edit");
        return sb.toString();
    }

    /**
     * 生成bomDeploy的Url
     *
     * @return
     */
    public static String buildBomDeployUrl() {
        StringBuilder sb = new StringBuilder(ConfigCenter.CRM_OBJECT_INNER_URL_PREFIX);
        sb.append("/bom/service/bomDeploy");
        return sb.toString();
    }

    /**
     * 生成BOMObj的TreeRelatedListV1的Url
     * @return
     */
    public static String buildBOMObjTreeRelatedListV1Url() {
        StringBuilder sb = new StringBuilder(ConfigCenter.CRM_OBJECT_INNER_URL_PREFIX);
        sb.append("/BOMObj/controller/TreeRelatedListV1");
        return sb.toString();
    }

    /**
     * 生成check_module_status的Url
     *
     * @return
     */
    public static String buildCheckModuleStatusUrl() {
        StringBuilder sb = new StringBuilder(ConfigCenter.CRM_OBJECT_INNER_URL_PREFIX);
        sb.append("/module_ctrl/service/check_module_status");
        return sb.toString();
    }
    /**
     * 生成批量新增对象的url
     *
     * @return
     */
    public static String buildBatchCreateObjectUrl() {
        return ConfigCenter.CRM_METADATA_URL_PREFIX +
                "/paas/metadata/data/bulk/create";
    }

    /**
     * 生成批量更新对象的url
     *
     * @return
     */
    public static String buildBatchUpdateObjectUrl() {
        return ConfigCenter.CRM_METADATA_URL_PREFIX +
                "/paas/metadata/data/batch/update";
    }
    /**
     * 查询自定义函数
     */
    public static String buildQueryFunctionUrl() {
        StringBuilder sb = new StringBuilder(ConfigCenter.CRM_OBJECT_INNER_URL_PREFIX);
        sb.append("/function/service/find");
        return sb.toString();
    }

    /**
     * 分配路由url
     */
    public static String buildConfigRouteUrl() {
        StringBuilder sb = new StringBuilder(ConfigCenter.DB_POR_URL);
        sb.append("/db-pod/erpSyncData/configRoute");
        return sb.toString();
    }

    public static String buildTriggerPollingMongoUrl() {
        StringBuilder sb = new StringBuilder(ConfigCenter.TASK_BASE_URL);
        sb.append("/erp/syncdata/probedata/executePollingTempData");
        return sb.toString();
    }
}
