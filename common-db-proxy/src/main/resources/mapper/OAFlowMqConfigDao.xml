<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.OAFlowMqConfigDao">
    <sql id="Base_Column_List">
        id,
        event_type,
        obj_api_name,
        apl_api_name,
        create_time,
        update_time,
        creator,
        modifier,
        tenant_id
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAFlowMqConfigEntity">
        <!--@mbg.generated-->
        <!--@Table erp_connect_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="apl_api_name" jdbcType="VARCHAR" property="aplApiName"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
    </resultMap>

    <!--auto generated by MybatisCodeHelper on 2020-08-31-->
    <select id="queryListByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from oa_flow_mq_config
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and event_type=#{eventType,jdbcType=VARCHAR}
        and obj_api_name=#{objApiName,jdbcType=VARCHAR}
    </select>

</mapper>