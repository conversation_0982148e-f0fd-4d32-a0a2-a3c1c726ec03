<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpDataFeatureDao">
  <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDataFeatureEntity">
    <!--@mbg.generated-->
    <!--@Table erp_data_feature-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="object_api_name" jdbcType="VARCHAR" property="objectApiName" />
    <result column="data_id" jdbcType="VARCHAR" property="dataId" />
    <result column="feature" jdbcType="VARCHAR" property="feature" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, object_api_name, data_id, feature, create_time, update_time
  </sql>


<!--auto generated by MybatisCodeHelper on 2021-05-21-->
  <select id="getOne" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from erp_data_feature
    where tenant_id=#{tenantId,jdbcType=VARCHAR} and data_id=#{dataId,jdbcType=VARCHAR}
  </select>
</mapper>