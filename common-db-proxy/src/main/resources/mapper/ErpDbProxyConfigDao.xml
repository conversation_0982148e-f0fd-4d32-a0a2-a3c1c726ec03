<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpDbProxyConfigDao">
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        tenant_id,
        data_center_id,
        obj_api_name,
        db_key,
        query_sql,
        date_time_condition_field,
        date_format,
        query_id_sql,
        create_time,
        update_time,
        last_modify_by,
        parent_obj_api_name,
        insert_sql,
        update_sql,
        invalid_sql,
        always_offset_zero
        <!--@sql from erp_db_proxy_config -->
    </sql>

    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDBProxyConfigEntity">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="db_key" jdbcType="VARCHAR" property="dbKey"/>
        <result column="query_sql" jdbcType="VARCHAR" property="querySql"/>
        <result column="date_time_condition_field" jdbcType="VARCHAR" property="dateTimeConditionField"/>
        <result column="date_format" jdbcType="VARCHAR" property="dateFormat"/>
        <result column="query_id_sql" jdbcType="VARCHAR" property="queryIdSql"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="last_modify_by" jdbcType="VARCHAR" property="lastModifyBy"/>
        <result column="parent_obj_api_name" jdbcType="VARCHAR" property="parentObjApiName"/>
        <result column="insert_sql" jdbcType="VARCHAR" property="insertSql"/>
        <result column="update_sql" jdbcType="VARCHAR" property="updateSql"/>
        <result column="invalid_sql" jdbcType="VARCHAR" property="invalidSql"/>
        <result column="always_offset_zero" property="alwaysOffsetZero"/>
    </resultMap>

    <select id="getDBProxyConfigByTenantAndObjApiName" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from erp_db_proxy_config
        where
        tenant_id=#{tenantId}
        and data_center_id=#{datacenterId}
        and obj_api_name=#{objApiName}
    </select>

    <select id="queryDBProxyConfigByTenantAndParentObjApiName"  resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from erp_db_proxy_config
        where
        tenant_id=#{tenantId}
        and data_center_id=#{datacenterId}
        and parent_obj_api_name=#{parentObjApiName}
    </select>

    <select id="queryByTenantId"  resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from erp_db_proxy_config
        where
        tenant_id=#{tenantId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-03-17-->
    <delete id="deleteByTenantId">
        delete from erp_db_proxy_config
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>


</mapper>