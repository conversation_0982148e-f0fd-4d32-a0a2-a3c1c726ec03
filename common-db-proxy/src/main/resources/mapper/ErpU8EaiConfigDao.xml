<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpU8EaiConfigDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        obj_api_name,
        data_center_id,
        id_field,
        db_key,
        query_sql,
        date_time_condition_field,
        label,
        query_id_sql,
        create_time,
        update_time,
        last_modify_by,
        parent_obj_api_name,
        use_combine,
        extend
    </sql>

    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpU8EaiConfigEntity">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="id_field" jdbcType="VARCHAR" property="idField"/>
        <result column="db_key" jdbcType="VARCHAR" property="dbKey"/>
        <result column="query_sql" jdbcType="VARCHAR" property="querySql"/>
        <result column="date_time_condition_field" jdbcType="VARCHAR" property="dateTimeConditionField"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="query_id_sql" jdbcType="VARCHAR" property="queryIdSql"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="last_modify_by" jdbcType="VARCHAR" property="lastModifyBy"/>
        <result column="parent_obj_api_name" jdbcType="VARCHAR" property="parentObjApiName"/>
        <result column="extend" jdbcType="VARCHAR" property="extend"/>
        <result column="use_combine" jdbcType="BOOLEAN" property="useCombine"/>
    </resultMap>

    <select id="getEaiConfigByTenantAndObjApiName" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from erp_u8_eai_config
        where
        tenant_id=#{tenantId}
        and data_center_id=#{datacenterId}
        and obj_api_name=#{objApiName}
    </select>

    <select id="queryEaiConfigByTenantAndParentObjApiName"  resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from erp_u8_eai_config
        where
        tenant_id=#{tenantId}
        and data_center_id=#{datacenterId}
        and parent_obj_api_name=#{parentObjApiName}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-03-17-->
    <delete id="deleteByTenantId">
        delete from erp_u8_eai_config
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>
</mapper>