<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity">
        <!--@mbg.generated-->
        <!--@Table erp_object_field-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="erp_object_apiname" jdbcType="VARCHAR" property="erpObjectApiName"/>
        <result column="field_apiname" jdbcType="VARCHAR" property="fieldApiName"/>
        <result column="field_label" jdbcType="VARCHAR" property="fieldLabel"/>
        <result column="required" jdbcType="BOOLEAN" property="required"/>
        <result column="field_define_type" jdbcType="VARCHAR" property="fieldDefineType"/>
        <result column="field_extend_value" jdbcType="LONGVARCHAR" property="fieldExtendValue"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id,data_center_id, channel, erp_object_apiname, field_apiname, field_label, required,
        field_define_type, field_extend_value, create_time, update_time
    </sql>

    <delete id="deleteByObjApiName">
        delete
        from erp_object_field
        where tenant_id = #{ei}
          and erp_object_apiname = #{objApiName}
          and data_center_id = #{dataCenterId}
    </delete>

    <delete id="deleteByTenantIdAndDcId">
        delete
        from erp_object_field
        where tenant_id = #{tenantId}
          and data_center_id = #{dataCenterId}
    </delete>

    <select id="pageByDcId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id = #{tenantId}
        and data_center_id = #{dataCenterId}
        <if test="id != null and id != ''">
            and id &gt; #{id}
        </if>
        order by id
        limit #{pageSize}
    </select>

    <!--auto generated by MybatisCodeHelper on 2020-09-03-->
    <select id="findIdField" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and erp_object_apiname = #{erpObjectApiName,jdbcType=VARCHAR}
          and field_define_type = 'id'
    </select>

    <select id="findIdField2" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        and erp_object_apiname = #{erpObjectApiName,jdbcType=VARCHAR}
        and field_define_type = 'id'
    </select>

    <select id="findData" resultMap="BaseResultMap">
        select * from erp_object_field
        where tenant_id = #{tenantId}
        <if test="dataCenterId != null and dataCenterId != ''">
            and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        </if>
        and erp_object_apiname = #{erpObjectApiName} ORDER BY update_time
    </select>

    <select id="findSelectOneFieldList" resultMap="BaseResultMap">
        select * from erp_object_field
        where tenant_id = #{tenantId}
        and data_center_id = #{dataCenterId}
        and erp_object_apiname in
        <foreach item="item" index="index" collection="objectApiNameList"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and field_define_type = 'select_one'
        and field_extend_value!='[]'
    </select>



    <select id="queryMasterDetailField" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and erp_object_apiname = #{erpObjectApiName,jdbcType=VARCHAR}
        and field_define_type = 'master_detail'
        and field_extend_value=#{extendObjectApiName,jdbcType=LONGVARCHAR}
    </select>

    <select id="queryObjExtendDTO" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto">
        select rel.erp_actual_object_apiname realObjApiName,
               obj.erp_object_apiname        splitObjApiName,
               obj.erp_object_extend_value   extentValue,
               rel.split_type                splitType,
               field.field_apiname           idFieldName,
               rel.split_seq                 splitSeq
        from erp_object_relationship rel
                     left join erp_object obj
                on rel.tenant_id = obj.tenant_id and rel.erp_split_object_apiname = obj.erp_object_apiname
                     left join (select *
                                from erp_object_field field
                                where tenant_id = #{tenantId} and data_center_id = #{dataCenterId} and field_define_type = 'id') field
                on obj.erp_object_apiname = field.erp_object_apiname
        where rel.tenant_id = #{tenantId}
          and rel.data_center_id = #{dataCenterId}
          and rel.erp_actual_object_apiname = #{realApiName}
    </select>


    <select id="findObjExtendDtoBySplit" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto">
        select rel.erp_actual_object_apiname realObjApiName,
        obj.erp_object_apiname        splitObjApiName,
        obj.erp_object_extend_value   extentValue,
        rel.split_type                splitType,
        field.field_apiname           idFieldName,
        rel.split_seq                 splitSeq
        from erp_object_relationship rel
        left join erp_object obj
        on rel.tenant_id = obj.tenant_id and rel.erp_split_object_apiname = obj.erp_object_apiname
        left join (select *
        from erp_object_field field
        where tenant_id = #{tenantId} and field_define_type = 'id') field
        on obj.erp_object_apiname = field.erp_object_apiname
        where rel.tenant_id = #{tenantId}
        and rel.erp_split_object_apiname = #{splitApiName}
        limit 1;
    </select>
    <!--auto generated by MybatisCodeHelper on 2020-11-17-->
    <select id="findByObjApiNameAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and erp_object_apiname=#{erpObjectApiName,jdbcType=VARCHAR} and
        field_define_type in
        <foreach item="item" index="index" collection="fieldDefineTypeCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="queryFieldsByTenantIdAndObjectApiName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where
        tenant_id=#{tenantId,jdbcType=VARCHAR}
        and erp_object_apiname=#{erpObjectApiName}
        <if test="queryStr != null and queryStr != ''">
            and (field_label like CONCAT('%',#{queryStr},'%') or field_apiname like CONCAT('%',#{queryStr},'%'))
        </if>
        order by update_time desc
        offset #{offset}
        limit #{limit}
    </select>
    <select id="countByTenantIdAndObjectApiName" resultType="java.lang.Integer">
        select count(id)
        from erp_object_field
        where
        tenant_id=#{tenantId,jdbcType=VARCHAR}
        and erp_object_apiname=#{erpObjectApiName}
        <if test="queryStr != null and queryStr != ''">
            and (field_label like CONCAT('%',#{queryStr},'%') or field_apiname like CONCAT('%',#{queryStr},'%'))
        </if>
    </select>
    <delete id="deleteByTenantId">
        delete
        from erp_object_field
        where tenant_id = #{tenantId}
    </delete>
    <select id="getObjectFieldByTenantId" resultMap="BaseResultMap">
        select
        id,erp_object_apiname,field_label,field_apiname,field_define_type,field_extend_value
        from erp_object_field
        where
        tenant_id=#{tenantId,jdbcType=VARCHAR}
        <if test="erpObjectApiName != null and erpObjectApiName != ''">
            and erp_object_apiname=#{erpObjectApiName}
        </if>
        and field_extend_value !=''
    </select>

    <select id="bulkQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id = #{tenantId}
          and data_center_id = #{dcId}
          and
        <foreach collection="objFieldsDtos" open="(" close=")" separator=" or " item="obj">
                erp_object_apiname = #{obj.objApiName} and field_apiname in
            <foreach collection="obj.fieldApiNames" open="(" close=")" separator=" , " item="field">
                #{field}
            </foreach>
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-04-03-->
    <select id="findByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-04-10-->
    <select id="findByObjsAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and data_center_id=#{dataCenterId,jdbcType=VARCHAR} and
        erp_object_apiname in
        <foreach item="item" index="index" collection="erpObjectApiNameCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and field_define_type=#{fieldDefineType,jdbcType=VARCHAR}
    </select>
    <select id="findByTenantIdAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_field
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
          and id=#{id,jdbcType=VARCHAR}
    </select>
</mapper>