<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p" xmlns="http://www.springframework.org/schema/beans"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <import resource="classpath*:spring/ei-ea-converter.xml"/>
  <import resource="classpath:spring/cus-crmrest.xml"/>
  <import resource="classpath:paasauthrest/paasauthrest.xml"/>
  <import resource="classpath:otherrest/otherrest.xml"/>

  <!--fs-orgainzation-adapter-api -->
  <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
  <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
  <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>
</beans>