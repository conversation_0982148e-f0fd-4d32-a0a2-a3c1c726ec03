<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-erp-sync-data</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <artifactId>common-db-proxy</artifactId>

    <dependencies>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-pod-client</artifactId>
            <version>${fs-pod-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>postgresql</artifactId>
                    <groupId>org.postgresql</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mybatis-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-db</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-other-rest-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-enterpriserelation-rest-api2</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>retrofit-spring2</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-crm-rest-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-auth-rest-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>dispatcher-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>sync-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-text</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>erp-preprocess-data-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-uc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.12.2</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.notifier</groupId>
            <artifactId>notifier-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.fxiaoke.common</groupId>-->
        <!--            <artifactId>fs-rest-es8-support</artifactId>-->
        <!--            <version>2.0.0-SNAPSHOT</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>httpcore</artifactId>-->
        <!--                    <groupId>org.apache.httpcomponents</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>snakeyaml</artifactId>-->
        <!--                    <groupId>org.yaml</groupId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-adapter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.zhxing</groupId>
            <artifactId>retrofit-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-restful-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.msg</groupId>
            <artifactId>fs-message-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-stone-sdk</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mockito-core</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-aviator</artifactId>
        </dependency>
        <!--企信-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-api</artifactId>
            <version>0.1.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>protostuff-core</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>protostuff-runtime</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>morphia</artifactId>
                    <groupId>org.mongodb.morphia</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <artifactId>rocketmq-client</artifactId>-->
        <!--            <groupId>com.alibaba.rocketmq</groupId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <!-- mock redis用于测试：https://github.com/microwww/redis-mock-->
        <dependency>
            <groupId>com.github.microwww</groupId>
            <artifactId>redis-server</artifactId>
            <version>0.3.1</version>
            <scope>test</scope>
        </dependency>
        <!--测试启动http服务使用-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
            <version>${hutool.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-dubbo-rest-plugin</artifactId>
        </dependency>
        <!--         重试组件-->
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-admin-api</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.6.0</version>
            <!-- use uber jar with all dependencies included, change classifier to http for smaller jar -->
            <classifier>http</classifier>
        </dependency>
        <!--powermock-->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.4.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.3.1</version>
        </dependency>

        <!--用于mongodb的单元测试 注意不能升级版本，新版本不支持jdk8运行，本地调试比较麻烦-->
        <dependency>
            <groupId>de.bwaldvogel</groupId>
            <artifactId>mongo-java-server-h2-backend</artifactId>
            <version>1.42.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.2.224</version>
            <scope>test</scope>
        </dependency>

        <!-- JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- JUnit Vintage Engine (allows JUnit 4 tests to run via JUnit 5 platform) -->
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Mockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>jedis-spring-support</artifactId>
        </dependency>
        <!-- End Redis -->
    </dependencies>
</project>