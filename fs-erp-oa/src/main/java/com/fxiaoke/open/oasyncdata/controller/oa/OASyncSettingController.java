package com.fxiaoke.open.oasyncdata.controller.oa;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.arg.CepCrmTypeArg;
import com.fxiaoke.open.oasyncdata.arg.CepListArg;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.mongo.OASettingsDao;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.FileService;
import com.fxiaoke.open.oasyncdata.service.NotificationService;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.service.OASyncApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA重试设置相关接口")
@RestController("oASyncSettingController")
@RequestMapping("cep/oa/setting")
@Slf4j
public class OASyncSettingController extends BaseController {
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private OASettingsDao oaSettingsDao;

    @ApiOperation(value = "保存设置")
    @RequestMapping(value = "/saveResyncRule", method = RequestMethod.POST)
    public Result<String> saveResyncRule(@RequestBody OAMessageResyncRule oaMessageResyncRule) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();
        OASettingVO oaSettingVO=new OASettingVO();
        oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
        oaSettingVO.setTenantId(tenantId);
        oaSettingVO.setCurrentDcId(dataCenterId);
        oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
        Result<String> saveResult = oaSettingService.upsertSettingInfo(tenantId, oaSettingVO, dataCenterId);
        return saveResult;
    }

    @ApiOperation(value = "返回设置的规则")
    @RequestMapping(value = "/returnRule", method = RequestMethod.POST)
    public Result<OAMessageResyncRule> returnRule(@RequestBody OAMessageResyncRule data) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId=getDcId();
        QueryOASettingArg queryOASettingArg=new QueryOASettingArg();
        queryOASettingArg.setTenantId(tenantId);
        queryOASettingArg.setCurrentDcId(dataCenterId);
        queryOASettingArg.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
        OASettingDoc configByType = oaSettingsDao.getConfigByType(tenantId, queryOASettingArg);
        if(ObjectUtils.isNotEmpty(configByType)){
            OASettingVO oaSettingVO= BeanUtil.copy(configByType,OASettingVO.class);
           OAMessageResyncRule oaMessageResyncRule= JSONObject.parseObject(oaSettingVO.getConfiguration(),OAMessageResyncRule.class);
            return Result.newSuccess(oaMessageResyncRule);
        }
        return Result.newSuccess();
    }


}
