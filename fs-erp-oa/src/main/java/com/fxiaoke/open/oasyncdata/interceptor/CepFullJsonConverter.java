package com.fxiaoke.open.oasyncdata.interceptor;

import com.facishare.cep.plugin.converter.FSConverter;
import com.google.common.collect.ImmutableList;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotWritableException;

import java.io.IOException;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/6
 */
public class CepFullJsonConverter extends FSConverter {

    public CepFullJsonConverter() {
        super(new CepFullJsonSerializer(), ImmutableList.of(MediaType.APPLICATION_JSON));
    }

    @Override
    public void write(Object o, MediaType mediaType, HttpOutputMessage httpOutputMessage) throws IOException, HttpMessageNotWritableException {
        httpOutputMessage.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        super.write(o, mediaType, httpOutputMessage);
    }
}
