package com.fxiaoke.open.oasyncdata.db.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.function.Function;

/**
 * trace 工具类
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/9
 */
@Slf4j
@Component
public class TraceUtil {

    private static Function<String, String> funcEiTEa = v -> v;

    private static Function<String,String> funcEiTLocale = v -> "zh-CN";

    @Autowired
    public void setFuncEiTEa(EIEAConverter eieaConverter) {
        if (eieaConverter != null) {
            funcEiTEa = v -> {
                try {
                    v = eieaConverter.enterpriseIdToAccount(Integer.parseInt(v));
                } catch (Exception e) {
                    log.warn("trace convert 2 ea exception:{}", v, e);
                }
                return v;
            };
        }
    }


    @Autowired
    public void setFuncEiTEa(I18NStringManager i18NStringManager) {
        if (i18NStringManager != null) {
            funcEiTLocale = v -> i18NStringManager.getDefaultLang(v);
        }
    }
    public static String initTraceWithFormat(String tenantId,String userId,String childLog) {
        if(StringUtils.isBlank(userId)){
            userId="-10000";
        }else if(userId.contains(".")){
            String[] splitList=userId.split(".");
            if(splitList.length>1){
                userId=splitList[1];
            }
        }
        return initTraceWithUser(tenantId,userId , "J-E.%s.%s-erpOA" + TimeUtil.hms(),childLog);
    }
    public static void setEiAndEa(String tenantId,String ea) {
        TraceContext traceContext = TraceContext.get();
        if (!ObjectUtil.equals(traceContext.getEi(), tenantId)) {
            traceContext.setEi(tenantId);
        }
        if (!ObjectUtil.equals(traceContext.getEa(), ea)) {
            traceContext.setEa(ea);
        }
        if (StrUtil.isEmpty(traceContext.getLocale())) {
            traceContext.setLocale(funcEiTLocale.apply(tenantId));
        }
    }

    public static String initTraceWithUser(String tenantId,String userId, String format,String childLog) {
        String ea = funcEiTEa.apply(tenantId);
        setEiAndEa(tenantId,ea);
        // 我知道以上代码有点迷惑，但其实只是想最大程度减少不必要的调用。
        String traceId = String.format(format, ea, userId);
        if(StringUtils.isNotBlank(childLog)){
            traceId=traceId+"-"+childLog;
        }
        initTrace(traceId);
        return traceId;
    }

    public static String get(){
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        return traceId;
    }
    /**
     * 如果原来有traceid，会移除
     * @param parentId
     */
    public static void initTrace(String parentId) {
        removeTrace();
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        if (StringUtils.isNotEmpty(traceId)) {
            log.debug("trans trace id,old:{},new:{}",traceId,parentId);
        }
        context.setTraceId(parentId);
        MDC.put("traceId", parentId);
    }

    public static void initTraceWithFormat(){
        try {
            String simpleUUID = IdUtil.simpleUUID();
            TraceUtil.initTrace(String.format("fs-erp-oa-%s", simpleUUID));
        } catch (Exception e) {
            e.printStackTrace();
            TraceUtil.initTrace(String.format("fs-erp-oa-%s", TimeUtil.hms()));
        }
    }


    public static void removeTrace() {
        TraceContext.remove();
        MDC.remove("traceId");
    }


//    public static void main(String[] args) throws UnsupportedEncodingException {
//        initTrace("test");
//        String s = addChildTrace("华为有限公司");
//        System.out.println(s);
//    }
}
