package com.fxiaoke.open.oasyncdata.mongo;


import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonDocument;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR> @Date: 15:32 2021/7/23
 * OA的配置信息
 * @Desc:
 */
@Slf4j
@Component
public class OASettingsDao {
    @Autowired
    private OASyncLogMongoStore oaSyncLogMongoStore;
    @Autowired
    private RedisDataSource redisDataSource;
    private static final String data_id = "dataId";
    private static final String data_name = "dataName";
    private static final String object_name = "objectName";
    private static final String status = "status";
    private static final String message = "message";
    private static final String object_api_name = "objApiName";
    private static final String event_type = "eventType";
    private static final String title = "title";
    private static final String receiver_id = "receiverId";
    private static final String create_time = "createTime";
    private static final String update_time = "updateTime";
    private static final String last_sync_log_id = "lastSyncLogId";
    private static final String data_json = "dataJson";
    private static final String tenant_id = "tenantId";



    public void batchInsert(String tenantId, List<OASettingDoc> syncLogs) {
        UpdateOneModel<OASettingDoc> updateOneModel;
        List<WriteModel<OASettingDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (OASettingDoc syncLog : syncLogs) {
            updateOneModel = new UpdateOneModel<>(OAMappingDataHelper.updateByType(syncLog), OAMappingDataHelper.upsertOaSettings(syncLog), updateOption);
            requests.add(updateOneModel);
        }
        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);

        MongoCollection<OASettingDoc> collection = oaSyncLogMongoStore.getOaSettingDocs(tenantId);
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
        return;
    }
    public void batchUpdateById(String tenantId, List<OASettingDoc> syncLogs) {
        UpdateOneModel<OASettingDoc> updateOneModel;
        List<WriteModel<OASettingDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (OASettingDoc syncLog : syncLogs) {
            updateOneModel = new UpdateOneModel<>(OAMappingDataHelper.updateByTypeFilter(syncLog), OAMappingDataHelper.upsertOaSettings(syncLog), updateOption);
            requests.add(updateOneModel);
        }
        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);

        MongoCollection<OASettingDoc> collection = oaSyncLogMongoStore.getOaSettingDocs(tenantId);
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
        }
        if (!bulkWriteResult.wasAcknowledged()) {
            throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
        return;
    }

  //  @Cached(name = "configTypeValue",cacheType = CacheType.BOTH,syncLocal = true,key="#tenantId+#arg.type+#arg.currentDcId",timeUnit = TimeUnit.SECONDS, expire = 60,localLimit = 200)
    public OASettingDoc getConfigByType(String tenantId,QueryOASettingArg arg) {
        MongoCollection<OASettingDoc> collection = oaSyncLogMongoStore.getOaSettingDocs(tenantId);
        Bson filter = buildFilter(arg);

        OASettingDoc settingDoc = collection.find(filter)
                .sort(Sorts.descending("updateTime")).first();
        return settingDoc;
    }


    public void deleteById(String tenantId,String id) {
        MongoCollection<OASettingDoc> collection = oaSyncLogMongoStore.getOaSettingDocs(tenantId);
        Bson filter = Filters.and(Filters.eq("_id", new ObjectId(id)));
        DeleteResult deleteResult = collection.deleteOne(filter);

    }

    public void deleteTenantAndType(String tenantId,QueryOASettingArg arg) {
        MongoCollection<OASettingDoc> collection = oaSyncLogMongoStore.getOaSettingDocs(tenantId);
        Bson filter = buildFilter(arg);
        DeleteResult deleteResult = collection.deleteOne(filter);

    }


    public List<OASettingDoc> listConfigByType(String tenantId,QueryOASettingArg arg) {
        List<OASettingDoc> oaSettingDocs=new ArrayList<>();
        MongoCollection<OASettingDoc> collection = oaSyncLogMongoStore.getOaSettingDocs(tenantId);
        Bson filter = buildFilter(arg);

         oaSettingDocs = collection.find(filter)
                .sort(Sorts.descending("updateTime")).limit(100).into(oaSettingDocs);
        return oaSettingDocs;
    }


    public List<OASettingDoc> getListSetting(String tenantId) {
        List<OASettingDoc> oaSettingDocs=new ArrayList<>();

        MongoCollection<OASettingDoc> collection = oaSyncLogMongoStore.getOaSettingDocs(tenantId);
        QueryOASettingArg queryOASettingArg=new QueryOASettingArg();
        queryOASettingArg.setTenantId(tenantId);
        Bson filter = buildFilter(queryOASettingArg);

        oaSettingDocs = collection.find(filter)
                .sort(Sorts.descending("updateTime")).into(oaSettingDocs);
        return oaSettingDocs;
    }

    private Bson buildUpdate(OASyncLogMappingDoc oaSyncLogSnapshotDoc){
        List<Bson> updates = new ArrayList<>();

        if(oaSyncLogSnapshotDoc.getStatus()!=null){
            updates.add(set(status,oaSyncLogSnapshotDoc.getStatus()));
        }
        if(oaSyncLogSnapshotDoc.getMessage()!=null){
            updates.add(set(message,oaSyncLogSnapshotDoc.getMessage()));
        }
        if(oaSyncLogSnapshotDoc.getLastSyncLogId()!=null){
            updates.add(set(last_sync_log_id,oaSyncLogSnapshotDoc.getLastSyncLogId()));
        }
        return Updates.combine(updates);
    }



    private Bson buildFilter(QueryOASettingArg arg) {
        List<Bson> filters = Lists.newArrayList();
        if(StringUtils.isNotEmpty(arg.getTenantId())){
            filters.add(Filters.eq("tenantId", arg.getTenantId()));
        }
        if (StringUtils.isNotEmpty(arg.getType()) ) {
            filters.add(Filters.eq("type", arg.getType()));
        }
        if (StringUtils.isNotEmpty(arg.getCurrentDcId())) {
            filters.add(Filters.eq("dataCenterId", arg.getCurrentDcId()));
        }
        if(CollectionUtils.isEmpty(filters)){
            return new BsonDocument();
        }
        Bson filter = Filters.and(filters);
        return filter;
    }
}
