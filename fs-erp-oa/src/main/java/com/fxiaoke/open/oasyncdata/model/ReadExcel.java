package com.fxiaoke.open.oasyncdata.model;

import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.Data;

import java.io.InputStream;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
public class ReadExcel {

    @Data
    public static class Arg<T>{
        /**
         * 文件流
         */
        private InputStream inputStream;
        /**
         * class类型
         */
        private Class<T> type ;
        /**
         * sheetName
         */
        private String sheetName ;
        /**
         * 读listener
         */
        private AnalysisEventListener<T> excelListener;
        
        private ExcelTypeEnum excelType;
    }
}
