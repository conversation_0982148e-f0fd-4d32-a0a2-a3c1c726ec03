package com.fxiaoke.open.oasyncdata.impl;


import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAObjFieldDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAObjFieldEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAObjectFieldService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2021/3/19
 * @Desc:OA同步策略查询
 */
@Service
@Slf4j
@Data
public class OAObjectFieldServiceImpl implements OAObjectFieldService {

    @Autowired
    private OAObjFieldDao oaObjFieldDao;

    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;
    private String formateDesc="%s.field.%s.label";

    /**
     * 对象名称：describeApiName + ".attribute.self.display_name"
     * 字段名称：describeApiName + ".field." + fieldApiName + ".label"
     * @param tenantId
     * @param lang
     * @return
     */

    @Override
    public Result<Map<String, List<OAObjectFieldVO>>> getObjectField(String tenantId, String lang) {
        //循环获取对应的流程占位符
        List<String> apiNames = Arrays.stream(ObjectApiEnum.values()).map(ObjectApiEnum::getObjApiName).collect(Collectors.toList());
        Map<String, List<OAObjectFieldVO>> oaObjectFieldVOList = Maps.newHashMap();
        apiNames.forEach(item -> {
            List<OAObjFieldEntity> oaObjFieldEntityList = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdAndObj(tenantId, item);
            List<OAObjectFieldVO> oaObjectFieldVOS = Lists.newArrayList();
            for (OAObjFieldEntity oaObjFieldEntity : oaObjFieldEntityList) {
                OAObjectFieldVO oaObjectFieldVO = new OAObjectFieldVO();
                BeanUtils.copyProperties(oaObjFieldEntity, oaObjectFieldVO);
                String i18NKey = String.format(formateDesc, oaObjectFieldVO.getObjApiName(), oaObjectFieldVO.getFieldApiName());
                String labelValue = i18NStringManager.get(i18NKey,  lang,tenantId,oaObjectFieldVO.getLabel());
                if(StringUtils.isNotEmpty(labelValue)){
                    oaObjectFieldVO.setLabel(labelValue);
                }
                oaObjectFieldVOS.add(oaObjectFieldVO);
            }
            oaObjectFieldVOList.put(item, oaObjectFieldVOS);
        });
        return Result.newSuccess(oaObjectFieldVOList);
    }

    @Override
    public Result<List> addOrUpdateObjectField(String tenantId, List<OAObjectFieldVO> oaObjectFieldVOS, String lang) {
        List<String> result = new ArrayList<>();
        for (OAObjectFieldVO oaObjectFieldVO : oaObjectFieldVOS) {
            int i;
            OAObjFieldEntity oaObjFieldEntity = BeanUtil.deepCopy(oaObjectFieldVO, OAObjFieldEntity.class);
            if (StringUtils.isBlank(oaObjFieldEntity.getId())) {
                String id = idGenerator.get();
                oaObjFieldEntity.setId(id);
                i = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaObjFieldEntity);
            } else {
                i = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(oaObjFieldEntity);
            }
            if (i == 1) {
                result.add(oaObjFieldEntity.getId() + i18NStringManager.get(I18NStringEnum.s1244,lang,tenantId));
            } else {
                result.add(oaObjFieldEntity.getId() + i18NStringManager.get(I18NStringEnum.s1245,lang,tenantId));
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List> addOrUpdateObjectFieldByApiName(String tenantId, String apiName,String prefix, String lang) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(headerObj, apiName);
        List<FieldDescribe> packageField = describe.getData().getDescribe().getFields().values().stream().filter(item -> !item.getDefineType().equals("custom")).collect(Collectors.toList());

        List<OAObjFieldEntity> oaObjFieldEntityList=Lists.newArrayList();
        for (int i = 0; i <packageField.size(); i++) {
            FieldDescribe fieldDescribe=packageField.get(i);
            OAObjFieldEntity oaObjFieldEntity=new OAObjFieldEntity();
            oaObjFieldEntity.setObjApiName(apiName);
            oaObjFieldEntity.setFieldApiName(fieldDescribe.getApiName());
            oaObjFieldEntity.setTenantId("0");
            oaObjFieldEntity.setLabel(fieldDescribe.getLabel());
            String replaceName=String.format("%03d", i+2);
            StringBuilder stringBuilder=new StringBuilder().append(prefix).append(replaceName);
            oaObjFieldEntity.setReplaceName(stringBuilder.toString());
            String id = idGenerator.get();
            oaObjFieldEntity.setId(id);
            oaObjFieldEntity.setCreateTime(System.currentTimeMillis());
            oaObjFieldEntity.setUpdateTime(System.currentTimeMillis());
            oaObjFieldEntityList.add(oaObjFieldEntity);

        }
        //默认都是F001是创建人
        OAObjFieldEntity oaObjFieldEntityCandidateIds=new OAObjFieldEntity();
        oaObjFieldEntityCandidateIds.setObjApiName(apiName);
        oaObjFieldEntityCandidateIds.setFieldApiName("candidate_ids");
        oaObjFieldEntityCandidateIds.setTenantId("0");
        oaObjFieldEntityCandidateIds.setLabel(i18NStringManager.get(I18NStringEnum.s1246,lang,tenantId));
        String id = idGenerator.get();
        oaObjFieldEntityCandidateIds.setId(id);
        oaObjFieldEntityCandidateIds.setReplaceName("#F001");
        oaObjFieldEntityCandidateIds.setCreateTime(System.currentTimeMillis());
        oaObjFieldEntityCandidateIds.setUpdateTime(System.currentTimeMillis());
        oaObjFieldEntityList.add(oaObjFieldEntityCandidateIds);
        //之前这张表没有建索引。。。。
        for (OAObjFieldEntity oaObjFieldEntity : oaObjFieldEntityList) {
            List<OAObjFieldEntity> byTenantIdByObjField = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdByObjField(oaObjFieldEntity.getTenantId(), oaObjFieldEntity.getObjApiName(), oaObjFieldEntity.getFieldApiName());
            if(CollectionUtils.isNotEmpty(byTenantIdByObjField)){
                oaObjFieldEntity.setId(byTenantIdByObjField.get(0).getId());
                oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(oaObjFieldEntity);
            }else {
                oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaObjFieldEntity);
            }

        }
        return Result.newSuccess();
    }

    @Override
    public Result<List> addOrUpdateSupportField(String tenantId, String apiName, String replaceName, String fieldApiName, String label) {
        OAObjFieldEntity oaObjFieldEntity=new OAObjFieldEntity();
        oaObjFieldEntity.setObjApiName(apiName);
        oaObjFieldEntity.setFieldApiName(fieldApiName);
        oaObjFieldEntity.setReplaceName(replaceName);
        oaObjFieldEntity.setTenantId("0");
        oaObjFieldEntity.setLabel(label);

        String id = idGenerator.get();
        oaObjFieldEntity.setId(id);
        oaObjFieldEntity.setCreateTime(System.currentTimeMillis());
        oaObjFieldEntity.setUpdateTime(System.currentTimeMillis());

        List<OAObjFieldEntity> byTenantIdByObjField = oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdByObjField(oaObjFieldEntity.getTenantId(), oaObjFieldEntity.getObjApiName(), oaObjFieldEntity.getFieldApiName());
        if(CollectionUtils.isNotEmpty(byTenantIdByObjField)){
            oaObjFieldEntity.setId(byTenantIdByObjField.get(0).getId());
            oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(oaObjFieldEntity);
        }else {
            oaObjFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaObjFieldEntity);
        }
        return Result.newSuccess();
    }


}
