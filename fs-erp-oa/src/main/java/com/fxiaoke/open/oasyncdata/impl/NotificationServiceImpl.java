package com.fxiaoke.open.oasyncdata.impl;



import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.service.NFileStorageService;

import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.restful.client.FRestApiProxyFactory;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.SendTextNoticeArg;
import com.fxiaoke.open.oasyncdata.db.constant.CommonConstant;
import com.fxiaoke.open.oasyncdata.db.manager.ErpConnectInfoManager;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.manager.TenantConfigurationManager;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.NotificationService;

import com.fxiaoke.otherrestapi.openmessage.arg.SendTextMessageArg;
import com.fxiaoke.otherrestapi.openmessage.common.MessageData;
import com.fxiaoke.otherrestapi.openmessage.common.MessageSendTypeEnum;
import com.fxiaoke.otherrestapi.openmessage.common.MessageTypeEnum;
import com.fxiaoke.otherrestapi.openmessage.result.SendTextMessageResult;
import com.fxiaoke.otherrestapi.openmessage.service.SendMessageService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.util.*;


/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/1
 */
@Slf4j
@Service("notificationService")
public class NotificationServiceImpl implements NotificationService {
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private I18NStringManager i18NStringManager;

    public String uploadTnFile(String ea, Integer userId, byte[] bytes) {
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUpload.Result result = storageService.nTempFileUpload(arg, ea);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile failed");
        }
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        throw new ErpSyncDataException(ResultCodeEnum.FILE_UPLOAD_FAILED,tenantId);
    }

    /**
     * 发送ERP数据同步应用通知到企信
     * 文本消息
     *
     * @param arg
     * @return
     */
    @Override
    public Result<Void> sendErpSyncDataAppNotice(SendTextNoticeArg arg) {
        String msg = arg.getMsg();
        String ea;
        if (StringUtils.isBlank(arg.getEnterpriseAccount())) {
            String tenantId = arg.getTenantId();
            int intTenantId = Integer.parseInt(tenantId);
            ea = eieaConverter.enterpriseIdToAccount(intTenantId);
        } else {
            ea = arg.getEnterpriseAccount();
        }
        List<Integer> receivers = arg.getReceivers();
        String appId = ConfigCenter.ERP_SYNC_DATA_APP_ID;
        String msgTitle = arg.getMsgTitle();
        if (StringUtils.isNotBlank(msgTitle)) {
            msg = String.format("---%s---\n%s", msgTitle, msg);
        }
        //如果文本信息过长，则转换成文本文件发送
        if (msg.length() < ConfigCenter.NOTICE_MAX_SIZE) {
            sendTextMessage(ea, receivers, appId, msg);
        } else {
            sendTextTransFileMsg(ea, receivers, appId, msgTitle, msg);
        }
        return Result.newSuccess();
    }






    private void sendTextTransFileMsg(String ea, List<Integer> toUserList, String appId, String title, String content) {
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        String tnPath = uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, content.getBytes(Charset.defaultCharset()));
        if (tnPath == null) {
            log.error("sendTextTransFileMsg uploadTnFile failed,ea:{},toUserList:{},content:{}", ea, toUserList, content);
            return;
        }
        String previewUrl = String.format(ConfigCenter.PREVIEW_FILE_PATH, tnPath + ".txt");
        String msg = i18NStringManager.get2(I18NStringEnum.s637.getI18nKey(),
                null,
                tenantId,
                String.format(I18NStringEnum.s637.getI18nValue(), title,"\n", previewUrl),
                Lists.newArrayList(title,"\n", previewUrl));
        sendTextMessage(ea, toUserList, appId, msg);
    }

    /**
     * 发送文本消息
     *
     * @param ea
     * @param toUserList
     * @param appId
     * @param content
     */
    private void sendTextMessage(String ea, List<Integer> toUserList, String appId, String content) {
        //最大长度为2000
        if (content.length() > 1998) {
            content = content.substring(0, 1900) + "...";
        }
        SendTextMessageArg arg = new SendTextMessageArg();
        arg.setMessageSendType(MessageSendTypeEnum.THIRD_PARTY_PUSH.getValue());
        MessageData textMessageVO = new MessageData();
        textMessageVO.setEnterpriseAccount(ea);
        textMessageVO.setToUserList(toUserList);
        textMessageVO.setAppId(appId);
        textMessageVO.setContent(content);
        textMessageVO.setType(MessageTypeEnum.TEXT.getType());
        textMessageVO.setPostId(idGenerator.get());
        arg.setTextMessageVO(textMessageVO);
        SendTextMessageResult sendTextMessageResult = sendMessageService.sendTextMessage(arg);
        if (!sendTextMessageResult.isSuccess()) {
            log.error("send text msg failed,arg:{},result:{}", arg, sendTextMessageResult);
        }
    }
}
