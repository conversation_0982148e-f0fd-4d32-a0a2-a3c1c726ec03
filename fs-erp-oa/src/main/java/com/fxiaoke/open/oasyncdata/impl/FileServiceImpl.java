package com.fxiaoke.open.oasyncdata.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.annotation.LogLevel;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.db.constant.CommonConstant;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.exsyexcel.handler.CustomCellWriteHandler;
import com.fxiaoke.open.oasyncdata.manager.FileManager;
import com.fxiaoke.open.oasyncdata.manager.OAEmployeeDataMappingListener;
import com.fxiaoke.open.oasyncdata.model.BuildExcelFile;
import com.fxiaoke.open.oasyncdata.model.ImportExcelFile;
import com.fxiaoke.open.oasyncdata.model.OAEmployeeDataMappingExcelVo;
import com.fxiaoke.open.oasyncdata.model.ReadExcel;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.EmployeeMappingService;
import com.fxiaoke.open.oasyncdata.service.FileService;
import com.fxiaoke.open.oasyncdata.util.ExcelUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Autowired
    private IdGenerator idGenerator;
   @Autowired
   private EmployeeMappingService employeeMappingService;
   @Autowired
   private FileManager fileManager;
   @Autowired
   private EIEAConverter eieaConverter;
   @Autowired
   private I18NStringManager i18NStringManager;

    @Override
    @LogLevel
    public Result<ImportExcelFile.Result> importExcelFile(ImportExcelFile.FieldDataMappingArg arg, String lang) throws IOException {
        Result<ImportExcelFile.Result> result;
        switch (arg.getExcelType()) {
            case OA_EMPLOYEE_DATA_MAPPING:
                result = importOAEmployeeDataMapping(arg,lang);
                break;
            default:
                throw new IllegalStateException("Unexpected excelType: " + arg.getExcelType());
        }
        if (!result.isSuccess()) {
            return result;
        }
        return result;
    }
    @Override
    public <R> Result<BuildExcelFile.Result> buildExcelFile(BuildExcelFile.Arg<R> buildExcelArg,String lang) {
        String ea = buildExcelArg.getEa();
        if (StringUtils.isBlank(ea)) {
            ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(buildExcelArg.getTenantId()));
        }
        List<R> dataList = buildExcelArg.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return new Result<>(ResultCodeEnum.LIST_EMPTY);
        }
        //写excel
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HorizontalCellStyleStrategy styleStrategy = buildExcelArg.getStyle();
        if (styleStrategy == null) {
            styleStrategy = ExcelUtils.getDefaultStyle();
        }
        ExcelWriter excelWriter = EasyExcel.write(outputStream, dataList.get(0).getClass())
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        final CustomCellWriteHandler customCellWriteHandler = new CustomCellWriteHandler(i18NStringManager, buildExcelArg.getTenantId(), lang);
        for (String sheetName : buildExcelArg.getSheetNames()) {
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).registerWriteHandler(customCellWriteHandler).build();
            excelWriter.write(dataList, writeSheet);
        }
        excelWriter.finish();
        //上传文件系统
        String tnPath = fileManager.uploadTnFile(ea, CommonConstant.SUPER_ADMIN_USER, outputStream.toByteArray());
        BuildExcelFile.Result result = new BuildExcelFile.Result();
        String fileName = StringUtils.appendIfMissing(buildExcelArg.getFileName(), ".xlsx");
        result.setFileName(fileName);
        result.setTnFilePath(tnPath);
        return new Result<>(result);
    }

    private Result<ImportExcelFile.Result> importOAEmployeeDataMapping(ImportExcelFile.FieldDataMappingArg arg, String lang) throws IOException {
        String tenantId = arg.getTenantId();
        ErpFieldTypeEnum dataType = ErpFieldTypeEnum.employee_oa;
        OAEmployeeDataMappingListener listener = new OAEmployeeDataMappingListener(tenantId, arg.getDataCenterId(), ErpChannelEnum.OA, dataType, employeeMappingService,
                idGenerator,i18NStringManager,lang);
        //读取excel
        ReadExcel.Arg<OAEmployeeDataMappingExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(OAEmployeeDataMappingExcelVo.class);

        InputStream inputStream = Objects.isNull(arg.getFileStream()) ? arg.getFile().getInputStream() : arg.getFileStream();
        readExcelArg.setInputStream(inputStream);
        com.alibaba.excel.support.ExcelTypeEnum excelType = Objects.isNull(arg.getFileType())? null : com.alibaba.excel.support.ExcelTypeEnum.valueOf(arg.getFileType().toUpperCase());
        readExcelArg.setExcelType(excelType);

        fileManager.readExcel(readExcelArg);
        ImportExcelFile.Result result = listener.getImportResult();
        log.info("importFieldDataMapping arg:{},result:{}", arg, result);
        return new Result<>(result);
    }

    @Override
    public Result<BuildExcelFile.Result> buildExcelTemplate(String ea, ImportExcelFile.FieldDataMappingArg arg, String lang) {
        switch (arg.getExcelType()) {

            case OA_EMPLOYEE_DATA_MAPPING:
                return buildOAEmployeeDataMappingTemplate(ea,lang);
            default:
                throw new IllegalStateException("Unexpected excelType: " + arg.getExcelType());
        }
    }

    private Result<BuildExcelFile.Result> buildOAEmployeeDataMappingTemplate(String ea, String lang) {
        String tenantId = eieaConverter.enterpriseAccountToId(ea)+"";
        OAEmployeeDataMappingExcelVo tempData = getOAEmployeeDataMappingExcelVoTempData(tenantId, lang);
        BuildExcelFile.Arg<OAEmployeeDataMappingExcelVo> arg = new BuildExcelFile.Arg<>();
        arg.setEa(ea);
        arg.setFileName(i18NStringManager.get(I18NStringEnum.s195,lang,tenantId));
        arg.setDataList(Collections.singletonList(tempData));
        arg.setSheetNames(Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s196,lang,tenantId)));
        return buildExcelFile(arg,lang);
    }

    public OAEmployeeDataMappingExcelVo getOAEmployeeDataMappingExcelVoTempData(String tenantId, String lang) {
        OAEmployeeDataMappingExcelVo vo = new OAEmployeeDataMappingExcelVo();
        vo.setOaEmployeeName(i18NStringManager.get(I18NStringEnum.s3778, lang, tenantId) + "xx");
        vo.setCrmEmployeeId(i18NStringManager.get(I18NStringEnum.s3778, lang, tenantId) + "10001");
        vo.setOaEmployeeId("gf2500");
        return vo;
    }
}
