package com.fxiaoke.open.oasyncdata.controller.oa;


import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.constant.OAAPLTypeEnum;
import com.fxiaoke.open.oasyncdata.db.manager.TenantEnvManager;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA链接参数相关接口")
@RestController("oaConnParamController")
@RequestMapping("cep/oa/connParam")
@Slf4j
public class OAConnParamController extends BaseController {
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private I18NStringManager i18NStringManager;


    @ApiOperation(value = "获取OA连接参数")
    @RequestMapping(value = "/getOAConnectInfo", method = RequestMethod.POST)
    public Result<OAConnectInfoVO> getOAConnectInfo(@RequestBody CepArg arg) {
        String tenantId = getLoginUserTenantId();
        Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId,arg.getCurrentDcId());
        if(oaConnectInfo.isSuccess()&& ObjectUtils.isNotEmpty(oaConnectInfo.getData())){
            OAConnectParam connectParams = oaConnectInfo.getData().getConnectParams();
            List<OAAplApiName> aplApiNames = connectParams.getAplApiNames();
            if(!CollectionUtils.isEmpty(aplApiNames)){
                aplApiNames=aplApiNames.stream().filter(item ->!item.getType().equals(OAAPLTypeEnum.DATADETAIL.getType())).collect(Collectors.toList());
                connectParams.setAplApiNames(aplApiNames);
            }
        }
        return oaConnectInfo;
    }

    @ApiOperation(value = "获取OA连接参数")
    @RequestMapping(value = "/queryDataCenterId", method = RequestMethod.POST)
    public Result<String> queryDataCenterId(@RequestBody CepArg arg) {
        String tenantId = getLoginUserTenantId();
        Result<List<OAConnectInfoVO>> listResult = oaConnParamService.listInfoByTenantId(tenantId);
        if(listResult.isSuccess()&& ObjectUtils.isNotEmpty(listResult.getData())){
          return Result.newSuccess(listResult.getData().get(0).getId());
        }
        return Result.newSuccess();
    }
    @ApiOperation(value = "企业是否是灰度企业")
    @RequestMapping(value = "/getEnv", method = RequestMethod.POST)
    public Result<TenantEnvInfo> getEnv() {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        Pair<String, String> tenantWebEnvironment = tenantEnvManager.getTenantWebEnvironment(tenantId);
        TenantEnvInfo tenantEnvInfo = new TenantEnvInfo();
        tenantEnvInfo.setEnv(tenantWebEnvironment.getLeft());
        tenantEnvInfo.setWebApp(tenantWebEnvironment.getRight());
        return Result.newSuccess(tenantEnvInfo);
    }

    @ApiOperation(value = "初始化OA对接信息")
    @RequestMapping(value = "/initOAConnectInfo", method = RequestMethod.POST)
    public Result<String> initOAConnectInfo(@RequestBody CepArg arg) {
        String tenantId = getLoginUserTenantId();
        String enterpriseName = getEnterpriseName();
        return oaConnParamService.initOAConnectInfo(tenantId, enterpriseName);
    }

    @ApiOperation(value = "更新OA连接参数")
    @RequestMapping(value = "/updateOAConnectInfo", method = RequestMethod.POST)
    public Result<String> updateOAConnectInfo(@RequestBody OAConnectInfoVO oaConnectInfoVO) {
        String tenantId = getLoginUserTenantId();
        //这里修改的只有oaRequest类型的
        Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId,oaConnectInfoVO.getCurrentDcId());
        if (!oaConnectInfo.isSuccess()){
            return Result.newError(oaConnectInfo.getErrMsg());
        }
        if (oaConnectInfo.getData()==null||oaConnectInfo.getData().getConnectParams()==null){
            return Result.newErrorByI18N(I18NStringEnum.s1267.getI18nKey(), I18NStringEnum.s1267.getI18nKey(),null);
        }
        OAConnectParam connectParams = oaConnectInfo.getData().getConnectParams();
        if (CollectionUtils.isEmpty(connectParams.getAplApiNames())){
            connectParams.setAplApiNames(new ArrayList<>());
        }
        List<OAAplApiName> aplApiNames = oaConnectInfo.getData().getConnectParams().getAplApiNames();
        //oa_request类型会替代之前的HEADER  URLSCRIPT
        String aplType=OAAPLTypeEnum.OA_REQUEST.getType();
        String requestAplName=oaConnectInfoVO.getConnectParams().getAplApiName(OAAPLTypeEnum.OA_REQUEST);
        ArrayList<String> otherAplType = Lists.newArrayList(OAAPLTypeEnum.HEADER.getType(), OAAPLTypeEnum.URLSCRIPT.getType(), OAAPLTypeEnum.TODO.getType());
        List<OAAplApiName> aplApiNameList =
                aplApiNames.stream().filter(t -> (!aplType.equals(t.getType())&&!otherAplType.contains(t.getType()))).collect(Collectors.toList());
        //不为空，则修改，空的会被覆盖
        if (!StringUtils.isEmpty(requestAplName)){
            OAAplApiName oaAplApiName=new OAAplApiName();
            oaAplApiName.setApiName(requestAplName);
            oaAplApiName.setType(aplType);
            aplApiNameList.add(oaAplApiName);
        }
        oaConnectInfoVO.getConnectParams().setAplApiNames(aplApiNameList);
        return oaConnParamService.updateOAConnectInfo(tenantId, oaConnectInfoVO);
    }

    @ApiOperation(value = "获取绑定的OA连接器函数")
    @RequestMapping(value = "/getOAApiLFunction", method = RequestMethod.POST)
    public Result<OAAplApiName> getOAApiLFunction(@RequestBody OAAplApiName oaAplApiName) {
        String tenantId = getLoginUserTenantId();
        Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId,oaAplApiName.getCurrentDcId());
        if (!oaConnectInfo.isSuccess()){
            return Result.newError(oaConnectInfo.getErrMsg());
        }

        if (StringUtils.isEmpty(oaAplApiName.getType())&& OAAPLTypeEnum.valueOf(oaAplApiName.getType())==null){
            return Result.newErrorByI18N(I18NStringEnum.s1266.getI18nKey(), I18NStringEnum.s1266.getI18nKey(),null);
        }

        if (oaConnectInfo.getData()==null||oaConnectInfo.getData().getConnectParams()==null){
            return Result.newSuccess();
        }
        OAConnectParam connectParams = oaConnectInfo.getData().getConnectParams();
        if (CollectionUtils.isEmpty(connectParams.getAplApiNames())){
            connectParams.setAplApiNames(new ArrayList<>());
        }

        List<OAAplApiName> aplApiNames = oaConnectInfo.getData().getConnectParams().getAplApiNames();


        Optional<OAAplApiName> first =
                aplApiNames.stream().filter(t -> t.getType().equals(oaAplApiName.getType())).findFirst();
        if (first.isPresent()){
            return Result.newSuccess(first.get());
        }
        return Result.newSuccess();
    }

    @ApiOperation(value = "绑定OA免登连接器函数")
    @RequestMapping(value = "/bindOACustomerFunction", method = RequestMethod.POST)
    public Result<String> bindOACustomerFunction(@RequestBody OAAplApiName oaAplApiName) {
        String tenantId = getLoginUserTenantId();
        Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId,oaAplApiName.getCurrentDcId());
        if (!oaConnectInfo.isSuccess()){
            return Result.newError(oaConnectInfo.getErrMsg());
        }

        if (StringUtils.isEmpty(oaAplApiName.getType())&&OAAPLTypeEnum.valueOf(oaAplApiName.getType())==null){
            return Result.newErrorByI18N(I18NStringEnum.s1266.getI18nKey(), I18NStringEnum.s1266.getI18nKey(),null);
        }

        if (oaConnectInfo.getData()==null||oaConnectInfo.getData().getConnectParams()==null){
            return Result.newErrorByI18N(I18NStringEnum.s1267.getI18nKey(), I18NStringEnum.s1267.getI18nKey(),null);
        }
        OAConnectParam connectParams = oaConnectInfo.getData().getConnectParams();
        if (CollectionUtils.isEmpty(connectParams.getAplApiNames())){
            connectParams.setAplApiNames(new ArrayList<>());
        }

        List<OAAplApiName> aplApiNames = oaConnectInfo.getData().getConnectParams().getAplApiNames();

        List<OAAplApiName> aplApiNameList =
                aplApiNames.stream().filter(t -> !oaAplApiName.getType().equals(t.getType())).collect(Collectors.toList());
        if (!StringUtils.isEmpty(oaAplApiName.getApiName())){
            aplApiNameList.add(oaAplApiName);
        }
        oaConnectInfo.getData().getConnectParams().setAplApiNames(aplApiNameList);
        return oaConnParamService.updateOAConnectInfo(tenantId, oaConnectInfo.getData());
    }

    @ApiOperation(value = "绑定OA连接器函数,会覆盖之前的请求函数")
    @Deprecated
    @RequestMapping(value = "/bindOACommonFunction", method = RequestMethod.POST)
    public Result<String> bindOACommonFunction(@RequestBody OAAplApiName oaAplApiName) {
        String tenantId = getLoginUserTenantId();
        Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId,oaAplApiName.getCurrentDcId());
        if (!oaConnectInfo.isSuccess()){
            return Result.newError(oaConnectInfo.getErrMsg());
        }

        if (StringUtils.isEmpty(oaAplApiName.getType())&&OAAPLTypeEnum.valueOf(oaAplApiName.getType())==null){
            return Result.newErrorByI18N(I18NStringEnum.s1266.getI18nKey(), I18NStringEnum.s1266.getI18nKey(),null);
        }

        if (oaConnectInfo.getData()==null||oaConnectInfo.getData().getConnectParams()==null){
            return Result.newErrorByI18N(I18NStringEnum.s1267.getI18nKey(), I18NStringEnum.s1267.getI18nKey(),null);
        }
        OAConnectParam connectParams = oaConnectInfo.getData().getConnectParams();
        if (CollectionUtils.isEmpty(connectParams.getAplApiNames())){
            connectParams.setAplApiNames(new ArrayList<>());
        }

        List<OAAplApiName> aplApiNames = oaConnectInfo.getData().getConnectParams().getAplApiNames();
        //oa_request类型会替代之前的HEADER  URLSCRIPT
        ArrayList<String> otherAplType = Lists.newArrayList(OAAPLTypeEnum.HEADER.getType(), OAAPLTypeEnum.URLSCRIPT.getType(), OAAPLTypeEnum.TODO.getType());
        List<OAAplApiName> aplApiNameList =
                aplApiNames.stream().filter(t -> (!oaAplApiName.getType().equals(t.getType())&&!otherAplType.contains(t.getType()))).collect(Collectors.toList());
        if (!StringUtils.isEmpty(oaAplApiName.getApiName())){
            aplApiNameList.add(oaAplApiName);
        }
        oaConnectInfo.getData().getConnectParams().setAplApiNames(aplApiNameList);
        return oaConnParamService.updateOAConnectInfo(tenantId, oaConnectInfo.getData());
    }

    @ApiOperation(value = "获取已知的系统信息列表")
    @PostMapping("/listKnownSystemInfos")
    public Result<List<SystemInfo>> listKnownSystemInfos(@RequestBody(required = false) CepArg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        final String s = i18NStringManager.get(I18NStringEnum.OaKnownSystem, lang, getLoginUserTenantId());
        List<SystemInfo> systemInfos = JSON.parseArray(s, SystemInfo.class);

        if (systemInfos == null) {
            systemInfos = ListUtil.empty();
        }
        return new Result<>(systemInfos);
    }
}
