package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.ExcelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public interface AsyncImportExcelData {

    @Data
    class Arg implements Serializable {
        @ApiModelProperty(value = "模板类型")
        @NotNull(message = "模板类型不能为空")
        private ExcelTypeEnum excelType;

        /**
         * @see com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum
         */
        @ApiModelProperty(value = "数据类型,或者对象apiName")
        @NotNull(message = "数据类型不能为空")
        private ErpFieldTypeEnum dataType;

        @ApiModelProperty(value = "文件npath")
        private String npath;

        @ApiModelProperty(value = "文件类型,XLSX/XLS")
        private String fileType = "XLSX";
    }

}
