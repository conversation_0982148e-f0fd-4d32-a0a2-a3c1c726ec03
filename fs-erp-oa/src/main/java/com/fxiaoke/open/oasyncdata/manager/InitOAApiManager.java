package com.fxiaoke.open.oasyncdata.manager;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncApiDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.entity.OASyncApiEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 用户对象业务
 *
 * <AUTHOR>
 * @date 2021/3/19
 */
@Component("initOAApiManager")
@Slf4j
public class InitOAApiManager {
    @Autowired
    private OASyncApiDao oaSyncApiDao;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;
    @Autowired
    private RedisDataSource redisDataSource;
    public static String OA_CONNECT_INFO="oa_connect_info_%s";
    @Autowired
    private I18NStringManager i18NStringManager;


    /**
     * 初始化企业OA对接信息
     *
     * @param tenantId
     * @param enterpriseName
     */
    public Result<String> initApiData(String tenantId, String enterpriseName) {
        log.info("Init ApiData start,tenantId={}", tenantId);
        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOAConnectInfoById(tenantId,null);
        if (oaConnectInfoEntity != null) {
            return Result.newSuccess();
        }
        createConnect(tenantId, enterpriseName, i18NStringManager.getByEi(I18NStringEnum.s414, tenantId));
        return Result.newSuccess();
    }

    public String createConnect(String tenantId, String enterpriseName,String connectName) {
        String redisKey=String.format(OA_CONNECT_INFO,tenantId);
        if(ObjectUtils.isEmpty(redisDataSource.get().get(redisKey))){
            redisDataSource.get().setex(redisKey,20,tenantId);
            OAConnectInfoEntity oaConnectInfoEntity;
            OAConnectParam oaConnectParam = new OAConnectParam();
            oaConnectParam.setResultFormat(new OAConnectParam.ResultFormat());
            oaConnectParam.setHeader(Maps.newHashMap());
            oaConnectParam.setConnectOaName(connectName);
            String oaConnectParamJson = GsonUtil.toJson(oaConnectParam);
            oaConnectInfoEntity =
                    OAConnectInfoEntity.builder().id(idGenerator.get()).tenantId(tenantId).connectParams(oaConnectParamJson).
                            createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis()).enterpriseName(enterpriseName).build();
            oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(oaConnectInfoEntity);
            return oaConnectInfoEntity.getId();
        }
        return null;
    }
}
