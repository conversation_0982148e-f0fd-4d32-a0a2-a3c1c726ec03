package com.fxiaoke.open.oasyncdata.db.config;

import com.jayway.jsonpath.spi.cache.CacheProvider;
import com.jayway.jsonpath.spi.cache.LRUCache;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommonConfig {


    static {
        try {
            //加大JsonPath缓存数量
            CacheProvider.setCache(new LRUCache(4000));
        } catch (Exception ignored) {
        }
        //设置fastjson默认转string时会写null值
        //因为代码里面太多fastJSON的地方，修改feature带来的影响不可控，放弃该方案。
//        JSON.DEFAULT_GENERATE_FEATURE |= WRITE_MAP_NULL_FEATURES;
    }
}
