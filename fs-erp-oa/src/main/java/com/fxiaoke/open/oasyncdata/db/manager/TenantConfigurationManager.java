package com.fxiaoke.open.oasyncdata.db.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.oasyncdata.annotation.LogLevel;
import com.fxiaoke.open.oasyncdata.annotation.LogLevelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.db.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.JacksonUtil;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 对企业配置加一层封装方便缓存
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/2/14
 */
@Component
@Slf4j
public class TenantConfigurationManager {
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private RedisDataSource redisDataSource;

    private static String prefix = "erpdss_";

    private static long cachehitFenmu = 0;
    private static long cachehitFenzi = 0;

    /**
     * 企业白名单缓存
     * key ，value tenantId集合
     */
    private static TimedCache<String, Set<String>> tenantWhiteListCache= CacheUtil.newTimedCache(10000);

    /**findone调用量太大了，缓存到redis
     * 如果修改了数据库中的配置，要1分钟后才能生效
     * */
    @LogLevel(LogLevelEnum.DEBUG)
    public ErpTenantConfigurationEntity findOne(String tenantId, String dataCenterId, String channel, String type) {
        ErpTenantConfigurationEntity result = null;
        String rediskey = prefix+tenantId+dataCenterId+channel+type;
        String redisValue = null;

        try {
            redisValue = redisDataSource.get().get(rediskey);
            if (null != redisValue) {
                cachehitFenzi++;
                cachehitFenmu++;
                result = new Gson().fromJson(redisValue, ErpTenantConfigurationEntity.class);
                log.debug("trace findOne from redis,tenantId:{},dataCenterId:{},channel:{},type:{}, result:{} ", tenantId, dataCenterId, channel, type, result);
                return result;
            }
        } catch (Exception e){
            //redis不可用，继续往下从db获取数据
            log.warn("access redis get exception, ", e);
        }

        /**redis中找不到，请求穿透到db，并存储到redis.  DB 也加上几秒的缓存，
         * 防止redis不可用时， 访问db的流量瞬间暴涨到冲垮db. **/
        result = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findOne(tenantId,dataCenterId,channel,type);
        if(null == result) {
            return result;
        }

        redisValue = new Gson().toJson(result);
        redisDataSource.get().setex(rediskey, 60L, redisValue);
        log.debug("trace findOne from db,tenantId:{},dataCenterId:{},channel:{},type:{}, result:{} ", tenantId,dataCenterId,channel,type, result);

        cachehitFenmu++;
        if(cachehitFenzi + cachehitFenzi  < cachehitFenmu) {
            /**redis cache的命中率小于 50% 太低了， 大部分请求都穿透到数据库了。**/
            log.warn("redis cache hit too low, cachehitFenmu:{}, cachehitFenzi:{}", cachehitFenmu, cachehitFenzi);
        }
        return result;
    }

    public int insert(String tenantId, ErpTenantConfigurationEntity entity) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(entity);
    }

    public int updateById(String tenantId, ErpTenantConfigurationEntity entity) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(entity);
    }

    public List<ErpTenantConfigurationEntity> queryList(String tenantId, ErpTenantConfigurationEntity entity) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(entity);
    }

    public int deleteById(String tenantId,String id) {
        return erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(id);
    }




    @Cached(expire = 120, cacheType = CacheType.LOCAL)
    @LogLevel(LogLevelEnum.DEBUG)
    public Set<String> getNorMalEnvs() {
        //配置
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("all")).findOne("all", "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.OA_NOR_ENV_TENANT.name());
        Set<String> set = new HashSet<>();
        if (config!=null&& StringUtils.isNotBlank(config.getConfiguration())) {
            set = ImmutableSet.copyOf(Splitter.on(";").split(config.getConfiguration()));
        }
        return set;
    }

    @Cached(expire = 120, cacheType = CacheType.LOCAL)
    @LogLevel(LogLevelEnum.DEBUG)
    public Set<String> getGrayEnvs() {
        //配置
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("all")).findOne("all", "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.OA_GRAY_ENV_TENANT.name());
        Set<String> set = new HashSet<>();
        if (config!=null&& StringUtils.isNotBlank(config.getConfiguration())) {
            set = ImmutableSet.copyOf(Splitter.on(";").split(config.getConfiguration()));
        }
        return set;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public Map<String,List<String>> getConfigRouteTenant() {
        //配置
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("all")).findOne("all", "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name());
        if (config==null|| StringUtils.isBlank(config.getConfiguration())) {
            return new HashMap<>();
        }
        Map<String,List<String>> map;
        try {
            map = JacksonUtil.fromJson(config.getConfiguration(), new TypeReference<Map<String,List<String>>>() {
            });
        } catch (ErpSyncDataException e) {
            log.error("parse map config error,config:{}", config);
            map = new HashMap<>();
        }
        return map;
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public Boolean updateConfigRouteTenant(Map<String,List<String>> value) {
        //配置
        ErpTenantConfigurationEntity config = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("all")).findOne("all", "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name());
        config.setConfiguration(JacksonUtil.toJson(value));
        config.setUpdateTime(System.currentTimeMillis());
        int update=erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("all")).updateById(config);
        return update==1;
    }

}
