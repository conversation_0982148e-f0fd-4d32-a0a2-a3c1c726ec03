package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.oasyncdata.constant.OAAPLTypeEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.OAAplApiName;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class OAConnectInfoManager {
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    public void updateTodoAplApiName(String tenantId,String aplApiName,String dataCenterId) {
        OAConnectInfoEntity entity = oaConnectInfoDao
                .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getOAConnectInfoById(tenantId,dataCenterId);
        OAConnectParam connectParam = GsonUtil.fromJson(entity.getConnectParams(), OAConnectParam.class);
        List<OAAplApiName> list = connectParam.getAplApiNames();
        if(list==null)
            list = new ArrayList<>();

        OAAplApiName todoAplApiName = new OAAplApiName();
        todoAplApiName.setApiName(aplApiName);
        todoAplApiName.setType(OAAPLTypeEnum.TODO.getType());

        OAAplApiName oaAplApiName = connectParam.getOAAplApiName(OAAPLTypeEnum.TODO);
        if(oaAplApiName==null) {
            list.add(todoAplApiName);
        } else {
            oaAplApiName.setApiName(aplApiName);
        }

        connectParam.setAplApiNames(list);

        entity.setConnectParams(JSONObject.toJSONString(connectParam));
        int count = oaConnectInfoDao
                .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .updateById(entity);
        log.info("OAConnectInfoManager.updateTodoAplApiName,count={}",count);
    }
}
