package com.fxiaoke.open.oasyncdata.db.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.common.data.OrderBy;
import com.fxiaoke.open.oasyncdata.db.model.K3SourceBillInfo;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.google.gson.reflect.TypeToken;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
public class ConfigCenter {
    /**
     * 每次轮询拉取erp数据最大数量
     */
    public static Integer ERP_LIST_MAX_NUM = 10000;

    /**
     * 数据同步执行超时时间,单位：分钟,默认10分钟
     */
    public static Integer END_UPDATE_TIME_OUT_MIN = 10;

    /**
     * 超级管理员
     */
    public static Set<String> SUPER_ADMINS = new HashSet<>();

    /**
     * 查询CRM明细排序对象规则
     */
    public static Map<String, List<OrderBy>> CRM_OBJ_QUERY_ORDERS = Collections.emptyMap();

    /**
     * key:formId value：pair<源单类型字段，源单编号字段>
     */
    public static Map<String, List<K3SourceBillInfo>> K3_SOURCE_BILL_INFO_MAP;

    /**
     * 手动执行策略url    manualExecutePloys
     */
    public static String MANUAL_EXECUTE_PLOYS_URL;
    /**
     * 单次发送mq最大数量
     */
    public static Integer SINGLE_SEND_MQ_SIZE = 20;
    /**
     * erp数据同步appId
     */
    public static String ERP_SYNC_DATA_APP_ID = "FSAID_9897f5";
    /**
     * 消息体最长数量
     */
    public static Integer NOTICE_MAX_SIZE = 500;
    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String PREVIEW_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/GetByPath?path=%s";
    /**
     * 文件预览路径，%s为带ext的npath
     */
    public static String DOWNLOAD_FILE_PATH = "https://www.ceshi112.com/FSC/EM/File/DownloadByPath?Path=%s&name=%s";
    /**
     * 导入接口超时时间
     */
    public static Long IMPORT_TIME_OUT = 2000L;
    /**
     * 导入接口超时时间
     */
    public static Long EXPORT_TIME_OUT = 5000L;
    /**
     * 导出数据维护文件包含的条数
     */
    public static Integer EXPORT_DATA_NUMBER = 50000;
    /**
     * 导出数据维护查询映射表的数量
     */
    public static Integer EXPORT_DATA_QUERY_NUMBER = 100;
    /**
     * 用来编辑默认K3描述的企业id
     */
    public static String EDIT_DEFAULT_K3_OBJ_TENANT_ID = "01";
    /**
     * 用来储存K3预设描述的企业id
     */
    public static String STORE_DEFAULT_K3_OBJ_TENANT_ID = "01";
    /**
     * inner标准接口调用的url前缀
     */
    public static String CRM_OBJECT_INNER_URL_PREFIX;
    /**
     *  元数据接口url前缀
     */
    public static String CRM_METADATA_URL_PREFIX;

    /**
     * OA不需要鉴权的企业
     */
    public static Set<String> SSO_ENTERPRISE_EI = Sets.newHashSet();

    /**
     * 写erp的tps限制
     */
    public static Integer WRITE_ERP_TPS_LIMIT = 3;

    /**
     * 慢队列保持时长
     */
    public static Integer SLOW_QUEUE_KEEP_SECOND = 30 * 60;

    /**
     * 创建产品分类等待时间
     */
    public static Long CREATE_CATEGORY_SLEEP_MILLI = 500L;

    /**
     * 编码ID对象表
     */
    public static Map<String, String> APINAME_FID_FNUMBER_MAPPING = new HashMap<>();

    /**
     * 是否灰度环境
     */
    public static boolean isGrayGroup = false;

    /**
     * 环境key
     */
    public static String ServiceEnvironment ;


    /**
     * U8需要审核的单据apiName
     */
    public static Set<String> U8_VERIFY_APINAME_SET;

    /**
     * U8的masterId的格式
     */
    public static Map<String, String> U8_MASTERID_STRUCTURE_MAP = new HashMap<>();

    /**
     * 一次轮询到的ERP数据的最大值，超过这个值，就会丢失本次轮询的结果，并记录到erp_polling_monitor表中
     */
    public static int POLLING_DATA_SIZE_THRESHOLD = 100;

    /**
     * 不需要过滤的字段
     */
    public static Set<String> NOT_NEED_FILTER_FIELD = Sets.newHashSet();

    /**
     * 需要转换成null的K3DataId
     */
    public static Set<String> TO_NULL_K3_DATA_ID = new HashSet<>();
    /**
     * 获取客户端和服务器建立连接的timeout
     */
    public static Integer CONNECT_TIME_OUT = 20000;

    /**
     * OA重试时间范围,默认3天
     */
    public static Long OA_RE_TIME = 7200000L;


    /**
     * 定时清理数据的范围,默认三周
     */
    public static Long CLEAN_SYNC_TIME_RANGE = 1814400000L;

    /**
     * 暂停轮询的企业
     */
    public static Set<String> SUSPENDED_POOLING_TENANTS = new HashSet<>();

    /**
     * erpTempData的mongo数据过期时间 单位：小时
     */
    public static Long ERP_TEMP_DATA_MONGO_EXPIRE_TIME = 12 + 24 * 30L;

    /**
     * InterfaceMonitorData的mongo数据过期时间 单位：小时
     */
    public static Long INTERFACE_MONITOR_DATA_MONGO_EXPIRE_TIME = 24 * 10L;

    /**
     * view接口的Content-Length大小限制
     */
    public static Long CONTENT_LENGTH_LIMIT = 1024 * 1024 * 300L;

    /**
     * 统计数据条数维度（秒）
     */
    public static Integer COUNT_DATA_PER_SECOND = 60;

    /**
     * 轮询erp正常数据,轮询erp历史数据共用
     * 统计数据条数维度（秒）
     */
    public static Integer COUNT_QUERY_ERP_DATA_DIMENSION = 60;

    /**
     * 轮询erp正常数据
     * 在一个统计维度内，条数限制(默认)
     */
    public static Integer DEFAULT_QUERY_ERP_DATA_LIMIT = 500;

    /**
     * 轮询erp正常数据
     * 特殊企业的条数限制
     */
    public static Map<String, Integer> TENANT_QUERY_ERP_DATA_LIMIT = Maps.newHashMap();

    /**
     * 轮询erp正常数据
     * 在一个统计维度内，条数限制(默认)
     */
    public static Integer DEFAULT_QUERY_ERP_HISTORY_DATA_LIMIT = 500;

    /**
     * 轮询erp正常数据
     * 特殊企业的条数限制
     */
    public static Map<String, Integer> TENANT_QUERY_ERP_HISTORY_DATA_LIMIT = Maps.newHashMap();

    /**
     * 把k3cloud的即时库存对接到CRM自定义库存对象的EI集合
     */
    public static List<String> K3CLOUD_STOCK_2_CRM_CUSTOM_OBJECT_EI_LIST = new ArrayList<>();

    /**
     * 提醒时间间隔(分钟)
     */
    public static Integer NOTIFICATION_TIME_INTERVAL = 3;

    /**
     * 接口日志是否写mongo库
     */
    public static Boolean INTERFACE_MONITOR_WRITE_MONGO = true;

    /**
     * 不保存日志监控的企业
     */
    public static Set<String> NOT_SAVE_INTERFACE_MONITOR_TENANTS = ImmutableSet.of();
    /**
     * 仅报错错误日志监控的企业
     */
    public static Set<String> ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS = ImmutableSet.of();

    /**
     * 超过16M过滤的对象名单
     */
    public static Set<String> OVER_OBJ_NAMES = Sets.newHashSet();

    /**
     * 历史数据任务同步中超时时间(毫秒)
     */
    public static Integer HISTORY_DATA_TASK_OVERTIME = 30 * 60 * 1000;


    /**
     * erp->crm同步数据由于夜间调高了同步速度，白天需要恢复默认同步速度的企业ei，
     * 这个ei不能放在gray环境，因为task不读取gray的配置文件
     */
    public static String RESET_SYNC_SPEED_2_CRM_TENANTID = "";

    /**
     * 灰度OA走MQ
     */
    public static Set<String> OA_MQ_GRAY_TENANT_ID = Sets.newHashSet();
    /**
     * 触发熔断的错误syncData数量
     */
    public static Integer BREAK_FAILED_SYNC_DATA_NUM = 1000;

    /**
     * 为方便112测试多企业场景，支持配置线程池核心线程数
     */
    public static Integer THREAD_POOL_CORE_SIZE = 10;

    /**
     * SAML协议idp唯一标识
     */
    public static String IDP_ENTITY_ID="https://www.ceshi112.com/erp/syncdata/open/saml/metadata";

    /**
     * 锐捷免登
     */
    public static String RUI_JIE_SSO="https://erpdss.my.ceshi112.com/saml2/sp/sso/login";

    /**
     * 配置在纷享的SP唯一标识
     */
    public static String SP_ENTITY_ID="spn:434e38be64b7f51a674bce6dcad2422e";
    /**
     * 域名管理
     */
    public static String ERP_DOMAIN_URL="http://www.ceshi112.com";
    /**
     * 超时任务使用开始时间
     */
    public static Boolean SYNC_DATA_TIME_OUT_START_TIME = true;


    /**
     * 执行历史数据同步，企业待分发数量阈值
     */
    public static Long TENANT_DISPATCHER_SIZE = 3000L;

    /**
     * 是否执行精简数据逻辑
     */
    public static Boolean IS_CUT_DOWN_FIELD = true;

    /**
     * socket time out 重试企业
     */
    public static Set<String> NEED_DO_SOCKET_TIME_OUT_TENANT =Sets.newHashSet();

    /**
     * CSM客户对象标识
     */
    public static String CSM_CUSTOMER_API_NAME="object_3UIfd__c";
    /**
     * 实施项目（新）对象标识
     */
    public static String IMPLEMENT_PROJECT_API_NAME="object_R7poC__c";

    /**
     * FS企业账号
     */
    public static String FS_ENTERPRISE_ID="78052";

    /**
     * CSM客户企业账号字段apiname
     */
    public static String CS_CUSTOMER_ENTERPRISE_API_NAME="field_1O0qO__c";

    /**
     * 实施项目(新)企业账号字段apiname
     */
    public static String IMPLEMENT_PROJECT_ENTERPRISE_ID="field_WOqvv__c";

    /**
     * 推送数据触发通知的数量
     */
    public static Integer PUSH_DATA_SIZE_NOTIFY = 1000;

    /**
     * 通过BillQuery接口查某条，查询条数限制
     */
    public static int BILL_QUERY_SIZE_LIMIT = 50000;

    /**
     * 不需要重试依赖数据企业
     */
    public static Set<String> NO_NEED_DO_RE_SYNC_DATA_TENANT =Sets.newHashSet();

    /**
     * 重试依赖数据天数（重试超过时间就删除掉）
     */
    public static Integer DO_RE_SYNC_DATA_DAY = 2;

    /**
     * 初始化映射，每limit条睡眠时间
     */
    public static Long INIT_MAPPING_SLEEP_MS_PER_LIMIT = 1000 * 3L;

    /**
     * fs的客户对象的企业Id字段
     */
    public static String FS_ACCOUNT_TENANT_ID="UDInt1__c";

    /**
     * 分配路由的url前缀
     */
    public static String DB_POR_URL;

    /**
     * 不开启专表企业
     */
    public static Set<String> DISABLE_DYNAMIC_TENANTS;
    /**
     * 是否消费沙盒事件
     * 注意将erpsync_sandbox_consumer配置为特殊的consumer，不然会让正式环境的mq不处理
     */
    public static boolean ENABLE_CONSUME_SANDBOX_EVENT = true;

    /**
     * dispatcher的mongo中，按照id拆分topic的企业列表
     */
    public static Set<String> DISPATCHER_MONGO_SPLIT_BYID_TENANTS = Sets.newHashSet();

    /**
     * 后端all与web对应关系
     */
    public static Map<String, String> ALL_ENV_TO_WEB_ENV = Maps.newHashMap();
    /**
     * 后端web与前端应用对应关系
     */
    public static Map<String, String> WEB_ENV_TO_WEB_APP_ENV = Maps.newHashMap();
    /**
     * 不需要定时任务同步的数据企业
     */
    public static List<String> NOT_RE_SYNC_TENANT = Lists.newArrayList();
    /**
     * 只有 处理失败的数据才会触发自动重试，这里配置对应的ei
     */
    public static List<String> OA_ONLY_RETRY_DEAL_FAILED_EI_LIST = Lists.newArrayList();
    /**
     * 不需要定时任务同步的数据企业
     */
    public static Set<String> FILTER_LOG_TENANT_ID = Sets.newHashSet();
    /**
     * 错误的信息跳转页面
     */
    public static String ERROR_REDIRECT_URL = "redirect:http://www.ceshi112.com/erp/syncdata/open/oa/redirect.jsp?error=%s&traceId=%s";
    public static String OA_ERROR_MSG="";
    public static String OA_BG_URL="";
    public static String OA_AVAH5_OBJECT_REDIRECT_URL="/hcrm/avah5#/crm/detail";
    public static String OA_AVAH5_PAGE_REDIRECT_URL="/hcrm/avah5";
    public static String OA_DINGTALK_PAGE_REDIRECT_URL="/hcrm/dingtalk";
    //跳转到avah5版页面的地址。这个需要crm版本是新版本。默认是跳转到dingtalk版的H5端
    public static Set<String> OA_REDIRECT_AVAH5_TENANT=Sets.newHashSet();
    /**
     * cdn base url地址
     */
    public static String CDN_BASE_URL = "https://a9.fspage.com/FSR/weex/erpdss";

    static {
        ConfigFactory.getInstance().getConfig("erp-sync-data-all", config -> {
            ENABLE_CONSUME_SANDBOX_EVENT = config.getBool("ENABLE_CONSUME_SANDBOX_EVENT",ENABLE_CONSUME_SANDBOX_EVENT);
            ALL_ENV_TO_WEB_ENV = JSON.parseObject(config.get("ALL_ENV_TO_WEB_ENV", "{\"yqsl\":\"vip\",\"haoliyou\":\"vip\",\"yinlu\":\"vip\",\"svip\":\"vip\",\"vip\":\"vip\",\"stage\":\"stage\",\"urgent\":\"urgent\",\"gray\":\"gray\",\"normal\":\"normal\"}"), Map.class);
            WEB_ENV_TO_WEB_APP_ENV = JSON.parseObject(config.get("WEB_ENV_TO_WEB_APP_ENV", "{\"vip\":\"vip\",\"stage\":\"normal\",\"urgent\":\"urgent\",\"gray\":\"gray\",\"normal\":\"normal\"}"), Map.class);
            DISABLE_DYNAMIC_TENANTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("DISABLE_DYNAMIC_TENANTS", "")));
            DB_POR_URL = config.get("DB_POR_URL", "");
            INIT_MAPPING_SLEEP_MS_PER_LIMIT = config.getLong("INIT_MAPPING_SLEEP_MS_PER_LIMIT", INIT_MAPPING_SLEEP_MS_PER_LIMIT);
            DO_RE_SYNC_DATA_DAY = config.getInt("PUSH_DATA_SIZE_NOTIFY", DO_RE_SYNC_DATA_DAY);
            NO_NEED_DO_RE_SYNC_DATA_TENANT = ImmutableSet.copyOf(Splitter.on(",").split(config.get("NO_NEED_DO_RE_SYNC_DATA_TENANT", "")));
            BILL_QUERY_SIZE_LIMIT = config.getInt("BILL_QUERY_SIZE_LIMIT", BILL_QUERY_SIZE_LIMIT);
            PUSH_DATA_SIZE_NOTIFY = config.getInt("PUSH_DATA_SIZE_NOTIFY", PUSH_DATA_SIZE_NOTIFY);
            NEED_DO_SOCKET_TIME_OUT_TENANT = ImmutableSet.copyOf(Splitter.on(",").split(config.get("NEED_DO_SOCKET_TIME_OUT_TENANT", "")));
            SYNC_DATA_TIME_OUT_START_TIME = config.getBool("SYNC_DATA_TIME_OUT_START_TIME", SYNC_DATA_TIME_OUT_START_TIME);
            IS_CUT_DOWN_FIELD = config.getBool("IS_CUT_DOWN_FIELD", IS_CUT_DOWN_FIELD);
            TENANT_DISPATCHER_SIZE = config.getLong("TENANT_DISPATCHER_SIZE", TENANT_DISPATCHER_SIZE);
            INTERFACE_MONITOR_WRITE_MONGO = config.getBool("INTERFACE_MONITOR_WRITE_MONGO", INTERFACE_MONITOR_WRITE_MONGO);
            NOT_SAVE_INTERFACE_MONITOR_TENANTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOT_SAVE_INTERFACE_MONITOR_TENANTS", "")));
            ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("ONLY_SAVE_ERROR_INTERFACE_MONITOR_TENANTS", "")));
            DISPATCHER_MONGO_SPLIT_BYID_TENANTS = ImmutableSet.copyOf(Splitter.on(",").split(config.get("DISPATCHER_MONGO_SPLIT_BYID_TENANTS", "")));
            NOTIFICATION_TIME_INTERVAL = config.getInt("NOTIFICATION_TIME_INTERVAL", NOTIFICATION_TIME_INTERVAL);
            DEFAULT_QUERY_ERP_HISTORY_DATA_LIMIT = config.getInt("DEFAULT_QUERY_ERP_HISTORY_DATA_LIMIT", DEFAULT_QUERY_ERP_HISTORY_DATA_LIMIT);
            TENANT_QUERY_ERP_HISTORY_DATA_LIMIT = JSON.parseObject(config.get("TENANT_QUERY_ERP_HISTORY_DATA_LIMIT", "{}"), Map.class);
            COUNT_QUERY_ERP_DATA_DIMENSION = config.getInt("COUNT_QUERY_ERP_DATA_DIMENSION", COUNT_QUERY_ERP_DATA_DIMENSION);
            DEFAULT_QUERY_ERP_DATA_LIMIT = config.getInt("DEFAULT_QUERY_ERP_DATA_LIMIT", DEFAULT_QUERY_ERP_DATA_LIMIT);
            TENANT_QUERY_ERP_DATA_LIMIT = JSON.parseObject(config.get("TENANT_QUERY_ERP_DATA_LIMIT", "{}"), Map.class);
            COUNT_DATA_PER_SECOND = config.getInt("COUNT_DATA_PER_SECOND", COUNT_DATA_PER_SECOND);
            CONTENT_LENGTH_LIMIT = config.getLong("CONTENT_LENGTH_LIMIT", CONTENT_LENGTH_LIMIT);
            INTERFACE_MONITOR_DATA_MONGO_EXPIRE_TIME = config.getLong("INTERFACE_MONITOR_DATA_MONGO_EXPIRE_TIME", INTERFACE_MONITOR_DATA_MONGO_EXPIRE_TIME);
            ERP_TEMP_DATA_MONGO_EXPIRE_TIME = config.getLong("ERP_TEMP_DATA_MONGO_EXPIRE_TIME", ERP_TEMP_DATA_MONGO_EXPIRE_TIME);
            ERP_LIST_MAX_NUM = config.getInt("ERP_LIST_MAX_NUM", ERP_LIST_MAX_NUM);
            END_UPDATE_TIME_OUT_MIN = config.getInt("END_UPDATE_TIME_OUT_MIN", END_UPDATE_TIME_OUT_MIN);
            SUPER_ADMINS = ImmutableSet.copyOf(Splitter.on(";").split(config.get("SUPER_ADMINS", "")));
            List<K3SourceBillInfo> k3SourceBillInfo = JacksonUtil.fromJson(config.get("K3_SOURCE_BILL_INFO", "[]"),
                    new TypeReference<List<K3SourceBillInfo>>() {
                    });
            K3_SOURCE_BILL_INFO_MAP = k3SourceBillInfo.stream().collect(Collectors.groupingBy(K3SourceBillInfo::getFormId));
            CRM_OBJ_QUERY_ORDERS = GsonUtil.fromJson(config.get("CRM_OBJ_QUERY_ORDERS", "{}"), new TypeToken<HashMap<String, List<OrderBy>>>() {
            }.getType());
            MANUAL_EXECUTE_PLOYS_URL = config.get("dss.task.manual.trigger.url");
            SINGLE_SEND_MQ_SIZE = config.getInt("SINGLE_SEND_MQ_SIZE", SINGLE_SEND_MQ_SIZE);
            ERP_SYNC_DATA_APP_ID = config.get("ERP_SYNC_DATA_APP_ID", ERP_SYNC_DATA_APP_ID);
            NOTICE_MAX_SIZE = config.getInt("NOTICE_MAX_SIZE", NOTICE_MAX_SIZE);
            PREVIEW_FILE_PATH = config.get("PREVIEW_FILE_PATH", PREVIEW_FILE_PATH);
            DOWNLOAD_FILE_PATH = config.get("DOWNLOAD_FILE_PATH", DOWNLOAD_FILE_PATH);
            IMPORT_TIME_OUT = config.getLong("IMPORT_TIME_OUT", IMPORT_TIME_OUT);
            EXPORT_TIME_OUT = config.getLong("EXPORT_TIME_OUT", EXPORT_TIME_OUT);
            OA_RE_TIME = config.getLong("OA_RE_TIME", OA_RE_TIME);
            EDIT_DEFAULT_K3_OBJ_TENANT_ID = config.get("EDIT_DEFAULT_K3_OBJ_TENANT_ID", EDIT_DEFAULT_K3_OBJ_TENANT_ID);
            STORE_DEFAULT_K3_OBJ_TENANT_ID = config.get("STORE_DEFAULT_K3_OBJ_TENANT_ID", STORE_DEFAULT_K3_OBJ_TENANT_ID);
            CRM_OBJECT_INNER_URL_PREFIX = config.get("crm.object.inner.url.prefix", "");
            CRM_METADATA_URL_PREFIX = config.get("crm.metadata.url.prefix", "");
            String mappingField = config.get("APINAME_FID_FNUMBER_MAPPING", "{}");
            APINAME_FID_FNUMBER_MAPPING = JacksonUtil.fromJson(mappingField, Map.class);
            WRITE_ERP_TPS_LIMIT = config.getInt("WRITE_ERP_TPS_LIMIT", WRITE_ERP_TPS_LIMIT);
            EXPORT_DATA_NUMBER = config.getInt("EXPORT_DATA_NUMBER", EXPORT_DATA_NUMBER);
            EXPORT_DATA_QUERY_NUMBER = config.getInt("EXPORT_DATA_QUERY_NUMBER", EXPORT_DATA_QUERY_NUMBER);
            SLOW_QUEUE_KEEP_SECOND = config.getInt("SLOW_QUEUE_KEEP_SECOND", SLOW_QUEUE_KEEP_SECOND);
            ServiceEnvironment = config.get("ServiceEnvironment", "normal");
            isGrayGroup = !config.get("erpSyncServiceGroup", "normal").equals("normal");
            U8_VERIFY_APINAME_SET = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("U8_VERIFY_APINAME_SET", ""))
            );
            String u8MasteridStructureMap = config.get("U8_MASTERID_STRUCTURE_MAP", "{}");
            U8_MASTERID_STRUCTURE_MAP = JacksonUtil.fromJson(u8MasteridStructureMap, Map.class);
            POLLING_DATA_SIZE_THRESHOLD = config.getInt("POLLING_DATA_SIZE_THRESHOLD", POLLING_DATA_SIZE_THRESHOLD);
            SSO_ENTERPRISE_EI = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("SSO_ENTERPRISE_EI", "40010021")));
            FILTER_LOG_TENANT_ID = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("FILTER_LOG_TENANT_ID", "")));
            NOT_NEED_FILTER_FIELD = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("NOT_NEED_FILTER_FIELD", "ID")));
            TO_NULL_K3_DATA_ID = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("TO_NULL_K3_DATA_ID", "0;-;--; ;")));
            SUSPENDED_POOLING_TENANTS = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("SUSPENDED_POOLING_TENANTS", "")));
            OA_REDIRECT_AVAH5_TENANT = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_REDIRECT_AVAH5_TENANT", "")));
            CLEAN_SYNC_TIME_RANGE = config.getLong("CLEAN_SYNC_TIME_RANGE", CLEAN_SYNC_TIME_RANGE);
            K3CLOUD_STOCK_2_CRM_CUSTOM_OBJECT_EI_LIST = ImmutableList.copyOf(
                    Splitter.on(";").split(config.get("K3CLOUD_STOCK_2_CRM_CUSTOM_OBJECT_EI_LIST", "")));
            OVER_OBJ_NAMES = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OVER_OBJ_NAMES", "")));
            RESET_SYNC_SPEED_2_CRM_TENANTID = config.get("RESET_SYNC_SPEED_2_CRM_TENANTID");
            OA_MQ_GRAY_TENANT_ID = ImmutableSet.copyOf(
                    Splitter.on(";").split(config.get("OA_MQ_GRAY_TENANT_ID", "")));
            THREAD_POOL_CORE_SIZE = config.getInt("THREAD_POOL_CORE_SIZE", THREAD_POOL_CORE_SIZE);
            BREAK_FAILED_SYNC_DATA_NUM = config.getInt("BREAK_FAILED_SYNC_DATA_NUM", BREAK_FAILED_SYNC_DATA_NUM);
            IDP_ENTITY_ID = config.get("IDP_ENTITY_ID", IDP_ENTITY_ID);
            RUI_JIE_SSO = config.get("RUI_JIE_SSO", RUI_JIE_SSO);
            SP_ENTITY_ID = config.get("SP_ENTITY_ID", SP_ENTITY_ID);
            ERP_DOMAIN_URL = config.get("ERP_DOMAIN_URL", ERP_DOMAIN_URL);
            CSM_CUSTOMER_API_NAME = config.get("CSM_CUSTOMER_API_NAME", CSM_CUSTOMER_API_NAME);
            FS_ENTERPRISE_ID = config.get("FS_ENTERPRISE_ID", FS_ENTERPRISE_ID);
            CS_CUSTOMER_ENTERPRISE_API_NAME = config.get("CS_CUSTOMER_ENTERPRISE_API_NAME", CS_CUSTOMER_ENTERPRISE_API_NAME);
            FS_ACCOUNT_TENANT_ID = config.get("FS_ACCOUNT_TENANT_ID", FS_ACCOUNT_TENANT_ID);
            ERROR_REDIRECT_URL = config.get("ERROR_REDIRECT_URL", ERROR_REDIRECT_URL);
            OA_ERROR_MSG = config.get("OA_ERROR_MSG", OA_ERROR_MSG);
            OA_BG_URL = config.get("OA_BG_URL", OA_BG_URL);
            NOT_RE_SYNC_TENANT = ImmutableList.copyOf(
                    Splitter.on(";").split(config.get("NOT_RE_SYNC_TENANT", "")));
            OA_ONLY_RETRY_DEAL_FAILED_EI_LIST = ImmutableList.copyOf(
                    Splitter.on(";").split(config.get("OA_ONLY_RETRY_DEAL_FAILED_EI_LIST", "")));
            CDN_BASE_URL = config.get("CDN_BASE_URL",CDN_BASE_URL);
        });
    }

}
