package com.fxiaoke.open.oasyncdata.db.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 轮询记录缓存，不支持多线程操作的。
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/11/18
 */
@Slf4j
public class PollingRecordBuffer {
    /**
     * 轮询结果记录容量
     * 注：PROBATION_HOURS *60 / 6 < CAPACITY
     */
    public static final int CAPACITY = 50;
    /**
     * 当持续失败当前次数，将强制更新最后修改时间
     */
    public static final int UPDATE_SYNC_TIME_NUM = 5;

    /**
     * 当同步记录缓冲区满了，并且失败数超过该占比，直接触发熔断机制
     */
    public static final int BREAK_FAILED_PERCENT = 70;

    /**
     * 轮询记录
     */
    private final List<PollingRecord> pollingRecords;

    /**
     * 最近持续失败的记录
     */
    private final List<PollingRecord> latestContinueFailedRecords;

    private PollingRecordBuffer(List<PollingRecord> pollingRecords) {
        this.pollingRecords = pollingRecords;
        int i;
        for (i = pollingRecords.size() - 1; i >= 0; i--) {
            if (pollingRecords.get(i).isSuccess()) {
                break;
            }
        }
        if (i == pollingRecords.size() - 1) {
            //最后一个不是错误记录，有问题，可能是存在并发情况
            log.warn("latest record is not failed,{}", pollingRecords);
        }
        latestContinueFailedRecords = pollingRecords.subList(i + 1, pollingRecords.size());
    }

    public static PollingRecordBuffer of(List<PollingRecord> pollingRecords) {
        return new PollingRecordBuffer(pollingRecords);
    }

    public boolean isFull() {
        return pollingRecords.size() >= CAPACITY;
    }

    /**
     * @return true 全失败
     */
    public boolean allFailed() {
        return isFull() && pollingRecords.stream().noneMatch(PollingRecord::isSuccess);
    }

    /**
     * @return true 失败占比过高导致熔断
     */
    public boolean breakByFailedPercent() {
        return isFull() && pollingRecords.stream().filter(v -> !v.isSuccess()).count() > CAPACITY * BREAK_FAILED_PERCENT / 100;
    }


    /**
     * 第n次失败之后更新时间
     *
     * @return
     */
    public boolean gteNTimesContinueFailed() {
        return latestContinueFailedRecords.size() >= UPDATE_SYNC_TIME_NUM;
    }


    @Getter
    @Setter
    @AllArgsConstructor(staticName = "of")
    public static class PollingRecord implements Serializable {
        private static final long serialVersionUID = -558795340468708636L;
        private final boolean success;
        private final long time;

        public String encode() {
            return success + ":" + time;
        }

        public static PollingRecord decode(String code) {
            String[] split = code.split(":");
            return PollingRecord.of(Boolean.parseBoolean(split[0]), Long.parseLong(split[1]));
        }
    }
}
