package com.fxiaoke.open.oasyncdata.db.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.lang.reflect.Method;
import java.util.concurrent.Executor;

/**
 * spring异步使用的线程池EnableAsync
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/6/4
 */
@Configuration
@EnableAsync
@Slf4j
public class ExecutorConfig implements AsyncConfigurer {

    @Bean
    public Executor timePointExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(3);
        executor.setQueueCapacity(5000);
        executor.setThreadNamePrefix("erpTimePointAsync-");
        executor.initialize();
        executor.setBeanName("timePointExecutor");
        executor.setRejectedExecutionHandler((r,e)-> log.info("timePointExecutor task discard"));
        return executor;
    }

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程
        executor.setCorePoolSize(5);
        //最大线程数
        executor.setMaxPoolSize(30);
        //队列长度
        executor.setQueueCapacity(5000);
        executor.setThreadNamePrefix("erpAsyncExetor-");
        executor.initialize();
        return executor;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }

    public static class SimpleAsyncUncaughtExceptionHandler2 extends SimpleAsyncUncaughtExceptionHandler{
        @Override
        public void handleUncaughtException(Throwable ex, Method method, Object... params) {
            log.error("Unexpected error occurred invoking async method:{},param:{}" + method,params, ex);
        }
    }
}