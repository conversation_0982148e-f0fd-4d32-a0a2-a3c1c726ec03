package com.fxiaoke.open.oasyncdata.interceptor;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023-10-12
 * 国际化专用AOP
 */
@Slf4j
@Aspect
@Order(1)
@Component
public class ControllerI18NAspect {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Around("execution(* com.fxiaoke.open.oasyncdata.controller.oa..*.*(..)) ")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();

        //获取前端当前语言，只能使用这个key，不能从cookie里面获取，因为这个request的已经被CEP改变
        String lang = request.getHeader("x-fs-locale");
        String tenantId = request.getHeader("x-fs-ei");

        Object result = joinPoint.proceed();
        log.info("ControllerI18NAspect.around,result={}", result);
        if (result != null) {
            Result result2 = null;
            if(result instanceof Result) {
                result2 = (Result) result;
            }
            log.info("ControllerI18NAspect.around,result2={}", result);
            if(result2!=null) {
                if(StringUtils.equalsIgnoreCase(result2.getErrCode(), ResultCodeEnum.SUCCESS.getErrCode())) {
                    if(!StringUtils.equalsIgnoreCase(result2.getErrMsg(),ResultCodeEnum.SUCCESS.getErrMsg())) {
                        log.info("ControllerI18NAspect.around,result2,success error msg changed", result);
                        return result;
                    }
                }
                if(StringUtils.isNotEmpty(result2.getI18nKey())) {
                    result2.setErrMsg(i18NStringManager.get2(result2.getI18nKey(), lang,tenantId, result2.getErrMsg(),result2.getI18nExtra()));
                }
                log.info("ControllerI18NAspect.around,end,result2={}", result2);
            }
        }
        log.info("ControllerI18NAspect.around,end,result={}", result);
        return result;
    }
}
