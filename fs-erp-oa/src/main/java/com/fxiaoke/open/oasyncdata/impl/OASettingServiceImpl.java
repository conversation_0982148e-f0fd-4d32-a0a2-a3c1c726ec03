package com.fxiaoke.open.oasyncdata.impl;



import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.mongo.OASettingsDao;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;

import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.model.OASettingVO;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:OA的相关配置信息，不想多创建pg表
 */
@Service
@Slf4j
@Data
public class OASettingServiceImpl implements OASettingService {
    @Autowired
    private OASettingsDao oaSettingsDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;




    @Override
    public Result<OASettingVO> getSettingInfo(String tenantId, OATenantEnum  type,String dataCenterId) {
        QueryOASettingArg queryOASettingArg=new QueryOASettingArg();
        queryOASettingArg.setTenantId(tenantId);
        queryOASettingArg.setCurrentDcId(dataCenterId);
        if(type!=null){
            queryOASettingArg.setType(type.name());
        }
        OASettingDoc configByType = oaSettingsDao.getConfigByType(tenantId, queryOASettingArg);
        if(ObjectUtils.isNotEmpty(configByType)){
            OASettingVO oaSettingVO= BeanUtil.copy(configByType,OASettingVO.class);
            return Result.newSuccess(oaSettingVO);
        }
        return Result.newSuccess();
    }

    public Map<String, String> listTenantNamesByIds( List<String> tenantIds) {

            Map<String, String> resultMap = new HashMap<>();
            try {
                BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
                arg.setEnterpriseIds(tenantIds.stream().map(v -> Integer.valueOf(v)).collect(Collectors.toList()));
                BatchGetSimpleEnterpriseDataResult result = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
                if (result != null && result.getSimpleEnterpriseList() != null) {
                    resultMap = result.getSimpleEnterpriseList().stream().collect(Collectors.toMap(v -> String.valueOf(v.getEnterpriseId())
                            , u -> u.getEnterpriseName(), (i, o) -> i));
                }
            } catch (Exception e) {
                log.info("get enterprise info error", e);
            }
            return resultMap;
    }

    @Override
    public Result<List<OASettingVO>> listSettingInfo(String tenantId, OATenantEnum oaTenantEnum, String dataCenterId) {
        List<String> tenantIds = Lists.newArrayList();
        QueryOASettingArg queryOASettingArg=new QueryOASettingArg();
        queryOASettingArg.setTenantId(tenantId);
        queryOASettingArg.setCurrentDcId(dataCenterId);
        if(oaTenantEnum!=null){
            queryOASettingArg.setType(oaTenantEnum.name());
        }
        List<OASettingDoc> oaSettingDocs = oaSettingsDao.listConfigByType(tenantId, queryOASettingArg);
        List<OASettingVO> oaSettingVOS=Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(oaSettingDocs)){
            tenantIds = oaSettingDocs.stream().map(OASettingDoc::getTenantId).collect(Collectors.toList());
        }
        //eieaconverter也走的这个接口
        Map<String, String>   simpleEnterpriseMap = listTenantNamesByIds(tenantIds);
        for (OASettingDoc oaSettingDoc : oaSettingDocs) {
            OASettingVO oaSettingVO= BeanUtil.copy(oaSettingDoc,OASettingVO.class);
            oaSettingVO.setId(oaSettingDoc.getId().toString());
            oaSettingVO.setEnterpriseName(simpleEnterpriseMap.get(oaSettingDoc.getTenantId()));
            oaSettingVOS.add(oaSettingVO);
        }
        return Result.newSuccess(oaSettingVOS);
    }


    @Override
    public Result<String> upsertSettingInfo(String tenantId, OASettingVO info,String dataCenterId) {
        OASettingDoc oaSettingDoc=new OASettingDoc();
        oaSettingDoc.setTenantId(tenantId);
        oaSettingDoc.setType(info.getType()) ;
        oaSettingDoc.setId(ObjectId.get());
        oaSettingDoc.setConfiguration(info.getConfiguration());
        oaSettingDoc.setDataCenterId(dataCenterId);
        oaSettingsDao.batchInsert(tenantId, Lists.newArrayList(oaSettingDoc));
        return Result.newSuccess();
    }


    @Override
    public Result<String> deleteSettingInfo(String tenantId, OATenantEnum oaTenantEnum,String dataCenterId) {
        QueryOASettingArg queryOASettingArg=new QueryOASettingArg();
        queryOASettingArg.setTenantId(tenantId);
        queryOASettingArg.setType(oaTenantEnum.name());
        queryOASettingArg.setCurrentDcId(dataCenterId);
        oaSettingsDao.deleteTenantAndType(tenantId,queryOASettingArg);
        return null;
    }

    @Override
    public Result<String> deleteSettingInfoById(String tenantId, String id) {
        oaSettingsDao.deleteById(tenantId,id);
        return Result.newSuccess();
    }

    @Override
    public <T> Result<T> getGenericInfo(String tenantId, OATenantEnum oaTenantEnum,String dataCenterId) {
        Result<OASettingVO> settingInfo = this.getSettingInfo(tenantId, oaTenantEnum,dataCenterId);
        if(settingInfo.isSuccess()&& ObjectUtils.isNotEmpty(settingInfo.getData())){
            Object object = JSONObject.parseObject(String.valueOf(settingInfo.getData().getConfiguration()), oaTenantEnum.getConfigType());
            return (Result<T>) Result.newSuccess(object);
        }
        return Result.newSuccess();
    }
}
