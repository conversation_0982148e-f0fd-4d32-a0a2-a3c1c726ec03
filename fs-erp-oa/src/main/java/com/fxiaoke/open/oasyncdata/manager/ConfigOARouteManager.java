package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;

import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.db.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.oasyncdata.db.manager.IdGenerator;
import com.fxiaoke.open.oasyncdata.db.manager.TenantConfigurationManager;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.HttpUrlUtils;
import com.fxiaoke.open.oasyncdata.db.util.OkHttpUtils;
import com.fxiaoke.open.oasyncdata.model.HttpResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ConfigOARouteManager {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private IdGenerator idGenerator;

    public Boolean configRoute(String tenantId,String resourceId) {
        Boolean result=postConfigRoute(tenantId,resourceId);
        if(result){
            Map<String, List<String>> configRoute=tenantConfigurationManager.getConfigRouteTenant();
            for(String sourceId:configRoute.keySet()){
                List<String> tenants=configRoute.get(sourceId);
                if(tenants.contains(tenantId)){//去掉
                    tenants.remove(tenantId);
                }
            }
            if(configRoute.containsKey(resourceId)){//加上
                configRoute.get(resourceId).add(tenantId);
            }else{
                configRoute.put(resourceId, Lists.newArrayList(tenantId));
            }
            result=tenantConfigurationManager.updateConfigRouteTenant(configRoute);
        }


        return result;
    }

    public void initRoute(String tenantId,String resourceId) {

        Map<String, List<String>> configRoute=tenantConfigurationManager.getConfigRouteTenant();
        if(configRoute.keySet().size()==0){
            ErpTenantConfigurationEntity erpTenantConfigurationEntity=new ErpTenantConfigurationEntity();
            erpTenantConfigurationEntity.setTenantId("all");
            erpTenantConfigurationEntity.setChannel(ErpChannelEnum.ALL.name());
            erpTenantConfigurationEntity.setDataCenterId("0");
            erpTenantConfigurationEntity.setCreateTime(System.currentTimeMillis());
            erpTenantConfigurationEntity.setUpdateTime(System.currentTimeMillis());
            erpTenantConfigurationEntity.setType(TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name()+1);
            erpTenantConfigurationEntity.setId(idGenerator.get());
            Map<String,List<String>> configRoutes=Maps.newHashMap();
            configRoutes.put(resourceId,Lists.newArrayList(tenantId));
            erpTenantConfigurationEntity.setConfiguration(JSONObject.toJSONString(configRoutes));
            erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpTenantConfigurationEntity);
        }

    }



    private Boolean postConfigRoute(String tenantId, String resourceId) {
        String url= HttpUrlUtils.buildConfigRouteUrl();
        /* 构造header */
        Map<String, String> headerMap = Maps.newHashMapWithExpectedSize(NumberUtils.INTEGER_ONE);
        headerMap.put("Content-type", "application/json;charset=utf-8");
        /* 构造body */
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(NumberUtils.INTEGER_TWO);
        paramMap.put("tenantId",tenantId);
        paramMap.put("resourceId",resourceId);
        HttpResponse response = OkHttpUtils.post(url, headerMap, JSONObject.toJSON(paramMap).toString());
        if(response!=null&&200==response.getCode()){
            JSONObject result=JSONObject.parseObject(response.getBody());
            if("OK".equals(result.getString("message"))){
                return true;
            }else{
                return false;
            }
        }else {
            log.warn("configRoute response={}",response);
            return false;
        }
    }
}
