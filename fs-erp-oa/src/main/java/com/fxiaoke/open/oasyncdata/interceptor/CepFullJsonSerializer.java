package com.fxiaoke.open.oasyncdata.interceptor;

import com.facishare.cep.plugin.exception.BizException;
import com.facishare.fcp.serialization.Serializer;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.UserVo;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.google.common.base.Charsets;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/7/6
 */
@Slf4j
public class CepFullJsonSerializer implements Serializer {

    @Override
    public byte[] encode(Object object) {
        if (object == null) {
            return new byte[0];
        } else {
            String ret;
            try {
                ret = GsonUtil.toJson(object);
            } catch (Exception var4) {
                throw new RuntimeException("Encode error! object=" + object, var4);
            }

            return ret.getBytes(Charsets.UTF_8);
        }
    }

    @Override
    public <T> T decode(Class<T> clazz, byte[] buffer) {
        if (buffer != null && buffer.length != 0) {
            String s = new String(buffer, Charsets.UTF_8);
            try {
                JsonElement jsonElement = JsonParser.parseString(s);
                if (jsonElement.isJsonObject()){
                    try{
                        String currentDcId = jsonElement.getAsJsonObject().get("currentDcId").getAsString();
                        if (currentDcId != null) {
                            UserVo userVo = UserContextHolder.get().get();
                            if (userVo != null) {
                                userVo.setDataCenterId(currentDcId);
                            }
                        }
                    }catch (Exception e){
                        log.info("parse currentDcId from json error",e);
                    }
                }
                return GsonUtil.getGson().fromJson(jsonElement,clazz);
            } catch (Exception e) {
                log.error("Decode error,target class:{} str:{}", clazz, s, e);
                throw new BizException(ResultCodeEnum.PARAM_ILLEGAL.getErrCode());
            }
        } else {
            return null;
        }
    }
}
