package com.fxiaoke.open.oasyncdata.util;

import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class BuriedSitesStatisticsUtil {

    private final static String SERVICE_NAME = "fs-erp-sync-data";

    private static OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    public void setErpConnectInfoManager(OAConnectInfoDao oaConnectInfoDao) {
        BuriedSitesStatisticsUtil.oaConnectInfoDao = oaConnectInfoDao;
    }

    public static void uploadBuriedStitesLog(String tenantId, String objApiName) {
        try {
            OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdWithCacheToOne(tenantId);
            AsyncLog(tenantId, objApiName, oaConnectInfoEntity.getEnterpriseName());
        } catch (Exception e) {
            log.error("upload BuriedStitesLogBySnap failed,e:", e);
        }
    }

    private static void AsyncLog(String tenantId, String objApiName, String enterpriseName) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("tenantId", tenantId);
        dataMap.put("objApiName", objApiName);
        dataMap.put("channel", ErpChannelEnum.OA);
        dataMap.put("enterpriseName", enterpriseName);
        dataMap.put("director", "CRM到OA");   // ignoreI18n  神策日志
        dataMap.put("trace", "EnterpriseObjectCount");
        dataMap.put("traceName", "OA平台统计企业对象同步次数");   // ignoreI18n   神策日志
        DataPersistor.asyncLog(SERVICE_NAME, dataMap);
    }

}
