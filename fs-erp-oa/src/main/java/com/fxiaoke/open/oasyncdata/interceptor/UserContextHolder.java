package com.fxiaoke.open.oasyncdata.interceptor;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.fxiaoke.open.oasyncdata.model.UserVo;


/**
 * <AUTHOR>
 */
public class UserContextHolder {

    /**
     * alibaba开源的支持线程池传递的线程变量
     * 注：需要修饰线程池才能使用参考com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController
     */
    private static final TransmittableThreadLocal<UserVo> USER_VO_HOLDER = new TransmittableThreadLocal<>();

    public static TransmittableThreadLocal<UserVo> get() {
        return USER_VO_HOLDER;
    }

    static void remove() {
        USER_VO_HOLDER.remove();
    }
}
