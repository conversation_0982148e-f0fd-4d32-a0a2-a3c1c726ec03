package com.fxiaoke.open.oasyncdata.util;

import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.oasyncdata.db.util.TraceUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;


/**
 * 冯院华hardy-22.08.22
 * <p>
 * 数据在本地聚合后执行批处理
 * <p>
 * 达到下面条件之一，就进行批量处理：
 * 1.数据条数累计达到 100条。 可配置。
 * 2.数据在内存的停留时间超过5s。 可配置。
 * <p>
 * 不支持控制队列的总数据量，因为数据产生速度比较快时，
 * 全局计数耗费大量CPU。
 * <br> <b>TIPS:</b>为防止漏消费，请在spring容器销毁时，清理容器内使用的LocalDispatcherUtil！调用processAll即可!
 */
@Slf4j
@Getter
@Setter
public class DelayQueueUtil<T> {
    private ConcurrentMap<String, DataQueue<T>> dataQueueMap;
    /**队列中的数据，超过queueCapacity立即批量处理**/
    private int queueCapacity  = 100;
    /**不满足queueCapacity的条件的，delay batchProcessTimeLimitInSecond时间后，交给大小为batchProcessThreadSize的线程池执行批处理。**/
    private static int batchProcessThreadSize = 3;
    private int batchProcessTimeLimitInSecond = 5;
    private ExecutorService processThreadPool;
    //延迟队列，用于上方固定线程数的延迟任务执行
    private DelayQueue<LocalDispatcherDelayJob> delayQueue = new DelayQueue<>();
    /**执行批处理数据的函数*/
    private BiConsumer<String, List<T> > batchProcessDataConsumer;

    public DelayQueueUtil(BiConsumer<String, List<T>> batchProcessDataConsumer) {
        this(batchProcessDataConsumer, batchProcessThreadSize);
    }

    public DelayQueueUtil(BiConsumer<String, List<T>> batchProcessDataConsumer, int batchProcessThreadSize) {
        int threadSize = batchProcessThreadSize;
        this.batchProcessDataConsumer = batchProcessDataConsumer;
        dataQueueMap = Maps.newConcurrentMap();
        processThreadPool = Executors.newFixedThreadPool(threadSize, new ThreadFactoryBuilder().setDaemon(true).setNameFormat("thread-localdispatch-%d").build());

        for (int i = 0; i < threadSize; i++) {
            //这个是按时间分发的线程处理
            processThreadPool.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        while (true) {
                            LocalDispatcherDelayJob delayed = delayQueue.take();
                            if (delayed != null) {
                                log.debug("{} do delayedjob key:{}",Thread.currentThread().getName(), delayed.getKey());
                                processBatchLoop(delayed.getKey());
                            }
                        }
                    } catch (Exception e) {
                        log.error("get exception, ",e);
                    }
                }
            });
        }
    }

    /**
     * 正常情况下，都应该在Spring容器销毁时调用！！！
     */
    public void processAll() {
        dataQueueMap.keySet().forEach(this::processBatchLoop);
    }

    /**
     * 单次消费队列所有数据
     */
    private int processQueueALlDataOnce(String key){
        //先提取，防止并发访问问题
        DataQueue<T> dataQueue = dataQueueMap.get(key);
        //以实际collection获取的数量大小为准
        int itemCount = 0;
        if (dataQueue != null){
            List<T> batchDataList = Lists.newArrayList();
            itemCount = dataQueue.drainTo(batchDataList);
            if (itemCount > 0) {
                //如果同时有多个线程访问，这里可能被其他线程消费，所以这里判断一下。
                try {
                    batchProcessDataConsumer.accept(key, batchDataList);
                } catch (Exception e) {
                    //批处理函数可能失败，不管，继续往下走，数据不能堵住。
                    log.debug("batch function fail, exception, ", e);
                }
            }
        }
        return itemCount;
    }

    /**
     * 循环处理数据，直至无法从队列获取数据
     */
    private void processBatchLoop(String key) {
        int itemCount;
        do {
            //直至拿不到数据，才会停止循环
            itemCount = processQueueALlDataOnce(key);
        } while (itemCount > 0);
    }

    public void produceDataCount1(String key, T objectData) {
        produceData(key, objectData, 1);
    }

    public void produceData(String key, T objectData, int count) {
        if (count <= 0 || count > 1000000) {
            //应该没人会传<0吧。。。预防一下，防止出现死锁。同时增加一个大于的判断，防止出现int溢出
            count = 1;
        }
        try {
            DataQueue<T> curTenantDataQueue = dataQueueMap.computeIfAbsent(key, k -> new DataQueue<>(queueCapacity));
            //put如果队列已满，会无限阻塞。
            int allCount = curTenantDataQueue.put(objectData,count);
            if(allCount >= queueCapacity) {
                /**数据达到批处理条件, 在当前线程上下文中，批量处理。仅处理一次，防止当前线程阻塞太久了。**/
                log.debug("produceData trigger processBatch for key{} ", key);
                processQueueALlDataOnce(key);
            }
            if (allCount == count) {
                //第一条数据，触发一个延迟任务。
                log.debug("put first data, add deleyjob for key:{}", key);
                LocalDispatcherDelayJob job = new LocalDispatcherDelayJob();
                //任务存储多语
                job.setLocale(I18nUtil.getLocaleFromTrace());
                job.setKey(key);
                job.delayMillis(batchProcessTimeLimitInSecond * 1000L);
                delayQueue.put(job);
            }
        }catch (Exception e){
            log.error("put data to queue for key:{} exception, ",key, e);
        }
    }

    public void produceDataBatch(String key, Collection<T> objectDataList) {
        for (T data : objectDataList) {
            produceDataCount1(key, data);
        }
    }

    static class DataQueue<E> {
        /**
         * 数据条数计数器
         */
        private final AtomicInteger counter = new AtomicInteger();

        private final LinkedBlockingDeque<E> queue;

        public DataQueue(int c) {
            queue = new LinkedBlockingDeque<>(c);
        }

        /**
         * @return 真实出队的数据条数
         */
        public int drainTo(Collection<? super E> c) {
            int i = queue.drainTo(c);
            //取出所有数据，并且将计数器重置为0.这里在并发情况下可能出现一种情况，在drainTo已经执行了，未执行counter.set，
            //又put了数据，这时的counter值是小的，但是不影响运行，不再另外加锁处理了。
            counter.set(0);
            return i;
        }

        /**
         * @param e
         * @param count
         * @return
         * @throws InterruptedException
         */
        public int put(E e, int count) throws InterruptedException {
            queue.put(e);
            //增加数量，这里返回的是原子的，drainTo之后首次put的时候（即便并发）总会有一个的allCount = count
            return counter.addAndGet(count);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static
    class LocalDispatcherDelayJob implements Delayed {
        private String key;
        private long workTime;
        private long interval;
        private String locale;

        @Override
        public int compareTo(@Nonnull Delayed delayed) {
            long result = this.workTime - ((LocalDispatcherDelayJob) delayed).workTime;
            if (result < 0) {
                return -1;
            } else if (result > 0) {
                return 1;
            } else {
                return 0;
            }
        }

        @Override
        public long getDelay(TimeUnit unit) {
            return unit.convert(workTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        }

        /**
         * 强制指定扫描间隔
         *
         * @param interval
         */
        public void delayMillis(long interval) {
            this.interval =interval;
            this.workTime = System.currentTimeMillis() + this.interval;
        }

        @Override
        public String toString() {
            return "LocalDispatcherDelayJob{" +
                    "key='" + key + '\'' +
                    ", workTime=" + workTime +
                    ", interval=" + interval +
                    '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            LocalDispatcherDelayJob that = (LocalDispatcherDelayJob) o;
            return Objects.equals(key, that.key);
        }

        @Override
        public int hashCode() {
            return Objects.hash(key, workTime, interval);
        }
    }
}
