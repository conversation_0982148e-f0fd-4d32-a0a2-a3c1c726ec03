package com.fxiaoke.open.oasyncdata.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.CustomFunctionConstant;
import com.fxiaoke.open.oasyncdata.constant.CustomFunctionConstantEx;
import com.fxiaoke.open.oasyncdata.constant.OAAPLTypeEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.manager.FunctionApiManager;
import com.fxiaoke.open.oasyncdata.manager.ProxyHttpClient;
import com.fxiaoke.open.oasyncdata.manager.UserLoginManager;
import com.fxiaoke.open.oasyncdata.manager.UserManager;
import com.fxiaoke.open.oasyncdata.model.OAAplApiName;
import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.model.ObjectData;
import com.fxiaoke.open.oasyncdata.result.Result2;
import com.fxiaoke.open.oasyncdata.result.base.ErpSyncDataException;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.OAUserAuthService;
import com.fxiaoke.open.oasyncdata.util.Sha1Util;
import com.fxiaoke.open.oasyncdata.util.XmlUtil;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.util.XmlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date: 2021/1/14
 * @Desc:OA鉴权相关
 */
@Service
@Slf4j
@Data
public class OAUserAuthServiceImpl implements OAUserAuthService {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Autowired
    private UserManager userManager;

    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;

    @Autowired
    private UserLoginManager userLoginManager;

    @Autowired
    private RedisDataSource redisDataSource;

    @ReloadableProperty("oa.author.app.url")
    private String oaAuthorAppUrl;

    @ReloadableProperty("oa.author.web.url")
    private String oaAuthorWebUrl;

    private static Long EXPIR_TIME = 1 * 24 * 60 * 60L;
    @Value("${uss.oauth2Url}")
    private String oauth2Url;

    @Value("${uss.oauthRedirectUrl}")
    String oauthRedirectUrl;

    private String ACCESS_TOKEN_KEY="WebHook_OA_accessToken%s%s";
    private String REFRESH_TOKEN_KEY="WebHook_OA_refreshToken%s%s";
    @Autowired
    private FunctionApiManager functionApiManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<String> authorize(String ei, String ticket, String apiName, String dataId, boolean isApp) {
        String userCode = null;
        try {
            String path = isApp ? oaAuthorAppUrl + apiName + "/" + dataId + "/" + ei : oaAuthorWebUrl + apiName + "/" + dataId + "/" + ei;
            String url = "https://cas.fosun.com:8443/cas/serviceValidate?ticket=" + ticket + "&service=" + URLEncoder.encode(path);
            String response = proxyHttpClient.doAndLog(ei,url,null,Collections.emptyMap(), RequestMethod.GET.name(),()->proxyHttpClient.getUrl(url, Collections.emptyMap()));
            Map<String, String> map = XmlUtil.xmlToMap(response);
            log.info("authorize map={}", map);
            if (!StringUtils.isEmpty(map.get("user"))) {
                userCode = userManager.getFxUserCode(map.get("user"), ei);
            }
        } catch (Exception e) {
            log.error("authorize fail.", e);
            return Result.newErrorByI18N(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage(),null,null);
        }
        return Result.newSuccess(userCode);
    }

    /**
     * 致远OA认证,获取用户ID
     *
     * @param ei
     * @param ticket
     * @return
     */
    @Override
    public Result<String> authorizeSeeYon(String ei, String ticket) {
        Result<String> ssoResult = authorizeSeeYonSSO(ei, ticket);
        String userCode = null;
        if (ssoResult.isSuccess()) {
            userCode = userManager.getFxUserCode(ssoResult.getData(), ei);
        } else {
            log.warn("authorizeSeeYon fail. ei={}", ei);
            return Result.newError(ResultCodeEnum.RESULT_NULL);
        }
        return Result.newSuccess(userCode);
    }

    /**
     * 织语CCWork单点登录认证
     *
     * @param ei
     * @param ticket
     * @param nonce
     * @return
     */
    @Override
    public Result<String> authorizeCCWork(String ei, String ticket, String nonce) {
        Map<String, String> params = Maps.newHashMap();
        params.put("ticket", ticket);
        params.put("nonce", nonce);
        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,null);
        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
        Result<String> result = Result.newSuccess();
        try {
            String url = oaConnectParam.getSsoAuthUrl();
            String response = proxyHttpClient.doAndLog(ei,url,params,Collections.emptyMap(),RequestMethod.POST.name(),()->proxyHttpClient.postUrl(url, params, Collections.emptyMap()));
            log.info("authorizeCCWork response={}", response);
            if (!StringUtils.isEmpty(response)) {
                Map<String, String> resultMap = GsonUtil.fromJson(response, Map.class);
                if ("0".equals(resultMap.get("errcode"))) {
                    String userCode = userManager.getFxUserCode(resultMap.get("account"), ei);
                    return Result.newSuccess(userCode);
                } else {
                    log.warn("authorizeCCWork fail. ei={},response={}", ei, response);
                    return Result.newError(ResultCodeEnum.RESULT_NULL);
                }
            } else {
                log.warn("authorizeCCWork fail. ei={},response={}", ei, response);
                return Result.newError(ResultCodeEnum.RESULT_NULL);
            }
        } catch (Exception e) {
            log.error("authorizeCCWork fail.", e);
            return Result.newErrorByI18N(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage(),null,null);
        }
    }

    /**
     * 明德鉴权
     *
     * @param token
     * @param loginid
     * @param stamp
     * @param ei
     * @return
     */
    @Override
    public Result<String> authorizeMD(String token, String loginid, String stamp, String ei) {
        String secret = "d73ec6289574d272c9cc2df5ff70488b";
        String sha1 = null;
        try {
            sha1 = Sha1Util.getSha1((secret + loginid + stamp).getBytes());
        } catch (NoSuchAlgorithmException e) {
            log.warn("authorizeMD decrypt fail, message={}", e.getMessage());
        }
        String userCode = null;
        if (sha1.equals(token)) {
            userCode = userManager.getFxUserCode(loginid, ei);
        } else {
            log.warn("authorizeMD fail. ei={}", ei);
            return Result.newError(ResultCodeEnum.RESULT_NULL);
        }
        return Result.newSuccess(userCode);
    }


    private Result<String> authorizeSeeYonSSO(String ei, String ticket) {
        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,null);
        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
        try {
            String url = oaConnectParam.getSsoAuthUrl() + ticket;
            String response = proxyHttpClient.doAndLog(ei,url,null,Collections.emptyMap(),RequestMethod.GET.name(),()->proxyHttpClient.getUrl(url, Collections.emptyMap()));
            //有些企业返回的数据是编码后的数据，需要解码
            String utf8_res = java.net.URLDecoder.decode(response, "utf-8");
            log.info("authorizeSeeYon response={},utf8_res:{}", response,utf8_res);
            if (!StringUtils.isEmpty(utf8_res)) {
                return Result.newSuccess(utf8_res.replaceAll("\r\n", ""));
            } else {
                log.warn("authorizeSeeYon fail. ei={}", ei);
                return Result.newError(ResultCodeEnum.RESULT_NULL);
            }
        } catch (Exception e) {
            log.error("authorizeSeeYon fail.", e);
            return Result.newErrorByI18N(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage(),null,null);
        }
    }


    @Override
    public Result<String> authorizeLL(String ei, String url, String ticket, boolean isApp) {
        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,null);
        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
        log.info("authorizeLL url={}", url);
        try {
            String posturl = oaConnectParam.getSsoAuthUrl() + ticket + "&service=" + URLEncoder.encode(url);
            Map<String, String> params = Maps.newHashMap();
            String response = proxyHttpClient.doAndLog(ei,posturl,params,Collections.emptyMap(),RequestMethod.POST.name(),()->proxyHttpClient.postUrl(posturl, params, Collections.emptyMap()));
            log.info("authorizeLL response={}", response);
            if (!StringUtils.isEmpty(response)) {
                if (!response.contains("yes")) {
                    log.warn("authorizeLL fail. ei={}", ei);
                    return Result.newError(ResultCodeEnum.NO_USER);
                } else {
                    String userId = response.replaceAll("[\\s\\t\\n\\r]", "").substring(3);
                    String userCode = userManager.getFxUserCode(userId, ei);
                    return Result.newSuccess(userCode);
                }
            } else {
                log.warn("authorizeLL fail. ei={}", ei);
                return Result.newError(ResultCodeEnum.RESULT_NULL);
            }
        } catch (Exception e) {
            log.error("authorizeLL fail.", e);
            return Result.newErrorByI18N(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage(),null,null);
        }
    }

    @Override
    public Result<String> authorizeWensli(String ei, String ticket, String apiName, String dataId) {
        String appid = "500722924";
        String secret = "02BtYyzXDVI7dSCxRj8y";
        String url = "https://www.yunzhijia.com/gateway/ticket/user/acquirecontext?accessToken=";
        String accessTokenKey = "Wensli_OA_accessToken" + appid + secret;
        try {
            String accessToken = redisDataSource.get().get(accessTokenKey);
            if (accessToken == null) {
                String refreshTokenKey = "Wensli_OA_refreshToken" + appid + secret;
                String refreshToken = redisDataSource.get().get(refreshTokenKey);
                String getTokenUrl = "";
                Map<String, String> getTokenParams = new HashMap() {{
                    put("appid", appid);
                    put("timestamp", System.currentTimeMillis());
                    put("scope", "app");
                }};
                //没有生成过token或refreshToken也过期了
                if (refreshToken == null) {
                    getTokenUrl = "https://www.yunzhijia.com/gateway/oauth2/token/getAccessToken";
                    getTokenParams.put("secret", secret);
                } else {  //刷新token
                    getTokenUrl = "https://www.yunzhijia.com/gateway/oauth2/token/refreshToken";
                    getTokenParams.put("refreshToken", refreshToken);
                }
                String finalGetTokenUrl = getTokenUrl;
                String tokenResponse = proxyHttpClient.doAndLog(ei,finalGetTokenUrl,getTokenParams,Collections.emptyMap(),RequestMethod.POST.name(),()->proxyHttpClient.postUrl(finalGetTokenUrl, getTokenParams, Collections.emptyMap()));
                log.info("Wensli token requestUrl={}, getTokenParams={}, response={}", getTokenUrl, getTokenParams, tokenResponse);
                JSONObject tokenJsonObject = JSONObject.parseObject(tokenResponse);
                if (!StringUtils.isEmpty(tokenResponse)) {
                    Boolean tokenSuccess = tokenJsonObject.getBoolean("success");
                    if (!tokenSuccess) {
                        log.warn("Wensli token fail, ei={}", ei);
                        return Result.newError(ResultCodeEnum.NO_USER);
                    } else {
                        JSONObject tokenData = tokenJsonObject.getJSONObject("data");
                        accessToken = tokenData.getString("accessToken");
                        refreshToken = tokenData.getString("refreshToken");
                    }
                }
                redisDataSource.get().set(accessTokenKey, accessToken, "NX", "EX", 6000);
                redisDataSource.get().set(refreshTokenKey, refreshToken, "NX", "EX", EXPIR_TIME);
            }

            url = url + accessToken;
            Map<String, String> params = new HashMap() {{
                put("appid", appid);
                put("ticket", ticket);
            }};
            String finalUrl = url;
            String response = proxyHttpClient.doAndLog(ei,finalUrl,params,Collections.emptyMap(),RequestMethod.POST.name(), ()->proxyHttpClient.postUrl(finalUrl, params, Collections.emptyMap()));
            log.info("Wensli authorize requestUrl={}, getOpenIdParams={}, response={}", url, params, response);
            if (!StringUtils.isEmpty(response)) {
                JSONObject jsonObject = JSONObject.parseObject(response);
                Boolean success = jsonObject.getBoolean("success");
                if (!success) {
                    log.warn("Wensli authorize fail, ei={}", ei);
                    return Result.newError(ResultCodeEnum.NO_USER);
                } else {
                    JSONObject data = jsonObject.getJSONObject("data");
                    String userId = data.getString("openid");
                    System.out.println("userId:" + userId);
                    String userCode = userManager.getFxUserCode(userId, ei);
                    return Result.newSuccess(userCode);
                }
            } else {
                log.warn("Wensli authorize fail, ei={}", ei);
                return Result.newError(ResultCodeEnum.RESULT_NULL);
            }
        } catch (Exception e) {
            log.warn("Wensli authorize fail,", e);
            return Result.newErrorByI18N(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage(),null,null);
        }
    }

    /**
     * 统一支持云之家免登
     * @param ei
     * @param ticket
     * @param apiName
     * @param dataId
     * @return
     */
    @Override
    public Result<String> authorizeWebHook(String ei, String ticket, String apiName, String dataId) {
        OAConnectInfoEntity oaConnectInfoEntity = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,null);
        OAConnectParam oaConnectParam = GsonUtil.fromJson(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
        String appId=oaConnectParam.getCommonMap().get("appid");
        String secret=oaConnectParam.getCommonMap().get("secret");
        String url = "https://www.yunzhijia.com/gateway/ticket/user/acquirecontext?accessToken=";
        try {
            //获取redis的token
            String accessTokenKey=String.format(ACCESS_TOKEN_KEY,appId,secret);
            String accessToken = redisDataSource.get().get(accessTokenKey);
            if(StringUtils.isEmpty(accessToken)){
                 accessToken = getInitToken(appId, secret, ei, accessTokenKey);
            }
            String builderUrl=new StringBuilder().append(url).append(accessToken).toString();
            Map<String, String> params = new HashMap() {{
                put("appid", appId);
                put("ticket", ticket);
            }};
            String finalBuilderUrl = builderUrl;
            String response = proxyHttpClient.doAndLog(ei,finalBuilderUrl,params,Collections.emptyMap(),RequestMethod.POST.name(),()->proxyHttpClient.postUrl(finalBuilderUrl, params, Collections.emptyMap()));
            log.info("webhook authorize requestUrl={}, getOpenIdParams={}, response={}", url, params, response);
            if (!StringUtils.isEmpty(response)) {
                JSONObject jsonObject = JSONObject.parseObject(response);
                Boolean success = jsonObject.getBoolean("success");
                if (!success) {
                   //有可能是token过期了，重新获取一次token
                    accessToken = getInitToken(appId, secret, ei, accessTokenKey);
                    builderUrl=new StringBuilder().append(url).append(accessToken).toString();
                    response = proxyHttpClient.doAndLog(ei,finalBuilderUrl,params,Collections.emptyMap(),RequestMethod.POST.name(), ()->proxyHttpClient.postUrl(finalBuilderUrl, params, Collections.emptyMap()));
                    if(!StringUtils.isEmpty(response)){
                         jsonObject = JSONObject.parseObject(response);
                         success = jsonObject.getBoolean("success");
                         if(!success){
                             log.warn("webhook authorize fail, ei={},response:{}", ei,response);
                             return Result.newError(ResultCodeEnum.NO_USER);
                         }
                    }
                }
                JSONObject data = jsonObject.getJSONObject("data");
                String userId = data.getString("openid");
                String userCode = userManager.getFxUserCode(userId, ei);
                return Result.newSuccess(userCode);
            } else {
                log.warn("Wensli authorize fail, ei={}", ei);
                return Result.newError(ResultCodeEnum.RESULT_NULL);
            }
        } catch (Exception e) {
            log.warn("Wensli authorize fail,", e);
            return Result.newErrorByI18N(ResultCodeEnum.OA_CALL_SERVICE_ERROR, e.getMessage(),null,null);

        }
    }

    private Map<String,String> simpleRetryGetToken(String ei,Map<String, String> getTokenParams,String getTokenUrl){
        Map<String,String> resultMap=Maps.newHashMap();
        String tokenResponse = proxyHttpClient.doAndLog(ei,getTokenUrl,getTokenParams,Collections.emptyMap(),RequestMethod.POST.name(), ()->proxyHttpClient.postUrl(getTokenUrl, getTokenParams, Collections.emptyMap()));
        JSONObject tokenJsonObject = JSONObject.parseObject(tokenResponse);
        Boolean tokenSuccess = tokenJsonObject.getBoolean("success");
        if(!tokenSuccess){
            log.warn("Wensli token fail, secret={},result:{}", JSONObject.toJSONString(getTokenParams),tokenResponse);
            return null;
        }
        JSONObject tokenData = tokenJsonObject.getJSONObject("data");
        String accessToken = tokenData.getString("accessToken");
        String refreshToken = tokenData.getString("refreshToken");
        resultMap.put("accessToken",accessToken);
        resultMap.put("refreshToken",refreshToken);
        return resultMap;
    }

    private String getInitToken(String appId,String secret,String ei,String accessTokenKey){
        String refreshTokenKey =String.format(REFRESH_TOKEN_KEY,appId,secret);
        String refreshToken = redisDataSource.get().get(refreshTokenKey);
        String getTokenUrl = "";
        String accessToken = "";
        Map<String, String> getTokenParams = new HashMap() {{
            put("appid", appId);
            put("timestamp", System.currentTimeMillis());
            put("scope", "app");
        }};
        //没有生成过token或refreshToken也过期了
        if (refreshToken == null) {
            getTokenUrl = "https://www.yunzhijia.com/gateway/oauth2/token/getAccessToken";
            getTokenParams.put("secret", secret);
        } else {  //刷新token
            getTokenUrl = "https://www.yunzhijia.com/gateway/oauth2/token/refreshToken";
            getTokenParams.put("refreshToken", refreshToken);
        }
        String finalGetTokenUrl = getTokenUrl;
        String tokenResponse = proxyHttpClient.doAndLog(ei,finalGetTokenUrl,getTokenParams,Collections.emptyMap(),RequestMethod.POST.name(), ()->proxyHttpClient.postUrl(finalGetTokenUrl, getTokenParams, Collections.emptyMap()));
        log.info("Wensli token requestUrl={}, getTokenParams={}, response={}", getTokenUrl, getTokenParams, tokenResponse);
        JSONObject tokenJsonObject = JSONObject.parseObject(tokenResponse);
        if (!StringUtils.isEmpty(tokenResponse)) {
            Boolean tokenSuccess = tokenJsonObject.getBoolean("success");
            if (!tokenSuccess) {
                log.warn("Wensli token fail, ei={}", ei);
                getTokenUrl = "https://www.yunzhijia.com/gateway/oauth2/token/getAccessToken";
                getTokenParams.put("secret", secret);
                Map<String, String> resultMap = simpleRetryGetToken(ei,getTokenParams, getTokenUrl);
                if(ObjectUtils.isEmpty(resultMap)){
                    return null;
                }
                accessToken=resultMap.get("accessToken");
                refreshToken=resultMap.get("refreshToken");
            } else {
                JSONObject tokenData = tokenJsonObject.getJSONObject("data");
                accessToken = tokenData.getString("accessToken");
                refreshToken = tokenData.getString("refreshToken");
            }
        }
        //覆盖
        redisDataSource.get().setex(accessTokenKey, 6000L, accessToken);
        redisDataSource.get().setex(refreshTokenKey, EXPIR_TIME,refreshToken);
        return accessToken;
    }


    /**
     * 支持齐鲁OA点击代办的身份认证流程，仅支持WEB端点击使用。因为app端没有接入IAM鉴权
     *
     * @param code
     * @param clientId
     * @param clientSecret
     * @param grantType
     * @return
     */
    @Override
    public Result<String> authorizeQiLuByWeb(String ei, String code, String clientId, String clientSecret, String grantType) {
        OAConnectInfoEntity tenantOAInfo = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,null);
        if (ObjectUtils.isEmpty(tenantOAInfo)) {
            return Result.newError(ResultCodeEnum.OA_UN_CONNECT);
        }
        OAConnectParam oaConnectParam = GsonUtil.fromJson(tenantOAInfo.getConnectParams(), OAConnectParam.class);
        String oauthUrl = oaConnectParam.getSsoAuthUrl();
        //获取accessToken
        String getAccessTokenUrl = "%s/idp/oauth2/getToken?client_id=%s&grant_type=authorization_code&code=%s&client_secret=%s";
        String completeAccessTokenUrl = String.format(getAccessTokenUrl, oauthUrl, clientId, code, clientSecret);
        log.info("accessToken url:{}", completeAccessTokenUrl);
        String accessTokenResult = proxyHttpClient.doAndLog(ei,completeAccessTokenUrl,Maps.newHashMap(),Collections.emptyMap(),RequestMethod.POST.name(), ()->proxyHttpClient.postUrl(completeAccessTokenUrl, Maps.newHashMap(), Collections.emptyMap()));
        log.info("proxy request token:{}", accessTokenResult);
        Map<String, String> resultMap = JSONObject.parseObject(accessTokenResult, new TypeReference<Map<String, String>>() {
        });
        if (ObjectUtils.isNotEmpty(resultMap.get("errcode"))) {
            return Result.newError(resultMap.get("errcode"), resultMap.get("msg"));
        }
        String accessToken = resultMap.get("access_token");
        String getUserInfo = "%s/idp/oauth2/getUserInfo?access_token=%s&client_id=%s";
        String completeUserInfoUrl = String.format(getUserInfo, oauthUrl,accessToken, clientId);
        log.info("complete url:{}", completeUserInfoUrl);
        String userInfoResult = proxyHttpClient.doAndLog(ei,completeUserInfoUrl,null,Collections.emptyMap(),RequestMethod.GET.name(),()->proxyHttpClient.getUrl(completeUserInfoUrl, Collections.emptyMap()));
        log.info("userInfoResult:{}", userInfoResult);
        Map<String, String> userInfoMap = JSONObject.parseObject(userInfoResult, new TypeReference<Map<String, String>>() {
        });
        String loginName = userInfoMap.get("loginName");
        if(StringUtils.isNotEmpty(loginName)){
            String fxUserCode = userManager.getFxUserCode(loginName, ei);
            return Result.newSuccess(fxUserCode);
        }
        return Result.newError(ResultCodeEnum.NO_USER);
    }

    @Override
    public Result<OAConnectInfoVO> getConnectInfo(String ei) {
        OAConnectInfoEntity tenantInfo = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,null);
        if(ObjectUtils.isEmpty(tenantInfo)){
            return null;
        }
        OAConnectParam oaConnectParam = GsonUtil.fromJson(tenantInfo.getConnectParams(), OAConnectParam.class);
        OAConnectInfoVO connectInfoVO = BeanUtil.copy(tenantInfo, OAConnectInfoVO.class);
        connectInfoVO.setConnectParams(oaConnectParam);
        return Result.newSuccess(connectInfoVO);
    }

    @Override
    public Result<Map<String, String>> getRuiJieInfo(String code,String serviceUrl) {
        //ticket=ST-368-gChqIqVuq9j83YCG9dw4sh1KDaMrg-sso-fddcfb8db-z9ng2&service=http://192.168.54.63:8080/user/index
        String completeUrl=oauth2Url.concat("p3/serviceValidate?ticket=").concat(code).concat("&service=").concat(serviceUrl);
        String result = proxyHttpClient.doAndLog("730173",completeUrl,null,Collections.emptyMap(),RequestMethod.GET.name(),()->proxyHttpClient.getUrl(completeUrl, Maps.newHashMap()));
        String error = XmlUtils.getTextForElement(result, "authenticationSuccess");
        if(StringUtils.isEmpty(error)){
            //TODO 返回鉴权失败
        }
        log.info("ruijie result:{}",result);
        String user = XmlUtils.getTextForElement(result, "user");
        String userAccount = XmlUtils.getTextForElement(result, "GH");
        String userName = XmlUtils.getTextForElement(result, "RJXM");
        String email = XmlUtils.getTextForElement(result, "RJEMAIL");
        String tel = XmlUtils.getTextForElement(result, "TEL");
        String employeeNumber = XmlUtils.getTextForElement(result, "RJGH");
        Map<String,String> userMap=Maps.newHashMap();
        userMap.put("user",user);
        userMap.put("userAccount",userAccount);
        userMap.put("userName",userName);
        userMap.put("email",email);
        userMap.put("tel",tel);
        userMap.put("employeeNumber",employeeNumber);
        return Result.newSuccess(userMap);
    }

    @Override
    public Result<String> getRuiJieRedirectUrl(String requestId) {
        String url = oauth2Url.concat("login?service=")
                .concat(oauthRedirectUrl).concat("&state=")
                .concat(requestId);
        return Result.newSuccess(url);
    }

    @Override
    public Result<String> getRuiJieLogOutUrl(String requestId) {
        String logOutUrl=oauth2Url.concat("logout?service=").concat(ConfigCenter.ERP_DOMAIN_URL)
                .concat("/erp/syncdata/open/saml/oauth2code");
        return Result.newSuccess(logOutUrl);
    }


    @Override
    public Result<String> detailAuth(Map<String, Object> params,String tenantId, HttpServletRequest request) {
        log.info("detailAuth params:{} ", params);
        Map<String,Object> requestParams = (Map<String, Object>) params.get("requestParams");
        String ei = (String) requestParams.get("ei");
        Boolean isApp = (Boolean) requestParams.get("isApp");
        String apiName = (String) requestParams.get("apiName");
        String dataId = (String) requestParams.get("dataId");
        //获取连接配置信息、APL代码配置等
        OAConnectInfoEntity tenantOAInfo = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,null);
        if(ObjectUtils.isEmpty(tenantOAInfo)){
            return Result.newError(ResultCodeEnum.OA_UN_CONNECT);
        }
        OAConnectParam oaConnectParam = GsonUtil.fromJson(tenantOAInfo.getConnectParams(), OAConnectParam.class);
        List<OAAplApiName> aplApiNames = oaConnectParam.getAplApiNames();
        String aplApiname = null;
        for (OAAplApiName item: aplApiNames) {
            if(OAAPLTypeEnum.DATADETAIL.getType().equals(item.getType())){
                aplApiname = item.getApiName();
            }
        }
        if(aplApiname == null){
            return Result.newSystemError(I18NStringEnum.s1247);
        }

        Map<String, Object> dataValueMap = new HashMap<>();
        dataValueMap.put("params",params);
        dataValueMap.put("oaConnectParam", oaConnectParam);
        // 执行APL代码
        Result<Map<String,Object>> execFuncResult= null;
        try{
            execFuncResult = executeFunction(ei, aplApiname, dataValueMap);
        }catch (Exception e){
            log.error(ResultCodeEnum.CUSTOM_FUNC_ERROR + "e:{}" + e.getMessage());
            return Result.newError(ResultCodeEnum.CUSTOM_FUNC_ERROR);
        }
        log.info("execFuncResult:{}", execFuncResult);

        if(!execFuncResult.isSuccess()){
            return Result.newError(execFuncResult.getErrMsg());
        }
        String redirectUrl = null;
        Map<String,Object> funcResultData = execFuncResult.getData();

        if(funcResultData.get("oaUser") != null){  //返回oaUser则转成成纷享用户ID，并生成单点登录链接
            String fxUserCode = userManager.getFxUserCode(String.valueOf(funcResultData.get("oaUser")), ei);
            if(fxUserCode == null){
                //funcResultData.get("oaUser") + "在账号绑定无映射关系"
                String oaUser = funcResultData.get("oaUser")!=null ? funcResultData.get("oaUser").toString() : null;
                String msg = i18NStringManager.get2(I18NStringEnum.s1256.getI18nKey(),
                        null,
                        ei,
                        String.format(I18NStringEnum.s1256.getI18nValue(), oaUser),
                        Lists.newArrayList(oaUser));

                return Result.newErrorByI18N(msg,
                        I18NStringEnum.s1256.getI18nKey(),
                        Lists.newArrayList(oaUser));
            }
            Result<String> redirectResult=null;
            if(funcResultData.get("redirectUrl")!=null){
                redirectResult =  userLoginManager.avoidLogin(Integer.parseInt(ei),fxUserCode,funcResultData.get("redirectUrl").toString(),request);
            }else{
                redirectResult =  userLoginManager.avoidLogin(Integer.parseInt(ei), fxUserCode, apiName, dataId, isApp,request);
            }
            if(!redirectResult.isSuccess()){
                return redirectResult;
            }
            redirectUrl = redirectResult.getData();
        }else if("1".equals(funcResultData.get("requireCallback"))){ //requireCallback为true，则重定向到redirectUrl
            redirectUrl = String.valueOf(funcResultData.get("redirectUrl"));
        }else {
           return Result.newSystemError(I18NStringEnum.s1255, JSON.toJSONString(funcResultData));
        }
        return Result.newSuccess(redirectUrl);
    }

    @Override
    public Result<String> commonRedirectUrl(Map<String, Object> params, HttpServletRequest request) {
        log.info("detailAuth params:{} ", params);
        Map<String,Object> requestParams = (Map<String, Object>) params.get("requestParams");
        String ei = (String) requestParams.get("ei");
        String dataCenterId = (String) requestParams.get("dataCenterId");//可能为空
        //获取连接配置信息、APL代码配置等
        OAConnectInfoEntity tenantOAInfo = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).getOAConnectInfoById(ei,dataCenterId);
        if(ObjectUtils.isEmpty(tenantOAInfo)){
            return Result.newError(ResultCodeEnum.OA_UN_CONNECT);
        }
        OAConnectParam oaConnectParam = GsonUtil.fromJson(tenantOAInfo.getConnectParams(), OAConnectParam.class);
        List<OAAplApiName> aplApiNames = oaConnectParam.getAplApiNames();
        String aplApiname = null;
        for (OAAplApiName item: aplApiNames) {
            if(OAAPLTypeEnum.DATADETAIL.getType().equals(item.getType())){
                aplApiname = item.getApiName();
            }
        }
        if(aplApiname == null){
            return Result.newSystemError(I18NStringEnum.s1247);
        }

        Map<String, Object> dataValueMap = new HashMap<>();
        dataValueMap.put("params",params);
        dataValueMap.put("oaConnectParam", oaConnectParam);
        // 执行APL代码
        Result<Map<String,Object>> execFuncResult= null;
        try{
            execFuncResult = executeFunction(ei, aplApiname, dataValueMap);
        }catch (Exception e){
            log.error(ResultCodeEnum.CUSTOM_FUNC_ERROR + "e:{}" + e.getMessage());
            return Result.newError(ResultCodeEnum.CUSTOM_FUNC_ERROR);
        }
        log.info("execFuncResult:{}", execFuncResult);

        if(!execFuncResult.isSuccess()){
            return Result.newError(execFuncResult.getErrMsg());
        }
        String redirectUrl = null;
        Map<String,Object> funcResultData = execFuncResult.getData();

        if(funcResultData.get("oaUser") != null){  //返回oaUser则转成成纷享用户ID，并生成单点登录链接
            String fxUserCode = userManager.getFxUserCodeByDataCenterId(String.valueOf(funcResultData.get("oaUser")), ei,dataCenterId);
            if(fxUserCode == null){
                return Result.newErrorByI18N(funcResultData.get("oaUser") + i18NStringManager.get(I18NStringEnum.s1257,null,null),null,null);
            }
            Result<String> redirectResult=null;
            if(funcResultData.get("redirectUrl")!=null){
               redirectResult =  userLoginManager.avoidLogin(Integer.parseInt(ei),fxUserCode,funcResultData.get("redirectUrl").toString(),request);
            }else {
                String apiName = (String) requestParams.get("apiName");
                String dataId = (String) requestParams.get("dataId");
                Boolean isApp=(Boolean) requestParams.get("isApp");
                redirectResult =  userLoginManager.avoidLogin(Integer.parseInt(ei), fxUserCode, apiName, dataId, isApp, request);
            }
            if(!redirectResult.isSuccess()){
                return redirectResult;
            }
            redirectUrl = redirectResult.getData();
        }else if("1".equals(funcResultData.get("requireCallback"))){ //requireCallback为true，则重定向到redirectUrl
            redirectUrl = String.valueOf(funcResultData.get("redirectUrl"));
        }else {
            return Result.newSystemError(I18NStringEnum.s1255, JSON.toJSONString(funcResultData));
        }
        return Result.newSuccess(redirectUrl);
    }

    /**
     * 执行APL函数
     * @param ei
     * @param aplApiName
     * @param dataValueMap APL函数入参
     * @return
     */
    private Result<Map<String,Object>> executeFunction(String ei, String aplApiName, Map<String, Object> dataValueMap){
        List<FunctionServiceParameterData> functionServiceParameterDataList = new ArrayList<>();
        FunctionServiceParameterData<Map> functionServiceParameterData = new FunctionServiceParameterData();
        functionServiceParameterData.setName(CustomFunctionConstant.SYNC_ARG_NAME);
        functionServiceParameterData.setType(CustomFunctionConstant.SYNC_ARG_TYPE_MAP);
        functionServiceParameterData.setValue(dataValueMap);
        functionServiceParameterDataList.add(functionServiceParameterData);

        FunctionServiceExecuteArg functionServiceExecuteArg = new FunctionServiceExecuteArg();
        functionServiceExecuteArg.setApiName(aplApiName);
        functionServiceExecuteArg.setNameSpace(CustomFunctionConstantEx.NAME_SPACE);
        functionServiceExecuteArg.setBindingObjectAPIName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        functionServiceExecuteArg.setParameters(functionServiceParameterDataList);
        HeaderObj headerObj = I18NHeaderObj.getHeader2(ei,i18NStringManager);

        ObjectData objectData= functionApiManager.executeCustomFunction(headerObj ,functionServiceExecuteArg, false);
        log.info("OAUserAuthServiceImpl.executeFunction,objectData={}",objectData);
        if(objectData==null) {
            return Result.newSystemError(I18NStringEnum.s1258);
        }
        int errCode = objectData.getInt("errCode");
        String errMessage = objectData.getString("errMessage");
        Map<String,Object> resultMap = objectData.getMap("result");

        Result2<Map<String,Object>> result = Result2.newError4CustomFunc(errCode,errMessage);

        Boolean success = MapUtils.getBoolean(resultMap,"success");
        String errorInfo = MapUtils.getString(resultMap,"errorInfo");

        if(success==false) {
            List<String> items = Splitter.on(":::").splitToList(errorInfo);
            if(CollectionUtils.isNotEmpty(items) && items.size()==2) {
                int code = 0;
                try {
                    code = Integer.valueOf(items.get(0));
                } catch (Exception e) {
                    code = errCode;
                }
                return Result2.newError(code,items.get(1));
            }
        }

        if(success!=null && success==false) {
            result.setIntErrCode(com.fxiaoke.open.oasyncdata.result.ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
            result.setErrMsg(errorInfo);
            return result;
        }
        Map<String,Object> functionResultMap;
        try {
            functionResultMap = (Map<String, Object>) resultMap.get("functionResult");
        } catch (Exception e) {
            throw new ErpSyncDataException(I18NStringEnum.s1252,ei);
        }

        if(functionResultMap==null) {
            result.setIntErrCode(com.fxiaoke.open.oasyncdata.result.ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
            result.setErrMsg(i18NStringManager.get(I18NStringEnum.s1254,null,ei));
            return result;
        }

        return Result.newSuccess(functionResultMap);
    }
}
