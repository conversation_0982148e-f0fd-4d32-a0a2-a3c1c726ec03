/*!
 * bootstrap-tabs-x v1.3.3
 * http://plugins.krajee.com/tabs-x
 *
 * Krajee jQuery plugin for bootstrap-tabs-x.
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2014 - 2017, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD 3-Clause
 * https://github.com/kartik-v/bootstrap-tabs-x/blob/master/LICENSE.md
 */!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(window.jQuery)}(function(t){"use strict";var e=function(e,a){return null===e||void 0===e||0===e.length||a&&""===t.trim(e)},a=function(e,a){var n=this;n.$element=t(e),n.init(a),n.listen()};a.prototype={constructor:a,init:function(a){var n=this,s=n.$element;t.each(a,function(t,e){n[t]=e}),n.initCache(),n.enableCache=!!n.enableCache,e(n.addCss)||s.hasClass(n.addCss)||s.addClass(n.addCss),n.$pane=s.find(".tab-pane.active"),n.$content=s.find(".tab-content"),n.$tabs=s.find(".nav-tabs"),n.isVertical=s.hasClass("tabs-left")||s.hasClass("tabs-right"),n.isVerticalSide=n.isVertical&&s.hasClass("tab-sideways"),n.isVertical&&n.$content.css("min-height",n.$tabs.outerHeight()+1+"px")},setTitle:function(a){var n=this,s=t.trim(a.text()),i=n.isVertical,c=e(a.data("maxTitleLength"))?n.maxTitleLength:a.data("maxTitleLength");i&&s.length>c-2&&e(a.attr("title"))&&a.attr("title",s)},listen:function(){var a=this,n=a.$element;n.find(".nav-tabs li.disabled").each(function(){t(this).find('[data-toggle="tab"]').removeAttr("data-toggle")}),n.find('.nav-tabs li [data-toggle="dropdown"]').each(function(){a.setTitle(t(this))}),n.find('.nav-tabs li [data-toggle="tab"]').each(function(){var n=t(this),s=n.closest("li");s.removeAttr("data-toggle"),a.setTitle(n),n.on("click",function(i){if(s.hasClass("disabled"))return void i.preventDefault();var c,r=t(this).attr("data-url"),o=this.hash,l=r+o;if(e(r)||a.enableCache&&a.cache.exist(l))return void n.trigger("tabsX:click");i.preventDefault();var d=t(o),h=t(this),u=h,f=t(this).attr("data-loading-class")||"kv-tab-loading",b=h.closest(".dropdown"),g=a.successCallback[o]||null,p=a.errorCallback[o]||null;e(b.attr("class"))||(u=b.find(".dropdown-toggle")),c=t.extend(!0,{},{type:"post",dataType:"json",url:r,beforeSend:function(t,e){d.html("<br><br><br>"),u.removeClass(f).addClass(f),n.trigger("tabsX:beforeSend",[t,e])},success:function(t,e,s){setTimeout(function(){d.html(t),h.tab("show"),u.removeClass(f),a.enableCache&&a.cache.set(l),g&&"function"==typeof g&&g(t,e,s),n.trigger("tabsX:success",[t,e,s])},300)},error:function(t,e,a){p&&"function"==typeof p&&p(t,e,a),n.trigger("tabsX:error",[t,e,a])},complete:function(t,e){n.trigger("tabsX:click",[t,e])}},a.ajaxSettings),t.ajax(c)})})},initCache:function(){var t=this,e=parseFloat(t.cacheTimeout);isNaN(e)&&(e=0),t.cache={data:{},create:function(){return(new Date).getTime()},exist:function(a){return!!t.cache.data[a]&&t.cache.create()-t.cache.data[a]<e},set:function(e){t.cache.data[e]=t.cache.create()}}},flushCache:function(t){var a=this;"string"==typeof t&&(t=[t]),"object"!=typeof t||e(t)?a.cache.data={}:Object.values(t).forEach(function(t){Object.keys(a.cache.data).forEach(function(e){e.endsWith(t)&&delete a.cache.data[e]})})}},t.fn.tabsX=function(e){var n=Array.apply(null,arguments),s=[];switch(n.shift(),this.each(function(){var i=t(this),c=i.data("tabsX"),r="object"==typeof e&&e;c||(c=new a(this,t.extend(!0,{},t.fn.tabsX.defaults,r,t(this).data())),i.data("tabsX",c)),"string"==typeof e&&s.push(c[e].apply(c,n))}),s.length){case 0:return this;case 1:return s[0];default:return s}},t.fn.tabsX.defaults={enableCache:!0,cacheTimeout:3e5,maxTitleLength:9,ajaxSettings:{},successCallback:{},errorCallback:{},addCss:"tabs-krajee"},t.fn.tabsX.Constructor=a,t(document).on("ready",function(){t(".tabs-x").tabsX({})})});