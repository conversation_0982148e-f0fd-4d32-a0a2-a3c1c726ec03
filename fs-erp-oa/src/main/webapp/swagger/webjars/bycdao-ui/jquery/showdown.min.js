/*! showdown v 2.0.0-alpha1 - 25-09-2018 */
(function(){function a(e){"use strict";var r={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(r));var t={};for(var a in r)r.hasOwnProperty(a)&&(t[a]=r[a].defaultValue);return t}var b={},t={},h={},m=a(!0),d="vanilla",p={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:a(!0),allOn:function(){"use strict";var e=a(!0),r={};for(var t in e)e.hasOwnProperty(t)&&(r[t]=!0);return r}()};function g(e,r){"use strict";var t=r?"Error in "+r+" extension->":"Error in unnamed extension",a={valid:!0,error:""};b.helper.isArray(e)||(e=[e]);for(var n=0;n<e.length;++n){var s=t+" sub-extension "+n+": ",o=e[n];if("object"!=typeof o)return a.valid=!1,a.error=s+"must be an object, but "+typeof o+" given",a;if(!b.helper.isString(o.type))return a.valid=!1,a.error=s+'property "type" must be a string, but '+typeof o.type+" given",a;var i=o.type=o.type.toLowerCase();if("language"===i&&(i=o.type="lang"),"html"===i&&(i=o.type="output"),"lang"!==i&&"output"!==i&&"listener"!==i)return a.valid=!1,a.error=s+"type "+i+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',a;if("listener"===i){if(b.helper.isUndefined(o.listeners))return a.valid=!1,a.error=s+'. Extensions of type "listener" must have a property called "listeners"',a}else if(b.helper.isUndefined(o.filter)&&b.helper.isUndefined(o.regex))return a.valid=!1,a.error=s+i+' extensions must define either a "regex" property or a "filter" method',a;if(o.listeners){if("object"!=typeof o.listeners)return a.valid=!1,a.error=s+'"listeners" property must be an object but '+typeof o.listeners+" given",a;for(var l in o.listeners)if(o.listeners.hasOwnProperty(l)&&"function"!=typeof o.listeners[l])return a.valid=!1,a.error=s+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+l+" must be a function but "+typeof o.listeners[l]+" given",a}if(o.filter){if("function"!=typeof o.filter)return a.valid=!1,a.error=s+'"filter" must be a function, but '+typeof o.filter+" given",a}else if(o.regex){if(b.helper.isString(o.regex)&&(o.regex=new RegExp(o.regex,"g")),!(o.regex instanceof RegExp))return a.valid=!1,a.error=s+'"regex" property must either be a string or a RegExp object, but '+typeof o.regex+" given",a;if(b.helper.isUndefined(o.replace))return a.valid=!1,a.error=s+'"regex" extensions must implement a replace string or function',a}}return a}if(b.helper={},b.extensions={},b.setOption=function(e,r){"use strict";return m[e]=r,this},b.getOption=function(e){"use strict";return m[e]},b.getOptions=function(){"use strict";return m},b.resetOptions=function(){"use strict";m=a(!0)},b.setFlavor=function(e){"use strict";if(!p.hasOwnProperty(e))throw Error(e+" flavor was not found");b.resetOptions();var r=p[e];for(var t in d=e,r)r.hasOwnProperty(t)&&(m[t]=r[t])},b.getFlavor=function(){"use strict";return d},b.getFlavorOptions=function(e){"use strict";if(p.hasOwnProperty(e))return p[e]},b.getDefaultOptions=function(e){"use strict";return a(e)},b.subParser=function(e,r){"use strict";if(!b.helper.isString(e))throw Error("showdown.subParser function first argument must be a string (the name of the subparser)");if(void 0===r){if(t.hasOwnProperty(e))return t[e];throw Error("SubParser named "+e+" not registered!")}t[e]=r},b.extension=function(e,r){"use strict";if(!b.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=b.helper.stdExtName(e),b.helper.isUndefined(r)){if(!h.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return h[e]}"function"==typeof r&&(r=r()),b.helper.isArray(r)||(r=[r]);var t=g(r,e);if(!t.valid)throw Error(t.error);h[e]=r},b.getAllExtensions=function(){"use strict";return h},b.removeExtension=function(e){"use strict";delete h[e]},b.resetExtensions=function(){"use strict";h={}},b.validateExtension=function(e){"use strict";var r=g(e,null);return!!r.valid||(console.warn(r.error),!1)},b.hasOwnProperty("helper")||(b.helper={}),void 0===this.document&&void 0===this.window){var e=require("jsdom");this.window=new e.JSDOM("",{}).window}function s(e,r){"use strict";return"¨E"+r.charCodeAt(0)+"E"}b.helper.document=this.window.document,b.helper.isString=function(e){"use strict";return"string"==typeof e||e instanceof String},b.helper.isFunction=function(e){"use strict";return e&&"[object Function]"==={}.toString.call(e)},b.helper.isArray=function(e){"use strict";return Array.isArray(e)},b.helper.isUndefined=function(e){"use strict";return void 0===e},b.helper.forEach=function(e,r){"use strict";if(b.helper.isUndefined(e))throw new Error("obj param is required");if(b.helper.isUndefined(r))throw new Error("callback param is required");if(!b.helper.isFunction(r))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(r);else if(b.helper.isArray(e))for(var t=0;t<e.length;t++)r(e[t],t,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var a in e)e.hasOwnProperty(a)&&r(e[a],a,e)}},b.helper.stdExtName=function(e){"use strict";return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},b.helper.escapeCharactersCallback=s,b.helper.escapeCharacters=function(e,r,t){"use strict";var a="(["+r.replace(/([\[\]\\])/g,"\\$1")+"])";t&&(a="\\\\"+a);var n=new RegExp(a,"g");return e=e.replace(n,s)};var _=function(e,r,t,a){"use strict";var n,s,o,i,l,c=a||"",u=-1<c.indexOf("g"),h=new RegExp(r+"|"+t,"g"+c.replace(/g/g,"")),m=new RegExp(r,c.replace(/g/g,"")),d=[];do{for(n=0;o=h.exec(e);)if(m.test(o[0]))n++||(i=(s=h.lastIndex)-o[0].length);else if(n&&!--n){l=o.index+o[0].length;var p={left:{start:i,end:s},match:{start:s,end:o.index},right:{start:o.index,end:l},wholeMatch:{start:i,end:l}};if(d.push(p),!u)return d}}while(n&&(h.lastIndex=s));return d};b.helper.matchRecursiveRegExp=function(e,r,t,a){"use strict";for(var n=_(e,r,t,a),s=[],o=0;o<n.length;++o)s.push([e.slice(n[o].wholeMatch.start,n[o].wholeMatch.end),e.slice(n[o].match.start,n[o].match.end),e.slice(n[o].left.start,n[o].left.end),e.slice(n[o].right.start,n[o].right.end)]);return s},b.helper.replaceRecursiveRegExp=function(e,r,t,a,n){"use strict";if(!b.helper.isFunction(r)){var s=r;r=function(){return s}}var o=_(e,t,a,n),i=e,l=o.length;if(0<l){var c=[];0!==o[0].wholeMatch.start&&c.push(e.slice(0,o[0].wholeMatch.start));for(var u=0;u<l;++u)c.push(r(e.slice(o[u].wholeMatch.start,o[u].wholeMatch.end),e.slice(o[u].match.start,o[u].match.end),e.slice(o[u].left.start,o[u].left.end),e.slice(o[u].right.start,o[u].right.end))),u<l-1&&c.push(e.slice(o[u].wholeMatch.end,o[u+1].wholeMatch.start));o[l-1].wholeMatch.end<e.length&&c.push(e.slice(o[l-1].wholeMatch.end)),i=c.join("")}return i},b.helper.regexIndexOf=function(e,r,t){"use strict";if(!b.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(r instanceof RegExp==!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var a=e.substring(t||0).search(r);return 0<=a?a+(t||0):a},b.helper.splitAtIndex=function(e,r){"use strict";if(!b.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,r),e.substring(r)]},b.helper.encodeEmailAddress=function(e){"use strict";var t=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,function(e){if("@"===e)e=t[Math.floor(2*Math.random())](e);else{var r=Math.random();e=.9<r?t[2](e):.45<r?t[1](e):t[0](e)}return e})},b.helper.padEnd=function(e,r,t){"use strict";return r>>=0,t=String(t||" "),e.length>r?String(e):((r-=e.length)>t.length&&(t+=t.repeat(r/t.length)),String(e)+t.slice(0,r))},b.helper.unescapeHTMLEntities=function(e){"use strict";return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")},b.helper._hashHTMLSpan=function(e,r){return"¨C"+(r.gHtmlSpans.push(e)-1)+"C"},b.helper.Event=function(e,r,t){"use strict";var a=t.regexp||null,n=t.matches||{},s=t.options||{},o=t.converter||null,i=t.globals||{};this.getName=function(){return e},this.getEventName=function(){return e},this._stopExecution=!1,this.parsedText=t.parsedText||null,this.getRegexp=function(){return a},this.getOptions=function(){return s},this.getConverter=function(){return o},this.getGlobals=function(){return i},this.getCapturedText=function(){return r},this.getText=function(){return r},this.setText=function(e){r=e},this.getMatches=function(){return n},this.setMatches=function(e){n=e},this.preventDefault=function(e){this._stopExecution=!e}},"undefined"==typeof console&&(console={warn:function(e){"use strict";alert(e)},log:function(e){"use strict";alert(e)},error:function(e){"use strict";throw e}}),b.helper.regexes={asteriskDashTildeAndColon:/([*_:~])/g,asteriskDashAndTilde:/([*_~])/g},b.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img width="20" height="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:'<img width="20" height="20" align="absmiddle" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAS1BMVEX///8jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS3b1q3b1q3b1q3b1q3b1q3b1q3b1q3b1q0565CIAAAAGXRSTlMAQHCAYCCw/+DQwPCQUBAwoHCAEP+wwFBgS2fvBgAAAUZJREFUeAHs1cGy7BAUheFFsEDw/k97VTq3T6ge2EmdM+pvrP6Iwd74XV9Kb52xuMU4/uc1YNgZLFOeV8FGdhGrNk5SEgUyPxAEdj4LlMRDyhVAMVEa2M7TBSeVZAFPdqHgzSZJwPKgcLFLAooHDJo4EDCw4gAtBoJA5UFj4Ng5LOGLwVXZuoIlji/jeQHFk7+baHxrCjeUwB9+s88KndvlhcyBN5BSkYNQIVVb4pV+Npm7hhuKDs/uMP5KxT3WzSNNLIuuoDpMmuAVMruMSeDyQBi24DTr43LAY7ILA1QYaWkgfHzFthYYzg67SQsCbB8GhJUEGCtO9n0rSaCLxgJQjS/JSgMTg2eBDEHAJ+H350AsjYNYscrErgI2e/l+mdR967TCX/v6N0EhPECYCP0i+IAoYQOE8BogNhQMEMdrgAQWHaMAAGi5I5euoY9NAAAAAElFTkSuQmCC">'},b.subParser("makehtml.blockGamut",function(e,r,t){"use strict";return e=t.converter._dispatch("makehtml.blockGamut.before",e,r,t).getText(),e=b.subParser("makehtml.blockQuotes")(e,r,t),e=b.subParser("makehtml.headers")(e,r,t),e=b.subParser("makehtml.horizontalRule")(e,r,t),e=b.subParser("makehtml.lists")(e,r,t),e=b.subParser("makehtml.codeBlocks")(e,r,t),e=b.subParser("makehtml.tables")(e,r,t),e=b.subParser("makehtml.hashHTMLBlocks")(e,r,t),e=b.subParser("makehtml.paragraphs")(e,r,t),e=t.converter._dispatch("makehtml.blockGamut.after",e,r,t).getText()}),b.subParser("makehtml.blockQuotes",function(e,r,t){"use strict";e=t.converter._dispatch("makehtml.blockQuotes.before",e,r,t).getText(),e+="\n\n";var a=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return r.splitAdjacentBlockquotes&&(a=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=e.replace(a,function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=b.subParser("makehtml.githubCodeBlocks")(e,r,t),e=(e=(e=b.subParser("makehtml.blockGamut")(e,r,t)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,function(e,r){var t=r;return t=(t=t.replace(/^  /gm,"¨0")).replace(/¨0/g,"")}),b.subParser("makehtml.hashBlock")("<blockquote>\n"+e+"\n</blockquote>",r,t)}),e=t.converter._dispatch("makehtml.blockQuotes.after",e,r,t).getText()}),b.subParser("makehtml.codeBlocks",function(e,o,i){"use strict";e=i.converter._dispatch("makehtml.codeBlocks.before",e,o,i).getText();return e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,function(e,r,t){var a=r,n=t,s="\n";return a=b.subParser("makehtml.outdent")(a,o,i),a=b.subParser("makehtml.encodeCode")(a,o,i),a=(a=(a=b.subParser("makehtml.detab")(a,o,i)).replace(/^\n+/g,"")).replace(/\n+$/g,""),o.omitExtraWLInCodeBlocks&&(s=""),a="<pre><code>"+a+s+"</code></pre>",b.subParser("makehtml.hashBlock")(a,o,i)+n})).replace(/¨0/,""),e=i.converter._dispatch("makehtml.codeBlocks.after",e,o,i).getText()}),b.subParser("makehtml.codeSpans",function(e,s,o){"use strict";return void 0===(e=o.converter._dispatch("makehtml.codeSpans.before",e,s,o).getText())&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,function(e,r,t,a){var n=a;return n=(n=n.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),n=r+"<code>"+(n=b.subParser("makehtml.encodeCode")(n,s,o))+"</code>",n=b.subParser("makehtml.hashHTMLSpans")(n,s,o)}),e=o.converter._dispatch("makehtml.codeSpans.after",e,s,o).getText()}),b.subParser("makehtml.completeHTMLDocument",function(e,r,t){"use strict";if(!r.completeHTMLDocument)return e;e=t.converter._dispatch("makehtml.completeHTMLDocument.before",e,r,t).getText();var a="html",n="<!DOCTYPE HTML>\n",s="",o='<meta charset="utf-8">\n',i="",l="";for(var c in void 0!==t.metadata.parsed.doctype&&(n="<!DOCTYPE "+t.metadata.parsed.doctype+">\n","html"!==(a=t.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==a||(o='<meta charset="utf-8">')),t.metadata.parsed)if(t.metadata.parsed.hasOwnProperty(c))switch(c.toLowerCase()){case"doctype":break;case"title":s="<title>"+t.metadata.parsed.title+"</title>\n";break;case"charset":o="html"===a||"html5"===a?'<meta charset="'+t.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+t.metadata.parsed.charset+'">\n';break;case"language":case"lang":i=' lang="'+t.metadata.parsed[c]+'"',l+='<meta name="'+c+'" content="'+t.metadata.parsed[c]+'">\n';break;default:l+='<meta name="'+c+'" content="'+t.metadata.parsed[c]+'">\n'}return e=n+"<html"+i+">\n<head>\n"+s+o+l+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",e=t.converter._dispatch("makehtml.completeHTMLDocument.after",e,r,t).getText()}),b.subParser("makehtml.detab",function(e,r,t){"use strict";return e=(e=(e=(e=(e=(e=t.converter._dispatch("makehtml.detab.before",e,r,t).getText()).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,function(e,r){for(var t=r,a=4-t.length%4,n=0;n<a;n++)t+=" ";return t})).replace(/¨A/g,"    ")).replace(/¨B/g,""),e=t.converter._dispatch("makehtml.detab.after",e,r,t).getText()}),b.subParser("makehtml.ellipsis",function(e,r,t){"use strict";return e=(e=t.converter._dispatch("makehtml.ellipsis.before",e,r,t).getText()).replace(/\.\.\./g,"…"),e=t.converter._dispatch("makehtml.ellipsis.after",e,r,t).getText()}),b.subParser("makehtml.emoji",function(e,r,t){"use strict";if(!r.emoji)return e;return e=(e=t.converter._dispatch("makehtml.emoji.before",e,r,t).getText()).replace(/:([\S]+?):/g,function(e,r){return b.helper.emojis.hasOwnProperty(r)?b.helper.emojis[r]:e}),e=t.converter._dispatch("makehtml.emoji.after",e,r,t).getText()}),b.subParser("makehtml.encodeAmpsAndAngles",function(e,r,t){"use strict";return e=(e=(e=(e=(e=t.converter._dispatch("makehtml.encodeAmpsAndAngles.before",e,r,t).getText()).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),e=t.converter._dispatch("makehtml.encodeAmpsAndAngles.after",e,r,t).getText()}),b.subParser("makehtml.encodeBackslashEscapes",function(e,r,t){"use strict";return e=(e=(e=t.converter._dispatch("makehtml.encodeBackslashEscapes.before",e,r,t).getText()).replace(/\\(\\)/g,b.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|:-])/g,b.helper.escapeCharactersCallback),e=t.converter._dispatch("makehtml.encodeBackslashEscapes.after",e,r,t).getText()}),b.subParser("makehtml.encodeCode",function(e,r,t){"use strict";return e=(e=t.converter._dispatch("makehtml.encodeCode.before",e,r,t).getText()).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,b.helper.escapeCharactersCallback),e=t.converter._dispatch("makehtml.encodeCode.after",e,r,t).getText()}),b.subParser("makehtml.escapeSpecialCharsWithinTagAttributes",function(e,r,t){"use strict";return e=(e=(e=t.converter._dispatch("makehtml.escapeSpecialCharsWithinTagAttributes.before",e,r,t).getText()).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,b.helper.escapeCharactersCallback)})).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,function(e){return e.replace(/([\\`*_~=|])/g,b.helper.escapeCharactersCallback)}),e=t.converter._dispatch("makehtml.escapeSpecialCharsWithinTagAttributes.after",e,r,t).getText()}),b.subParser("makehtml.githubCodeBlocks",function(e,s,o){"use strict";return s.ghCodeBlocks?(e=o.converter._dispatch("makehtml.githubCodeBlocks.before",e,s,o).getText(),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,function(e,r,t,a){var n=s.omitExtraWLInCodeBlocks?"":"\n";return a=b.subParser("makehtml.encodeCode")(a,s,o),a="<pre><code"+(t?' class="'+t+" language-"+t+'"':"")+">"+(a=(a=(a=b.subParser("makehtml.detab")(a,s,o)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+n+"</code></pre>",a=b.subParser("makehtml.hashBlock")(a,s,o),"\n\n¨G"+(o.ghCodeBlocks.push({text:e,codeblock:a})-1)+"G\n\n"})).replace(/¨0/,""),o.converter._dispatch("makehtml.githubCodeBlocks.after",e,s,o).getText()):e}),b.subParser("makehtml.hashBlock",function(e,r,t){"use strict";return e=(e=t.converter._dispatch("makehtml.hashBlock.before",e,r,t).getText()).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(t.gHtmlBlocks.push(e)-1)+"K\n\n",e=t.converter._dispatch("makehtml.hashBlock.after",e,r,t).getText()}),b.subParser("makehtml.hashCodeTags",function(e,s,o){"use strict";e=o.converter._dispatch("makehtml.hashCodeTags.before",e,s,o).getText();return e=b.helper.replaceRecursiveRegExp(e,function(e,r,t,a){var n=t+b.subParser("makehtml.encodeCode")(r,s,o)+a;return"¨C"+(o.gHtmlSpans.push(n)-1)+"C"},"<code\\b[^>]*>","</code>","gim"),e=o.converter._dispatch("makehtml.hashCodeTags.after",e,s,o).getText()}),b.subParser("makehtml.hashElement",function(e,r,a){"use strict";return function(e,r){var t=r;return t=(t=(t=t.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),t="\n\n¨K"+(a.gHtmlBlocks.push(t)-1)+"K\n\n"}}),b.subParser("makehtml.hashHTMLBlocks",function(e,r,s){"use strict";e=s.converter._dispatch("makehtml.hashHTMLBlocks.before",e,r,s).getText();var t=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],a=function(e,r,t,a){var n=e;return-1!==t.search(/\bmarkdown\b/)&&(n=t+s.converter.makeHtml(r)+a),"\n\n¨K"+(s.gHtmlBlocks.push(n)-1)+"K\n\n"};r.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,function(e,r){return"&lt;"+r+"&gt;"}));for(var n=0;n<t.length;++n)for(var o,i=new RegExp("^ {0,3}(<"+t[n]+"\\b[^>]*>)","im"),l="<"+t[n]+"\\b[^>]*>",c="</"+t[n]+">";-1!==(o=b.helper.regexIndexOf(e,i));){var u=b.helper.splitAtIndex(e,o),h=b.helper.replaceRecursiveRegExp(u[1],a,l,c,"im");if(h===u[1])break;e=u[0].concat(h)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,b.subParser("makehtml.hashElement")(e,r,s)),e=(e=b.helper.replaceRecursiveRegExp(e,function(e){return"\n\n¨K"+(s.gHtmlBlocks.push(e)-1)+"K\n\n"},"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,b.subParser("makehtml.hashElement")(e,r,s)),e=s.converter._dispatch("makehtml.hashHTMLBlocks.after",e,r,s).getText()}),b.subParser("makehtml.hashHTMLSpans",function(e,r,t){"use strict";return e=(e=(e=(e=(e=t.converter._dispatch("makehtml.hashHTMLSpans.before",e,r,t).getText()).replace(/<[^>]+?\/>/gi,function(e){return b.helper._hashHTMLSpan(e,t)})).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,function(e){return b.helper._hashHTMLSpan(e,t)})).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,function(e){return b.helper._hashHTMLSpan(e,t)})).replace(/<[^>]+?>/gi,function(e){return b.helper._hashHTMLSpan(e,t)}),e=t.converter._dispatch("makehtml.hashHTMLSpans.after",e,r,t).getText()}),b.subParser("makehtml.unhashHTMLSpans",function(e,r,t){"use strict";e=t.converter._dispatch("makehtml.unhashHTMLSpans.before",e,r,t).getText();for(var a=0;a<t.gHtmlSpans.length;++a){for(var n=t.gHtmlSpans[a],s=0;/¨C(\d+)C/.test(n);){var o=RegExp.$1;if(n=n.replace("¨C"+o+"C",t.gHtmlSpans[o]),10===s){console.error("maximum nesting of 10 spans reached!!!");break}++s}e=e.replace("¨C"+a+"C",n)}return e=t.converter._dispatch("makehtml.unhashHTMLSpans.after",e,r,t).getText()}),b.subParser("makehtml.hashPreCodeTags",function(e,s,o){"use strict";e=o.converter._dispatch("makehtml.hashPreCodeTags.before",e,s,o).getText();return e=b.helper.replaceRecursiveRegExp(e,function(e,r,t,a){var n=t+b.subParser("makehtml.encodeCode")(r,s,o)+a;return"\n\n¨G"+(o.ghCodeBlocks.push({text:e,codeblock:n})-1)+"G\n\n"},"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=o.converter._dispatch("makehtml.hashPreCodeTags.after",e,s,o).getText()}),b.subParser("makehtml.headers",function(e,l,c){"use strict";e=c.converter._dispatch("makehtml.headers.before",e,l,c).getText();var u=isNaN(parseInt(l.headerLevelStart))?1:parseInt(l.headerLevelStart),r=l.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,t=l.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=(e=e.replace(r,function(e,r){var t=b.subParser("makehtml.spanGamut")(r,l,c),a=l.noHeaderId?"":' id="'+h(r)+'"',n="<h"+u+a+">"+t+"</h"+u+">";return b.subParser("makehtml.hashBlock")(n,l,c)})).replace(t,function(e,r){var t=b.subParser("makehtml.spanGamut")(r,l,c),a=l.noHeaderId?"":' id="'+h(r)+'"',n=u+1,s="<h"+n+a+">"+t+"</h"+n+">";return b.subParser("makehtml.hashBlock")(s,l,c)});var a=l.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function h(e){var r,t;if(l.customizedHeaderId){var a=e.match(/\{([^{]+?)}\s*$/);a&&a[1]&&(e=a[1])}return r=e,t=b.helper.isString(l.prefixHeaderId)?l.prefixHeaderId:!0===l.prefixHeaderId?"section-":"",l.rawPrefixHeaderId||(r=t+r),r=l.ghCompatibleHeaderId?r.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():l.rawHeaderId?r.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():r.replace(/[^\w]/g,"").toLowerCase(),l.rawPrefixHeaderId&&(r=t+r),c.hashLinkCounts[r]?r=r+"-"+c.hashLinkCounts[r]++:c.hashLinkCounts[r]=1,r}return e=e.replace(a,function(e,r,t){var a=t;l.customizedHeaderId&&(a=t.replace(/\s?\{([^{]+?)}\s*$/,""));var n=b.subParser("makehtml.spanGamut")(a,l,c),s=l.noHeaderId?"":' id="'+h(t)+'"',o=u-1+r.length,i="<h"+o+s+">"+n+"</h"+o+">";return b.subParser("makehtml.hashBlock")(i,l,c)}),e=c.converter._dispatch("makehtml.headers.after",e,l,c).getText()}),b.subParser("makehtml.horizontalRule",function(e,r,t){"use strict";e=t.converter._dispatch("makehtml.horizontalRule.before",e,r,t).getText();var a=b.subParser("makehtml.hashBlock")("<hr />",r,t);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,a)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,a)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,a),e=t.converter._dispatch("makehtml.horizontalRule.after",e,r,t).getText()}),b.subParser("makehtml.images",function(e,r,m){"use strict";function l(e,r,t,a,n,s,o,i){var l=m.gUrls,c=m.gTitles,u=m.gDimensions;if(t=t.toLowerCase(),i||(i=""),-1<e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))a="";else if(""===a||null===a){if(""!==t&&null!==t||(t=r.toLowerCase().replace(/ ?\n/g," ")),a="#"+t,b.helper.isUndefined(l[t]))return e;a=l[t],b.helper.isUndefined(c[t])||(i=c[t]),b.helper.isUndefined(u[t])||(n=u[t].width,s=u[t].height)}r=r.replace(/"/g,"&quot;").replace(b.helper.regexes.asteriskDashTildeAndColon,b.helper.escapeCharactersCallback);var h='<img src="'+(a=a.replace(b.helper.regexes.asteriskDashTildeAndColon,b.helper.escapeCharactersCallback))+'" alt="'+r+'"';return i&&b.helper.isString(i)&&(h+=' title="'+(i=i.replace(/"/g,"&quot;").replace(b.helper.regexes.asteriskDashTildeAndColon,b.helper.escapeCharactersCallback))+'"'),n&&s&&(h+=' width="'+(n="*"===n?"auto":n)+'"',h+=' height="'+(s="*"===s?"auto":s)+'"'),h+=" />"}return e=(e=(e=(e=(e=(e=m.converter._dispatch("makehtml.images.before",e,r,m).getText()).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,l)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,function(e,r,t,a,n,s,o,i){return l(e,r,t,a=a.replace(/\s/g,""),n,s,0,i)})).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,l)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,l)).replace(/!\[([^\[\]]+)]()()()()()/g,l),e=m.converter._dispatch("makehtml.images.after",e,r,m).getText()}),b.subParser("makehtml.italicsAndBold",function(e,r,t){"use strict";function a(e,r,t){return r+e+t}return e=t.converter._dispatch("makehtml.italicsAndBold.before",e,r,t).getText(),e=(e=(e=(e=r.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*)___\b/g,function(e,r){return a(r,"<strong><em>","</em></strong>")})).replace(/\b__(\S[\s\S]*)__\b/g,function(e,r){return a(r,"<strong>","</strong>")})).replace(/\b_(\S[\s\S]*?)_\b/g,function(e,r){return a(r,"<em>","</em>")}):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,function(e,r){return/\S$/.test(r)?a(r,"<strong><em>","</em></strong>"):e})).replace(/__(\S[\s\S]*?)__/g,function(e,r){return/\S$/.test(r)?a(r,"<strong>","</strong>"):e})).replace(/_([^\s_][\s\S]*?)_/g,function(e,r){return/\S$/.test(r)?a(r,"<em>","</em>"):e})).replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,function(e,r){return/\S$/.test(r)?a(r,"<strong><em>","</em></strong>"):e})).replace(/\*\*(\S[\s\S]*?)\*\*/g,function(e,r){return/\S$/.test(r)?a(r,"<strong>","</strong>"):e})).replace(/\*([^\s*][\s\S]*?)\*/g,function(e,r){return/\S$/.test(r)?a(r,"<em>","</em>"):e}),e=t.converter._dispatch("makehtml.italicsAndBold.after",e,r,t).getText()}),function(){function l(i,l,c,u,h){return h=!!h,function(e,r,t,a,n,s,o){return/\n\n/.test(e)?e:f(_(i,l+".captureStart",e,r,t,a,o,c,u),c,u,h)}}function _(e,r,t,a,n,s,o,i,l){return l.converter._dispatch(r,t,i,l,{regexp:e,matches:{wholeMatch:t,text:a,id:n,url:s,title:o}})}function f(e,r,t,a){var n=e.getMatches().wholeMatch,s=e.getMatches().text,o=e.getMatches().id,i=e.getMatches().url,l=e.getMatches().title,c="";if(l||(l=""),o=o?o.toLowerCase():"",a)i="";else if(!i){if(o||(o=s.toLowerCase().replace(/ ?\n/g," ")),i="#"+o,b.helper.isUndefined(t.gUrls[o]))return n;i=t.gUrls[o],b.helper.isUndefined(t.gTitles[o])||(l=t.gTitles[o])}i=i.replace(b.helper.regexes.asteriskDashTildeAndColon,b.helper.escapeCharactersCallback),""!==l&&null!==l&&(l=' title="'+(l=(l=l.replace(/"/g,"&quot;")).replace(b.helper.regexes.asteriskDashTildeAndColon,b.helper.escapeCharactersCallback))+'"'),r.openLinksInNewWindow&&!/^#/.test(i)&&(c=' target="¨E95Eblank"'),s=b.subParser("makehtml.codeSpans")(s,r,t),s=b.subParser("makehtml.emoji")(s,r,t),s=b.subParser("makehtml.underline")(s,r,t),s=b.subParser("makehtml.italicsAndBold")(s,r,t),s=b.subParser("makehtml.strikethrough")(s,r,t),s=b.subParser("makehtml.ellipsis")(s,r,t);var u='<a href="'+i+'"'+l+c+">"+(s=b.subParser("makehtml.hashHTMLSpans")(s,r,t))+"</a>";return u=b.subParser("makehtml.hashHTMLSpans")(u,r,t)}var a="makehtml.links";b.subParser("makehtml.links",function(e,r,t){return e=t.converter._dispatch(a+".start",e,r,t).getText(),e=b.subParser("makehtml.links.reference")(e,r,t),e=b.subParser("makehtml.links.inline")(e,r,t),e=b.subParser("makehtml.links.referenceShortcut")(e,r,t),e=b.subParser("makehtml.links.angleBrackets")(e,r,t),e=(e=(e=b.subParser("makehtml.links.ghMentions")(e,r,t)).replace(/<a\s[^>]*>[\s\S]*<\/a>/g,function(e){return b.helper._hashHTMLSpan(e,t)})).replace(/<img\s[^>]*\/?>/g,function(e){return b.helper._hashHTMLSpan(e,t)}),e=b.subParser("makehtml.links.naked")(e,r,t),e=t.converter._dispatch(a+".end",e,r,t).getText()}),b.subParser("makehtml.links.inline",function(e,r,t){var a=a+".inline",n=/\[(.*?)]()()()()\(<? ?>? ?(?:["'](.*)["'])?\)/g,s=/\[((?:\[[^\]]*]|[^\[\]])*)]()\s?\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,o=/\[([\S ]*?)]\s?()\( *<?([^\s'"]*?(?:\([\S]*?\)[\S]*?)?)>?\s*(?:()(['"])(.*?)\5)? *\)/g,i=/\[([\S ]*?)]\s?()\( *<?([^\s'"]*?(?:\([\S]*?\)[\S]*?)?)>?\s+()()\((.*?)\) *\)/g;return e=(e=(e=(e=(e=t.converter._dispatch(a+".start",e,r,t).getText()).replace(n,l(n,a,r,t,!0))).replace(s,l(s,a,r,t))).replace(o,l(o,a,r,t))).replace(i,l(i,a,r,t)),e=t.converter._dispatch(a+".end",e,r,t).getText()}),b.subParser("makehtml.links.reference",function(e,r,t){var a=a+".reference",n=/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g;return e=(e=t.converter._dispatch(a+".start",e,r,t).getText()).replace(n,l(n,a,r,t)),e=t.converter._dispatch(a+".end",e,r,t).getText()}),b.subParser("makehtml.links.referenceShortcut",function(e,r,t){var a=a+".referenceShortcut",n=/\[([^\[\]]+)]()()()()()/g;return e=(e=t.converter._dispatch(a+".start",e,r,t).getText()).replace(n,l(n,a,r,t)),e=t.converter._dispatch(a+".end",e,r,t).getText()}),b.subParser("makehtml.links.ghMentions",function(e,o,i){var l=l+"ghMentions";if(!o.ghMentions)return e;e=i.converter._dispatch(l+".start",e,o,i).getText();var c=/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d._-]+?[a-z\d]+)*))/gi;return e=e.replace(c,function(e,r,t,a,n){if("\\"===t)return r+a;if(!b.helper.isString(o.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var s=o.ghMentionsLink.replace(/{u}/g,n);return r+f(_(c,l+".captureStart",e,a,null,s,null,o,i),o,i)}),e=i.converter._dispatch(l+".end",e,o,i).getText()}),b.subParser("makehtml.links.angleBrackets",function(e,a,n){var s="makehtml.links.angleBrackets";e=n.converter._dispatch(s+".start",e,a,n).getText();var o=/<(((?:https?|ftp):\/\/|www\.)[^'">\s]+)>/gi;e=e.replace(o,function(e,r,t){return f(_(o,s+".captureStart",e,r,null,r="www."===t?"http://"+r:r,null,a,n),a,n)});var i=/<(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi;return e=e.replace(i,function(e,r){var t="mailto:";return r=b.subParser("makehtml.unescapeSpecialChars")(r,a,n),a.encodeEmails?(t=b.helper.encodeEmailAddress(t+r),r=b.helper.encodeEmailAddress(r)):t+=r,f(_(i,s+".captureStart",e,r,null,t,null,a,n),a,n)}),e=n.converter._dispatch(s+".end",e,a,n).getText()}),b.subParser("makehtml.links.naked",function(e,m,d){if(!m.simplifiedAutoLink)return e;var p="makehtml.links.naked";e=d.converter._dispatch(p+".start",e,m,d).getText();var g=/([_*~]*?)(((?:https?|ftp):\/\/|www\.)[^\s<>"'`´.-][^\s<>"'`´]*?\.[a-z\d.]+[^\s<>"']*)\1/gi;e=e.replace(g,function(e,r,t,a){for(var n="",s=t.length-1;0<=s;--s){var o=t.charAt(s);if(/[_*~,;:.!?]/.test(o))t=t.slice(0,-1),n=o+n;else if(/\)/.test(o)){var i=t.match(/\(/g)||[],l=t.match(/\)/g);if(!(i.length<l.length))break;t=t.slice(0,-1),n=o+n}else{if(!/]/.test(o))break;var c=t.match(/\[/g)||[],u=t.match(/\]/g);if(!(c.length<u.length))break;t=t.slice(0,-1),n=o+n}}var h=t;return t="www."===a?"http://"+t:t,h=h.replace(b.helper.regexes.asteriskDashTildeAndColon,b.helper.escapeCharactersCallback),r+f(_(g,p+".captureStart",e,h,null,t,null,m,d),m,d)+n+r});var n=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim;return e=e.replace(n,function(e,r,t){var a="mailto:";return t=b.subParser("makehtml.unescapeSpecialChars")(t,m,d),m.encodeEmails?(a=b.helper.encodeEmailAddress(a+t),t=b.helper.encodeEmailAddress(t)):a+=t,r+f(_(n,p+".captureStart",e,t,null,a,null,m,d),m,d)}),e=d.converter._dispatch(p+".end",e,m,d).getText()})}(),b.subParser("makehtml.lists",function(e,h,u){"use strict";function m(e,r){u.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var t=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,c=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return h.disableForced4SpacesIndentedSublists&&(t=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(t,function(e,r,t,a,n,s,o){o=o&&""!==o.trim();var i=b.subParser("makehtml.outdent")(n,h,u),l="";return s&&h.tasklists&&(l=' class="task-list-item" style="list-style-type: none;"',i=i.replace(/^[ \t]*\[(x|X| )?]/m,function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return o&&(e+=" checked"),e+=">"})),i=i.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,function(e){return"¨A"+e}),/^#+.+\n.+/.test(i)&&(i=i.replace(/^(#+.+)$/m,"$1\n")),r||-1<i.search(/\n{2,}/)?(i=b.subParser("makehtml.githubCodeBlocks")(i,h,u),i=b.subParser("makehtml.blockGamut")(i,h,u)):(i=(i=b.subParser("makehtml.lists")(i,h,u)).replace(/\n$/,""),i=(i=b.subParser("makehtml.hashHTMLBlocks")(i,h,u)).replace(/\n\n+/g,"\n\n"),i=c?b.subParser("makehtml.paragraphs")(i,h,u):b.subParser("makehtml.spanGamut")(i,h,u)),i="<li"+l+">"+(i=i.replace("¨A",""))+"</li>\n"})).replace(/¨0/g,""),u.gListLevel--,r&&(e=e.replace(/\s+$/,"")),e}function d(e,r){if("ol"===r){var t=e.match(/^ *(\d+)\./);if(t&&"1"!==t[1])return' start="'+t[1]+'"'}return""}function n(n,s,o){var i=h.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,l=h.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,c="ul"===s?i:l,u="";if(-1!==n.search(c))!function e(r){var t=r.search(c),a=d(n,s);-1!==t?(u+="\n\n<"+s+a+">\n"+m(r.slice(0,t),!!o)+"</"+s+">\n",c="ul"===(s="ul"===s?"ol":"ul")?i:l,e(r.slice(t))):u+="\n\n<"+s+a+">\n"+m(r,!!o)+"</"+s+">\n"}(n);else{var e=d(n,s);u="\n\n<"+s+e+">\n"+m(n,!!o)+"</"+s+">\n"}return u}return e=u.converter._dispatch("lists.before",e,h,u).getText(),e+="¨0",e=(e=u.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(e,r,t){return n(r,-1<t.search(/[*+-]/g)?"ul":"ol",!0)}):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(e,r,t,a){return n(t,-1<a.search(/[*+-]/g)?"ul":"ol",!1)})).replace(/¨0/,""),e=u.converter._dispatch("makehtml.lists.after",e,h,u).getText()}),b.subParser("makehtml.metadata",function(e,r,a){"use strict";if(!r.metadata)return e;function n(e){(e=(e=(a.metadata.raw=e).replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,function(e,r,t){return a.metadata.parsed[r]=t,""})}return e=(e=(e=(e=a.converter._dispatch("makehtml.metadata.before",e,r,a).getText()).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,function(e,r,t){return n(t),"¨M"})).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,function(e,r,t){return r&&(a.metadata.format=r),n(t),"¨M"})).replace(/¨M/g,""),e=a.converter._dispatch("makehtml.metadata.after",e,r,a).getText()}),b.subParser("makehtml.outdent",function(e,r,t){"use strict";return e=(e=(e=t.converter._dispatch("makehtml.outdent.before",e,r,t).getText()).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),e=t.converter._dispatch("makehtml.outdent.after",e,r,t).getText()}),b.subParser("makehtml.paragraphs",function(e,r,t){"use strict";for(var a=(e=(e=(e=t.converter._dispatch("makehtml.paragraphs.before",e,r,t).getText()).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),n=[],s=a.length,o=0;o<s;o++){var i=a[o];0<=i.search(/¨(K|G)(\d+)\1/g)?n.push(i):0<=i.search(/\S/)&&(i=(i=b.subParser("makehtml.spanGamut")(i,r,t)).replace(/^([ \t]*)/g,"<p>"),i+="</p>",n.push(i))}for(s=n.length,o=0;o<s;o++){for(var l="",c=n[o],u=!1;/¨(K|G)(\d+)\1/.test(c);){var h=RegExp.$1,m=RegExp.$2;l=(l="K"===h?t.gHtmlBlocks[m]:u?b.subParser("makehtml.encodeCode")(t.ghCodeBlocks[m].text,r,t):t.ghCodeBlocks[m].codeblock).replace(/\$/g,"$$$$"),c=c.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,l),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(c)&&(u=!0)}n[o]=c}return e=(e=(e=n.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),t.converter._dispatch("makehtml.paragraphs.after",e,r,t).getText()}),b.subParser("makehtml.runExtension",function(e,r,t,a){"use strict";if(e.filter)r=e.filter(r,a.converter,t);else if(e.regex){var n=e.regex;n instanceof RegExp||(n=new RegExp(n,"g")),r=r.replace(n,e.replace)}return r}),b.subParser("makehtml.spanGamut",function(e,r,t){"use strict";return e=t.converter._dispatch("makehtml.span.before",e,r,t).getText(),e=b.subParser("makehtml.codeSpans")(e,r,t),e=b.subParser("makehtml.escapeSpecialCharsWithinTagAttributes")(e,r,t),e=b.subParser("makehtml.encodeBackslashEscapes")(e,r,t),e=b.subParser("makehtml.images")(e,r,t),e=t.converter._dispatch("smakehtml.links.before",e,r,t).getText(),e=b.subParser("makehtml.links")(e,r,t),e=t.converter._dispatch("smakehtml.links.after",e,r,t).getText(),e=b.subParser("makehtml.emoji")(e,r,t),e=b.subParser("makehtml.underline")(e,r,t),e=b.subParser("makehtml.italicsAndBold")(e,r,t),e=b.subParser("makehtml.strikethrough")(e,r,t),e=b.subParser("makehtml.ellipsis")(e,r,t),e=b.subParser("makehtml.hashHTMLSpans")(e,r,t),e=b.subParser("makehtml.encodeAmpsAndAngles")(e,r,t),r.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),e=t.converter._dispatch("makehtml.spanGamut.after",e,r,t).getText()}),b.subParser("makehtml.strikethrough",function(e,r,t){"use strict";return r.strikethrough&&(e=(e=t.converter._dispatch("makehtml.strikethrough.before",e,r,t).getText()).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,function(e,r){return"<del>"+r+"</del>"}),e=t.converter._dispatch("makehtml.strikethrough.after",e,r,t).getText()),e}),b.subParser("makehtml.stripLinkDefinitions",function(e,i,l){"use strict";var r=function(e,r,t,a,n,s,o){return r=r.toLowerCase(),t.match(/^data:.+?\/.+?;base64,/)?l.gUrls[r]=t.replace(/\s/g,""):l.gUrls[r]=b.subParser("makehtml.encodeAmpsAndAngles")(t,i,l),s?s+o:(o&&(l.gTitles[r]=o.replace(/"|'/g,"&quot;")),i.parseImgDimensions&&a&&n&&(l.gDimensions[r]={width:a,height:n}),"")};return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,r)).replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,r)).replace(/¨0/,"")}),b.subParser("makehtml.tables",function(e,_,f){"use strict";if(!_.tables)return e;function r(e){var r,t=e.split("\n");for(r=0;r<t.length;++r)/^ {0,3}\|/.test(t[r])&&(t[r]=t[r].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(t[r])&&(t[r]=t[r].replace(/\|[ \t]*$/,"")),t[r]=b.subParser("makehtml.codeSpans")(t[r],_,f);var a,n,s,o,i,l=t[0].split("|").map(function(e){return e.trim()}),c=t[1].split("|").map(function(e){return e.trim()}),u=[],h=[],m=[],d=[];for(t.shift(),t.shift(),r=0;r<t.length;++r)""!==t[r].trim()&&u.push(t[r].split("|").map(function(e){return e.trim()}));if(l.length<c.length)return e;for(r=0;r<c.length;++r)m.push((a=c[r],/^:[ \t]*--*$/.test(a)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(a)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(a)?' style="text-align:center;"':""));for(r=0;r<l.length;++r)b.helper.isUndefined(m[r])&&(m[r]=""),h.push((n=l[r],s=m[r],o=void 0,o="",n=n.trim(),(_.tablesHeaderId||_.tableHeaderId)&&(o=' id="'+n.replace(/ /g,"_").toLowerCase()+'"'),"<th"+o+s+">"+(n=b.subParser("makehtml.spanGamut")(n,_,f))+"</th>\n"));for(r=0;r<u.length;++r){for(var p=[],g=0;g<h.length;++g)b.helper.isUndefined(u[r][g]),p.push((i=u[r][g],"<td"+m[g]+">"+b.subParser("makehtml.spanGamut")(i,_,f)+"</td>\n"));d.push(p)}return function(e,r){for(var t="<table>\n<thead>\n<tr>\n",a=e.length,n=0;n<a;++n)t+=e[n];for(t+="</tr>\n</thead>\n<tbody>\n",n=0;n<r.length;++n){t+="<tr>\n";for(var s=0;s<a;++s)t+=r[n][s];t+="</tr>\n"}return t+="</tbody>\n</table>\n"}(h,d)}return e=(e=(e=(e=f.converter._dispatch("makehtml.tables.before",e,_,f).getText()).replace(/\\(\|)/g,b.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,r)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,r),e=f.converter._dispatch("makehtml.tables.after",e,_,f).getText()}),b.subParser("makehtml.underline",function(e,r,t){"use strict";return r.underline?(e=t.converter._dispatch("makehtml.underline.before",e,r,t).getText(),e=(e=r.literalMidWordUnderscores?e.replace(/\b_?__(\S[\s\S]*)___?\b/g,function(e,r){return"<u>"+r+"</u>"}):e.replace(/_?__(\S[\s\S]*?)___?/g,function(e,r){return/\S$/.test(r)?"<u>"+r+"</u>":e})).replace(/(_)/g,b.helper.escapeCharactersCallback),e=t.converter._dispatch("makehtml.underline.after",e,r,t).getText()):e}),b.subParser("makehtml.unescapeSpecialChars",function(e,r,t){"use strict";return e=(e=t.converter._dispatch("makehtml.unescapeSpecialChars.before",e,r,t).getText()).replace(/¨E(\d+)E/g,function(e,r){var t=parseInt(r);return String.fromCharCode(t)}),e=t.converter._dispatch("makehtml.unescapeSpecialChars.after",e,r,t).getText()}),b.subParser("makeMarkdown.blockquote",function(e,r){"use strict";var t="";if(e.hasChildNodes())for(var a=e.childNodes,n=a.length,s=0;s<n;++s){var o=b.subParser("makeMarkdown.node")(a[s],r);""!==o&&(t+=o)}return t="> "+(t=t.trim()).split("\n").join("\n> ")}),b.subParser("makeMarkdown.codeBlock",function(e,r){"use strict";var t=e.getAttribute("language"),a=e.getAttribute("precodenum");return"```"+t+"\n"+r.preList[a]+"\n```"}),b.subParser("makeMarkdown.codeSpan",function(e){"use strict";return"`"+e.innerHTML+"`"}),b.subParser("makeMarkdown.emphasis",function(e,r){"use strict";var t="";if(e.hasChildNodes()){t+="*";for(var a=e.childNodes,n=a.length,s=0;s<n;++s)t+=b.subParser("makeMarkdown.node")(a[s],r);t+="*"}return t}),b.subParser("makeMarkdown.header",function(e,r,t){"use strict";var a=new Array(t+1).join("#"),n="";if(e.hasChildNodes()){n=a+" ";for(var s=e.childNodes,o=s.length,i=0;i<o;++i)n+=b.subParser("makeMarkdown.node")(s[i],r)}return n}),b.subParser("makeMarkdown.hr",function(){"use strict";return"---"}),b.subParser("makeMarkdown.image",function(e){"use strict";var r="";return e.hasAttribute("src")&&(r+="!["+e.getAttribute("alt")+"](",r+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(r+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"),r}),b.subParser("makeMarkdown.links",function(e,r){"use strict";var t="";if(e.hasChildNodes()&&e.hasAttribute("href")){var a=e.childNodes,n=a.length;t="[";for(var s=0;s<n;++s)t+=b.subParser("makeMarkdown.node")(a[s],r);t+="](",t+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(t+=' "'+e.getAttribute("title")+'"'),t+=")"}return t}),b.subParser("makeMarkdown.list",function(e,r,t){"use strict";var a="";if(!e.hasChildNodes())return"";for(var n=e.childNodes,s=n.length,o=e.getAttribute("start")||1,i=0;i<s;++i)if(void 0!==n[i].tagName&&"li"===n[i].tagName.toLowerCase()){a+=("ol"===t?o.toString()+". ":"- ")+b.subParser("makeMarkdown.listItem")(n[i],r),++o}return a.trim()}),b.subParser("makeMarkdown.listItem",function(e,r){"use strict";for(var t="",a=e.childNodes,n=a.length,s=0;s<n;++s)t+=b.subParser("makeMarkdown.node")(a[s],r);return/\n$/.test(t)?t=t.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):t+="\n",t}),b.subParser("makeMarkdown.node",function(e,r,t){"use strict";t=t||!1;var a="";if(3===e.nodeType)return b.subParser("makeMarkdown.txt")(e,r);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":t||(a=b.subParser("makeMarkdown.header")(e,r,1)+"\n\n");break;case"h2":t||(a=b.subParser("makeMarkdown.header")(e,r,2)+"\n\n");break;case"h3":t||(a=b.subParser("makeMarkdown.header")(e,r,3)+"\n\n");break;case"h4":t||(a=b.subParser("makeMarkdown.header")(e,r,4)+"\n\n");break;case"h5":t||(a=b.subParser("makeMarkdown.header")(e,r,5)+"\n\n");break;case"h6":t||(a=b.subParser("makeMarkdown.header")(e,r,6)+"\n\n");break;case"p":t||(a=b.subParser("makeMarkdown.paragraph")(e,r)+"\n\n");break;case"blockquote":t||(a=b.subParser("makeMarkdown.blockquote")(e,r)+"\n\n");break;case"hr":t||(a=b.subParser("makeMarkdown.hr")(e,r)+"\n\n");break;case"ol":t||(a=b.subParser("makeMarkdown.list")(e,r,"ol")+"\n\n");break;case"ul":t||(a=b.subParser("makeMarkdown.list")(e,r,"ul")+"\n\n");break;case"precode":t||(a=b.subParser("makeMarkdown.codeBlock")(e,r)+"\n\n");break;case"pre":t||(a=b.subParser("makeMarkdown.pre")(e,r)+"\n\n");break;case"table":t||(a=b.subParser("makeMarkdown.table")(e,r)+"\n\n");break;case"code":a=b.subParser("makeMarkdown.codeSpan")(e,r);break;case"em":case"i":a=b.subParser("makeMarkdown.emphasis")(e,r);break;case"strong":case"b":a=b.subParser("makeMarkdown.strong")(e,r);break;case"del":a=b.subParser("makeMarkdown.strikethrough")(e,r);break;case"a":a=b.subParser("makeMarkdown.links")(e,r);break;case"img":a=b.subParser("makeMarkdown.image")(e,r);break;default:a=e.outerHTML+"\n\n"}return a}),b.subParser("makeMarkdown.paragraph",function(e,r){"use strict";var t="";if(e.hasChildNodes())for(var a=e.childNodes,n=a.length,s=0;s<n;++s)t+=b.subParser("makeMarkdown.node")(a[s],r);return t=t.trim()}),b.subParser("makeMarkdown.pre",function(e,r){"use strict";var t=e.getAttribute("prenum");return"<pre>"+r.preList[t]+"</pre>"}),b.subParser("makeMarkdown.strikethrough",function(e,r){"use strict";var t="";if(e.hasChildNodes()){t+="~~";for(var a=e.childNodes,n=a.length,s=0;s<n;++s)t+=b.subParser("makeMarkdown.node")(a[s],r);t+="~~"}return t}),b.subParser("makeMarkdown.strong",function(e,r){"use strict";var t="";if(e.hasChildNodes()){t+="**";for(var a=e.childNodes,n=a.length,s=0;s<n;++s)t+=b.subParser("makeMarkdown.node")(a[s],r);t+="**"}return t}),b.subParser("makeMarkdown.table",function(e,r){"use strict";var t,a,n="",s=[[],[]],o=e.querySelectorAll("thead>tr>th"),i=e.querySelectorAll("tbody>tr");for(t=0;t<o.length;++t){var l=b.subParser("makeMarkdown.tableCell")(o[t],r),c="---";if(o[t].hasAttribute("style"))switch(o[t].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":c=":---";break;case"text-align:right;":c="---:";break;case"text-align:center;":c=":---:"}s[0][t]=l.trim(),s[1][t]=c}for(t=0;t<i.length;++t){var u=s.push([])-1,h=i[t].getElementsByTagName("td");for(a=0;a<o.length;++a){var m=" ";void 0!==h[a]&&(m=b.subParser("makeMarkdown.tableCell")(h[a],r)),s[u].push(m)}}var d=3;for(t=0;t<s.length;++t)for(a=0;a<s[t].length;++a){var p=s[t][a].length;d<p&&(d=p)}for(t=0;t<s.length;++t){for(a=0;a<s[t].length;++a)1===t?":"===s[t][a].slice(-1)?s[t][a]=b.helper.padEnd(s[t][a].slice(-1),d-1,"-")+":":s[t][a]=b.helper.padEnd(s[t][a],d,"-"):s[t][a]=b.helper.padEnd(s[t][a],d);n+="| "+s[t].join(" | ")+" |\n"}return n.trim()}),b.subParser("makeMarkdown.tableCell",function(e,r){"use strict";var t="";if(!e.hasChildNodes())return"";for(var a=e.childNodes,n=a.length,s=0;s<n;++s)t+=b.subParser("makeMarkdown.node")(a[s],r,!0);return t.trim()}),b.subParser("makeMarkdown.txt",function(e){"use strict";var r=e.nodeValue;return r=(r=r.replace(/ +/g," ")).replace(/¨NBSP;/g," "),r=(r=(r=(r=(r=(r=(r=(r=(r=b.helper.unescapeHTMLEntities(r)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")}),b.Converter=function(t){"use strict";var s={},o=[],i=[],c={},a=d,l={parsed:{},raw:"",format:""};function n(e,r){if(r=r||null,b.helper.isString(e)){if(r=e=b.helper.stdExtName(e),b.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(e,r){"function"==typeof e&&(e=e(new b.Converter));b.helper.isArray(e)||(e=[e]);var t=g(e,r);if(!t.valid)throw Error(t.error);for(var a=0;a<e.length;++a)switch(e[a].type){case"lang":o.push(e[a]);break;case"output":i.push(e[a]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(b.extensions[e],e);if(b.helper.isUndefined(h[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=h[e]}"function"==typeof e&&(e=e()),b.helper.isArray(e)||(e=[e]);var t=g(e,r);if(!t.valid)throw Error(t.error);for(var a=0;a<e.length;++a){switch(e[a].type){case"lang":o.push(e[a]);break;case"output":i.push(e[a])}if(e[a].hasOwnProperty("listeners"))for(var n in e[a].listeners)e[a].listeners.hasOwnProperty(n)&&u(n,e[a].listeners[n])}}function u(e,r){if(!b.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof r)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof r+" given");e=e.toLowerCase(),c.hasOwnProperty(e)||(c[e]=[]),c[e].push(r)}!function(){for(var e in t=t||{},m)m.hasOwnProperty(e)&&(s[e]=m[e]);{if("object"!=typeof t)throw Error("Converter expects the passed parameter to be an object, but "+typeof t+" was passed instead.");for(var r in t)t.hasOwnProperty(r)&&(s[r]=t[r])}s.extensions&&b.helper.forEach(s.extensions,n)}(),this._dispatch=function(e,r,t,a,n){e=e.toLowerCase();var s=n||{};s.converter=this,s.text=r,s.options=t,s.globals=a;var o=new b.helper.Event(e,r,s);if(c.hasOwnProperty(e))for(var i=0;i<c[e].length;++i){var l=c[e][i](o);l&&void 0!==l&&o.setText(l)}return o},this.listen=function(e,r){return u(e,r),this},this.makeHtml=function(r){if(!r)return r;var e,t,a,n={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:o,outputModifiers:i,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return r=(r=(r=(r=(r=r.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),s.smartIndentationFix&&(t=(e=r).match(/^\s*/)[0].length,a=new RegExp("^\\s{0,"+t+"}","gm"),r=e.replace(a,"")),r="\n\n"+r+"\n\n",r=(r=b.subParser("makehtml.detab")(r,s,n)).replace(/^[ \t]+$/gm,""),b.helper.forEach(o,function(e){r=b.subParser("makehtml.runExtension")(e,r,s,n)}),r=b.subParser("makehtml.metadata")(r,s,n),r=b.subParser("makehtml.hashPreCodeTags")(r,s,n),r=b.subParser("makehtml.githubCodeBlocks")(r,s,n),r=b.subParser("makehtml.hashHTMLBlocks")(r,s,n),r=b.subParser("makehtml.hashCodeTags")(r,s,n),r=b.subParser("makehtml.stripLinkDefinitions")(r,s,n),r=b.subParser("makehtml.blockGamut")(r,s,n),r=b.subParser("makehtml.unhashHTMLSpans")(r,s,n),r=(r=(r=b.subParser("makehtml.unescapeSpecialChars")(r,s,n)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),r=b.subParser("makehtml.completeHTMLDocument")(r,s,n),b.helper.forEach(i,function(e){r=b.subParser("makehtml.runExtension")(e,r,s,n)}),l=n.metadata,r},this.makeMarkdown=function(e){e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<");var r=b.helper.document.createElement("div");r.innerHTML=e;var t={preList:function(e){for(var r=e.querySelectorAll("pre"),t=[],a=0;a<r.length;++a)if(1===r[a].childElementCount&&"code"===r[a].firstChild.tagName.toLowerCase()){var n=r[a].firstChild.innerHTML.trim(),s=r[a].firstChild.getAttribute("data-language")||"";if(""===s)for(var o=r[a].firstChild.className.split(" "),i=0;i<o.length;++i){var l=o[i].match(/^language-(.+)$/);if(null!==l){s=l[1];break}}n=b.helper.unescapeHTMLEntities(n),t.push(n),r[a].outerHTML='<precode language="'+s+'" precodenum="'+a.toString()+'"></precode>'}else t.push(r[a].innerHTML),r[a].innerHTML="",r[a].setAttribute("prenum",a.toString());return t}(r)};!function e(r){for(var t=0;t<r.childNodes.length;++t){var a=r.childNodes[t];3===a.nodeType?/\S/.test(a.nodeValue)?(a.nodeValue=a.nodeValue.split("\n").join(" "),a.nodeValue=a.nodeValue.replace(/(\s)+/g,"$1")):(r.removeChild(a),--t):1===a.nodeType&&e(a)}}(r);for(var a=r.childNodes,n="",s=0;s<a.length;s++)n+=b.subParser("makeMarkdown.node")(a[s],t);return n},this.setOption=function(e,r){s[e]=r},this.getOption=function(e){return s[e]},this.getOptions=function(){return s},this.addExtension=function(e,r){n(e,r=r||null)},this.useExtension=function(e){n(e)},this.setFlavor=function(e){if(!p.hasOwnProperty(e))throw Error(e+" flavor was not found");var r=p[e];for(var t in a=e,r)r.hasOwnProperty(t)&&(s[t]=r[t])},this.getFlavor=function(){return a},this.removeExtension=function(e){b.helper.isArray(e)||(e=[e]);for(var r=0;r<e.length;++r){for(var t=e[r],a=0;a<o.length;++a)o[a]===t&&o[a].splice(a,1);for(;0<i.length;++a)i[0]===t&&i[0].splice(a,1)}},this.getAllExtensions=function(){return{language:o,output:i}},this.getMetadata=function(e){return e?l.raw:l.parsed},this.getMetadataFormat=function(){return l.format},this._setMetadataPair=function(e,r){l.parsed[e]=r},this._setMetadataFormat=function(e){l.format=e},this._setMetadataRaw=function(e){l.raw=e}};"function"==typeof define&&define.amd?define(function(){"use strict";return b}):"undefined"!=typeof module&&module.exports?module.exports=b:this.showdown=b}).call(this);
//# sourceMappingURL=showdown.min.js.map
