<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:beans="http://www.springframework.org/schema/beans"
       xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context-3.2.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
	                       http://www.springframework.org/schema/mvc
	                       http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">

    <!-- 解析 CEP 传入的参数，包括用户身份、客户端信息等 -->
    <import resource="classpath:META-INF/fs-cep-plugin.xml" />
    <context:component-scan base-package="com.fxiaoke.open.oasyncdata.controller">
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice"/>
    </context:component-scan>

    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
        <property name="prefix" value="/"/>
        <property name="suffix" value=".*"/>
    </bean>

    <aop:aspectj-autoproxy/>

    <mvc:default-servlet-handler/>

    <mvc:annotation-driven>
        <mvc:message-converters>
            <!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
            <beans:bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <beans:constructor-arg value="UTF-8"/>
            </beans:bean>
            <!--不使用fsConverter是因为无法反序列化带泛型的类型-->
            <beans:bean class="com.fxiaoke.open.oasyncdata.interceptor.CepGsonConverter">
                <beans:property name="supportedMediaTypes">
                    <beans:list>
                        <beans:value>application/json</beans:value>
                    </beans:list>
                </beans:property>
            </beans:bean>
            <bean class="com.facishare.cep.plugin.converter.ProtobufConverter" id="protobufConverter"/>
            <bean class="com.facishare.cep.plugin.converter.SimpleJsonConverter" id="simpleJsonConverter"/>
        </mvc:message-converters>
        <mvc:argument-resolvers>
            <bean class="com.facishare.cep.plugin.resolver.RequestAnnotationResolver"/>
        </mvc:argument-resolvers>
        <mvc:return-value-handlers>
            <bean class="com.facishare.cep.plugin.idempotent.IdempotentReturnValueHandler"/>
        </mvc:return-value-handlers>
    </mvc:annotation-driven>
    <!-- 拦截器配置 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <mvc:exclude-mapping path="/**/anonymousQuery" />
            <mvc:exclude-mapping path="/api/v1/**" />
            <mvc:exclude-mapping path="/monitor/**" />
            <bean class="com.fxiaoke.open.oasyncdata.interceptor.UserInterceptors"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <beans:import resource="classpath*:spring/web-applicationContext.xml" />
</beans>
