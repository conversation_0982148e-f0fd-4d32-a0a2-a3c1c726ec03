<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.oasyncdata.db.dao.OASyncLogDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        data_id,
        data_json,
        status,
        message,
        obj_api_name,
        event_type,
        title,
        receiver_id,
        create_time,
        update_time
    </sql>
    <update id="updateSyncLogEntity">
        update oa_sync_log
        <set>
            <if test="syncLogEntity.status != null and syncLogEntity.status!=''">
                 status=#{syncLogEntity.status},
            </if>
            <if test="syncLogEntity.dataName != null and syncLogEntity.dataName!=''">
                data_name=#{syncLogEntity.dataName},
            </if>
            <if test="syncLogEntity.title != null and syncLogEntity.title!=''">
                title=#{syncLogEntity.title},
            </if>
            <if test="syncLogEntity.objectName != null and syncLogEntity.objectName!=''">
                object_name=#{syncLogEntity.objectName},
            </if>
        </set>
        where id=#{id}
    </update>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.oasyncdata.db.entity.OASyncLogEntity">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_id" jdbcType="VARCHAR" property="dataId"/>
        <result column="business_data_id" jdbcType="VARCHAR" property="businessDataId"/>
        <result column="data_json" jdbcType="VARCHAR" property="dataJson"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="message" jdbcType="VARCHAR" property="message"/>
        <result column="obj_api_name" jdbcType="VARCHAR" property="objApiName"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="receiver_id" jdbcType="VARCHAR" property="receiverId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="data_name" jdbcType="VARCHAR" property="dataName"/>
        <result column="object_name" jdbcType="VARCHAR" property="objectName"/>
    </resultMap>

    <select id="queryOALog" resultMap="BaseResultMap">
        select *
        from oa_sync_log
        where
        tenant_id=#{tenantId}
        <if test="objApiName != null and objApiName!=''">
            and obj_api_name=#{objApiName}
        </if>
        <if test="id != null and id!=''">
            and id=#{id}
        </if>
        <if test="dataId != null and dataId!=''">
            and data_id = #{dataId}
        </if>
        <if test="businessDataId != null and businessDataId!=''">
            and business_data_id = #{dataId}
        </if>
        <if test="status != null and status!=''">
            and status=#{status}
        </if>
        <if test="eventType != null and eventType!=''">
            and event_type=#{eventType}
        </if>
        <if test="receiverId == null">
            and receiver_id is null
        </if>
        <if test="receiverId != null">
            and receiver_id=#{receiverId}
        </if>
        order by update_time desc
    </select>

    <select id="queryOALogGroup" resultMap="BaseResultMap">
        select * from oa_sync_log
        where
        tenant_id=#{oaSyncLogDaoArg.tenantId}
        <if test="oaSyncLogDaoArg.objApiName != null and oaSyncLogDaoArg.objApiName!=''">
            and obj_api_name=#{oaSyncLogDaoArg.objApiName}
        </if>
        <if test="oaSyncLogDaoArg.dataId != null and oaSyncLogDaoArg.dataId!=''">
            and data_id=#{oaSyncLogDaoArg.dataId}
        </if>
        <if test="oaSyncLogDaoArg.dataName != null and oaSyncLogDaoArg.dataName!=''">
            and data_name=#{oaSyncLogDaoArg.dataName}
        </if>
        <if test="oaSyncLogDaoArg.objectName != null and oaSyncLogDaoArg.objectName!=''">
            and object_name=#{oaSyncLogDaoArg.objectName}
        </if>
        <if test="oaSyncLogDaoArg.receiverId != null and oaSyncLogDaoArg.receiverId!=''">
            and receiver_id=#{oaSyncLogDaoArg.receiverId}
        </if>
        <if test="oaSyncLogDaoArg.status != null and oaSyncLogDaoArg.status!=''">
            and status=#{oaSyncLogDaoArg.status}
        </if>
        order by update_time desc
        offset #{offset}
        limit #{limit}
    </select>

    <select id="countOALogGroup" resultType="int">
        select count(*) from oa_sync_log
        where
        tenant_id=#{oaSyncLogDaoArg.tenantId}
        <if test="oaSyncLogDaoArg.objApiName != null and oaSyncLogDaoArg.objApiName!=''">
            and obj_api_name=#{oaSyncLogDaoArg.objApiName}
        </if>
        <if test="oaSyncLogDaoArg.dataId != null and oaSyncLogDaoArg.dataId!=''">
            and data_id=#{oaSyncLogDaoArg.dataId}
        </if>
        <if test="oaSyncLogDaoArg.dataName != null and oaSyncLogDaoArg.dataName!=''">
            and data_name=#{oaSyncLogDaoArg.dataName}
        </if>
        <if test="oaSyncLogDaoArg.objectName != null and oaSyncLogDaoArg.objectName!=''">
            and object_name=#{oaSyncLogDaoArg.objectName}
        </if>
        <if test="oaSyncLogDaoArg.receiverId != null and oaSyncLogDaoArg.receiverId!=''">
            and receiver_id=#{oaSyncLogDaoArg.receiverId}
        </if>
        <if test="oaSyncLogDaoArg.status != null and oaSyncLogDaoArg.status!=''">
            and status=#{oaSyncLogDaoArg.status}
        </if>

    </select>

    <select id="queryByTypeApi" resultType="java.lang.String">
        select id from oa_sync_log where id in(
        select max(org.id) from oa_sync_log as org , (select data_id,event_type,receiver_id
        from oa_sync_log
        where id in (select min(id) from oa_sync_log where tenant_id = #{tenantId}
        <if test="objApiName != null and objApiName!=''">
            and obj_api_name=#{objApiName}
        </if>
        <if test="eventType != null and eventType!=''">
            and event_type=#{eventType}
        </if>
        group by data_id,event_type,receiver_id)
        and status!='1'
        and update_time > #{time}) as res
        where res.data_id =  org.data_id and res.event_type = org.event_type and res.receiver_id = org.receiver_id
        GROUP BY org.data_id,org.event_type,org.receiver_id) and status!='1'
    </select>

    <select id="getLogExceprionNum" resultType="java.lang.Integer">
        select count(1) from oa_sync_log where data_id=#{dataId} and tenant_id = #{tenantId} and event_type=#{eventType}
    </select>
    <select id="queryOALogHistory" resultType="com.fxiaoke.open.oasyncdata.db.entity.OASyncLogEntity">
        select * from oa_sync_log where tenant_id =#{tenantId}  and create_time between #{startTime} and #{endTime} order by update_time limit #{limit} offset #{offset}
    </select>
</mapper>