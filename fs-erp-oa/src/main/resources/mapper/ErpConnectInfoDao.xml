<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.oasyncdata.db.dao.ErpConnectInfoDao">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        channel,
        data_center_name,
        enterprise_name,
        connect_params,
        create_time,
        update_time,
        number
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.oasyncdata.db.entity.ErpConnectInfoEntity">
        <!--@mbg.generated-->
        <!--@Table erp_connect_info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="data_center_name" jdbcType="VARCHAR" property="dataCenterName"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="connect_params" jdbcType="LONGVARCHAR" property="connectParams"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="number" property="number"/>
    </resultMap>
    <select id="listByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        order by id;
    </select>

    <select id="listErpDcByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and channel !='CRM'
        order by id;
    </select>

    <select id="getByIdAndTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="listTenantId" resultType="java.lang.String">
        select distinct (tenant_id)
        from erp_connect_info;
    </select>

    <select id="getCurrentConnectInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR} order by update_time desc limit 1;
    </select>

    <select id="getCRMConnectInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id = #{tenantId,jdbcType=VARCHAR} and channel=#{channel} order by update_time desc limit 1;
    </select>

<!--auto generated by MybatisCodeHelper on 2022-03-08-->
    <select id="countByTenantIdAndChannel" resultType="java.lang.Integer">
        select count(1)
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and channel=#{channel,jdbcType=VARCHAR}
    </select>

    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
    </select>
    <select id="queryInfoByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_connect_info
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and data_center_name=#{name,jdbcType=VARCHAR} limit 1;
    </select>
</mapper>