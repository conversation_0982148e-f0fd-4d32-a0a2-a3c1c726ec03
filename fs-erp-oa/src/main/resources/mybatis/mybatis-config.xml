<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<settings>
		<setting name="cacheEnabled" value="false" />
		<setting name="lazyLoadingEnabled" value="true" />
		<setting name="multipleResultSetsEnabled" value="true" />
		<setting name="useColumnLabel" value="true" />
		<setting name="useGeneratedKeys" value="false" />
		<setting name="autoMappingBehavior" value="PARTIAL" />
		<setting name="defaultExecutorType" value="SIMPLE" />
		<setting name="defaultStatementTimeout" value="25" />
		<setting name="safeRowBoundsEnabled" value="false" />
		<setting name="mapUnderscoreToCamelCase" value="true" />
		<setting name="localCacheScope" value="SESSION" />
		<setting name="jdbcTypeForNull" value="OTHER" />
<!--		<setting name="logImpl" value="STDOUT_LOGGING"/>-->
		<setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString" />
	</settings>
	<typeHandlers>
		<typeHandler javaType="string" handler="com.github.mybatis.handler.StringTypeUtf8mb4Handler" />
	</typeHandlers>
	<plugins>
		<plugin interceptor="com.github.mybatis.interceptor.MasterSlaveInterceptor" />
		<plugin interceptor="com.github.mybatis.interceptor.PaginationAutoMapInterceptor" />
	</plugins>
</configuration>
