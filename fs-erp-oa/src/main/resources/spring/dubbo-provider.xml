<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:protocol id="dubbo" name="dubbo" port="28170"/>
    <dubbo:provider  timeout="15000" filter="tracerpc" />

    <dubbo:service interface="com.fxiaoke.open.oasyncdata.service.OAConnParamService" ref="oAConnParamService" protocol="dubbo"/>


</beans>