<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <context:component-scan base-package="com.fxiaoke.open.oasyncdata.db"/>
    <!-- 数据库路由配置 -->
    <bean id="myBatisRoutePolicy" class="com.facishare.paas.pod.mybatis.MyBatisRoutePolicy">
        <property name="biz" value="ERPSD"/>
        <property name="application" value="fs-erp-sync-data"/>
        <property name="dialect" value="postgresql"/>
        <!-- 控制pgbouncer和从库，不指定默认是paas,如果需要请自行指定，一般一个业务一个就可以，如paas,bi,不同应用可共用 -->
        <property name="grayConfig" value="erpSyncData"/>
    </bean>
    <!-- 数据库配置 -->
    <bean id="myBatisDataSource" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="tenantPolicy" ref="myBatisRoutePolicy"/>
        <property name="configName" value="erp-sync-data-all"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="myBatisDataSource"/>
        <!-- 传入PageHelper的插件 -->
        <property name="plugins">
            <array>
                <!-- 传入插件的对象 -->
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <props>
                            <prop key="helperDialect">postgresql</prop>
                            <prop key="reasonable">true</prop>
                        </props>
                    </property>
                </bean>
            </array>
        </property>
        <property name="typeAliasesPackage" value="com.fxiaoke.open.oasyncdata.db.entity.*"/>
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>
        <property name="mapperLocations">
            <array>
                <value>classpath*:/mapper/*.xml</value>
            </array>
        </property>
    </bean>
    <!-- scan for mapper and let them be autowired, interface path -->
    <bean id="dbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.fxiaoke.open.oasyncdata.db.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="myBatisDataSource"/>
    </bean>
    <!-- 开启注解事务 -->
    <tx:annotation-driven/>
    <bean class="com.facishare.id.client.IDClient" name="idClient">
        <constructor-arg index="0" value="${erpsyncdata-id-client-host}"/>
        <constructor-arg index="1" value="${erpsyncdata-id-service-limit}"/>
    </bean>

    <!-- Redis配置 -->
    <bean id="jedisSupport" class="com.github.jedis.support.JedisFactoryBean"
          p:configName="erp-sync-data-all"/>
    <bean id="redisDataSource" class="com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource">
        <property name="jedisCmd" ref="jedisSupport"/>
    </bean>

    <!--okHttp-->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="erp-sync-data-all"/>




    <!-- 数据库配置 hikari连接池，用于执行建表索引语句-->
    <bean id="hikariDataSource" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="tenantPolicy" ref="myBatisRoutePolicy"/>
        <property name="configName" value="erp-sync-data-all"/>
        <property name="connectionPoolDriver" value="hikari"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="hikariSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="hikariDataSource"/>
        <property name="mapperLocations">
            <array>
                <value>classpath*:/table.mapper/*.xml</value>
            </array>
        </property>
    </bean>
    <!-- scan for mapper and let them be autowired, interface path -->
    <bean id="hikariDbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.fxiaoke.open.oasyncdata.db.dao"/>
        <property name="sqlSessionFactoryBeanName" value="hikariSqlSessionFactory"/>
    </bean>

</beans>