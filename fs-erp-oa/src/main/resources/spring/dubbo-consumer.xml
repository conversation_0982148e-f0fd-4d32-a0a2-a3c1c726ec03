<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
           http://code.alibabatech.com/schema/dubbo
           http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="fs-erp-oa"/>
    <dubbo:registry address="${dubbo.registry.address}"/>

    <dubbo:consumer timeout="30000" filter="tracerpc" retries="0"/>

<!--    <dubbo:reference id="enterpriseEditionService"-->
<!--                     interface="com.facishare.uc.api.service.EnterpriseEditionService"-->
<!--                     protocol="dubbo" timeout="3000"/>-->
    <dubbo:reference id="ssoLoginService" interface="com.facishare.userlogin.api.service.SSOLoginService"/>
    <dubbo:reference id="activeSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService"/>
    <dubbo:reference id="employeeProviderService"
                     interface="com.facishare.organization.api.service.EmployeeProviderService" protocol="dubbo"
                     version="5.7"/>
    <dubbo:reference id="departmentProviderService"
                     interface="com.facishare.organization.api.service.DepartmentProviderService" protocol="dubbo"
                     version="5.7"/>
    <dubbo:reference id="openMessageBatchService" interface="com.facishare.qixin.api.open.OpenMessageBatchService" async="true" protocol="dubbo" retries="0" timeout="5000"/>
</beans>