<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:component-scan base-package="com.fxiaoke.open.oasyncdata,com.fxiaoke.open.erpsyncdata"/>
    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="erp-sync-data-all"/>
    <!-- JVM，logback 监控上报 -->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!-- crmOAMessageListener -->
    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" init-method="start">
        <constructor-arg name="configName" value="fs-erp-sync-oa-mq2"/>
        <constructor-arg name="messageListener" ref="crmOAMessageListener"/>
        <property name="nameServerKey" value="name.server"/>
        <property name="consumeTopicKey" value="consume.topic"/>
        <property name="groupNameKey" value="consumer.group"/>
    </bean>
    <!-- crmApprovalMessageListener -->
    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" init-method="start">
        <constructor-arg name="configName" value="fs-erp-sync-oa-mq2"/>
        <constructor-arg name="messageListener" ref="crmApprovalMessageListener"/>
        <property name="nameServerKey" value="approval.name.server"/>
        <property name="consumeTopicKey" value="approval.consume.topic"/>
        <property name="groupNameKey" value="approval.consumer.group"/>
    </bean>
    <!-- paasMetaMessageListener -->
    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" init-method="start">
        <constructor-arg name="configName" value="fs-erp-sync-oa-mq2"/>
        <constructor-arg name="messageListener" ref="paasMetaMessageListener"/>
        <property name="nameServerKey" value="meta.name.server"/>
        <property name="consumeTopicKey" value="meta.consume.topic"/>
        <property name="groupNameKey" value="meta.consumer.group"/>
    </bean>
    <!--    这个是旧mq封装-->
    <!--    <bean class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
    <!--        <constructor-arg index="0" value="fs-erp-sync-oa-mq2"/>-->
    <!--        <constructor-arg index="1" value="name.server"/>-->
    <!--        <constructor-arg index="2" value="consumer.group"/>-->
    <!--        <constructor-arg index="3" value="consume.topic"/>-->
    <!--        <constructor-arg index="4" ref="crmOAMessageListener"/>-->
    <!--    </bean>-->
    <!--    <bean id="approvalMQProcessor" name="approvalMQProcessor" class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
    <!--        <constructor-arg index="0" value="fs-erp-sync-oa-mq2"></constructor-arg>-->
    <!--        <constructor-arg index="1" value="approval.name.server"></constructor-arg>-->
    <!--        <constructor-arg index="2" value="approval.consumer.group"></constructor-arg>-->
    <!--        <constructor-arg index="3" value="approval.consume.topic"></constructor-arg>-->
    <!--        <constructor-arg index="4" ref="crmApprovalMessageListener"></constructor-arg>-->
    <!--    </bean>-->

    <!--    <bean id="paasMetaMessageProcessor" name="paasMetaMessageProcessor" class="com.facishare.common.rocketmq.AutoConfRocketMQProcessor" init-method="init">-->
    <!--        <constructor-arg index="0" value="fs-erp-sync-oa-mq2"></constructor-arg>-->
    <!--        <constructor-arg index="1" value="meta.name.server"></constructor-arg>-->
    <!--        <constructor-arg index="2" value="meta.consumer.group"></constructor-arg>-->
    <!--        <constructor-arg index="3" value="meta.consume.topic"></constructor-arg>-->
    <!--        <constructor-arg index="4" ref="paasMetaMessageListener"></constructor-arg>-->
    <!--    </bean>-->

    <!-- 日志拦截器 -->
    <bean id="logInterceptor" class="com.fxiaoke.open.oasyncdata.db.interceptor.LogInterceptor"/>

    <!-- 异常的统一处理 拦截器 -->
    <bean id="apiExceptionInterceptor" class="com.fxiaoke.open.oasyncdata.db.interceptor.ApiExceptionInterceptor"/>

<!--    <aop:config>-->
<!--&lt;!&ndash;        <aop:pointcut id="serviceMethods" expression="execution(* com.fxiaoke.open.oasyncdata.impl..*.*(..)) or execution(* com.fxiaoke.open.oasyncdata.manager..*.*(..))"/>&ndash;&gt;-->
<!--        <aop:pointcut id="serviceAndManagerMethods" expression="execution(* com.fxiaoke.open.oasyncdata.impl..*.*(..)) or execution(* com.fxiaoke.open.oasyncdata.manager..*.*(..))"/>-->

<!--        <aop:advisor order="0" pointcut-ref="serviceAndManagerMethods" advice-ref="logInterceptor"/>-->
<!--&lt;!&ndash;        <aop:advisor order="1" pointcut-ref="serviceMethods" advice-ref="apiExceptionInterceptor"/>&ndash;&gt;-->
<!--    </aop:config>-->
    <bean class="com.facishare.id.client.IDClient" name="idClient">
        <constructor-arg index="0" value="${erpsyncdata-id-client-host}"/>
        <constructor-arg index="1" value="${erpsyncdata-id-service-limit}"/>
    </bean>
    <!--okHttp-->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-erp-oa-all"/>
    <import resource="classpath:spring/dubbo-consumer.xml"/>
    <import resource="all-rest-api.xml"/>
    <import resource="crm-oa-rest.xml"/>
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxUploadSize" value="104857600"/>
        <property name="maxInMemorySize" value="40960"/>
    </bean>
    <!-- 文件传输-->
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>
    <import resource="classpath:spring/license-client.xml"/>
    <import resource="classpath:spring/xxl-job.xml"/>
    <import resource="classpath*:spring/mongo-store.xml"/>
    <import resource="classpath*:spring/dubbo-provider.xml"/>
</beans>