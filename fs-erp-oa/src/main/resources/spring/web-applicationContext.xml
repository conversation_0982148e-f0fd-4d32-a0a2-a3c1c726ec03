<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">


    <import resource="classpath*:spring/common-spring.xml"/>
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/oa-sync-data.xml"/>
    <!--接入CEP 参考原来fs-cep-plugin.xml，自定义了序列化类-->
    <import resource="classpath:spring/web-cep-plugin.xml"/>
</beans>