<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p" default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

  <bean id="crmRestRetrofitFactory" class="com.fxiaoke.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory" p:configNames="fs-crm-rest-api" init-method="init">
    <property name="okHttpSupport" ref="erOkHttpSupport"/>
    <property name="seializeNulls" value="true"/>
  </bean>
  <bean id="erOkHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-rest-api-http-support"/>

  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MetadataActionService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MetadataControllerService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MetadataDataService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectDescribeService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.DuplicatesearchService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectRecordTypeService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MetadataTagService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MetadataTagDataService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectDataService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MarketingService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MemberService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.LeadsPoolService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.WechatFanService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.FlowService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" id="crmDeviceRestService" p:type="com.fxiaoke.crmrestapi.service.CrmDeviceService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.RoleService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.SmartFormService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectLayoutService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.PaasMetadataRoleService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MetadataTenantService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.PartnerService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.RoleFieldPrivlidgeService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.SkuSpuService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.PaasGlobalDataService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.RoleV2Service">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.SyncFieldService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.CurrencyService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.CrmErService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>

  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.CountryAreaService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>

  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectValueMappingService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>

  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.TenantSceneService">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.ObjectDataServiceV3">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
  <bean class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.crmrestapi.service.MetadataControllerServiceV3">
    <property name="factory" ref="crmRestRetrofitFactory"/>
  </bean>
</beans>
