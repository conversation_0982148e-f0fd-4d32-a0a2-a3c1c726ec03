package oa;

import lombok.extern.slf4j.Slf4j;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath*:spring/web-applicationContext.xml"})
@Slf4j
public abstract class BaseTest {
    @BeforeClass
    public static void before() {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-erp-oa");
    }
}
