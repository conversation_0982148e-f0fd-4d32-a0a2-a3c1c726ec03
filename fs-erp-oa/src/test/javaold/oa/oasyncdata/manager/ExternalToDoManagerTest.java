package oa.oasyncdata.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.v3.GetByIdArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.open.oasyncdata.arg.AutoBindArg;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.OAAPLTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.manager.ExternalToDoManager;
import com.fxiaoke.open.oasyncdata.manager.SyncLogManager;
import com.fxiaoke.open.oasyncdata.model.OAAplApiName;
import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.model.OAObjectDataMqData;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.open.oasyncdata.service.OASettingService;
import com.fxiaoke.open.oasyncdata.service.OASyncLogService;
import com.google.common.collect.Lists;
import oa.BaseTest;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


public class ExternalToDoManagerTest extends BaseTest {

    @Autowired
    ExternalToDoManager externalToDoManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private OASyncLogService oaSyncLogService;


    @Test
    public void dealTodo() {
        String json ="{\"ea\":\"88521\",\"ei\":88521,\"senderId\":-10000,\"receiverIds\":[1000],\"sourceId\":\"672d9a307ed2a64bdad84ed8\",\"bizType\":\"452\",\"url\":\"todo?apiname\\u003dAccountObj\\u0026id\\u003d672d9a2d7fe4150001a3fba0\\u0026ea\\u003d88521\",\"title\":\"待处理的CRM审批流程\",\"content\":\"****流(2024-11-08 12****\",\"form\":[{\"key\":\"流程主题\",\"value\":\"客户审批流(2024-11-08 12:57)\"},{\"key\":\"客户名称\",\"value\":\"测试重试\"},{\"key\":\"1级行业\",\"value\":\"\"},{\"key\":\"客户级别\",\"value\":\"\"},{\"key\":\"成交状态\",\"value\":\"未成交\"},{\"key\":\"负责人\",\"value\":\"杨贤杰\"}],\"generateUrlType\":1,\"extraDataMap\":{\"activityId\":\"1\",\"objectApiName\":\"AccountObj\",\"activityInstanceId\":\"\",\"applicantId\":\"1000\",\"workflowInstanceId\":\"672d9a307896196f9e9c5ad0\",\"taskId\":\"672d9a307ed2a64bdad84ed8\",\"objectId\":\"672d9a2d7fe4150001a3fba0\"},\"groupKeys\":[]}";
        CreateTodoArg arg = JSONObject.parseObject(json, CreateTodoArg.class);
        CreateTodoResult todo = externalToDoManager.createTodo(arg,false,null);
        assert todo!=null;
    }


    @Test
    public void testResyncData(){
        String data="{\"ea\":\"88521\",\"ei\":88521,\"operators\":[1000],\"sourceId\":\"674ff44626024075751c3848\",\"bizType\":\"452\",\"handleUserIds\":[1000],\"generateUrlType\":0,\"extraDataMap\":{},\"groupKeys\":[]}";
        DealTodoArg dealTodoArg=JSONObject.parseObject(data,DealTodoArg.class);
        DealTodoResult dealTodoResult = externalToDoManager.dealTodo(dealTodoArg, false, null);
        System.out.println(dealTodoResult);
//        oaSyncLogService.resyncSettingCondition("88521");
    }


    @Test
    public void testResync() {
        String msg = "{\"body\":[{\"afterTriggerData\":{\"last_modified_time\":1684830899543,\"is_pause_login\":false,\"\n" +
                "version\":\"8\"},\"beforeTriggerData\":{\"last_modified_time\":1684830488037,\"version\":\"7\"},\"context\":{\"appId\":\"CRM\",\"tenantId\":\"81243\",\"userId\":\"1069\"},\"entityId\":\"PersonnelObj\",\"eventId\":\"646c7ab3000187000105416a\",\"objectId\":\"1080\",\"triggerType\":\"u\"}],\"dROp\"\n" +
                ":false,\"deleteOp\":false,\"describeDeleteOp\":false,\"describeUpdateOp\":false,\"fieldDeleteOp\":false,\"fieldUpdateOp\":false,\"insterOp\":false,\"invalidOp\":false,\"name\":\"object_data\",\"op\":\"u\",\"recoverOp\":false,\"tenantId\":\"81243\",\"updateOp\":true}";
        OAObjectDataMqData oaObjectDataMqData = JSONObject.parseObject(msg, OAObjectDataMqData.class);
        for (OAObjectDataMqData.EventObject eventObject : oaObjectDataMqData.getBody()) {
            String objectApiName = eventObject.getEntityId();
            if ("PersonnelObj".equals(objectApiName)) {

                String tenantId = eventObject.getContext().getTenantId();
                //设置了自动绑定规则的，才会更新账户映射
                Result<Object> genericInfo = oaSettingService.getGenericInfo(tenantId, OATenantEnum.OA_AUTO_BIND_FIELD,null);
                if (ObjectUtils.isNotEmpty(genericInfo.getData())) {
                    String objectDataId = eventObject.getObjectId();
                    HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
                    GetByIdArg getByIdArg = new GetByIdArg();
                    getByIdArg.setDescribeApiName(objectApiName);
                    getByIdArg.setDataId(objectDataId);
                    getByIdArg.setIncludeInvalid(true);
                    com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> getObjectData
                            = objectDataServiceV3.getById(headerObj, getByIdArg);
                    if (getObjectData.isSuccess() && ObjectUtils.isNotEmpty(getObjectData.getData())) {
                        AutoBindArg autoBindArg = JSONObject.parseObject(JSONObject.toJSONString(genericInfo.getData()), AutoBindArg.class);
                        ObjectData objectData = getObjectData.getData().getObjectData();
                        Object oaUsrId = objectData.get(autoBindArg.getOaUserIdField());
                        Object oaUserName = objectData.get(autoBindArg.getOaUserNameFiled());
                        if (ObjectUtils.isNotEmpty(oaUserName) && ObjectUtils.isNotEmpty(oaUsrId)) {
                            //两个字段都不为空，才设置映射。
                            String fxUser = objectData.get("user_id").toString();
                            String fxUserName = objectData.get("name").toString();
                            ErpFieldDataMappingEntity erpFieldDataMappingEntities = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).
                                    findOneData(tenantId, ErpFieldTypeEnum.employee_oa, fxUser, null);
                            if (ObjectUtils.isNotEmpty(erpFieldDataMappingEntities)) {
                                erpFieldDataMappingEntities.setErpDataId(oaUserName.toString());
                                erpFieldDataMappingEntities.setErpDataName(oaUsrId.toString());
                                erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(erpFieldDataMappingEntities);
                            } else {
                                erpFieldDataMappingEntities = new ErpFieldDataMappingEntity();
                                erpFieldDataMappingEntities.setErpDataName(oaUserName.toString());
                                erpFieldDataMappingEntities.setErpDataId(oaUsrId.toString());
                                erpFieldDataMappingEntities.setChannel(ErpChannelEnum.OA);
                                erpFieldDataMappingEntities.setDataType(ErpFieldTypeEnum.employee_oa);
                                erpFieldDataMappingEntities.setFsDataId(fxUser);
                                erpFieldDataMappingEntities.setFsDataName(fxUserName);
                                erpFieldDataMappingEntities.setTenantId(tenantId);
                                erpFieldDataMappingEntities.setCreateTime(System.currentTimeMillis());
                                erpFieldDataMappingEntities.setUpdateTime(System.currentTimeMillis());

                                erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).insert(erpFieldDataMappingEntities);
                            }
                        }
                    }
                }
            }


            Result<Object> genericInfo = oaSettingService.getGenericInfo("81243", OATenantEnum.OA_AUTO_BIND_FIELD,null);
            AutoBindArg autoBindArg = JSONObject.parseObject(JSONObject.toJSONString(genericInfo.getData()), AutoBindArg.class);

            OAAplApiName oaAplApiName = new OAAplApiName();
            oaAplApiName.setApiName("1111");
            oaAplApiName.setType(OAAPLTypeEnum.OA_REQUEST.getType());
            Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo("84801",null);
            List<OAAplApiName> aplApiNames = oaConnectInfo.getData().getConnectParams().getAplApiNames();
            //oa_request类型会替代之前的HEADER  URLSCRIPT /TODO
            ArrayList<String> otherAplType = Lists.newArrayList(OAAPLTypeEnum.HEADER.getType(), OAAPLTypeEnum.URLSCRIPT.getType(), OAAPLTypeEnum.TODO.getType());
            List<OAAplApiName> aplApiNameList =
                    aplApiNames.stream().filter(t -> (!oaAplApiName.getType().equals(t.getType()) && !otherAplType.contains(t.getType()))).collect(Collectors.toList());
            if (!StringUtils.isEmpty(oaAplApiName.getApiName())) {
                aplApiNameList.add(oaAplApiName);
            }



        }
    }
}