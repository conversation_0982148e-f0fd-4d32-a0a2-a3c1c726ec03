package oa.oasyncdata.redisionTest;


import com.alicp.jetcache.redisson.RedissonCacheBuilder;
import com.alicp.jetcache.support.FastjsonKeyConvertor;
import com.alicp.jetcache.support.JavaValueDecoder;
import com.alicp.jetcache.support.JavaValueEncoder;
import com.alicp.jetcache.support.KryoValueDecoder;
import com.alicp.jetcache.support.KryoValueEncoder;

import org.junit.Ignore;
import org.junit.Test;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;

import java.util.Random;
import java.util.concurrent.TimeUnit;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import oa.BaseTest;
import org.junit.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class RedisionTest  extends BaseTest {
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private RedissonClient redissonClient;
    @Test
    public void redissonTest() throws Exception {


        RedissonCacheBuilder.createBuilder()
                .keyConvertor(FastjsonKeyConvertor.INSTANCE)
                .valueEncoder(JavaValueEncoder.INSTANCE)
                .valueDecoder(JavaValueDecoder.INSTANCE)
                .redissonClient(redissonClient)
                .keyPrefix(new Random().nextInt() + "")
                .expireAfterWrite(500, TimeUnit.MILLISECONDS)
                .buildCache();
    }


}
