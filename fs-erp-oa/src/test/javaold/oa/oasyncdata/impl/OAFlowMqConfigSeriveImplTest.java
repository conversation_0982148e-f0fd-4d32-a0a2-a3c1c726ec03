package oa.oasyncdata.impl;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncLogDao;
import com.fxiaoke.open.oasyncdata.db.manager.TenantConfigurationManager;
import com.fxiaoke.open.oasyncdata.impl.OASyncLogServiceImpl;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAConnParamService;
import com.fxiaoke.open.oasyncdata.service.OAFlowMqConfigService;
import lombok.extern.slf4j.Slf4j;
import oa.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Ignore
@Slf4j
public class OAFlowMqConfigSeriveImplTest extends BaseTest {

    @Autowired
    OAFlowMqConfigService oaFlowMqConfigService;
    @Autowired
    private OASyncLogServiceImpl oaSyncLogService;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private OASyncLogDao oaSyncLogDao;
    @Autowired
    private OAConnParamService oaConnParamService;

    @Test
    public void addOrUpdate() {
        Result<List<OAConnectInfoVO>> listResult = oaConnParamService.listInfoByTenantId("88521");

//        oaSyncLogMappinsDao.deleteByObjectId("81961",new ObjectId("6321a0afe72e9f389af338fe"));

//        List<OASyncLogMappingDoc> oneDataByReceiver = oaSyncLogMappinsDao.findOneDataByReceiver("88521",
//                ObjectApiEnum.TO_SEND_SALES_ORDER_OBJ.getObjApiName(), "64edcbeb8e6c060001a61ce0456", Lists.newArrayList("1002"));


        QueryOASyncLogArg oaSyncLogArg=new QueryOASyncLogArg();
//        oaSyncLogArg.setDataName("82777测试客户004");
        oaSyncLogArg.setPage(1);
        oaSyncLogArg.setPageSize(20);
        oaSyncLogArg.setTenantId("89029");
//        oaSyncLogArg.setBusinessType("crmToDo");
//        oaSyncLogArg.setStartTime(1673510462000L);
//        oaSyncLogArg.setEndTime(1673510462000L);
        Result<OASyncLogResultArg> oaSyncLogResultArgResult = oaSyncLogService.queryOALogGroup(oaSyncLogArg,null);


        String tenantId = "81961";
        List<OAFlowMqConfigResult> oaFlowMqConfigEntities = new ArrayList<>();
        OAFlowMqConfigResult oaFlowMqConfigEntity = new OAFlowMqConfigResult();
        oaFlowMqConfigEntity.setId("775355790581694464");

        oaFlowMqConfigEntity.setTenantId(tenantId);
        oaFlowMqConfigEntity.setAplApiName("xxxx4");
        oaFlowMqConfigEntity.setEventType("xxxx5");
        oaFlowMqConfigEntity.setObjApiName("xxxx6");
        oaFlowMqConfigEntities.add(oaFlowMqConfigEntity);

        oaFlowMqConfigService.addOrUpdate(tenantId, oaFlowMqConfigEntities,null);
    }

    @Test
    public void testQueryInfo(){
        QueryOASyncLogArg queryOASyncLogArg=new QueryOASyncLogArg();
        queryOASyncLogArg.setTenantId("81961");
//        queryOASyncLogArg.setDataId("6321abd88e1c494a3a148984");
//        queryOASyncLogArg.setObjApiName("ApprovalTaskObj");
//        queryOASyncLogArg.setReceiverId("1000");
        Result<List<OASyncLogVO>> listResult = oaSyncLogService.queryOALog(queryOASyncLogArg);
    }

    @Test
    public void list() {
        for (int i = 0; i < 10; i++) {
            Set<String> grayEnvs = tenantConfigurationManager.getGrayEnvs();
        }


        String tenantId = "81961";
        Result<List<OAFlowMqConfigResult>> reuslt = oaFlowMqConfigService.list(tenantId);
        log.info(reuslt + "");
    }

    @Test
    public void deleteById() {

//        Map<String, String> params = new HashMap<>();
//        params.put("id", "775355790581694464");
//        String tenantId = "81961";
//        Result<String> result = oaFlowMqConfigService.deleteById(tenantId, params);
//        log.info(result + "");


//        String json = "{\n" +
//                "    \"tag\": \"object_01mo0__c\",\n" +
//                "    \"eventData\": {\n" +
//                "      \"status\": \"pass\",\n" +
//                "      \"instanceId\": \"6256616eabed564718897b27\",\n" +
//                "      \"workflowId\": \"6180bcb7f2bc5851ae045922\",\n" +
//                "      \"sourceWorkflowId\": \"apprLPV2FZBK29__crmappr\",\n" +
//                "      \"dataId\": \"6256616de6073500012869d4\",\n" +
//                "      \"tenantId\": \"71557\",\n" +
//                "      \"entityId\": \"object_01mo0__c\",\n" +
//                "      \"triggerType\": \"Create\",\n" +
//                "      \"callbackData\": null\n" +
//                "    },\n" +
//                "    \"id\": \"6256616eabed564718897b27\",\n" +
//                "    \"eventType\": \"instance_change\"\n" +
//                "  }";
//        JSONObject jsonObject = JSONObject.parseObject(json.getBytes(StandardCharsets.UTF_8), JSONObject.class);
//        String json1 = JSONObject.toJSONString(jsonObject);
//        HashMap<String, Object> body = GsonUtil.fromJson(json1,HashMap.class);
//        log.info(body+"");
    }
}