package oa.oasyncdata.impl;


import com.fxiaoke.open.oasyncdata.manager.InitOAApiManager;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.service.OAObjectFieldService;
import oa.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Ignore
public class OAObjectFieldServiceImplTest extends BaseTest {

    @Autowired
    OAObjectFieldService oaObjectFieldService;

    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private InitOAApiManager initOAApiManager;

    @Test
    public void testMappingUpsert(){

        initOAApiManager.createConnect("8196171","测试同步","测试同步");
        initOAApiManager.createConnect("8196171","测试同步","测试同步");




//        OASyncLogMappingDoc oaSyncLogMappingDoc=new OASyncLogMappingDoc();
//        oaSyncLogMappingDoc.setDataId("89989900098881");
//        ObjectId mappingDocId=ObjectId.get();
//        oaSyncLogMappingDoc.setId(mappingDocId);
//        oaSyncLogMappingDoc.setTenantId("81961");
//        oaSyncLogMappingDoc.setObjApiName("objApiname");
//        oaSyncLogMappingDoc.setBusinessDataId("89989900098881");
//
//        oaSyncLogMappinsDao.batchInsert("81961", Lists.newArrayList(oaSyncLogMappingDoc));

    }



    @Test
    public void addOrUpdateObjectField() {
        String tenantId = "0";
        List<OAObjectFieldVO> oaObjectFieldVOList = new ArrayList<>();
        OAObjectFieldVO oaObjFieldEntity = new OAObjectFieldVO();
        oaObjFieldEntity.setId("728581282145632262");
        oaObjFieldEntity.setTenantId("0");
        oaObjFieldEntity.setObjApiName("ApprovalTaskObj");
        oaObjFieldEntity.setLabel("审批时间(时间戳)");
        oaObjFieldEntity.setFieldApiName("reply_timestamp");
        oaObjFieldEntity.setReplaceName("#F064");
        oaObjFieldEntity.setUpdateTime(1650791409262l);
        oaObjFieldEntity.setCreateTime(1650791409262l);
        oaObjectFieldVOList.add(oaObjFieldEntity);

        OAObjectFieldVO oaObjFieldEntity1 = new OAObjectFieldVO();
        oaObjFieldEntity1.setTenantId("0");
        oaObjFieldEntity1.setObjApiName("ApprovalTaskObj");
        oaObjFieldEntity1.setLabel("测试字段(时间戳)");
        oaObjFieldEntity1.setFieldApiName("test");
        oaObjFieldEntity1.setReplaceName("#F10000");
        oaObjFieldEntity1.setUpdateTime(1650791409262l);
        oaObjFieldEntity1.setCreateTime(1650791409262l);
        oaObjectFieldVOList.add(oaObjFieldEntity1);
        
        oaObjectFieldService.addOrUpdateObjectField(tenantId, oaObjectFieldVOList,null);
    }
}