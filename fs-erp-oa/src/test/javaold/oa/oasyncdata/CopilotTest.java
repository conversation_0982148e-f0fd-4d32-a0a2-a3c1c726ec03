package oa.oasyncdata;

/**
 * <AUTHOR>
 * @create 2024/2/21 14:51
 * @desc
 */
public class CopilotTest {
    //请写出迪杰斯特拉的java实现,具体实现代码
    //具体的代码
public static void main(String[] args) {
        int[][] graph = {
                {0, 1, 5, 65535, 65535, 65535, 65535, 65535, 65535},
                {1, 0, 3, 7, 5, 65535, 65535, 65535, 65535},
                {5, 3, 0, 65535, 1, 7, 65535, 65535, 65535},
                {65535, 7, 65535, 0, 2, 65535, 3, 65535, 65535},
                {65535, 5, 1, 2, 0, 3, 6, 9, 65535},
                {65535, 65535, 7, 65535, 3, 0, 65535, 5, 65535},
                {65535, 65535, 65535, 3, 6, 65535, 0, 2, 7},
                {65535, 65535, 65535, 65535, 9, 5, 2, 0, 4},
                {65535, 65535, 65535, 65535, 65535, 65535, 7, 4, 0}
        };
        int start = 0;
        //具体实现dijkstra算法
        int[] shortPath = dijkstra(graph, start);
        for (int i = 0; i < shortPath.length; i++) {
            System.out.println("从" + start + "出发到" + i + "的最短距离为：" + shortPath[i]);
        }
    }
    //具体实现dijkstra算法
    public static int[] dijkstra(int[][] graph, int start) {
        int n = graph.length;
        int[] shortPath = new int[n];
        shortPath[0] = 0;
        String[] path = new String[n];
        for (int i = 0; i < n; i++) {
            path[i] = new String(start + "-->" + i);
        }
        int[] visited = new int[n];
        for (int count = 1; count < n; count++) {
            int k = -1;
            int d = Integer.MAX_VALUE;
            for (int i = 0; i < n; i++) {
                if (visited[i] == 0 && graph[start][i] < d) {
                    d = graph[start][i];
                    k = i;
                }
            }
            shortPath[k] = d;
            visited[k] = 1;
            for (int i = 0; i < n; i++) {
                if (visited[i] == 0 && graph[start][k] + graph[k][i] < graph[start][i]) {
                    graph[start][i] = graph[start][k] + graph[k][i];
                    path[i] = path[k] + "-->" + i;
                }
            }
        }
        for (int i = 0; i < n; i++) {
            System.out.println("从" + start + "出发到" + i + "的最短路径为：" + path[i]);
        }
        System.out.println("=====================================");
        return shortPath;
    }




}
