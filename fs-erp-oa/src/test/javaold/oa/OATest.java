package oa;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.MultiLevelCache;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.QueryOASettingArg;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.mongo.OASettingsDao;
import com.fxiaoke.open.oasyncdata.mongo.OASyncLogMappingsDao;
import com.fxiaoke.open.oasyncdata.db.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.dao.OASyncLogDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.BeanUtil;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.example.SSOLoginOASerivce;
import com.fxiaoke.open.oasyncdata.manager.*;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.*;
import com.fxiaoke.open.oasyncdata.util.HeaderScriptUtil;
import com.google.common.collect.Lists;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/24 17:40
 * @Version 1.0
 */

@Slf4j
public class OATest extends BaseTest {
    @Autowired
    private SSOLoginOASerivce ssoLoginOA;
    @Autowired
    private OASyncApiService oaSyncApiService;
    @Autowired
    private OAObjectFieldService oaObjectFieldService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private OASyncLogDao oaSyncLogDao;
    @Autowired
    private OASyncLogService oaSyncLogService;
    @Autowired
    private ConfigOARouteManager configOARouteManager;
    @Autowired
    private OASyncLogMappingsDao oaSyncLogMappingsDao;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private OAFlowManager oaFlowManager;
    @Autowired
    private ExternalToDoManager externalToDoManager;
    @Autowired
    private OAUserAuthService oaUserAuthService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private OASettingsDao oaSettingsDao;
    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private RedissonClient redissonClient;


    @Test
    public void testError() {
        log.info("test password:{}","8898898338489");
        log.info("test secret:{}","8898898338489");
        log.info("test secret2:{}","8898898338489");
        String message="";
        boolean result= StringUtils.isNotBlank(message);
        OAConnectInfoEntity connectInfo = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81243")).getOAConnectInfoById("81243",null);
        OAConnectParam oldOAConnectParam = GsonUtil.fromJson(connectInfo.getConnectParams(), OAConnectParam.class);

        HeaderScriptUtil.getHeaderMap(oldOAConnectParam.getUrlScript(), "http://**************:9095/seeyon/rest/token",null);
    }
    @Test
    public void testUpdate(){


        String scriptCode="import groovy.json.JsonSlurper\n" +
                "import sun.net.www.protocol.https.DelegateHttpsURLConnection\n" +
                "public static String getToken(){\n" +
                "    String token = null;\n" +
                "    String requestUrl = \"http://www.yunzhijia.com/gateway/oauth2/token/getAccessToken\";\n" +
                "    // 修改请求地址\n" +
                "    HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();\n" +
                "    // 设置连接主机服务器的超时时间：15000毫秒\n" +
                "    connection.setConnectTimeout(15000);\n" +
                "    connection.setUseCaches(false);\n" +
                "    connection.setRequestProperty(\"Content-Type\", \"application/json; charset=UTF-8\");\n" +
                "    connection.setRequestMethod(\"POST\");\n" +
                "    String paramJson = \"{ \\\"appId\\\": \\\"500000088\\\",\\\"secret\\\": \\\"hwW9BwVS7fzHv5XejeJG\\\", \\\"timestamp\\\":\"+ new Date().time+ \",\\\"scope\\\": \\\"app\\\"}\";\n" +
                "    // 设置读取远程返回的数据时间：60000毫秒\n" +
                "    connection.setReadTimeout(60000);\n" +
                "    connection.setDoOutput(true);\n" +
                "    connection.setDoInput(true);\n" +
                "    connection.getOutputStream().write(paramJson.getBytes(\"UTF-8\"));\n" +
                "    connection.connect();\n" +
                "    int code = connection.getResponseCode();\n" +
                "    if (code == 200) {\n" +
                "        InputStream inputStream = connection.getInputStream();\n" +
                "        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));\n" +
                "        String line;\n" +
                "        StringBuffer buffer = new StringBuffer();\n" +
                "        while ((line = bufferedReader.readLine()) != null) {\n" +
                "            buffer.append(line);\n" +
                "        }\n" +
                "        String str = buffer.toString();\n" +
                "        def jsonSlurper = new JsonSlurper();\n" +
                "        Map map = jsonSlurper.parseText(str);\n" +
                "        Map data = (Map)map.get(\"data\");\n" +
                "        token = data.get(\"accessToken\");\n" +
                "    } else {\n" +
                "        throw new RuntimeException(\"握手异常(\" + connection.getResponseCode() + \")！\" + connection.getResponseMessage());\n" +
                "    }\n" +
                "    return token;\n" +
                "}\n" +
                "\n" +
                "url = url + getToken()";
        log.info("exec groovy code:{}, get exception: ", scriptCode);
     log.info("password:{}","888998998912");
     log.info("phone:{}","888998998912");
     log.info("password:{}","{ public String getToken() {\\n      String token = null;\\n      String userName = \\\"fxxk\\\";       // 填写客户提供的账号\\n      String password = \\\"2a4944da-ecec-4a9c-8f00-140a43d43ba8\\\";}");
        OAConnectInfoEntity oaConnectInfoEntity2=new OAConnectInfoEntity();
        oaConnectInfoEntity2.setConnectParams("password:18989899812");
     log.info("pwd:{}", JSONObject.toJSONString(oaConnectInfoEntity2));
     log.info("password:{}","{\"connectParams\":\"{\\\"headerScript\\\":\\\"\\\",\\\"urlScript\\\":\\\"import groovy.json.JsonSlurper\\\\n  import sun.net.www.protocol.https.DelegateHttpsURLConnection\\\\n  import javax.crypto.Mac\\\\n  import javax.crypto.spec.SecretKeySpec\\\\n  \\\\n  public String getToken() {\\\\n      String token = null;\\\\n      String userName = \\\\\\\"fxxk\\\\\\\";       // 填写客户提供的账号\\\\n      String password = \\\\\\\"2a4944da-ecec-4a9c-8f00-140a43d43ba8\\\\\\\";   // 填写客户提供的密码\\\\n      String requestUrl = \\\\\\\"http://**************:9095/seeyon/rest/token/\\\\\\\" + userName + \\\\\\\"/\\\\\\\" + password;  // 修改请求地址\\\\n      HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();\\\\n      connection.setRequestMethod(\\\\\\\"GET\\\\\\\");\\\\n              // 设置连接主机服务器的超时时间：15000毫秒\\\\n              connection.setConnectTimeout(15000); connection.setUseCaches(false); connection.setRequestProperty(\\\\\\\"Content-Type\\\\\\\", \\\\\\\"text/xml; charset=UTF-8\\\\\\\");\\\\n                  // 设置读取远程返回的数据时间：60000毫秒\\\\n                  connection.setReadTimeout(60000); connection.setDoOutput(true); connection.setDoInput(true); connection.connect(); int code = connection.getResponseCode();\\\\n                  if (code == 200) {\\\\n                      InputStream inputStream = connection.getInputStream();\\\\n                      BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));\\\\n                      String line;\\\\n                      StringBuffer buffer = new StringBuffer();\\\\n                      while ((line = bufferedReader.readLine()) != null) {\\\\n                          buffer.append(line);\\\\n                      }\\\\n                      String str = buffer.toString();\\\\n                      def jsonSlurper = new JsonSlurper()\\\\n                      Map map = jsonSlurper.parseText(str)\\\\n                      token = map.get(\\\\\\\"id\\\\\\\")\\\\n                      }\\\\n                      else {\\\\n                          throw new RuntimeException(\\\\\\\"握手异常(\\\\\\\" + connection.getResponseCode() + \\\\\\\")！\\\\\\\" + connection.getResponseMessage())\\\\n                          }\\\\n                          return url + \\\\\\\"?token=\\\\\\\" + token;\\\\n                      }\\\\n                      url = getToken();\\\\n                      return url;\\\",\\\"ssoAuthUrl\\\":\\\"http://**************:9095/seeyon/thirdpartyController.do?ticket=\\\",\\\"resultFormat\\\":{\\\"codeName\\\":\\\"success1\\\",\\\"msgName\\\":\\\"errorMsgs\\\",\\\"dataName\\\":\\\"name\\\",\\\"successCode\\\":\\\"true\\\",\\\"xmlJsonField\\\":\\\"\\\"},\\\"header\\\":{\\\"Content-Type\\\":\\\"application/json\\\",\\\"Cache-Control\\\":\\\"no-cache\\\"},\\\"aplApiNames\\\":[{\\\"apiName\\\":\\\"convert_push__c\\\",\\\"type\\\":\\\"oaRequest\\\"},{\\\"apiName\\\":\\\"loginurl__c\\\",\\\"type\\\":\\\"dataDetail\\\"}],\\\"notifyNotBindUse\\\":true}\",\"createTime\":1617332314278,\"enterpriseName\":\"zsl测试企业022\",\"id\":\"661901148825190400\",\"tenantId\":\"81243\",\"updateTime\":1617332314278}");
    }
    @Test
    public void testOa(){
        String accessTokenKey = "WebHook_OA_accessToken" + "500818514" + "PtnzuIn1rXRcBWJ5zMuu";
//        Long del1 = redisDataSource.get().del(accessTokenKey);
        String accessToken = redisDataSource.get().get("WebHook_OA_accessToken500818514PtnzuIn1rXRcBWJ5zMuu");
        redisDataSource.get().setex(accessTokenKey,   100L,"9099090");

        Long ttl = redisDataSource.get().ttl(accessTokenKey);

//        String refreshTokenKey = "WebHook_OA_refreshToken" + "*********" + "mFFUzB4PCnGYLjanoIS8";
//        Long del = redisDataSource.get().del(accessTokenKey);
//        Long del1 = redisDataSource.get().del(refreshTokenKey);


//        redisDataSource.get().setex(refreshTokenKey, 100L,"mFFUzB4PCnGYLjanoIS82");
        oaUserAuthService.authorizeWebHook("81961","APPURLWITHTICKETa4dacc10ec90de453715dd0f8c1accaa","AccountObj","dataId");

        String enterpriseAccount = enterpriseEditionService.getEnterpriseAccount(1000);
//        String url="https://oatest.qilupharma.com/services/OfsTodoDataWebService?wsdl";
//        String body="{\n" +
//                "    \"syscode\": \"fxiaoke\",\n" +
//                "    \"nodename\": \"节点名称1\",\n" +
//                "    \"requestname\": \"标题1:⻬鲁制药-单据编号: FKSQ210 *********-\",\n" +
//                "    \"creator\": \"009776\",\n" +
//                "    \"receiver\": \"009776\",\n" +
//                "    \"workflowname\": \"测试流程类型\",\n" +
//                "    \"pcurl\": \"/ecs-console/api/sso/login?appMenu=openConsole%7Cconsole%7C%2F%23%2Fexpenseclaim%3 FbillDefineId%3D0193b9b8e75b11eabd0e79c913ade5c3%26billMainId%3D74fff553f67511ebbb4e93 9d46c17b75%26taskId%3D4a8851f5f7d311eb8f655b97527e339b%26billDefineHistoryId%3Dcea00eb 0f4c611eb85cb83a56194ee9a%26scene%3DEXAMINE_APPROVAL%26isCheckUserTask%3Dtrue&cId=clie nt1&clientType=WEB&language=zh_CN&loginName=002876&timestamp=*************&sign=7e830c bbcbf41fe8bfcb0e4fba8cd487&type=sign\",\n" +
//                "    \"receivedatetime\": \"2021-08-08 07:00:43\",\n" +
//                "    \"appurl\": \"/ecs-console/api/sso/login?appMenu=openConsole%7Cconsole%7C%2F%23%2Fexpenseclaim%3 FbillDefineId%3D0193b9b8e75b11eabd0e79c913ade5c3%26billMainId%3D74fff553f67511ebbb4e93 9d46c17b75%26taskId%3D4a8851f5f7d311eb8f655b97527e339b%26billDefineHistoryId%3Dcea00eb 0f4c611eb85cb83a56194ee9a%26scene%3DEXAMINE_APPROVAL%26isCheckUserTask%3Dtrue%26MenuI d%3D56dcfd90a5bf11e8a1a103bb1accbe1b%26backMod%3Djump&cId=client1&clientType=APP&langu age=zh_CN&loginName=002876&timestamp=1628377243905&sign=7fc74e895e81e71deb82dee62a6333 05&type=sign\",\n" +
//                "    \"createdatetime\": \"2021-08-08 07:00:43\",\n" +
//                "    \"flowid\": \"123451116\"\n" +
//                "}";
//        String result = proxyHttpClient.postUrl(url, body, Maps.newHashMap());
//        log.info("result");
//
        try {
            URL oaUrl=new URL("https://oatest.qilupharma.com/services/OfsTodoDataWebService?wsdl");

        } catch (MalformedURLException e) {
            e.printStackTrace();
        }


    }
    @Test
    public void createSSOurl(){

        // 假设您有一个名为 json 的 JSONObject 对象
        JSONObject json = new JSONObject();
        json.put("name", "Alice");
        json.put("age", 25);

// 使用 JSONPath 表达式进行查询
        String name = JsonPath.read(json.toJSONString(), "name");
        int age = JsonPath.read(json.toJSONString(), "$.age");

        System.out.println("Name: " + name);
        System.out.println("Age: " + age);




        Map<String, String> getToken=  ssoLoginOA.getAccessToken();
        String login = ssoLoginOA.skipLogin(getToken.get("corpAccessToken"),getToken.get("corpId"),"***********");
        String concatDetailUrl = ssoLoginOA.concatDetailUrl(login, "https://www.ceshi112.com/XV/Home/Index#crm/list/=/AccountObj");
        log.info("login");
    }
    @Test
    public void testCreateOrder(){

        int size = ConfigCenter.OA_MQ_GRAY_TENANT_ID.size();
        boolean contains = ConfigCenter.OA_MQ_GRAY_TENANT_ID.contains("81961");
        log.info("size");


    }

    @Test
    public void testValidName() {
        String name = "/客户创建//客/户";
        String match = "[/]";
        // 创建 Pattern 对象
        Pattern r = Pattern.compile(match);
        // 现在创建 matcher 对象
        Matcher m = r.matcher(name.trim());
        String result = m.replaceAll("_");
        log.info(result);
    }

    @Test
    public void testNewInterface(){
//        List<String> objByCrmType = ObjectApiEnum.convertObjByCrmType("crmToDo");
//        //返回OA配置的状态配置
//        Result<List<OASyncApiVO>> bpmTask = oaSyncApiService.getOASyncApiList("81243", null,objByCrmType);
        Result<Map<String, Integer>> openApiStatus = oaSyncApiService.getOpenApiStatus("88521",null);
//        log.info(""+openApiStatus);

        Result<Integer> integerResult = oaSyncApiService.deleteOaEvent("81961", Lists.newArrayList("677608755330154419"),null);
        Result<Map<String, Integer>> openApiStatus1 = oaSyncApiService.getOpenApiStatus("81961",null);

    }

    @Test
    public void testField(){
        Result<Map<String, List<OAObjectFieldVO>>> objectField = oaObjectFieldService.getObjectField("1", "en");
        log.info("o");
    }

    @Test
    public void testField2(){

        oaObjectFieldService.addOrUpdateObjectFieldByApiName("81243","JournalObj","#J",null);
        int number=1;
        String paddedNumber = String.format("%03d", number);
        String tenantId="81243";
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(headerObj, "CasesObj");
        List<FieldDescribe> packageField = describe.getData().getDescribe().getFields().values().stream().filter(item -> !item.getDefineType().equals("custom")).collect(Collectors.toList());

    }

    @Test
    public void testEnum(){
        Collection<ErpChannelEnum> values = ErpChannelEnum.CHANNEL_CODE_MAP.values();
        Set<ErpChannelEnum> unPurchasedSet =  ErpChannelEnum.CHANNEL_CODE_MAP.values().stream().collect(Collectors.toSet());
        String name = ErpChannelEnum.DING_DING_CRM.name();
        log.info(name);
    }

    @Test
    public void testInitOa(){
        configOARouteManager.initRoute("100912","10091221212");

    }

    @Test
    public void testTransferData() throws ParseException {

        syncLogManager.reSyncData("88521","a50b24f705084a50bf26ffbb58a5b33b","675038f695cc600a98612e95",null);
//        Result<String> stringResult = oaSyncLogService.transferData("84801", 1661767337000L, 1664445737631l);


    }



    @Test
    public void testQueryCondition(){

        oaSyncLogService.reSyncAllFailData("88521");

        List<String> allTenantIds = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(DataBaseBatchIndexUtil.notTenantId)).listTenantId();
        List<String> tenantIds = allTenantIds.stream().distinct().collect(Collectors.toList());//去重
        ArrayList<String> strings = Lists.newArrayList("81243");
        tenantIds.removeAll(ConfigCenter.NOT_RE_SYNC_TENANT);

//        OASyncLogMappingDoc byId = oaSyncLogMappinsDao.getById("81961", new ObjectId("6321abdad515be7c6524dfa45"));


        Result<String> stringResult = oaSyncLogService.reSyncAllFailData("81961");

    }

    @Test
    public void testResyncData(){
//        OAFlowMqConfigEntity oaFlowMqConfigEntity = new OAFlowMqConfigEntity();
//        oaFlowMqConfigEntity.setEventType("2");
//        oaFlowMqConfigEntity.setTenantId("81961");
//        oaFlowMqConfigEntity.setObjApiName(ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getObjApiName());
//        List<OAFlowMqConfigEntity> oaFlowMqConfigEntities=null;
//        for (int i = 0; i < 10; i++) {
//          oaFlowMqConfigEntities = oaFlowManager.queryList("81961", oaFlowMqConfigEntity);
//        }
//
//        int size = oaFlowMqConfigEntities.size();

        Result<String> stringResult = oaSyncLogService.resyncSettingCondition("88521");

    }


    @Test
    public void batchGetObject(){
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf("84801"), CrmConstants.SYSTEM_USER);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName("AccountObj");
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        searchQueryInfo.setSearchSource("db");
        ArrayList<String> dataLists = Lists.newArrayList("6438f726c71def00018fc7a6", "6438f4b0c71def00018e9865", "6438f00fc71def00018c45e3", "6434c672c3dbba00012a153b");
        SearchTemplateQuery copy = BeanUtil.copy(searchQueryInfo, SearchTemplateQuery.class);
        searchQueryInfo.addFilter("_id", dataLists,FilterOperatorEnum.IN);
        //作废、正常、已删除数据都查询
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2","-1","0","1"), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> objectDataQueryListResultResult = objectDataServiceV3.queryList(headerObj, findV3Arg);
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2","-1","0","1"), FilterOperatorEnum.IN);
        copy.addFilter("_id", dataLists,FilterOperatorEnum.EQ);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(copy));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> objectDataQueryListResultResult1 = objectDataServiceV3.queryList(headerObj, findV3Arg);
        log.info("listObjectData");

    }

    @Test
    public void testSetting(){
//        Map<String,Object> requestParams= Maps.newHashMap();
//        String dataCenterId = (String) requestParams.get("dataCenterId");
//        OASettingVO oaSettingVO=new OASettingVO();
//        oaSettingVO.setType(OATenantEnum.OA_AUTO_BIND_FIELD.name());
//        oaSettingVO.setTenantId("812497");
//        AutoBindArg autoBindArg=new AutoBindArg();
//        autoBindArg.setOaUserIdField("field_vp260__c");
//        autoBindArg.setOaUserNameFiled("field_m20dS__c");
//        oaSettingVO.setConfiguration(JSONObject.toJSONString(autoBindArg));
        String json="{\n" +
                "    \"messageStatus\": [\n" +
                "        \"0\",\n" +
                "        \"2\"\n" +
                "    ],\n" +
                "    \"status\": false,\n" +
                "    \"businessTypes\": [\n" +
                "        \"crmToDo\"\n" +
                "    ],\n" +
                "    \"resyncEventTypes\": [\n" +
                "        \"1\",\n" +
                "        \"2\",\n" +
                "        \"3\"\n" +
                "    ],\n" +
                "    \"intervalMinutes\": 10,\n" +
                "    \"resyncCounts\": 2,\n" +
                "    \"currentDcId\": \"815999308504301568\"\n" +
                "}";
        OAMessageResyncRule oaMessageResyncRule=JSONObject.parseObject(json,new TypeReference<OAMessageResyncRule>(){});

        OASettingVO oaSettingVO = new OASettingVO();
        oaSettingVO.setConfiguration(JSONObject.toJSONString(oaMessageResyncRule));
        oaSettingVO.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
        oaSettingVO.setDataCenterId("948095164144320512");
        oaSettingVO.setTenantId("88521");

        Result<String> settingInfo = oaSettingService.upsertSettingInfo("88521", oaSettingVO,"948095164144320512");

        QueryOASettingArg queryOASettingArg=new QueryOASettingArg();
        queryOASettingArg.setTenantId("88521");
        queryOASettingArg.setCurrentDcId("948095164144320512");
        queryOASettingArg.setType(OATenantEnum.OA_SETTING_RETRY_MESSAGE.name());
        OASettingDoc configByType = oaSettingsDao.getConfigByType("88521", queryOASettingArg);
        configByType = oaSettingsDao.getConfigByType("88521", queryOASettingArg);
        if(ObjectUtils.isNotEmpty(configByType)){
            OASettingVO oaSettingVO4= BeanUtil.copy(configByType,OASettingVO.class);
            OAMessageResyncRule oaMessageResyncRule4= JSONObject.parseObject(oaSettingVO.getConfiguration(),OAMessageResyncRule.class);

        }

        log.info(settingInfo.toString());
    }
    @Test
    public void testQueryInfo(){
//        oaSettingService.deleteSettingInfo("89670",OATenantEnum.OA_AUTO_BIND_FIELD,null);
//        Result<List<OASettingVO>> listResult = oaSettingService.listSettingInfo("01", null, null);

        oaSettingService.getSettingInfo("88521", OATenantEnum.OA_AUTO_BIND_FIELD,"985505708282937344");

        Config config = redissonClient.getConfig();
        for (int i = 0; i < 10; i++) {
            oaSettingService.getSettingInfo("81243", OATenantEnum.OA_AUTO_BIND_FIELD,null);
            if(i==4){
                OASettingVO oaSettingVO=new OASettingVO();
                oaSettingVO.setType(OATenantEnum.OA_AUTO_BIND_FIELD.name());
                oaSettingVO.setTenantId("81243");
                oaSettingService.upsertSettingInfo("81243",oaSettingVO,"81243");
            }
//            CacheBuilder configTypeValue = globalCacheConfig.getLocalCacheBuilders().get("configTypeValue");
//            CacheBuilder configTypeValue1 = globalCacheConfig.getRemoteCacheBuilders().get("configTypeValue");
            MultiLevelCache<Object, Object> configTypeValue =
                    (MultiLevelCache<Object, Object>) cacheManager.getCache("configTypeValue");
            Object object = configTypeValue.get("81243OA_AUTO_BIND_FIELD");
            System.out.println(object);
        }

    }

    @Test
    public void testData(){
        Result<String> stringResult = oaSettingService.deleteSettingInfoById("88521", "65d72eafcfe50d454fcd3322");
        log.info("result");
    }



}
