package oa;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAUserAuthService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/8/23 16:24
 * @description
 */
@Ignore
@Slf4j
public class WensliOATest extends BaseTest {
    @Autowired
    private OAUserAuthService oaUserAuthService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Test
    public void test(){
        Result<String> stringResult = oaUserAuthService.authorizeWensli("80771", "APPURLWITHTICKET2a208a5e46bc26fa26bdf9f770984109", "apiname", "dataId");
        System.out.println(stringResult);
    }
    @Test
    public void testApproval(){
        String tenantIdStr="81961";
        Integer tenantId = Integer.valueOf(tenantIdStr);
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, -10000);
        String erpOrgObj = ObjectApiEnum.FS_APPROVAL_TASK_OBJ.getObjApiName();
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add("618d0577abb2092507a2280d");
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
        log.info("datalist:{}",dataListRes);
        ObjectData objectData = dataListRes.getData().getDataList().get(0);
        String data = JSONObject.toJSONString(objectData);
    }
}
