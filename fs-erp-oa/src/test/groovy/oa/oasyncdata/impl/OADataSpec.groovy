package oa.oasyncdata.impl

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3
import com.google.common.base.Splitter
import com.google.common.collect.ImmutableSet
import org.apache.commons.lang3.tuple.MutableTriple
import org.bson.Document
import org.junit.Ignore
import org.redisson.executor.CronExpression
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit
import java.util.function.BiConsumer
import java.util.stream.Collectors

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */

class OADataSpec extends  Specification{

    @Unroll
    def "校验数据符合条件的通知"(){
        given:


        when :
        String fullUser = "88521" + "." + 1000;
        String superAdmins="81138.1001;81138.1004;81772.1001;81961.1000;81772.1000;81243.1000;81243.1069;82777.1000;82777.1007;82814.1000;80787.1000;80771.1000;82335.1000;82370.1000;83384.1000;83952.1000;82814.1000;84035.1000;83384.1014;83363.1000;82379.1004;84375.1000;81772.1000;81243.1069;80771.1069;80771.1004;84801.1000;82857.1002;85103.1000;88521.1000;88466.1000;90314.1000";

        Set<String> SUPER_ADMINS = ImmutableSet.copyOf(Splitter.on(";").split(superAdmins));
        boolean contains = SUPER_ADMINS.contains(fullUser);
        boolean result=isTimeToTrigger(cronExpress,new Date(),5)
        then:
        expressResult==result
        where:
        cronExpress|expressResult
        "0 0 3 * * ? *"|false
        "* 0/1 * * * ? "|true

    }
    public static void main(String[] args) {
        String fullUser = "88521" + "." + 1000;
        String superAdmins="81138.1001;81138.1004;81772.1001;81961.1000;81772.1000;81243.1000;81243.1069;82777.1000;82777.1007;82814.1000;80787.1000;80771.1000;82335.1000;82370.1000;83384.1000;83952.1000;82814.1000;84035.1000;83384.1014;83363.1000;82379.1004;84375.1000;81772.1000;81243.1069;80771.1069;80771.1004;84801.1000;82857.1002;85103.1000;88521.1000;88466.1000;90314.1000";

        Set<String> SUPER_ADMINS = ImmutableSet.copyOf(Splitter.on(";").split(superAdmins));
        boolean contains = SUPER_ADMINS.contains(fullUser);
        System.out.println("fulluser");
        System.out.println(contains);
    }
    private boolean isTimeToTrigger(String expression,Date currentTime,Integer rangeMinute){
        try {
            CronExpression cron = new CronExpression(expression);
            Date startRange = new Date(currentTime.getTime() - rangeMinute * 60000);
            Date endRange = new Date(currentTime.getTime() + rangeMinute * 60000);

            while (startRange.before(endRange)) {
                if (cron.isSatisfiedBy(startRange)) {
                    return true;
                }
                startRange = new Date(startRange.getTime() + 60000); // 每次增加1分钟
            }
        } catch (Exception e) {
            log.info("xlljob express error message:{}",e.getMessage());
        }
        return false;
    }




}
