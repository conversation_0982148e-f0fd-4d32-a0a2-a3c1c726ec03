package oa.oasyncdata.impl

import com.fxiaoke.open.oasyncdata.mq.CRMApprovalMessageListener
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer
import org.apache.rocketmq.common.ServiceState
import org.junit.Test
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class MqConsumerTest extends Specification {
    @Test
    void testCreate() {
        when:
        def listener = new CRMApprovalMessageListener()
        def consumer = new AutoConfMQPushConsumer("fs-erp-sync-oa-mq2", listener)
        consumer.setNameServerKey("approval.name.server")
        consumer.setConsumeTopicKey("approval.consume.topic")
        consumer.setGroupNameKey("approval.consumer.group")
        consumer.start()

        then:
        consumer.getNativeConsumer() != null
        consumer.getNativeConsumer().defaultMQPushConsumerImpl.serviceState == ServiceState.RUNNING

        cleanup:
        consumer.close()
    }
}
