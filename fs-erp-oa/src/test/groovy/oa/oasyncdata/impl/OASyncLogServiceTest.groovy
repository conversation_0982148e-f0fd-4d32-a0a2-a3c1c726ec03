package oa.oasyncdata.impl

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.oasyncdata.result.base.Result
import com.fxiaoke.open.oasyncdata.service.OASyncLogService
import oa.oasyncdata.BaseSpockTest
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class OASyncLogServiceTest extends BaseSpockTest {
    @Autowired
    private OASyncLogService oaSyncLogService
    @Autowired
    private I18NStringManager i18NStringManager;

    @Test
    void reSyncData() {
        //审批流程
//        def data = oaSyncLogService.reSyncData("65952c1daca8eb527fd61a66",
//                "2c55c5172c8d413587f5f4b1f056ee4e",
//                "89029",
//                "en")
        //业务流程
        def data2 = oaSyncLogService.reSyncData("65aa28839357dd52a4534597",
                "948095164144320512",
                "88521",
                "en")
        assert data2.isSuccess()
    }

    @Test
    void reSyncAllFailData() {
        def result = oaSyncLogService.reSyncAllFailData("89029")
        println(result)
    }
}
