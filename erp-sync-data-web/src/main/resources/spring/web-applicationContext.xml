<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <import resource="web-common.xml"/>
    <import resource="web-dubbo-provider.xml"/>
    <import resource="web-dubbo-consumer.xml"/>
    <import resource="erp-sync-data-web-rest-client.xml"/>
    <import resource="web-mq-producer.xml"/>
    <import resource="classpath*:spring/common-spring.xml"/>
    <import resource="classpath*:spring/syncmain-context.xml"/>
    <import resource="classpath*:spring/syncconverter-context.xml"/>
    <import resource="classpath*:spring/syncwriter-context.xml"/>
    <import resource="classpath*:spring/common-db-proxy.xml"/>
    <import resource="classpath*:spring/admin-context.xml"/>
    <import resource="classpath*:spring/erp-preprocess-data.xml"/>
    <import resource="classpath*:spring/erp-apiproxy-data.xml"/>
    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-provider.xml"/>
    <!--  迁移服务  -->
    <import resource="classpath:spring/transfer-sharding-mongo.xml"/>
</beans>