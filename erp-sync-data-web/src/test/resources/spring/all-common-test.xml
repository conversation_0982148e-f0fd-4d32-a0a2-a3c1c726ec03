<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd  http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata"/>


    <dubbo:application name="${dubbo.application.name}"/>
    <dubbo:registry address="${dubbo.registry.address}"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>


    <!--统一异常捕捉 erp-sync-data-->
    <bean id="erpApiExceptionInterceptor" class="com.fxiaoke.open.erpsyncdata.dbproxy.interceptor.ApiExceptionInterceptor"/>
    <aop:config>
        <aop:aspect id="apiExceptionTransfer" ref="erpApiExceptionInterceptor" order="2">
            <aop:around pointcut=" execution(* com.fxiaoke.open.erpsyncdata.*.service..*.*(..))" method="around"/>
        </aop:aspect>
    </aop:config>


    <!--配置中心 -->
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:syncdata-applicationContext.properties"
          p:configName="erp-sync-data-all"/>

    <!-- 参数校验 -->
    <bean id="validator" class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean"/>
    <bean class="org.springframework.validation.beanvalidation.MethodValidationPostProcessor"/>

    <bean id="overrideOuterService" class="com.fxiaoke.open.erpsyncdata.preprocess.impl.ErpOverrideOuterServiceImpl"></bean>

    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="erp-sync-data-all"/>
</beans>