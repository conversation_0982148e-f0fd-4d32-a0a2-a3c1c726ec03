<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.web.transfer"/>
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <import resource="classpath:spring/transfer-sharding-mongo.xml"/>
    <import resource="classpath:spring/common-db-proxy.xml"/>
    <import resource="classpath:spring/common-spring.xml"/>

</beans>