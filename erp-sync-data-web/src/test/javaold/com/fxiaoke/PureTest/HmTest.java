package com.fxiaoke.PureTest;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.util.Calendar;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/9/8
 */
public class HmTest {
    public static void main(String[] args) {
        check("12:34");
        check("12:64");
        check("01:14");
        check("01");
        check("2022-04-01T16:00:00.000Z");
    }

    private static void check(String str) {
        try {
            Calendar calendar = DateUtil.parseByPatterns(str, "HH:mm");
            DateTime dateTime = DateUtil.date(calendar);
            System.out.println(str + " " + dateTime.hour(true) + " " + dateTime.minute());
        } catch (Exception exception) {
            System.out.println(str + " error");
        }
    }
}
