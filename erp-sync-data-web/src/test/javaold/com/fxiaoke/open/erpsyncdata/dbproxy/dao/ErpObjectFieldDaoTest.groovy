package com.fxiaoke.open.erpsyncdata.dbproxy.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectFieldsService
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.gson.Gson
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/3
 */
@Ignore
class ErpObjectFieldDaoTest extends BaseSpockTest {
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao

    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService

    @Test
    void findIdField() {
        def result = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).findIdField("79675","erpObj")
        println(result)
    }
    @Test
    void countByTenantIdAndObjectApiName() {
        def result = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).countByTenantIdAndObjectApiName("79675","saleout_fake_1600327793984","出")
        def result2 = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).queryFieldsByTenantIdAndObjectApiName("79675","saleout_fake_1600327793984","出",2,1);
        println(result)
    }

    @Test
    void updateErpObjectFieldsInOrder() {
        def json = "{\n" +
                "    \"actualErpObject\": {\n" +
                "        \"id\": \"683686940065005568\",\n" +
                "        \"dataCenterId\": \"663712151783211008\",\n" +
                "        \"channel\": \"ERP_K3CLOUD\",\n" +
                "        \"erpObjectType\": \"REAL_OBJECT\",\n" +
                "        \"erpObjectApiName\": \"test_object_md\",\n" +
                "        \"erpObjectExtendValue\": \"FIELD_SPLIT\",\n" +
                "        \"erpObjectName\": \"测试对象主从\",\n" +
                "        \"deleteStatus\": false\n" +
                "    },\n" +
                "    \"fakeErpObject\": [\n" +
                "        {\n" +
                "            \"id\": \"683686940132114432\",\n" +
                "            \"dataCenterId\": \"663712151783211008\",\n" +
                "            \"channel\": \"ERP_K3CLOUD\",\n" +
                "            \"erpObjectType\": \"SPLIT_OBJECT\",\n" +
                "            \"splitType\": \"NOT_SPLIT\",\n" +
                "            \"erpObjectApiName\": \"test_object_md.BillHead\",\n" +
                "            \"erpObjectExtendValue\": \"\",\n" +
                "            \"erpObjectName\": \"测试对象\",\n" +
                "            \"erpObjectFields\": [\n" +
                "                {\n" +
                "                    \"id\": \"683687505960501248\",\n" +
                "                    \"channel\": \"ERP_K3CLOUD\",\n" +
                "                    \"erpObjectApiName\": \"test_object_md.BillHead\",\n" +
                "                    \"fieldApiName\": \"number\",\n" +
                "                    \"fieldLabel\": \"编码\",\n" +
                "                    \"required\": false,\n" +
                "                    \"fieldDefineType\": \"id\",\n" +
                "                    \"fieldExtendValue\": \"\",\n" +
                "                    \"saveCode\": \"number.save\",\n" +
                "                    \"viewCode\": \"number.view\",\n" +
                "                    \"saveExtend\": \"\",\n" +
                "                    \"viewExtend\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"683688402769805312\",\n" +
                "                    \"channel\": \"ERP_K3CLOUD\",\n" +
                "                    \"erpObjectApiName\": \"test_object_md.BillHead\",\n" +
                "                    \"fieldApiName\": \"master_field_3\",\n" +
                "                    \"fieldLabel\": \"主字段3\",\n" +
                "                    \"required\": false,\n" +
                "                    \"fieldDefineType\": \"text\",\n" +
                "                    \"fieldExtendValue\": \"\",\n" +
                "                    \"saveCode\": \"master_field_3.save\",\n" +
                "                    \"viewCode\": \"master_field_3.view\",\n" +
                "                    \"saveExtend\": \"\",\n" +
                "                    \"viewExtend\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"683688288047202304\",\n" +
                "                    \"channel\": \"ERP_K3CLOUD\",\n" +
                "                    \"erpObjectApiName\": \"test_object_md.BillHead\",\n" +
                "                    \"fieldApiName\": \"master_field_2\",\n" +
                "                    \"fieldLabel\": \"主字段2\",\n" +
                "                    \"required\": false,\n" +
                "                    \"fieldDefineType\": \"number\",\n" +
                "                    \"fieldExtendValue\": \"\",\n" +
                "                    \"saveCode\": \"master_field_2\",\n" +
                "                    \"viewCode\": \"master_field_2\",\n" +
                "                    \"saveExtend\": \"\",\n" +
                "                    \"viewExtend\": \"\"\n" +
                "                },{\n" +
                "                    \"id\": \"683687656250802176\",\n" +
                "                    \"channel\": \"ERP_K3CLOUD\",\n" +
                "                    \"erpObjectApiName\": \"test_object_md.BillHead\",\n" +
                "                    \"fieldApiName\": \"master_field_1\",\n" +
                "                    \"fieldLabel\": \"主字段1\",\n" +
                "                    \"required\": false,\n" +
                "                    \"fieldDefineType\": \"text\",\n" +
                "                    \"fieldExtendValue\": \"\",\n" +
                "                    \"saveCode\": \"master_field_1.save\",\n" +
                "                    \"viewCode\": \"master_field_1.view\",\n" +
                "                    \"saveExtend\": \"\",\n" +
                "                    \"viewExtend\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"deleteStatus\": false\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"683688151681990656\",\n" +
                "            \"dataCenterId\": \"663712151783211008\",\n" +
                "            \"channel\": \"ERP_K3CLOUD\",\n" +
                "            \"erpObjectType\": \"SPLIT_OBJECT\",\n" +
                "            \"splitType\": \"DETAIL2DETAIL_SPLIT\",\n" +
                "            \"erpObjectApiName\": \"test_object_md.child\",\n" +
                "            \"erpObjectExtendValue\": \"test_object_md.child\",\n" +
                "            \"erpObjectName\": \"从对象\",\n" +
                "            \"erpObjectFields\": [\n" +
                "                {\n" +
                "                    \"id\": \"683688732676980736\",\n" +
                "                    \"channel\": \"ERP_K3CLOUD\",\n" +
                "                    \"erpObjectApiName\": \"test_object_md.child\",\n" +
                "                    \"fieldApiName\": \"child_number\",\n" +
                "                    \"fieldLabel\": \"编码\",\n" +
                "                    \"required\": false,\n" +
                "                    \"fieldDefineType\": \"id\",\n" +
                "                    \"fieldExtendValue\": \"\",\n" +
                "                    \"saveCode\": \"child_number.save\",\n" +
                "                    \"viewCode\": \"child_number.view\",\n" +
                "                    \"saveExtend\": \"\",\n" +
                "                    \"viewExtend\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"683688977691443200\",\n" +
                "                    \"channel\": \"ERP_K3CLOUD\",\n" +
                "                    \"erpObjectApiName\": \"test_object_md.child\",\n" +
                "                    \"fieldApiName\": \"detail_field_2\",\n" +
                "                    \"fieldLabel\": \"从字段2\",\n" +
                "                    \"required\": false,\n" +
                "                    \"fieldDefineType\": \"text\",\n" +
                "                    \"fieldExtendValue\": \"\",\n" +
                "                    \"saveCode\": \"detail_field_2.save\",\n" +
                "                    \"viewCode\": \"detail_field_2.view\",\n" +
                "                    \"saveExtend\": \"\",\n" +
                "                    \"viewExtend\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"683688904073019392\",\n" +
                "                    \"channel\": \"ERP_K3CLOUD\",\n" +
                "                    \"erpObjectApiName\": \"test_object_md.child\",\n" +
                "                    \"fieldApiName\": \"detail_field_1\",\n" +
                "                    \"fieldLabel\": \"从字段1\",\n" +
                "                    \"required\": false,\n" +
                "                    \"fieldDefineType\": \"text\",\n" +
                "                    \"fieldExtendValue\": \"\",\n" +
                "                    \"saveCode\": \"detail_field_1.save\",\n" +
                "                    \"viewCode\": \"detail_field_1.view\",\n" +
                "                    \"saveExtend\": \"\",\n" +
                "                    \"viewExtend\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"deleteStatus\": false\n" +
                "        }\n" +
                "    ]\n" +
                "}"
        ErpObjectRelationshipResult result = new Gson().fromJson(json,ErpObjectRelationshipResult.class)
        System.out.println(result)

        Result<Void> result2 = erpObjectFieldsService.updateErpObjectFieldsInOrder("80774","663712151783211008",result)
        System.out.println(result2)
    }

    @Test
    void fieldTest() {
        ErpObjectFieldEntity entity = new ErpObjectFieldEntity();
        entity.setTenantId("80774")
        entity.setErpObjectApiName("test_object_md.child")
        List<ErpObjectFieldEntity> list = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("80774")).queryList(entity)
        System.out.println(list)
    }

    @Test
    void test2() {
        ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
        erpObjectDescResult.id="683686940065005568"
        erpObjectDescResult.erpObjectApiName="test_object_md"
        erpObjectDescResult.splitSeq=2
        Result<ErpObjectRelationshipResult> result = erpObjectFieldsService.queryErpObjectAndFieldsByActualObjAndDcId("80774",1000,
        erpObjectDescResult,"663712151783211008")
        System.out.println(result)
    }
}
