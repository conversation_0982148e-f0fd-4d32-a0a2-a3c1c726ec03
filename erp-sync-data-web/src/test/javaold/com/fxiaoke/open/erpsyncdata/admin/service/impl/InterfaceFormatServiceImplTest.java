package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.InterfaceFormatService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceFormatResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 10:48 2020/9/16
 * @Desc:
 */
@Ignore
@Slf4j
public class InterfaceFormatServiceImplTest extends BaseTest {
    @Autowired
    private InterfaceFormatService interfaceFormatService;

    @Test
    public void getAddDataInterfaceFormat() {
        ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
        erpObjectDescResult.setErpObjectApiName("saleorder");
        Result<ErpInterfaceFormatResult> addDataInterfaceFormat = interfaceFormatService.getAddDataInterfaceFormat("79675",1003,erpObjectDescResult,"", null, null,null,null);
        String addStr=GsonUtil.toJson(addDataInterfaceFormat.getData());
        log.info("Result={}",addDataInterfaceFormat);
    }

    @Test
    public void getQueryDataListInterfaceFormat() {
        ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
        erpObjectDescResult.setErpObjectApiName("saleorder");
        Result<ErpInterfaceFormatResult> queryDataListInterfaceFormat = interfaceFormatService.getQueryDataListInterfaceFormat("79675",1003,erpObjectDescResult,"", null, null,null);
        String queryStr=GsonUtil.toJson(queryDataListInterfaceFormat.getData());
        Result<ErpInterfaceFormatResult> addDataInterfaceFormat = interfaceFormatService.getAddDataInterfaceFormat("79675",1003,erpObjectDescResult,"", null, null,null,null);
        String addStr=GsonUtil.toJson(addDataInterfaceFormat.getData());
        log.info("Result={}",queryDataListInterfaceFormat);
    }


}