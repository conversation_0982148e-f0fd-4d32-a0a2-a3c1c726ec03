package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ProductiveService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Sets;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Ignore
public class ProductiveServiceImplTest extends BaseTest {
    @Resource
    private ProductiveService productiveService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;


    @Test
    public void isObjectPreset() {
        Result<Integer> result = productiveService.isObjectPreset("SalesOrderProductObj",
                "SAL_SaleOrder.SaleOrderEntry");
        System.out.println(result);
    }

    @Test
    public void getFieldMappingData() {
        Result<FieldMappingsResult> result = productiveService.getFieldMappingData("SAL_SaleOrder.SaleOrderEntry",
                "SalesOrderProductObj");
        System.out.println(result);
    }

    @Test
    public void getFieldMappingByName(){


//        Set<ObjectFieldResult.Option> sourceOptions= Sets.newHashSet();
//        ObjectFieldResult.Option option1=new ObjectFieldResult.Option();
//        option1.setValue("00u1");
//        option1.setLabel(1);
//        ObjectFieldResult.Option option2=new ObjectFieldResult.Option();
//        option2.setValue("0032");
//        option2.setLabel(2);
//        sourceOptions.add(option1);
//        sourceOptions.add(option2);
//
//        Set<ObjectFieldResult.Option> destOptions= Sets.newHashSet();
//
//        ObjectFieldResult.Option option11=new ObjectFieldResult.Option();
//        option11.setValue("00u");
//        option11.setLabel(1);
//        ObjectFieldResult.Option option22=new ObjectFieldResult.Option();
//        option22.setValue("002");
//        option22.setLabel(2);
//        destOptions.add(option11);
//        destOptions.add(option22);
//
//        List<Object> destList = destOptions.stream().map(ObjectFieldResult.Option::getValue).collect(Collectors.toList());
//        List<Object> sourceList = sourceOptions.stream().map(ObjectFieldResult.Option::getValue).collect(Collectors.toList());
//        boolean b1 = sourceList.retainAll(destList);
//        boolean b = sourceOptions.retainAll(destOptions);

        Result<SyncPloyDetailResult> syncPloyDetailResult = adminSyncPloyDetailService.getById("81243", "eb7187e3970e42718b1855250ddead25",null);
        productiveService.getFieldTemplateByName("AccountObj","BD_Customer.BillHead",syncPloyDetailResult.getData(),null);
    }
}
