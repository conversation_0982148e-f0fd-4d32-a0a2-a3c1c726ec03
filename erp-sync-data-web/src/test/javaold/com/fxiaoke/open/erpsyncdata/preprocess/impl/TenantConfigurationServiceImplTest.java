package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CopyTenantConfigurationArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CopyService;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 16:29 2020/12/23
 * @Desc:
 */
@Ignore
public class TenantConfigurationServiceImplTest extends BaseTest {
    @Autowired
    private CopyService copyService;
    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @Test
    public void copyTenantPloyAndPloyDetail() {
        CopyTenantConfigurationArg.CopyPloyAndDetailArg arg=new CopyTenantConfigurationArg.CopyPloyAndDetailArg();
        arg.setSourceTenantId("81243");
        arg.setSourceDataCenterId("628312575457230848");
        arg.setTargetTenantId("81961");
        arg.setDestDataCenterId("764884533511847936");
        arg.setDirection(2);
//        arg.setObjApiNames(Lists.newArrayList("ContactObj"));
        Result<List<String>> listResult = copyService.copyTenantPloyAndPloyDetail(arg,null);
        System.out.println("");
    }

    @Test
    public void copyTenantErpObj() {
        CopyTenantConfigurationArg.CopyErpObjArg arg=new CopyTenantConfigurationArg.CopyErpObjArg();
        arg.setSourceTenantId("81772");
        arg.setSourceDataCenterId("642530472589131776");
        arg.setTargetTenantId("79675");
        arg.setDestDataCenterId("663935893406875648");
        arg.setObjApiNames(Lists.newArrayList("PRD_MO"));
        Result<List<String>> listResult = copyService.copyTenantErpObj(arg,null);
        System.out.println(listResult);
    }

    @Test
    public void copyTenantEai() {
        CopyTenantConfigurationArg.CopyEaiConfigArg arg=new CopyTenantConfigurationArg.CopyEaiConfigArg();
        arg.setSourceTenantId("82814");
        arg.setSourceDataCenterId("734091863128670208");
        arg.setTargetTenantId("82814");
        arg.setDestDataCenterId("7340918631286709998");
        Result<List<String>> listResult = copyService.copyTenantEaiConfig(arg,null);
        System.out.println(listResult);
    }

    @Test
    public void copyTenantConnectInfo() {
        CopyTenantConfigurationArg.CopyConnectArg arg=new CopyTenantConfigurationArg.CopyConnectArg();
        arg.setSourceTenantId("81138");
        arg.setSourceDataCenterId("620483680381042688");
        arg.setTargetTenantId("81961");
        Result<List<String>> listResult = copyService.copyTenantConnectInfo(arg.getSourceTenantId(),arg.getSourceDataCenterId(),arg.getTargetTenantId(),null);
        System.out.println("");
    }

    @Test
    public void updateConfig() {
//        Result<ErpTenantConfiguration> configuration = tenantConfigurationService.queryConfig(TenantConfigurationTypeEnum.GRAY_TENANTS);
//        System.out.println(configuration);
//        Result<Void> booleanResult = tenantConfigurationService.updateConfig(ConfigCenterConfig.GRAY_TENANTS,"82777;81243");
//        System.out.println(booleanResult);
    }

    @Test
    public void getUseDirectOrderChange() {
        boolean useDirectOrderChange = configCenterConfig.getUseDirectOrderChange("84801");
        System.out.println(useDirectOrderChange);
    }
}