package com.fxiaoke.open.erpsyncdata.preprocess.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.common.data.SyncDataDependData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncConditionsData
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/5/18
 */
@Ignore
class ErpOverrideCRMOuterServiceImplTest extends BaseSpockTest {
    @Autowired
    private ErpOverrideOuterServiceImpl overrideOuterService

    @Test
    public void testGetRelateList() {
        SyncDataDependData syncDataDependData=SyncDataDependData.builder().build()
        syncDataDependData.setTenantId("81243")
        syncDataDependData.setObjApiName("SAL_SaleOrder.BillHead")
        syncDataDependData.setDataId("101650#XSDD001067")
        syncDataDependData.setSyncDataId("8db9cc16083c4730b968529fd08b361f")
        SyncDataDependData.DependMappingData dependMappingData=SyncDataDependData.DependMappingData.builder().build()
        dependMappingData.setDependSourceApiName("BD_Customer.BillHead")
        dependMappingData.setDependDataId("CUST00322")
        dependMappingData.setDependDestApiName("AccountObj")
        syncDataDependData.setDependMappingDataList(Lists.newArrayList(dependMappingData))
        def data = overrideOuterService.saveDependData("81243", syncDataDependData);
        def result1 = overrideOuterService.listCrmDetailsOrder(
                "81772","81772","SalesOrderProductObj","order_id","609cdfab38ec88000143ea78",new SyncConditionsData(),200,0
        )
        def result2 = overrideOuterService.listDetailDatasByIdAndFilter(
                "81772","81772",1,"SalesOrderProductObj","order_id","609cdfab38ec88000143ea78",new SyncConditionsData(),200,0
        )
        printf(JacksonUtil.toJson(result1))
        printf(JacksonUtil.toJson(result2))
    }

    @Test void testGetObjectMainAttribute(){
        def attribute = overrideOuterService.getObjectMainAttribute("81243");
        println ""
    }
}
