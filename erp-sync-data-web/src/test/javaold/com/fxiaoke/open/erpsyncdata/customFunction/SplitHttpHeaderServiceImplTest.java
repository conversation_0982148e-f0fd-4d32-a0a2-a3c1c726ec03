//package com.fxiaoke.open.erpsyncdata.customFunction;
//
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
//import com.fxiaoke.open.erpsyncdata.web.service.customFunction.SplitHttpHeaderServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.HashMap;
//import java.util.Map;
//
//@Ignore
//@Slf4j
//public class SplitHttpHeaderServiceImplTest extends BaseTest {
//    @Autowired
//    private SplitHttpHeaderServiceImpl splitHttpHeaderService;
//
//    @Test
//    public void test1() {
//
//        CustomFunctionCommonArg commonArg = new CustomFunctionCommonArg();
//        Map<String, String> header = new HashMap<>();
//
//        header.put("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
//        header.put("Accept", "application/json");
//        header.put("x-csrf-token","fetch");
//        header.put("Authorization","Basic Q1JNOkVlQWJGSnllcVVobGhBUEtIZ2hxVjlSak10emRyZURRTFtMYmJ2eUQ=");
//        Map<String,Object>requestArg=new HashMap<>();
//        requestArg.put("requestHeader",header);
//        requestArg.put("requestUrl","https://my300316-api.saps4hanacloud.cn/sap/opu/odata/sap/API_SALES_ORDER_SRV/");
//
//        commonArg.setParams(JacksonUtil.toJson(requestArg));
//        commonArg.setTenantId("123");
//        commonArg.setType("splitHttpHeader");
//        Result<String> stringResult = splitHttpHeaderService.executeLogic(commonArg);
//        Map<String,Object>ds=JacksonUtil.fromJson(stringResult.getData(),Map.class);
//        log.info(JacksonUtil.toJson(ds));
//    }
//
//
//
//}
