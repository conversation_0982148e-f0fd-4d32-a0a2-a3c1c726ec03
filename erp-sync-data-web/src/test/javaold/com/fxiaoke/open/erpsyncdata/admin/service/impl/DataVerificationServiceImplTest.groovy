package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.TypeReference
import com.facishare.converter.EIEAConverter
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager
import com.fxiaoke.open.erpsyncdata.admin.result.DataVerificationResult
import com.fxiaoke.open.erpsyncdata.admin.service.DataVerificationService
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataVerificationIdStatusDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataVerificationTaskDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationIdStatus
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.collect.Lists
import org.junit.Ignore
import spock.lang.Specification

/**
 * <AUTHOR> @Date: 16:20 2022/11/9
 * @Desc:
 */
@Ignore
class DataVerificationServiceImplTest extends Specification {
    DataVerificationTaskDao dataVerificationTaskDao;
    DataVerificationIdStatusDao dataVerificationIdStatusDao;
    FileManager fileManager;
    EIEAConverter eieaConverter
    AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    ErpHistoryDataTaskDao erpHistoryDataTaskDao;
    ErpTempDataDao erpTempDataDao;
    ErpConnectInfoDao erpConnectInfoDao;
    ErpObjectRelationshipDao erpObjectRelationshipDao;
    ErpFieldExtendDao erpFieldExtendDao;
    SyncDataMappingsDao adminSyncDataMappingsDao;

    private DataVerificationService dataVerificationService;

    private Integer index = 0;
    private static String tenantId = "84801"
    private static String ployEntry = "{\"createTime\":1652949847213,\"destDataCenterId\":\"780777210996457472\",\"destObjectApiName\":\"ProductObj\",\"destTenantIds\":[\"84801\"],\"destTenantType\":1,\"detailObjectMappings\":[],\"detailObjectSyncConditions\":[],\"fieldMappings\":[{\"destApiName\":\"record_type\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"owner\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1000\",\"valueType\":1},{\"destApiName\":\"product_status\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"name\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"comName\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"category\",\"destType\":\"category\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialGroup.FNumber\",\"sourceType\":\"category\",\"value\":\"\"},{\"destApiName\":\"batch_sn\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"1\"},{\"destOption\":\"3\",\"sourceOption\":\"3\"},{\"destOption\":\"2\",\"sourceOption\":\"2\"}],\"sourceApiName\":\"VirtualHasBatchAndSerial\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"is_package\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"0\"},{\"destOption\":true,\"sourceOption\":\"1\"}],\"sourceApiName\":\"SubHeadEntity.FSuite\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"product_code\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceType\":\"id\",\"value\":\"\"},{\"destApiName\":\"product_spec\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSpecification\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"barcode\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SubHeadEntity.FBARCODE\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"field_898d1__c\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"false\"},{\"destOption\":true,\"sourceOption\":\"true\"}],\"sourceApiName\":\"SubHeadEntity3.FIsPR\",\"sourceType\":\"true_or_false\",\"value\":\"\"},{\"destApiName\":\"price\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSubHeadEntity.FPurPrice_CMK\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"unit\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"3\",\"sourceOption\":\"WJ001\"},{\"destOption\":\"1\",\"sourceOption\":\"002\"},{\"destOption\":\"10\",\"sourceOption\":\"UOM015\"},{\"destOption\":\"7\",\"sourceOption\":\"100011\"}],\"sourceApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"sourceType\":\"select_one\",\"value\":\"\"}],\"id\":\"a78b668ae39849b284f9e8e696d247cb\",\"integrationStreamName\":\"erp->crm物料\",\"integrationStreamNodes\":{},\"isValid\":true,\"sourceDataCenterId\":\"780777150699143168\",\"sourceObjectApiName\":\"BD_MATERIAL.BillHead\",\"sourceTenantIds\":[\"84801\"],\"sourceTenantType\":2,\"status\":1,\"syncConditions\":{\"apiName\":\"BD_MATERIAL.BillHead\",\"filters\":[],\"isSyncForce\":true},\"syncPloyId\":\"84801\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 0-23 * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncDependForce\":false,\"syncType\":\"get\"},\"tenantId\":\"84801\",\"updateTime\":1668138667007}"
    private static List<Map<IdSyncStatus, Map<String, DataVerificationIdStatus>>> tempMap = JSONObject.parseObject("[{\"not_temp\":{\"A\":{\"dataId\":\"A\",\"status\":\"not_temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"B\":{\"dataId\":\"B\",\"status\":\"not_temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"C\":{\"dataId\":\"C\",\"status\":\"not_temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"}},\"temp\":{}},{\"not_temp\":{\"C\":{\"dataId\":\"C\",\"status\":\"not_temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"}},\"temp\":{\"A\":{\"dataId\":\"A\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"B\":{\"dataId\":\"B\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"}}},{\"not_temp\":{},\"temp\":{\"A\":{\"dataId\":\"A\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"B\":{\"dataId\":\"B\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"C\":{\"dataId\":\"C\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"}}},{\"not_temp\":{},\"temp\":{\"A\":{\"dataId\":\"A\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"B\":{\"dataId\":\"B\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"C\":{\"dataId\":\"C\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"}}},{\"not_temp\":{},\"temp\":{\"A\":{\"dataId\":\"A\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"B\":{\"dataId\":\"B\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"},\"C\":{\"dataId\":\"C\",\"status\":\"temp\",\"statusReason\":\"remark\",\"syncLogId\":\"sync_log_id\"}}}]"
            , new TypeReference<List<Map<IdSyncStatus, Map<String, DataVerificationIdStatus>>>>() {})
    private static List<List<SyncDataMappingsEntity>> mapping = JSONObject.parseObject("[[],[],[{\"sourceDataId\":\"A\",\"remark\":\"remark\",\"isCreated\":false}],[{\"sourceDataId\":\"A\",\"remark\":\"remark\",\"isCreated\":true}],[{\"sourceDataId\":\"A\",\"remark\":\"remark\",\"isCreated\":true},{\"sourceDataId\":\"B\",\"remark\":\"remark\",\"isCreated\":true},{\"sourceDataId\":\"C\",\"remark\":\"remark\",\"isCreated\":true}]]"
            , new TypeReference<List<List<SyncDataMappingsEntity>>>() {})
    private static List<DataVerificationResult> dataVerificationResults = JSONObject.parseObject("[{\"notTempIdListSize\":3,\"notMappingIdListSize\":0,\"notCreatedIdListSize\":0},{\"notTempIdListSize\":1,\"notMappingIdListSize\":2,\"notCreatedIdListSize\":0},{\"notTempIdListSize\":0,\"notMappingIdListSize\":2,\"notCreatedIdListSize\":1},{\"notTempIdListSize\":0,\"notMappingIdListSize\":2,\"notCreatedIdListSize\":0},{\"notTempIdListSize\":0,\"notMappingIdListSize\":0,\"notCreatedIdListSize\":0}]",
            new TypeReference<List<DataVerificationResult>>() {})

    void setup() {
        dataVerificationTaskDao = Mock()
        dataVerificationIdStatusDao = Mock()
        dataVerificationIdStatusDao.batchInsert(*_) >> {}
        fileManager = Mock()
        eieaConverter = Mock()
        adminSyncPloyDetailDao = Stub();
        adminSyncPloyDetailDao.setTenantId(_) >> adminSyncPloyDetailDao
        erpHistoryDataTaskDao = Stub();
        erpHistoryDataTaskDao.setTenantId(_) >> erpHistoryDataTaskDao
        erpConnectInfoDao = Stub();
        erpConnectInfoDao.setTenantId(_) >> erpConnectInfoDao
        erpObjectRelationshipDao = Stub();
        erpObjectRelationshipDao.setTenantId(_) >> erpObjectRelationshipDao
        erpFieldExtendDao = Stub();
        erpFieldExtendDao.setTenantId(_) >> erpFieldExtendDao
        adminSyncDataMappingsDao = Stub();
        adminSyncDataMappingsDao.setTenantId(_) >> adminSyncDataMappingsDao
        erpTempDataDao = Mock()
        dataVerificationService = new DataVerificationServiceImpl(
                dataVerificationTaskDao: dataVerificationTaskDao,
                dataVerificationIdStatusDao: dataVerificationIdStatusDao,
                fileManager: fileManager,
                eieaConverter: eieaConverter,
                erpTempDataDao: erpTempDataDao,
                adminSyncPloyDetailDao: adminSyncPloyDetailDao,
                erpHistoryDataTaskDao: erpHistoryDataTaskDao,

                erpConnectInfoDao: erpConnectInfoDao,
                erpObjectRelationshipDao: erpObjectRelationshipDao,
                erpFieldExtendDao: erpFieldExtendDao,
                setSyncDataMappingsDao: adminSyncDataMappingsDao
        )
    }

    def "VerifyDataId"() {

    }

    def "VerifyDataIdByTaskNum"() {
    }

    def "VerifyDataIdByFile"() {
    }

    def "VerifyDataIdByIdList"() {
        setup:
        erpTempDataDao.listNotTempIdList(*_) >> { return tempMap.get(index) }
        adminSyncDataMappingsDao.listCreatedBySourceDataIds(*_) >> { return mapping.get(index) }
        for (int i = 0; i < 5; i++) {
            index = i
            when:
            Result<DataVerificationResult> result = dataVerificationService.verifyDataIdByIdList("",
                    tenantId, "", "BD_MATERIAL.BillHead", "ProductObj", "780777150699143168", "BD_MATERIAL", Lists.newArrayList("A", "B", "C"));
            then:
            verifyAll(result.getData()){
                result.isSuccess()
                result.getData() != null
                dataVerificationResults.get(index).getNotTempIdListSize() == notTempIdListSize
                dataVerificationResults.get(index).getNotMappingIdListSize() == notMappingIdListSize
                dataVerificationResults.get(index).getNotCreatedIdListSize() == notCreatedIdListSize
            }
        }

    }

    def "FindNotMappingIdList"() {
    }

    def "FindNotTempIdList"() {

    }

    def "QueryDataVerificationTask"() {
    }

    def "UploadDataVerificationIdField"() {
    }
}
