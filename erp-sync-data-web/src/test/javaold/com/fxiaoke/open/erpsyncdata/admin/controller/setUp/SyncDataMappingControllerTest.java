//package com.fxiaoke.open.erpsyncdata.admin.controller.setUp;
//
//import com.fxiaoke.open.erpsyncdata.BaseTest;
//import com.fxiaoke.open.erpsyncdata.admin.arg.SyncDataMappingListByPloyDetailIdArg;
//import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingListByPloyDetailIdResult;
//import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
//import com.fxiaoke.open.erpsyncdata.web.controller.erp.SyncDataMappingController;
//import com.google.common.collect.Lists;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//
//@Ignore
//public class SyncDataMappingControllerTest extends BaseTest {
//    @Autowired
//    private SyncDataMappingController syncDataMappingController;
//    @Autowired
//    private AdminSyncDataMappingService adminSyncDataMappingService;
//
//    @Test
//    public void listByPloyDetailId() {
//        SyncDataMappingListByPloyDetailIdArg arg = new SyncDataMappingListByPloyDetailIdArg();
//        arg.setPloyDetailId("764537180682584065");
//        arg.setPageSize(20);
//        arg.setPageNumber(1);
////        arg.setStatus(0);
//        arg.setSourceDataId("809296127811223554");
//        Result<SyncDataMappingListByPloyDetailIdResult> result = syncDataMappingController.listByPloyDetailId(arg,null);
//        System.out.println(result);
//    }
//
//    /**
//     * 获取主从信息接口
//     */
//    @Test
//    public void listByPloyDetailId2() {
//        SyncDataMappingListByPloyDetailIdArg arg = new SyncDataMappingListByPloyDetailIdArg();
//        arg.setPloyDetailId("16e87e066bf54fe6b12274f381eae514");
//        arg.setPageSize(20);
//        arg.setPageNumber(1);
//        //arg.setStatus(0);
//        arg.setSyncDataMappingId("1b93f10b36814ebe86f44f375a8e656f");
//        Result<SyncDataMappingListByPloyDetailIdResult> result = syncDataMappingController.listByPloyDetailId(arg,null);
//        System.out.println(result);
//    }
//
//    /**
//     * 根据主对象过滤接口
//     */
//    @Test
//    public void listByPloyDetailId3() {
//        SyncDataMappingListByPloyDetailIdArg arg = new SyncDataMappingListByPloyDetailIdArg();
//        arg.setPloyDetailId("671b2cb0991d437fa5ba4e5ec67a7556");
//        arg.setPageSize(20);
//        arg.setPageNumber(1);
//        arg.setStatus(0);
//        arg.setSourceObjectApiName("STK_TransferDirect.BillHead");
//        //arg.setDestObjectApiName("object_h2n7v__c");
//        Result<SyncDataMappingListByPloyDetailIdResult> result = syncDataMappingController.listByPloyDetailId(arg,null);
//        System.out.println(result);
//    }
//
//    /**
//     * 根据从对象过滤接口
//     */
//    @Test
//    public void listByPloyDetailId4() {
//        SyncDataMappingListByPloyDetailIdArg arg = new SyncDataMappingListByPloyDetailIdArg();
//        arg.setPloyDetailId("671b2cb0991d437fa5ba4e5ec67a7556");
//        arg.setPageSize(20);
//        arg.setPageNumber(1);
//        arg.setStatus(0);
//        arg.setSourceObjectApiName("STK_TransferDirect.TransferDirectEntry");
//        //arg.setDestObjectApiName("object_2Y72t__c");
//        Result<SyncDataMappingListByPloyDetailIdResult> result = syncDataMappingController.listByPloyDetailId(arg,null);
//        System.out.println(result);
//    }
//
//    @Test
//    public void deleteBySyncDataMappingIds() {
//        List<String> ids = Lists.newArrayList("64c33aa01e2f890001bba834");
//        adminSyncDataMappingService.deleteBySyncDataMappingIds(88466,
//                "6436278b3dcc6b0001e76652",
//                1000,
//                ids,
//                "68ca8e2895e943ff8dd3437ed67b08e5", null);
//    }
//}
