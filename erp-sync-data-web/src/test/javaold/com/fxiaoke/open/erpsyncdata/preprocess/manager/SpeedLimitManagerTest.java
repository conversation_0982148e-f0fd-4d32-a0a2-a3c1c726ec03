package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 15:39 2021/7/30
 * @Desc:
 */
@Ignore
public class SpeedLimitManagerTest extends BaseTest {
    @Autowired
    private SpeedLimitManager speedLimitManager;


    @Test
    public void count2ErpMsgQuantity() {
    }

    @Test
    public void getCount2CrmMsgQuantity() {
    }

    @Test
    public void count2CrmMsgQuantity() {
    }
}